package com.jdh.o2oservice.facade.angel;

import com.jdh.o2oservice.application.angel.service.AngelLocationApplication;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angel.AngelLocationExport;
import com.jdh.o2oservice.export.angel.dto.AngelLocationDto;
import com.jdh.o2oservice.export.angel.query.AngelLocationRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-04-24 18:14
 * @Desc : 服务者地理位置
 */
@Component("angelLocationExport")
public class AngelLocationExportImpl implements AngelLocationExport {
    @Autowired
    private AngelLocationApplication angelLocationApplication;
    /**
     * 查询护士经纬度
     * @param request
     * @return
     */
    @Override
    public Response<AngelLocationDto> getLocation(AngelLocationRequest request) {
        AngelLocationDto res = angelLocationApplication.getLocation(request.getAngelId());
        return ResponseUtil.buildSuccResponse(res);
    }
}
