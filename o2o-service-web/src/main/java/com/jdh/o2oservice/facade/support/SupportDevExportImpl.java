package com.jdh.o2oservice.facade.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.jd.common.web.LoginContext;
import com.jd.i18n.order.dict.FieldKeyEnum;
import com.jd.jim.cli.Cluster;
import com.jd.jim.cli.protocol.KeyScanResult;
import com.jd.jim.cli.protocol.ScanOptions;
import com.jd.purchase.domain.old.bean.Cart;
import com.jd.purchase.domain.old.bean.Order;
import com.jd.purchase.utils.serializer.helper.SerializersHelper;
import com.jdh.o2oservice.application.angel.service.ActivityApplication;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelSkillDictApplication;
import com.jdh.o2oservice.application.angel.service.SsApplication;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductDataCleanApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderApplication;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.settlement.service.JdServiceSettleApplication;
import com.jdh.o2oservice.application.support.service.*;
import com.jdh.o2oservice.application.trade.service.JdOrderFullApplication;
import com.jdh.o2oservice.application.via.ManViaApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.JdhStoreTransferStationTypeEnum;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.event.EventConsumerTask;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.extension.ConsumerTaskRepository;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.mybatisplus.MaxLimitInterceptor;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angel.model.JdhStation;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.AngelStationPageQuery;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchIdentifier;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.rpc.RiskQueryServiceRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.DispatchRiskStrategyParam;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepPageQuery;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStation;
import com.jdh.o2oservice.core.domain.provider.model.JdhStoreTransferStationRel;
import com.jdh.o2oservice.core.domain.provider.repository.db.JdhStoreTransferStationRelRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.JdhStoreTransferStationRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderStoreRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhVoucherStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.model.DomainAppointmentTime;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DirectionServiceRpc;
import com.jdh.o2oservice.core.domain.support.reach.enums.ReachTypeEnum;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachMessage;
import com.jdh.o2oservice.core.domain.support.reach.repository.db.JdhReachConfigRepository;
import com.jdh.o2oservice.core.domain.trade.enums.TradeEventTypeEnum;
import com.jdh.o2oservice.core.domain.trade.event.OrderSplitEventBody;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.export.angel.cmd.AngelActivityRecruitmentCmd;
import com.jdh.o2oservice.export.angel.cmd.AngelSyncCmd;
import com.jdh.o2oservice.export.angel.cmd.CreateAngelSkillDictCmd;
import com.jdh.o2oservice.export.angel.dto.AngelSkillDictDto;
import com.jdh.o2oservice.export.angel.dto.GetUserAccountDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelPageRequest;
import com.jdh.o2oservice.export.angel.query.AngelSkillDictRequest;
import com.jdh.o2oservice.export.dispatch.cmd.*;
import com.jdh.o2oservice.export.laboratory.dto.JdhStoreTransferStationDto;
import com.jdh.o2oservice.export.laboratory.dto.QueryMerchantStoreDetailResponse;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantStoreDetailByParamRequest;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantStoreListByParamRequest;
import com.jdh.o2oservice.export.promise.cmd.CancelAppointCmd;
import com.jdh.o2oservice.export.promise.cmd.CreateVoucherCmd;
import com.jdh.o2oservice.export.promise.cmd.ExpireVoucherCmd;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.query.VoucherPageRequest;
import com.jdh.o2oservice.export.report.cmd.MedicalReportSaveCmd;
import com.jdh.o2oservice.export.support.SupportDevExport;
import com.jdh.o2oservice.export.support.command.DistributeFeedbackCmd;
import com.jdh.o2oservice.export.support.command.ImitatePublishEventCmd;
import com.jdh.o2oservice.export.support.command.PricingServiceCalculateCmd;
import com.jdh.o2oservice.export.support.command.SubmitAppointmentStartCmd;
import com.jdh.o2oservice.export.support.dto.PricingServiceCalculateResultDto;
import com.jdh.o2oservice.export.trade.dto.JdOrderFullDTO;
import com.jdh.o2oservice.export.trade.query.JdOrderFullPageParam;
import com.jdh.o2oservice.export.via.dto.ViaCompletePromiseDto;
import com.jdh.o2oservice.export.via.query.ViaCompletePromiseRequest;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelWorkPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelWorkPo;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhReachMessagePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachMessagePo;
import com.jdh.o2oservice.job.support.PopOrderExpireScanJob;
import com.jdh.o2oservice.job.support.PromiseAppointmentBeforeScanJob;
import com.jdh.o2oservice.job.support.PromiseAppointmentTodayScanJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SupportDevExportImpl
 *
 * <AUTHOR>
 * @date 2024/01/18
 */
@Service
@Slf4j
public class SupportDevExportImpl implements SupportDevExport {

    /**
     * voucherApplication
     */
    @Autowired
    private VoucherApplication voucherApplication;

    /**
     *
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * generateIdFactory
     */
    @Autowired
    private GenerateIdFactory generateIdFactory;

    /**
     * devApplication
     */
    @Resource
    private DevApplication devApplication;

    /**
     * cluster
     */
    @Autowired
    private Cluster cluster;

    /**
     * TdeClientUtil
     */
    @Autowired
    private TdeClientUtil tdeClientUtil;

    /**
     * dispatchApplication
     */
    @Autowired
    private DispatchApplication dispatchApplication;

    /**
     * activityApplication
     */
    @Autowired
    private ActivityApplication activityApplication;

    /**
     * supportDevApplication
     */
    @Autowired
    private SupportDevApplication supportDevApplication;

    /**
     * 触达application
     */
    @Resource
    private ReachApplication reachApplication;

    /**
     * es同步
     */
    @Autowired
    private JdOrderFullApplication jdOrderFullApplication;

    /**
     * 护士
     */
    @Resource
    private AngelApplication angelApplication;

    /**
     * 商品域数据清洗
     */
    @Resource
    private ProductDataCleanApplication productDataCleanApplication;

    /**
     * promiseRepository
     */
    @Autowired
    private PromiseRepository promiseRepository;

    /**
     * angelShipRepository
     */
    @Autowired
    private AngelShipRepository angelShipRepository;

    /**
     * 商家门店仓库
     */
    @Resource
    ProviderStoreRepository providerStoreRepository;

    /**
     * manViaApplication
     */
    @Autowired
    private ManViaApplication manViaApplication;

    /**
     * angelSkillDictApplication
     */
    @Resource
    AngelSkillDictApplication angelSkillDictApplication;

    /**
     * popOrderExpireScanJob
     */
    @Resource
    private PopOrderExpireScanJob popOrderExpireScanJob;

    /**
     * promiseAppointmentBeforeScanJob
     */
    @Resource
    private PromiseAppointmentBeforeScanJob promiseAppointmentBeforeScanJob;

    /**
     * promiseAppointmentTodayScanJob
     */
    @Resource
    private PromiseAppointmentTodayScanJob promiseAppointmentTodayScanJob;

    /**
     * jdhPromisePoMapper
     */
    @Autowired
    private JdhPromisePoMapper jdhPromisePoMapper;
    /**
     * jdhPromisePoMapper
     */
    @Autowired
    private JdhAngelWorkPoMapper jdhAngelWorkPoMapper;

    /**
     * duccConfig
     */
    @Autowired
    private DuccConfig duccConfig;

    /**
     * 订单rpc
     */
    @Resource
    OrderInfoRpc orderInfoRpc;

    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    @Autowired
    private UserFeedbackApplication userFeedbackApplication;
    /**
     * autoTestSupportApplication
     */
    @Autowired
    private AutoTestSupportApplication autoTestSupportApplication;

    /**
     * orderListByParentId
     */
    @Autowired
    private JdOrderRepository jdOrderRepository;

    @Resource
    private SsApplication ssApplication;

    @Resource
    private PromiseApplication promiseApplication;

    @Resource
    private JdServiceSettleApplication jdServiceSettleApplication;

    /**
     *
     */
    @Resource
    private PricingServiceApplication pricingServiceApplication;
    @Resource
    private ConsumerTaskRepository consumerTaskRepository;

    @Resource
    private JdhStoreTransferStationRepository jdhStoreTransferStationRepository;

    @Resource
    private JdhStoreTransferStationRelRepository jdhStoreTransferStationRelRepository;

    @Resource
    JdhStationRepository jdhStationRepository;

    /**
     * 接口信息
     */
    @Resource
    ProviderStoreApplication providerStoreApplication;

    @Resource
    ProviderApplication providerApplication;



    /**
     * 创建voucher单
     *
     * @param cmd CMD
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.createVoucher")
    public Response<List<PromiseDto>> createVoucher(CreateVoucherCmd cmd) {
        return ResponseUtil.buildSuccResponse(voucherApplication.createVoucher(cmd));
    }

    /**
     * 创建voucher单
     *
     * @param cmdList CMD
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.batchCreateVoucher")
    public Response<Boolean> batchCreateVoucher(List<CreateVoucherCmd> cmdList) {
        return ResponseUtil.buildSuccResponse(voucherApplication.batchCreateVoucher(cmdList));
    }

    /**
     * 手动处置事件
     *
     * @param eventId      事件ID
     * @param consumerCode 消费者代码
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.disposeAsyncEvent")
    public Response<Boolean> disposeAsyncEvent(Long eventId, String consumerCode) {
        eventCoordinator.disposeAsyncEvent(eventId, consumerCode);
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    @Override
    public Response<Boolean> batchDisposeAsyncEvent(List<Long> eventIds, String consumerCode) {
        CompletableFuture.runAsync(()->{
            for (Long eventId : eventIds) {
                eventCoordinator.disposeAsyncEvent(eventId, consumerCode);
            }
        });

        return null;
    }

    /**
     * 模仿发布事件
     *
     * @param cmd cmd
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.imitatePublishEvent")
    public Response<Boolean> imitatePublishEvent(ImitatePublishEventCmd cmd) {
        return ResponseUtil.buildSuccResponse(supportDevApplication.imitatePublishEvent(cmd));
    }

    /**
     * 获取ID
     *
     * @return {@link Response}<{@link String}>
     */
    @Override
    public Response<String> getId() {
        return ResponseUtil.buildSuccResponse(generateIdFactory.getIdStr());
    }

    /**
     * 批次获取ID
     *
     * @param num num
     * @return {@link Response}<{@link List}<{@link String}>>
     */
    @Override
    public Response<List<String>> batchGetId(Integer num) {
        return ResponseUtil.buildSuccResponse(generateIdFactory.getBatchId(num).stream().map(Object::toString).collect(Collectors.toList()));
    }

    @Resource
    private TransferApplication transferApplication;
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.syncOrder")
    public Response<String> syncOrder(String orderId, String createTime) {
        String logid= Objects.toString(MDC.get("PFTID"), null);
        CompletableFuture.runAsync(()->{
            try {
                MDC.put("PFTID", logid);
                log.info("SupportDevExportImpl->start syncOrder orderId={}, createTime={}", orderId, createTime);
                transferApplication.syncPopLocOldOrder(orderId, createTime);
            }finally {
                MDC.remove("PFTID");
            }
        });
        return ResponseUtil.buildSuccResponse("true");
    }

    @Override
    public Response<String> syncPromise(String orderId, String createTime) {
        String logid= Objects.toString(MDC.get("PFTID"), null);
        CompletableFuture.runAsync(()->{
            try {
                MDC.put("PFTID", logid);
                log.info("SupportDevExportImpl->start syncPromise orderId={}, createTime={}", orderId, createTime);
                transferApplication.syncPopLocOldAppointment(orderId);
            }finally {
                MDC.remove("PFTID");
            }
        });
        return ResponseUtil.buildSuccResponse("true");
    }

    /**
     * 缓存批处理删除
     *
     * @param keyList keyList
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.batchDelCacheByKeyList")
    public Response<Boolean> batchDelCacheByKeyList(List<String> keyList) {
        if (CollUtil.isNotEmpty(keyList)){
            cluster.del(ArrayUtil.toArray(keyList, String.class));
        }
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    /**
     * 缓存批处理删除
     *
     * @param keyPrefix keyPrefix
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.batchGetCacheKeyByPrefix")
    public Response<List<String>> batchGetCacheKeyByPrefix(String keyPrefix) {
        return ResponseUtil.buildSuccResponse(this.scanKeyList(keyPrefix));
    }

    /**
     * scanKeyList
     *
     * @param keyPrefix keyPrefix
     * @return {@link List}<{@link String}>
     */
    private List<String> scanKeyList(String keyPrefix){
        ScanOptions.ScanOptionsBuilder scanOptions = ScanOptions.scanOptions();
        scanOptions.count(1000);
        scanOptions.match(keyPrefix);
        KeyScanResult<String> scan = new KeyScanResult();
        //为""表示从头开始遍历key
        scan.setCursor("");
        List<String> result = new ArrayList<>();
        //判断是否遍历结束
        while (!scan.isFinished()) {
            try {
                scan = cluster.scan(scan.getCursor(), scanOptions.build());
                result.addAll(scan.getResult());
            } catch (Exception e) {
                log.error("{}", scan.getCursor(), e);
            }
        }
        return result;
    }

    /**
     * 缓存批处理删除
     *
     * @param keyPrefix keyPrefix
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.batchDelCacheByPrefix")
    public Response<Boolean> batchDelCacheByPrefix(String keyPrefix) {
        List<String> keyList = this.scanKeyList(keyPrefix);
        if (CollUtil.isNotEmpty(keyList)){
            cluster.del(ArrayUtil.toArray(keyList, String.class));
        }
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    /**
     * 获取字符串缓存值
     *
     * @param key 关键
     * @return {@link Response}<{@link String}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.getStringCacheByKey")
    public Response<String> getStringCacheByKey(String key) {
        return ResponseUtil.buildSuccResponse(cluster.get(key));
    }

    /**
     *
     * @param fromLocation
     * @param toLocation
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.getDirectionResult")
    public Response<String> getDirectionResult(String fromLocation, String toLocation){
        String result = devApplication.getDirectionResult(fromLocation, toLocation, DirectionServiceRpc.TravelMode.BICYCLING);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 是可解密
     *
     * @param str str
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.isDecryptable")
    public Response<Boolean> isDecryptable(String str) {
        return ResponseUtil.buildSuccResponse(tdeClientUtil.isDecryptable(str));
    }

    /**
     * TDE解密
     *
     * @param cipherText 密文
     * @return {@link Response}<{@link String}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.tdeDecrypt")
    public Response<String> tdeDecrypt(String cipherText) {
        return ResponseUtil.buildSuccResponse(tdeClientUtil.decrypt(cipherText));
    }

    /**
     * TDE加密
     *
     * @param writePlaintext 明文
     * @return {@link Response}<{@link String}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.tdeEncrypt")
    public Response<String> tdeEncrypt(String writePlaintext) {
        return ResponseUtil.buildSuccResponse(tdeClientUtil.encrypt(writePlaintext));
    }

    /**
     * 计算通配符关键字
     *
     * @param writePlaintext 明文
     * @return {@link Response}<{@link String}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.calculateWildCardKeyWord")
    public Response<String> calculateWildCardKeyWord(String writePlaintext) {
        return ResponseUtil.buildSuccResponse(tdeClientUtil.calculateWildCardKeyWord(writePlaintext));
    }

    /**
     * 计算通配符关键字
     *
     * @param writePlaintext 明文
     * @return {@link Response}<{@link String}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.obtainWildCardKeyWordIndex")
    public Response<String> obtainWildCardKeyWordIndex(String writePlaintext) {
        return ResponseUtil.buildSuccResponse(tdeClientUtil.obtainWildCardKeyWordIndex(writePlaintext));
    }


    /**
     * 计算通配符关键字
     *
     * @param writePlaintext 明文
     * @return {@link Response}<{@link String}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.obtainKeyWordIndex")
    public Response<String> obtainKeyWordIndex(String writePlaintext) {
        return ResponseUtil.buildSuccResponse(tdeClientUtil.obtainKeyWordIndex(writePlaintext));
    }

    /**
     * 手动处理派单结果
     * @param cmdStr
     * @return
     */
    @Override
    public Response<Boolean> createDispatchDetail(String cmdStr) {
        DispatchCallbackCmd cmd = JSON.parseObject(cmdStr, DispatchCallbackCmd.class);
        dispatchApplication.callBack(cmd);
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    /**
     * 计算通配符关键字
     *
     * @param writePlaintext 明文
     * @return {@link Response}<{@link String}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.calculateKeyWord")
    public Response<String> calculateKeyWord(String writePlaintext) {
        return ResponseUtil.buildSuccResponse(tdeClientUtil.calculateKeyWord(writePlaintext));
    }

    /**
     * 创建派单任务
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.calculateKeyWord")
    public Response<Boolean> submitDispatch(SubmitDispatchCmd cmd){
        return ResponseUtil.buildSuccResponse(dispatchApplication.submitDispatch(cmd));
    }

    /**
     * 处理互医侧派单
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> handleNewNethpDispatch(DispatchNewNethpHandleCmd cmd) {
        return ResponseUtil.buildSuccResponse(dispatchApplication.handleNewNethpDispatch(cmd));
    }

    /**
     * 刷新履约人信息
     *
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.refreshPromisePatient")
    public Response<Boolean> refreshPromisePatient(String verticalCode,String createTime) {
        return ResponseUtil.buildSuccResponse(supportDevApplication.refreshPromisePatient(verticalCode,createTime));
    }

    /**
     * 刷新履约人信息
     *
     * @param promiseIdList
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.refreshPromisePatient")
    public Response<Boolean> refreshPromisePatientByPromiseId(List<Long> promiseIdList) {
        return ResponseUtil.buildSuccResponse(supportDevApplication.refreshPromisePatientByPromiseId(promiseIdList));
    }

    @Override
    public Response<String> generatePutUrl(String contentType, String filePath) {
        return ResponseUtil.buildSuccResponse(devApplication.generatePutUrl(contentType, filePath));
    }

    /**
     * 导入标准指标
     * @param ossKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.importStandardIndicator")
    public Response<Boolean> importStandardIndicator(String ossKey){
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.importStandardIndicator(ossKey));
    }

    /**
     * 导出新老指标映射
     * @param mappingOssKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.exportMappingIndicator")
    public Response<Boolean> exportMappingIndicator(String mappingOssKey){
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.exportMappingIndicator(mappingOssKey));
    }

    /**
     * 导出新老数据清洗结果
     * @param mappingOssKey
     * @param type
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.exportProgramItemData")
    public Response<Boolean> exportProgramItemData(String mappingOssKey, Integer type, Integer partNum){
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.exportProgramItemData(mappingOssKey, type, partNum));
    }

    /**
     * 清洗业务项目数据（老 -> 新）
     * @param mappingOssKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.cleanBizServiceItem")
    public Response<Boolean> cleanBizServiceItem(String mappingOssKey){
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.cleanBizServiceItem(mappingOssKey));
    }

    /**
     * 清洗自营商品项目
     * @param mappingOssKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.cleanSelfServiceItem")
    public Response<Boolean> cleanSelfServiceItem(String ossKey, String mappingOssKey){
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.cleanSelfServiceItem(ossKey, mappingOssKey));
    }

    /**
     * 新增自营商品项目
     * @param ossKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.addSelfServiceItem")
    public Response<Boolean> addSelfServiceItem(String ossKey){
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.addSelfServiceItem(ossKey));
    }
    /**
     * 清洗京东服务
     * @param mappingOssKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.cleanService")
    public Response<Boolean> cleanService(String mappingOssKey){
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.cleanService(mappingOssKey));
    }

    /**
     * 清洗自营商品套餐和项目关系
     * @param mappingOssKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.cleanSelfServiceRel")
    public Response<Boolean> cleanSelfServiceRel(String mappingOssKey){
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.cleanSelfServiceRel(mappingOssKey));
    }

    /**
     * 清洗pop商品数据
     * @param ossKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.cleanPopProductSku")
    public Response<Boolean> cleanPopProductSku(String ossKey){
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.cleanPopProductSku(ossKey));
    }

    /**
     * 修正pop sku信息
     * @param ossKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.revisePopProductSku")
    public Response<Boolean> revisePopProductSku(String ossKey, String mappingOssKey) {
       return ResponseUtil.buildSuccResponse(productDataCleanApplication.revisePopProductSku(ossKey, mappingOssKey));
    }

    /**
     * 导出老项目创建新业务项目
     * @param ossKey
     * @param standardItemId
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.importOldItemList")
    public Response<Boolean> importOldItemList(String ossKey, Long standardItemId, Set<Long> serviceIdList) {
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.importOldItemList(ossKey, standardItemId, serviceIdList));
    }

    /**
     *
     * @param ossKey1
     * @param ossKey2
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.comparePopProductSku")
    public Response<Boolean> comparePopProductSku(String ossKey1, String ossKey2) {
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.comparePopProductSku(ossKey1, ossKey2));
    }

    /**
     *
     * @param ossKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.refreshProductSkuSuitable")
    public Response<Boolean> refreshProductSkuSuitable(String ossKey) {
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.refreshProductSkuSuitable(ossKey));
    }

    /**
     * 清洗pop商品项目
     * @param ossKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.cleanPopProductItem")
    public Response<Boolean> cleanPopProductItem(String ossKey) {
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.cleanPopProductItem(ossKey));
    }

    /**
     * 清洗商品和项目结算价
     * @param ossKey
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> cleanSkuItemSettlementPrice(String ossKey) {
        return ResponseUtil.buildSuccResponse(jdServiceSettleApplication.cleanSkuItemSettlementPrice(ossKey));
    }

    /**
     * 清洗自营SKU套餐项目名称
     * @param ossKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.cleanSelfProgramItemName")
    public Response<Boolean> cleanSelfProgramItemName(String ossKey) {
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.cleanSelfProgramItemName(ossKey));
    }

    /**
     * 修正自营 项目数据
     * @param ossKey
     * @param mappingOssKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.reviseSelfProductSku")
    public Response<Boolean> reviseSelfProductSku(String ossKey, String mappingOssKey) {
       return ResponseUtil.buildSuccResponse(productDataCleanApplication.reviseSelfProductSku(ossKey, mappingOssKey));
    }

    /**
     *
     * @param json
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.supplementProductSku")
    public Response<Boolean> supplementProductSku(String json) {
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.supplementProductSku(json));
    }

    /**
     *
     * @param json
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.refreshProductSku")
    public Response<Boolean> refreshProductSku(String json) {
        return ResponseUtil.buildSuccResponse(productDataCleanApplication.refreshProductSku(json));
    }

    @Override
    public Response<Boolean> executeTask(String taskId) {
        reachApplication.executeTask(Long.valueOf(taskId));
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.testFullOrder")
    public Response<Boolean> reloadFullOrderInfo(String promiseIds) {
        try {
            String[] promiseIdArray = promiseIds.split(",");
            for(String promiseIdStr: promiseIdArray){
                jdOrderFullApplication.reloadFullOrderInfo(new Long(promiseIdStr));
            }
            return ResponseUtil.buildSuccResponse(true);
        }catch (Exception e){
            log.error("",e);
        }
        return ResponseUtil.buildSuccResponse(false);
    }

    /**
     * 同步护士数据
     * @param angelSyncCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.syncAngelAuditInfoFromNethp")
    public Response<Boolean> syncAngelAuditInfoFromNethp(AngelSyncCmd angelSyncCmd){
        angelApplication.syncAngelAuditInfoFromNethp(angelSyncCmd);
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * reloadFullOrderInfoBatch
     *
     * @param verticalCode 业务身份
     * @param createTime   创建时间
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.support.SupportDevExportImpl.reloadFullOrderInfoBatch")
    public Response<Boolean> reloadFullOrderInfoBatch(String verticalCode, String createTime) {
        if(StrUtil.isEmpty(verticalCode) && StrUtil.isEmpty(createTime)){
            return ResponseUtil.buildSuccResponse(false);
        }
        int pageNum = 1;
        int pageSize = 50;
        PromiseRepPageQuery pageQuery = PromiseRepPageQuery.builder()
                .verticalCode(verticalCode)
                .createTimeStart(TimeUtils.timeStrToDate(createTime, TimeFormat.LONG_PATTERN_LINE))
                .build();
        pageQuery.setPageSize(pageSize);
        boolean hasNextPage;
        do {
            pageQuery.setPageNum(pageNum);
            Page<JdhPromise> page = promiseRepository.page(pageQuery);
            log.info("SupportDevExportImpl -> reloadFullOrderInfoBatch page:{}",JSON.toJSONString(page));
            if(Objects.isNull(page)){
                break;
            }

            List<JdhPromise> records = page.getRecords();
            if(CollUtil.isEmpty(records)){
                break;
            }
            hasNextPage = page.hasNext();
            records.forEach(ele -> {
                try {
                    log.info("SupportDevExportImpl -> reloadFullOrderInfoBatch ele:{}",JSON.toJSONString(ele));
                    jdOrderFullApplication.reloadFullOrderInfo(ele.getPromiseId());
                }catch (Exception e){
                    log.error("SupportDevExportImpl -> reloadFullOrderInfoBatch error",e);
                }

            });
            pageNum ++;
        }while (hasNextPage);

        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * triggerJob
     *
     * @param jobName 作业名称
     * @param method  方法
     * @return {@link Response}<{@link Boolean}>
     */
    @LogAndAlarm
    @Override
    public Response<Boolean> triggerJob(String jobName, String method) {


        ExecutorService pool = ExecutorPoolFactory.getForkJoinPoolByPFinder();
        CompletableFuture.runAsync(()->{
            try {
                if (StringUtils.equals("popOrderExpireScanJob", jobName)) {
                    if (StringUtils.equals(method, "tenDaySms")) {
                        popOrderExpireScanJob.tenDaySms();
                    }
                } else if (StringUtils.equals("promiseAppointmentBeforeScanJob", jobName)) {
                    promiseAppointmentBeforeScanJob.scanBeforeDay();
                } else if (StringUtils.equals("promiseAppointmentTodayScanJob", jobName)) {
                    promiseAppointmentTodayScanJob.scanToday();
                }
            }catch (Exception r){
                log.error("triggerJob error", r);
            }

        }, pool);
        return null;
    }

    /**
     * 重新派单
     * @return
     */
    @Override
    public Response<Boolean> reDispatch(ReDispatchCmd cmd) {
        cmd.setRoleType(NumConstant.NUM_4);
        return ResponseUtil.buildSuccResponse(dispatchApplication.reDispatch(cmd));
    }

    /**
     * 指定派单
     * @param cmd
     * @return
     */
    @Override
    public Response<Boolean> targetDispatch(TargetDispatchCmd cmd) {
        cmd.setRoleType(NumConstant.NUM_4);
        return ResponseUtil.buildSuccResponse(dispatchApplication.targetDispatch(cmd).getTargetResult());
    }

    /**
     * 接单护士转单
     * @param cmd
     * @return
     */
    @Override
    public Response<Boolean> receiveTransferDispatch(TargetDispatchCmd cmd) {
        cmd.setRoleType(NumConstant.NUM_4);
        return ResponseUtil.buildSuccResponse(dispatchApplication.receiveTransferDispatch(cmd).getTargetResult());
    }

    /**
     * @Description: 分页查询服务者信息
     * @param request
     **/
    @Override
    public Response<PageDto<JdhAngelDto>> queryAngelByPage(AngelPageRequest request) {
        return ResponseUtil.buildSuccResponse(angelApplication.queryAngelByPage(request));
    }

    /**
     * expireVoucher
     *
     * @param itemIds itemIds
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    public Response<Boolean> queryStationListForServiceItemAllInOneStore(List<Long> itemIds) {
        List<JdhStationServiceItemRel> jdhStationServiceItemRel = new ArrayList<>();
        itemIds.forEach(s -> {
            JdhStationServiceItemRel rel = new JdhStationServiceItemRel();
            rel.setServiceItemId(s);
            jdhStationServiceItemRel.add(rel);
        });
        providerStoreRepository.queryStationListForServiceItemAllInOneStore(jdhStationServiceItemRel);
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * 查询es的服务单列表
     *
     * @param param param
     * @return {@link Response}<{@link PageDto}<{@link JdOrderFullDTO}>>
     */
    @Override
    @LogAndAlarm
    public Response<PageDto<JdOrderFullDTO>> queryPage(JdOrderFullPageParam param) {
        PageDto<JdOrderFullDTO> dtoPageDto = jdOrderFullApplication.queryPage(param);

        //如果有工单，组装运单
        if (Objects.nonNull(dtoPageDto) && CollectionUtils.isNotEmpty(dtoPageDto.getList())) {
            //遍历取出工单id
            Set<Long> workIds = dtoPageDto.getList().stream().filter(jdOrderFullDTO -> StringUtils.isNotBlank(jdOrderFullDTO.getWorkId())).map(jdOrderFullDTO -> Long.valueOf(jdOrderFullDTO.getWorkId())).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(workIds)){
                //查询工单对应的运单列表
                AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
                angelShipDBQuery.setWorkIds(workIds);
                List<AngelShip> angelShipList = angelShipRepository.findList(angelShipDBQuery);
                if (CollectionUtils.isNotEmpty(angelShipList)){
                    //按照工单ID对运单列表分组
                    Map<Long, List<AngelShip>> workId2ShipList = angelShipList.stream().collect(Collectors.groupingBy(AngelShip::getWorkId));
                    for (JdOrderFullDTO jdOrderFullDTO : dtoPageDto.getList()) {
                        if (StringUtils.isBlank(jdOrderFullDTO.getWorkId())) {
                            continue;
                        }
                        List<AngelShip> angelShips = workId2ShipList.get(Long.valueOf(jdOrderFullDTO.getWorkId()));
                        if (CollectionUtils.isEmpty(angelShips)) {
                            continue;
                        }
                        //如果运单送到实验室ID与实验室履约单记录的实验室ID一致，补充运单信息
                        Optional<AngelShip> optional = angelShips.stream().filter(p -> StringUtils.equals(p.getReceiverId(), jdOrderFullDTO.getLaboratoryStationId())).findFirst();
                        if (optional.isPresent()) {
                            AngelShip angelShip = optional.get();
                            jdOrderFullDTO.setShipId(angelShip.getShipId());
                            jdOrderFullDTO.setOutShipId(angelShip.getOutShipId());
                        }
                    }
                }
            }
        }
        return ResponseUtil.buildSuccResponse(dtoPageDto);
    }

    /**
     * queryCompletePromise
     *
     * @param request 请求
     * @return {@link Response}<{@link ViaCompletePromiseDto}>
     */
    @Override
    @LogAndAlarm
    public Response<ViaCompletePromiseDto> queryCompletePromise(ViaCompletePromiseRequest request) {
        ViaCompletePromiseDto promiseDto = manViaApplication.queryCompletePromise(request);
        return ResponseUtil.buildSuccResponse(promiseDto);
    }

    /**
     * 刷新es数据
     *
     * @param serviceType
     * @param createTime
     * @param endTime
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    public Response<Boolean> refreshFullOrderIndex(String serviceType, String createTime, String endTime) {
        int pageNum = 1;
        int pageSize = 500;

        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhPromisePo::getServiceType,serviceType);
        queryWrapper.gt(JdhPromisePo::getCreateTime,TimeUtils.timeStrToDate(createTime,TimeFormat.LONG_PATTERN_LINE));
        queryWrapper.lt(JdhPromisePo::getCreateTime,TimeUtils.timeStrToDate(endTime,TimeFormat.LONG_PATTERN_LINE));
        queryWrapper.eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByAsc(JdhPromisePo::getId);

        IPage<JdhPromisePo> page = null;
        do {
            try {
                Page<JdhPromisePo> param = new Page<>(pageNum, pageSize);
                param.setOptimizeCountSql(false);
                page = jdhPromisePoMapper.selectPage(param, queryWrapper);
                if (page == null || CollectionUtil.isEmpty(page.getRecords())){
                    break;
                }
                log.info("SupportDevExportImpl refreshFullOrderIndex serviceType:{} totalPage:{} pageNum:{} total:{}",serviceType,page.getPages(),page.getCurrent(),page.getTotal());
                for (JdhPromisePo promisePo : page.getRecords()) {

                    try {
                        jdOrderFullApplication.reloadFullOrderInfo(promisePo.getPromiseId());
                    }catch (Exception e){
                        //ignore
                        log.error("SupportDevExportImpl refreshFullOrderIndex Exception promisePo:{}",JSON.toJSONString(promisePo),e);
                    }

                }
                pageNum++;
            }catch (Exception e){
                log.error("SupportDevExportImpl refreshFullOrderIndex Exception",e);
                break;
            }
        }while (true);
        log.info("SupportDevExportImpl refreshFullOrderIndex reload end serviceType:{} ",serviceType);

        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    /**
     * expireVoucher
     *
     * @param voucherId 凭证id
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    public Response<Boolean> expireVoucher(Long voucherId) {
        Boolean expireVoucher = voucherApplication.expireVoucher(ExpireVoucherCmd.builder().voucherId(voucherId).build());
        return ResponseUtil.buildSuccResponse(expireVoucher);
    }

    /**
     * 发起派单
     * @param cmd
     * @return
     */
    @Override
    public Response<Boolean> executeDispatch(AngelDispatchCmd cmd) {
        return ResponseUtil.buildSuccResponse(dispatchApplication.angelDispatch(cmd));
    }

    /**
     * 派单拉完成
     * @param promiseIds
     * @return
     */
    @Override
    public Response<Boolean> dispatchComplete(List<String> promiseIds) {
        if (CollectionUtil.isEmpty(promiseIds)) {
            return ResponseUtil.buildSuccResponse(Boolean.TRUE);
        }
        Integer count = 0;
        for (String promiseId : promiseIds) {
            try {
                DispatchCompleteCmd cmd = DispatchCompleteCmd.builder().promiseId(Long.valueOf(promiseId)).build();
                dispatchApplication.dispatchComplete(cmd);
                count++;
            } catch (Exception e) {
                log.error("SupportDevExportImpl executeDispatchComplete Exception promiseId:{}",promiseId,e);
            }
        }
        log.info("SupportDevExportImpl executeDispatchComplete done count:{}",count);
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    /**
     * 护士活动同步状态
     * @param cmd
     * @return
     */
    @Override
    public Response<Boolean> syncAngelActivityProgress(AngelActivityRecruitmentCmd cmd) {
        return ResponseUtil.buildSuccResponse(activityApplication.syncAngelActivityProgress(cmd));
    }

    /**
     * getExcludeAlarmCode
     *
     * @return {@link Response}<{@link Object}>
     */
    @Override
    public Response<Object> getExcludeAlarmCode() {
        //Map<String, List<String>> excludeAlarmCode = duccConfig.getExcludeAlarmCode();
        return ResponseUtil.buildSuccResponse(duccConfig.getExcludeAlarmCode());
    }

    /**
     * 订单大数据
     *
     * @param orderId
     * @return {@link Response}<{@link Object}>
     */
    @Override
    public Response<Object> getOrderDataInfo(Long orderId) {
        Map<String, Object> retMap = new HashMap<>();
        // Long originOrderId = orderInfoRpc.getOriginalOrderId(orderId);
        Long originOrderId = orderId;
        Map<String,Object> dataMap = orderInfoRpc.getOrderData(originOrderId);
        if (CollUtil.isNotEmpty(dataMap)) {
            String cartxml = Objects.toString(dataMap.get(FieldKeyEnum.V_CARTXML.getFieldName()));
            String orderxml = Objects.toString(dataMap.get(FieldKeyEnum.V_ORDERXML.getFieldName()));
            // 2.解压缩
            orderxml = com.jd.orderver.component.utils.ZipUtils.gunzip(orderxml);
            cartxml = com.jd.orderver.component.utils.ZipUtils.gunzip(cartxml);
            // 3.按照返回 serializationType 进行反序列化成对象
            String serializationType = Objects.toString(dataMap.get(FieldKeyEnum.V_CBDFLAG.getFieldName()));
            Cart serializeCart = SerializersHelper.ofString(cartxml, Cart.class, serializationType);
            Order serializeOrder = SerializersHelper.ofString(orderxml, Order.class, serializationType);
            retMap.put(FieldKeyEnum.V_CARTXML.getFieldName(), serializeCart);
            retMap.put(FieldKeyEnum.V_ORDERXML.getFieldName(), serializeOrder);
            if (serializeCart != null) {
                log.info("SupportDevExportImpl getOrderDataInfo findAllSkus={}", JSON.toJSONString(serializeCart.findAllSkus()));
                log.info("SupportDevExportImpl getOrderDataInfo findAllSKURelationInfo={}", JSON.toJSONString(serializeCart.findAllSKURelationInfo()));
            }
        }
        return ResponseUtil.buildSuccResponse(retMap);
    }

    /**
     * 同步报告到档案中心
     * @param medicalReportSaveCmd
     * @return
     */
    @Override
    public Response<Boolean> syncMedicalReportToReportCenter(MedicalReportSaveCmd medicalReportSaveCmd) {
        Boolean syncMedicalReportToReportCenter = medicalPromiseApplication.syncMedicalReportToReportCenter(medicalReportSaveCmd);
        return ResponseUtil.buildSuccResponse(syncMedicalReportToReportCenter);
    }

    @Override
    @LogAndAlarm
    public String invokeMethod(String className, String methodName, List<String> paramJsons, List<String> paramTypes, String rType) {
        String result = devApplication.invokeMethod(className, methodName, paramJsons, paramTypes, rType);
        return "tid:"+ MDC.get("PFTID")+" 结果:"+StringUtils.defaultString(result,"");
    }

    /**
     * 最大测试极限
     *
     * @return {@link Response }<{@link String }>
     */
    @Override
    @LogAndAlarm
    public Response<String> testLimitMax() {
        List<JdOrder> orderListByParentId = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(0L).build());
        return ResponseUtil.buildSuccResponse(JSON.toJSONString(orderListByParentId));
    }

    /**
     * 测试页面限制
     *
     * @return {@link Response }<{@link String }>
     */
    @Override
    @LogAndAlarm
    public Response<String> testPageLimit() {
        PromiseRepPageQuery pageQuery = PromiseRepPageQuery.builder().build();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(10);
        Page<JdhPromise> page = promiseRepository.page(pageQuery);
        return ResponseUtil.buildSuccResponse(JSON.toJSONString(page));
    }

    /**
     * 测试页面限制
     *
     * @return {@link Response }<{@link String }>
     */
    @Override
    @LogAndAlarm
    public Response<String> testPageLimitSkip() {

        MaxLimitInterceptor.ignore();

        PromiseRepPageQuery pageQuery = PromiseRepPageQuery.builder().build();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(10);
        Page<JdhPromise> page = promiseRepository.page(pageQuery);

        MaxLimitInterceptor.close();

        return ResponseUtil.buildSuccResponse(JSON.toJSONString(page));
    }

    /**
     * 分配用户反馈问题
     * @param feedbackScene
     * @param promiseId
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> distributeFeedback(String feedbackScene, Long promiseId) {
        DistributeFeedbackCmd cmd = new DistributeFeedbackCmd();
        cmd.setPromiseId(promiseId);
        cmd.setFeedbackScene(feedbackScene);
        userFeedbackApplication.distributeFeedback(cmd);
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * 刷新自动化测试白名单数据
     * @return
     */
    @Override
    public Response<Boolean> refreshAutomatedTestAggregate() {
        autoTestSupportApplication.refreshAutomatedTestAggregate();
        return Response.buildSuccessResult(Boolean.TRUE);
    }

    /**
     * 重置订单数据
     * @param orderId
     * @return
     */
    @Override
    public Response<Boolean> resetAutomatedTestAggregateData(String orderId){
        return Response.buildSuccessResult(autoTestSupportApplication.resetAutomatedTestAggregateData(orderId));
    }

    /**
     * 提交预约信息执行自动化测试流程
     * @param cmd
     * @return
     */
    @Override
    public Response<Boolean> submitAppointmentAndAutomatedTestStart(SubmitAppointmentStartCmd cmd){
        return Response.buildSuccessResult(autoTestSupportApplication.submitAppointmentAndAutomatedTestStart(cmd));
    }

    /**
     * 新增护士技能
     * @param createAngelSkillDictCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> insertAngelSkillDict(CreateAngelSkillDictCmd createAngelSkillDictCmd) {
        if (Objects.isNull(createAngelSkillDictCmd) || Objects.isNull(createAngelSkillDictCmd.getItemType())
                || StringUtils.isBlank(createAngelSkillDictCmd.getServiceGroupId()) || StringUtils.isBlank(createAngelSkillDictCmd.getAngelSkillName())) {
            return ResponseUtil.buildSuccResponse(false);
        }
        //判断技能重复
        AngelSkillDictRequest request = new AngelSkillDictRequest();
        request.setAngelSkillNameList(com.google.common.collect.Lists.newArrayList(createAngelSkillDictCmd.getAngelSkillName()));
        List<AngelSkillDictDto> angelSkillDictDtos = angelSkillDictApplication.queryListBySkillInfo(request);
        if (CollectionUtils.isNotEmpty(angelSkillDictDtos)) {
            return ResponseUtil.buildSuccResponse(true);
        }
        Boolean result = angelSkillDictApplication.batchInsert(com.google.common.collect.Lists.newArrayList(createAngelSkillDictCmd));
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询闪送余额
     * @return
     */
    @Override
    public Response<GetUserAccountDto> getUserAccount() {
        return Response.buildSuccessResult(ssApplication.getUserAccount());
    }

    @Override
    public Boolean removeMessage(String startDate, String endDate, List<Integer> statusList) {
        return devApplication.removeMessage(startDate, endDate, statusList);
    }

    @Override
    public Boolean expireVoucherByDate(String startDate) {

       CompletableFuture.runAsync(()->{
           try {

               LocalDateTime end = LocalDateTime.now();
               LocalDateTime startTime = TimeUtils.timeStrToLocalDate(startDate, TimeFormat.LONG_PATTERN_LINE);
               // 如果startTime 小于当前时间 则继续查询
               while (startTime.isBefore(end)) {
                   int pageSize = 50;
                   int pageNum = 1;
                   LocalDateTime rightTime = startTime.plusDays(1);
                   Date dateRangeRight = DateUtil.beginOfDay(TimeUtils.localDateTimeToDate(rightTime));
                   Date dateRangeStart = DateUtil.beginOfDay(TimeUtils.localDateTimeToDate(startTime));

                   VoucherPageRequest pageRequest = VoucherPageRequest.builder()
                           .statusNotInList(Arrays.asList(JdhVoucherStatusEnum.COMPLETE.getStatus(), JdhVoucherStatusEnum.INVALID.getStatus()))
                           .expireDateLt(dateRangeRight)
                           .expireDateGt(dateRangeStart)
                           .build();
                   Response<PageDto<VoucherDto>> page;
                   do {
                       pageRequest.setPageNum(pageNum);
                       pageRequest.setPageSize(pageSize);
                       page = voucherApplication.pageQueryVoucher(pageRequest);
                       log.info("VoucherExpireJob -> execute pagination:{}", JSON.toJSONString(page));
                       if (Objects.nonNull(page) && Objects.nonNull(page.getData())) {
                           List<VoucherDto> voucherDtoList = page.getData().getList();
                           for (VoucherDto voucherDto : voucherDtoList) {
                               try {
                                   if (!Objects.equals(voucherDto.getStatus(), JdhVoucherStatusEnum.EXPIRED.getStatus())){
                                       ExpireVoucherCmd cmd = ExpireVoucherCmd.builder()
                                               .voucherId(voucherDto.getVoucherId()).build();
                                       voucherApplication.expireVoucher(cmd);
                                   }
                                   Thread.sleep(300L);
                               } catch (Exception e) {
                                   log.error("VoucherExpireJob -> expireVoucher exception voucherDto:{}", JSON.toJSONString(voucherDto), e);
                               }
                           }
                       }
                       pageNum++;
                   } while (Objects.nonNull(page) && Objects.nonNull(page.getData()) && pageNum <= page.getData().getTotalPage());
                   startTime = startTime.plusDays(1);
               }
               log.error("expireVoucherByDate end");

           }catch (Exception e){
               log.error("expireVoucherByDate error", e);
           }
       });

       return true;
    }


    @Override
    public Boolean validatePublish(String json, String eventType) {

        if (StringUtils.equals(eventType, PromiseEventTypeEnum.PROMISE_CANCEL_FAIL.getCode())) {
            CancelAppointCmd cmd = JSON.parseObject(json, CancelAppointCmd.class);
            promiseApplication.cancel(cmd);
        }

        return Boolean.TRUE;
    }

    @Override
    public Boolean validateDistributeEvent(Long eventId, String consumerCode) {

        EventConsumerTask task = consumerTaskRepository.getTask(eventId, consumerCode);
        eventCoordinator.distributeEvent(task);
        return Boolean.TRUE;
    }

    /**
     * 计算价格，返回详细信息
     * @param cmd
     * @return
     */
    @Override
    public Response<PricingServiceCalculateResultDto> calculatePriceForDetail(PricingServiceCalculateCmd cmd) {
        return ResponseUtil.buildSuccResponse(pricingServiceApplication.calculatePriceForDetail(cmd));
    }


    @Resource
    private DispatchRepository dispatchRepository;

    @Override
    public Boolean validatePublishDelayEvent(String dispatchId, String endDate) {
        JdhDispatchIdentifier dispatchIdentifier = new JdhDispatchIdentifier(Long.valueOf(dispatchId));
        JdhDispatch jdhDispatch = dispatchRepository.find(dispatchIdentifier);
        eventCoordinator.publishDelay(EventFactory.newDelayEvent(jdhDispatch, DispatchEventTypeEnum.DISPATCH_FAIL_ALARM_DELAY, null, 10L));

        return null;
    }

    @Override
    public Boolean selfTransportCompleteOrder(String orderId) {
        JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(Long.valueOf(orderId)));
        jdOrder.setOrderStatus(OrderStatusEnum.ORDER_SIGNED.getStatus());
        log.info("[JdOrderMsgApplicationImpl->dealMessage], jdOrder={}", JSON.toJSONString(jdOrder));
        //save
        jdOrderRepository.updateOrderStatusByOrderId(jdOrder);
        //推送事件
        eventCoordinator.publish(EventFactory.newDefaultEvent(jdOrder, TradeEventTypeEnum.ORDER_DELIVERY_COMPLETED, new OrderSplitEventBody(jdOrder)));
        return Boolean.TRUE;
    }


    @Resource
    private JdhReachMessagePoMapper jdhReachMessagePoMapper;
    @Override
    public Boolean updateMessageBoxIndex(String userPin, String startTime, String endTime) {

        /**
         * 已完单的护士上门检测、护理订单
         */
        CompletableFuture.runAsync(()->{
            LocalDateTime start = TimeUtils.timeStrToLocalDate(startTime, TimeFormat.LONG_PATTERN_LINE);
            LocalDateTime end = TimeUtils.timeStrToLocalDate(endTime, TimeFormat.LONG_PATTERN_LINE);
            try {
                // 每次
                while (start.isBefore(end)) {
                    log.info("updateMessageBoxIndex==== startTime={}", start);
                    LocalDateTime close = start.plusMinutes(10);
                    List<JdhReachMessagePo> pos = jdhReachMessagePoMapper.listBox(userPin, TimeUtils.localDateTimeToDate(start), TimeUtils.localDateTimeToDate(close));
                    if (CollectionUtils.isEmpty(pos)){
                        log.info("updateMessageBoxIndex==== closeTime={}", close);
                    }else{
                        log.info("updateMessageBoxIndex==== pos size={}", pos.size());
                        for (JdhReachMessagePo po : pos) {
                            if (StringUtils.isBlank(po.getBoxMessageGroupIndex())){
                                if (Objects.equals(po.getReachType(), ReachTypeEnum.APP_NOTIFY.getCode())
                                        && StringUtils.equals("angel", po.getAppId())){
                                    JdhReachConfigRepository configRepository = SpringUtil.getBean(JdhReachConfigRepository.class);
                                    Integer integer = configRepository.findBoxGroupByMsgType(po.getMessageBizType());
                                    String index = MessageFormat.format(JdhReachMessage.MESSAGE_BOX_INDEX_TEMPLATE, po.getUserPin(), integer);
                                    jdhReachMessagePoMapper.updateBoxIndex(po.getMessageId(), index);
                                }
                            }
                        }
                    }

                    start = close;
                    log.info("updateMessageBoxIndex==== closeTime={}", close);
                }

            }catch (Throwable e){
                log.info("updateMessageBoxIndex error============", e);
            }
        });
        return Boolean.TRUE;
    }

    @Resource
    private RiskQueryServiceRpc riskQueryServiceRpc;
    /**
     * 已完单的用户和护士风控校验检测
     * @return
     */
    @Override
    public Boolean dispatchRiskTest(List<Long> promiseIds) {

        /**
         * 已完单的护士上门检测、护理订单
         */
        CompletableFuture.runAsync(()->{
            List<String> records = Lists.newArrayList();

            List<String> codes = Lists.newArrayList("xfylHomeTest","xfylHomeCare","nhHomeTest","nhHomeCare","xfylVtpHomeTest","xfylVtpHomeCare");
            try {
                // 每次
                    LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.in(JdhPromisePo::getPromiseId, promiseIds);
                    queryWrapper.in(JdhPromisePo::getVerticalCode, codes);
                    queryWrapper.eq(JdhPromisePo::getPromiseStatus, JdhPromiseStatusEnum.COMPLETE.getStatus());
                    queryWrapper.eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode());

                    List<JdhPromisePo> promisePos = jdhPromisePoMapper.selectList(queryWrapper);
                    if (CollectionUtils.isEmpty(promisePos)) {
                        return;
                    }

                    Map<Long, JdhPromisePo> promisePoMap = promisePos.stream()
                            .collect(Collectors.toMap(JdhPromisePo::getPromiseId, Function.identity(), (o, n) -> o));

                    LambdaQueryWrapper<JdhAngelWorkPo> dispatchQueryWrapper = Wrappers.lambdaQuery();
                    dispatchQueryWrapper.in(JdhAngelWorkPo::getPromiseId, promiseIds)
                            .eq(JdhAngelWorkPo::getWorkStatus, AngelWorkStatusEnum.COMPLETED.getType())
                            .eq(JdhAngelWorkPo::getYn, YnStatusEnum.YES.getCode());
                    List<JdhAngelWorkPo> workPos = jdhAngelWorkPoMapper.selectList(dispatchQueryWrapper);

                    StringBuilder sb = new StringBuilder();
                    for (JdhAngelWorkPo workPo : workPos) {
                        JdhPromisePo promise = promisePoMap.get(workPo.getPromiseId());
                        DomainAppointmentTime appointmentTime = new DomainAppointmentTime();
                        appointmentTime.setAppointmentStartTime(TimeUtils.dateToLocalDateTime(promise.getAppointmentStartTime()));
                        appointmentTime.setAppointmentEndTime(TimeUtils.dateToLocalDateTime(promise.getAppointmentEndTime()));
                        DispatchRiskStrategyParam param = DispatchRiskStrategyParam.builder()
                                .userPin(promise.getUserPin())
                                .virtualPin(Boolean.FALSE)
                                .verticalCode(workPo.getVerticalCode())
                                .aggregateId(workPo.getSourceId())
                                .provinceCode(promise.getProvinceCode())
                                .cityCode(promise.getCityCode())
                                .districtCode(promise.getDistrictCode())
                                .townCode(promise.getTownCode())
                                .addressDetail(promise.getStoreAddr())
                                .appointmentTime(appointmentTime)
                                .angelId(Long.valueOf(workPo.getAngelId()))
                                .angelPin(workPo.getAngelPin())
                                .angelPhone(workPo.getAngelPhone())
                                .build();
                        riskQueryServiceRpc.queryStrategy(param);

                        sb.append(workPo.getWorkId()).append(',')
                                .append(promise.getSourceVoucherId()).append(',')
                                .append(promise.getUserPin()).append(',')
                                .append(workPo.getAngelPin()).append(',')
                                .append(workPo.getAngelId()).append(',');
                        records.add(sb.toString());
                    }

                log.info("dispatchRiskTest end=======");
                log.info(JSON.toJSONString(records));
            }catch (Throwable e){
                log.info("dispatchRiskTest error============", e);

            }

        });
        return Boolean.TRUE;
    }

    @Override
    public Response<Boolean> initTransformStation(QueryMerchantStoreListByParamRequest queryMerchantStoreListByParamRequest) {
        PageDto<QueryMerchantStoreDetailResponse> queryMerchantStoreDetailResponse = providerStoreApplication.queryMerchantStoreListByParam(queryMerchantStoreListByParamRequest);
        if (queryMerchantStoreDetailResponse == null || CollUtil.isEmpty(queryMerchantStoreDetailResponse.getList())) {
            return Response.buildSuccessResult(true);
        }
        int total = (int) queryMerchantStoreDetailResponse.getTotalCount(); // 总量
        int pageSize = queryMerchantStoreListByParamRequest.getPageSize(); // 每页数量
        int totalPage = (total + pageSize - 1) / pageSize; // 向上取整
        for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
            QueryMerchantStoreListByParamRequest req = new QueryMerchantStoreListByParamRequest();
            req.setPageNum(pageNum);
            req.setPageSize(pageSize);
            req.setBusinessType(16);
            PageDto<QueryMerchantStoreDetailResponse> pageDto = providerStoreApplication.queryMerchantStoreListByParam(req);
            if (pageDto == null || CollUtil.isEmpty(pageDto.getList())) {
                continue;
            }
            for (QueryMerchantStoreDetailResponse detail : pageDto.getList()) {
                QueryMerchantStoreDetailByParamRequest request = new QueryMerchantStoreDetailByParamRequest();
                request.setQueryTransferStation(true);
                request.setJdStoreId(detail.getJdStoreId());
                List<JdhStoreTransferStationDto> ret = providerStoreApplication.queryStoreTransferStation(request);
                if (CollUtil.isEmpty(ret) || CollUtil.isEmpty(ret.stream().filter(s -> JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType().equals(s.getStationType())).collect(Collectors.toList()))) {
                    JdhStoreTransferStation jdhStoreTransferStation = new JdhStoreTransferStation();
                    Long stationId = generateIdFactory.getId();
                    jdhStoreTransferStation.setStationType(JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType());
                    jdhStoreTransferStation.setJdStationId(stationId);
                    jdhStoreTransferStation.setJdStationName(detail.getStoreName());
                    jdhStoreTransferStation.setJdStationAddress(detail.getStoreAddr());
                    jdhStoreTransferStation.setCreateUser("system");
                    jdhStoreTransferStation.setUpdateUser("system");
                    jdhStoreTransferStation.setJdStationLatitude(StringUtils.isBlank(detail.getLat()) ? null : Double.parseDouble(detail.getLat()));
                    jdhStoreTransferStation.setJdStationLongitude(StringUtils.isBlank(detail.getLng()) ? null : Double.parseDouble(detail.getLng()));
                    jdhStoreTransferStationRepository.save(jdhStoreTransferStation);
                    jdhStoreTransferStationRelRepository.save(JdhStoreTransferStationRel.builder().jdStoreId(detail.getJdStoreId()).jdTransferStationId(stationId).build());
                }
            }
        }

        return Response.buildSuccessResult(true);
    }

    @Override
    public Response<Boolean> initAngelStation(QueryMerchantStoreListByParamRequest queryMerchantStoreListByParamRequest) {
        Page<JdhStation> pageList = jdhStationRepository.findPageList(AngelStationPageQuery.builder().build());
        if (pageList == null || CollUtil.isEmpty(pageList.getRecords())) {
            return Response.buildSuccessResult(true);
        }
        int total = (int) pageList.getTotal(); // 总量
        int pageSize = queryMerchantStoreListByParamRequest.getPageSize(); // 每页数量
        int totalPage = (total + pageSize - 1) / pageSize; // 向上取整
        log.info("initAngelStation total={}", total);
        for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
            AngelStationPageQuery req = new AngelStationPageQuery();
            req.setPageNum(pageNum);
            req.setPageSize(pageSize);
            Page<JdhStation> pageDto = jdhStationRepository.findPageList(req);
            if (pageDto == null || CollUtil.isEmpty(pageDto.getRecords())) {
                continue;
            }
            List<String> err = new ArrayList<>();
            for (JdhStation detail : pageDto.getRecords()) {
                log.info("initAngelStation detail={}", JSON.toJSONString(detail));
                if (detail.getJdTransferStationId() != null || StringUtils.isBlank(detail.getStationId())) {
                    continue;
                }
                log.info("initAngelStation 骑手 detail={}", JSON.toJSONString(detail));
                QueryMerchantStoreDetailByParamRequest request = new QueryMerchantStoreDetailByParamRequest();
                request.setQueryTransferStation(true);
                request.setJdStoreId(detail.getStationId());
                List<JdhStoreTransferStationDto> ret = providerStoreApplication.queryStoreTransferStation(request);
                if (CollUtil.isEmpty(ret) || ret.stream().noneMatch(s -> JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType().equals(s.getStationType()))) {
                    err.add(detail.getStationId());
                    continue;
                }
                JdhStation updateJdhStation = new JdhStation();
                updateJdhStation.setJdTransferStationId(ret.stream().filter(s -> JdhStoreTransferStationTypeEnum.SELF_RECEIVE_SAMPLE.getType().equals(s.getStationType())).collect(Collectors.toList()).get(0).getJdStationId());
                updateJdhStation.setAngelStationId(detail.getAngelStationId());
                jdhStationRepository.updateStation(updateJdhStation);
            }
            if (CollUtil.isNotEmpty(err)) {
                throw new BusinessException(new DynamicErrorCode("-1", "实验室无自收样接驳点,"+ Joiner.on(",").join(err)));
            }
        }

        return Response.buildSuccessResult(true);
    }
}
