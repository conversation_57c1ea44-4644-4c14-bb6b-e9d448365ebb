package com.jdh.o2oservice.facade.trade;

import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.AbTest;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.trade.enums.RefundTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.export.promise.cmd.SubmitAppointmentDraftCmd;
import com.jdh.o2oservice.export.trade.TradeGwExport;
import com.jdh.o2oservice.export.trade.TradeJsfExport;
import com.jdh.o2oservice.export.trade.dto.*;
import com.jdh.o2oservice.export.trade.query.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 交易color接口
 * @author: yangxiyu
 * @date: 2024/1/9 11:23 上午
 * @version: 1.0
 */
@Component
public class TradeGwExportImpl implements TradeGwExport {


    @Resource
    private TradeApplication tradeApplication;

    @Resource
    private TradeJsfExport tradeJsfExport;

    /**
     * 保存草稿
     *
     * @param param param
     * @return {@link Response}<{@link TradeDraftDto}>
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeGwExportImpl.saveDraft")
    public Response<TradeDraftDto> saveDraft(Map<String, String> param) {
        SubmitAppointmentDraftCmd cmd = GwMapUtil.convertToParam(param, SubmitAppointmentDraftCmd.class);
        String url = tradeApplication.saveDraft(cmd);
        TradeDraftDto draftDto = new TradeDraftDto();
        draftDto.setSettleUrl(url);
        return ResponseUtil.buildSuccResponse(draftDto);
    }

    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeGwExportImpl.executeAction")
    @Override
    @AbTest
    public Response<OrderUserActionDTO> executeAction(Map<String, String> param) {
        OrderUserActionParam orderUserActionParam = GwMapUtil.convertToParam(param, OrderUserActionParam.class);
        return tradeJsfExport.executeAction(orderUserActionParam);
    }

    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeGwExportImpl.submitOrder")
    @Override
    public Response<SubmitOrderDTO> submitOrder(Map<String, String> param) {
        SubmitOrderParam submitOrderParam = GwMapUtil.convertToParam(param, SubmitOrderParam.class);
        return tradeJsfExport.submitOrder(submitOrderParam);
    }

    /**
     * 订单支付收银台
     *
     * @param param 提单参数
     * @return SubmitOrderDTO
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeGwExportImpl.getOrderPayUrl")
    public Response<String> getOrderPayUrl(Map<String, String> param) {
        OrderPayUrlParam orderPayUrlParam = GwMapUtil.convertToParam(param, OrderPayUrlParam.class);
        AssertUtils.nonNull(orderPayUrlParam,BusinessErrorCode.ILLEGAL_ARG_ERROR);
        String payUrl = tradeApplication.getOrderPayUrl(orderPayUrlParam);
        return ResponseUtil.buildSuccResponse(payUrl);
    }

    /**
     * 取消待支付订单
     *
     * @param paramMap
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeGwExportImpl.cancelPayOrder")
    public Response<Boolean> cancelPayOrder(Map<String, String> paramMap) {
        CancelOrderParam cancelOrderParam = GwMapUtil.convertToParam(paramMap, CancelOrderParam.class);
        AssertUtils.nonNull(cancelOrderParam,BusinessErrorCode.ILLEGAL_ARG_ERROR);
        Boolean result = tradeApplication.cancelPayOrder(cancelOrderParam);
        return ResponseUtil.buildSuccResponse(result);
    }

    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeGwExportImpl.avaiableTime")
    @Override
    public Response<AvaiableAppointmentTimeDTO> avaiableTime(Map<String, String> param) {
        AvaiableAppointmentTimeParam avaiableAppointmentTimeParam = GwMapUtil.convertToParam(param, AvaiableAppointmentTimeParam.class);
        return tradeJsfExport.queryAvaiableAppointmentTime(avaiableAppointmentTimeParam);
    }

    /**
     * 预约单退款
     * 1.待预约，预约失败的才可申请退款
     * 2.OCS查询付款类型列表
     * 3.退款拆分计算
     *
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeGwExportImpl.xfylOrderRefund",logSwitch = false)
    public Response<Boolean> xfylOrderRefund(Map<String, String> param) {
        RefundOrderParam refundOrderParam = GwMapUtil.convertToParam(param, RefundOrderParam.class);
        AssertUtils.nonNull(refundOrderParam, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        AssertUtils.nonNull(refundOrderParam.getOrderId(), TradeErrorCode.ORDER_ID_NULL);
        refundOrderParam.setRefundSource(CommonConstant.ONE_STR);
        refundOrderParam.setRefundType(RefundTypeEnum.ORDER_REFUND.getType());
        refundOrderParam.setOperator("用户");
        return ResponseUtil.buildSuccResponse(tradeApplication.xfylOrderRefund(refundOrderParam));
    }

    /**
     * 获取订单退款原因列表
     *
     * @param param param
     * @return {@link Response}<{@link Boolean}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeGwExportImpl.getCancelReasons")
    public Response<List<OrderCancelReasonDto>> getCancelReasons(Map<String, String> param) {
        RefundOrderParam refundOrderParam = GwMapUtil.convertToParam(param, RefundOrderParam.class);
        return ResponseUtil.buildSuccResponse(tradeApplication.getOrderCancelReasons(refundOrderParam));
    }

    /**
     * 查询用户服务过的历史护士
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeGwExportImpl.queryServiceHistoryAngel")
    public Response<List<ServiceHistoryAngelDTO>> queryServiceHistoryAngel(Map<String, String> param) {
        QueryServiceHistoryAngelParam queryServiceHistoryAngelParam = GwMapUtil.convertToParam(param, QueryServiceHistoryAngelParam.class);
        return ResponseUtil.buildSuccResponse(tradeApplication.queryServiceHistoryAngel(queryServiceHistoryAngelParam));
    }

    /**
     * 查询意向服务者
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.TradeGwExportImpl.queryIntendedAngel")
    public Response<IntendedAngelDTO> queryIntendedAngel(Map<String, String> param) {
        QueryIntendedAngelParam queryIntendedAngelParam = GwMapUtil.convertToParam(param, QueryIntendedAngelParam.class);
        return ResponseUtil.buildSuccResponse(tradeApplication.queryIntendedAngel(queryIntendedAngelParam));
    }

}
