package com.jdh.o2oservice.facade.support;

import com.jdh.o2oservice.application.support.service.UserFeedbackApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.UserFeedbackGwExport;
import com.jdh.o2oservice.export.user.cmd.SubmitUserFeedbackCmd;
import com.jdh.o2oservice.export.user.dto.UserFeedbackAggregationDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName UserFeedbackGwExportImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/1/24 18:18
 **/
@Service("userFeedbackGwExport")
@Slf4j
public class UserFeedbackGwExportImpl implements UserFeedbackGwExport {

    /**
     * userFeedbackApplication
     */
    @Resource
    private UserFeedbackApplication userFeedbackApplication;

    /**
     * 提交用户反馈
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<UserFeedbackAggregationDTO> submitUserFeedback(Map<String, String> param) {
        SubmitUserFeedbackCmd cmd = GwMapUtil.convertToParam(param, SubmitUserFeedbackCmd.class);
        //contentType 反馈内容类型未传，默认是枚举类型回答
        cmd.setContentType(Objects.isNull(cmd.getContentType()) ? 3 : cmd.getContentType());
        UserFeedbackAggregationDTO aggregationDTO = userFeedbackApplication.submitUserFeedback(cmd);
        return Response.buildSuccessResult(aggregationDTO);
    }
}