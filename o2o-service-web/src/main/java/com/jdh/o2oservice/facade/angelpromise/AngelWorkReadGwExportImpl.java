package com.jdh.o2oservice.facade.angelpromise;

import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.base.util.UserPinContext;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.export.angelpromise.AngelWorkReadGwExport;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.*;
import com.jdh.o2oservice.job.angelpromise.AngelFinishServedOverOneDayJob;
import com.jdh.o2oservice.job.angelpromise.AngelFinishServedOverTwoDayJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName:AngelWorkReadGwExportImpl
 * @Description: 工单读服务实现
 * @Author: yaoqinghai
 * @Date: 2024/5/8 16:03
 * @Vserion: 1.0
 **/
@Service("angelWorkReadGwExport")
@Slf4j
public class AngelWorkReadGwExportImpl implements AngelWorkReadGwExport {

    @Resource
    private AngelWorkApplication angelWorkApplication;

    @Resource
    private AngelPromiseApplication angelPromiseApplication;

    /** */
    @Resource
    private AngelFinishServedOverOneDayJob angelFinishServedOverOneDayJob;
    /** */
    @Resource
    private AngelFinishServedOverTwoDayJob angelFinishServedOverTwoDayJob;

    /**
     * 查询工单的统计信息
     *
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "AngelWorkReadGwExportImpl.countAngelWorkCount")
    public Response<AngelWorkCountDto> countAngelWorkCount(Map<String, String> param) {
        AngelWorkCountQuery angelWorkCountQuery = GwMapUtil.convertToParam(param, AngelWorkCountQuery.class);
        angelWorkCountQuery.setAngelPin(UserPinContext.get());
        return ResponseUtil.buildSuccResponse(angelWorkApplication.countAngelWorkCount(angelWorkCountQuery));
    }

    /**
     * 查询服务者轨迹
     *
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "AngelWorkReadGwExportImpl.queryAngelRealLocation")
    public Response<AngelRealTrackDto> queryAngelRealTrack(Map<String, String> param) {
        AngelRealLocationQuery angelRealLocationQuery = GwMapUtil.convertToParam(param, AngelRealLocationQuery.class);
        //查询护士实时位置
        return ResponseUtil.buildSuccResponse(angelWorkApplication.queryAngelRealTrack(angelRealLocationQuery));
    }

    /**
     * 运营端查询工单明细
     *
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "AngelWorkReadGwExportImpl.queryAngelWorkForMan")
    public Response<AngelWorkDetailForManDto> queryAngelWorkForMan(Map<String, String> param) {
        AngelWorkDetailForManRequest detailForManQuery = GwMapUtil.convertToParam(param, AngelWorkDetailForManRequest.class);
        detailForManQuery.setOperator(UserPinContext.get());

        return ResponseUtil.buildSuccResponse(angelWorkApplication.queryAngelWorkForMan(detailForManQuery));
    }

    /**
     * 查询工单实验室列表
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelWorkReadGwExportImpl.queryWorkStationList")
    @UserPinCheck
    public Response<List<AngelWorkCourierFloorDto>> queryWorkStationList(Map<String, String> param) {
        AngelWorkQuery angelWorkQuery = GwMapUtil.convertToParam(param, AngelWorkQuery.class);
        return ResponseUtil.buildSuccResponse(angelWorkApplication.queryWorkStationList(angelWorkQuery));
    }

    /**
     * 查询工单列表或者最近一次的无效工单
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelWorkReadGwExportImpl.queryWorkListOrRecently")
    public Response<AngelWorkDetailDto> queryWorkListOrRecently(Map<String, String> param) {
        AngelWorkQuery angelWorkQuery = GwMapUtil.convertToParam(param, AngelWorkQuery.class);
        return ResponseUtil.buildSuccResponse(angelPromiseApplication.queryWorkListOrRecently(angelWorkQuery));
    }

    /**
     * 护士取消工单判断是否超过3小时不能取消
     *
     * @param param 接口入参
     * @return Boolean
     */
    @Override
    @LogAndAlarm(jKey = "AngelWorkReadGwExportImpl.checkCancelTimeout")
    public Response<Boolean> checkCancelTimeout(Map<String, String> param) {
        AngelWorkQuery angelWorkQuery = GwMapUtil.convertToParam(param, AngelWorkQuery.class);
        return ResponseUtil.buildSuccResponse(angelPromiseApplication.checkCancelTimeout(angelWorkQuery));
    }

    /**
     * 查询工单实验室列表
     *
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelWorkReadGwExportImpl.delayTest")
    public Response<Boolean> delayTest(Long id) {
        return ResponseUtil.buildSuccResponse(angelWorkApplication.delayTest(id));
    }

    /**
     *
     * @param angelShipQuery
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelWorkReadGwExportImpl.getAngelShipByShipInfo")
    public Response<AngelShipDto> getAngelShipByShipInfo(AngelShipQuery angelShipQuery) {
        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        List<Long> shipIds = new ArrayList<>();
        shipIds.add(angelShipQuery.getShipId());
        angelShipDBQuery.setShipIds(shipIds);
        angelShipDBQuery.setWorkId(angelShipQuery.getWorkId());
        AngelShipDto angelShipDto = angelWorkApplication.getAngelShipByShipInfo(angelShipDBQuery);
        return ResponseUtil.buildSuccResponse(angelShipDto);
    }

    /**
     *
     * @param angelShipQuery
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelWorkReadGwExportImpl.getShipOrderDetail")
    public Response<AngelShipDto> getShipOrderDetail(AngelShipQuery angelShipQuery) {
        AngelShipDto angelShipDto = angelWorkApplication.getShipOrderDetail(angelShipQuery);
        return ResponseUtil.buildSuccResponse(angelShipDto);
    }

    @Override
    public Response<Boolean> angelFinishServedOverOneDayJob() {
        angelFinishServedOverOneDayJob.execute(null);
        return ResponseUtil.buildSuccResponse(true);
    }

    @Override
    public Response<Boolean> angelFinishServedOverTwoDayJob() {
        angelFinishServedOverTwoDayJob.execute(null);
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * 查询工单列表
     * @param angelWorkQuery
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelWorkReadGwExportImpl.getAngelWorkList")
    public Response<List<AngelWorkDto>> getAngelWorkList(AngelWorkQuery angelWorkQuery) {
        List<AngelWorkDto> result = angelWorkApplication.getAngelWorkList(angelWorkQuery);
        return ResponseUtil.buildSuccResponse(result);
    }
}
