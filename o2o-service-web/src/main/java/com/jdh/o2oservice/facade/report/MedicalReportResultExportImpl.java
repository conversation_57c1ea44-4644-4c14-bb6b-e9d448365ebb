package com.jdh.o2oservice.facade.report;

import cn.hutool.core.date.DateUtil;
import com.jdh.o2oservice.application.report.service.MedicalReportResultApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.report.MedicalReportResultExport;
import com.jdh.o2oservice.export.report.cmd.MedPromisePosRateCmd;
import com.jdh.o2oservice.export.report.dto.MedPromisePositiveRateDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/20
 */
@Component
@Slf4j
public class MedicalReportResultExportImpl implements MedicalReportResultExport {

    /**
     * 自动注入的MedicalReportResultApplication对象，用于在该类中调用相关方法。
     */
    @Autowired
    private MedicalReportResultApplication medicalReportResultApplication;

    /**
     * 自动注入的DuccConfig对象，用于在该类中获取Ducc相关配置信息。
     */
    @Autowired
    private DuccConfig duccConfig;

    /**
     * 查询阳性率
     *
     * @param medPromisePosRateCmd 查询条件
     * @return 阳性率
     */
    @Override
    @LogAndAlarm
    public Response<MedPromisePositiveRateDTO> queryMedPromisePositiveRate(MedPromisePosRateCmd medPromisePosRateCmd) {
        if (Objects.isNull(medPromisePosRateCmd.getServiceItemId())){
            medPromisePosRateCmd.setServiceItemId(duccConfig.getDefaultStationServiceItemId());
        }
        if (Objects.isNull(medPromisePosRateCmd.getStartTime())|| Objects.isNull(medPromisePosRateCmd.getEndTime())){
            Date date = new Date();
            medPromisePosRateCmd.setStartTime(DateUtil.beginOfDay(date));
            medPromisePosRateCmd.setEndTime(DateUtil.endOfDay(date));
        }
        MedPromisePositiveRateDTO res = null;
        if (duccConfig.getReportPosRateSwitch()){
            res = medicalReportResultApplication.queryMedPromisePositiveRateNew(medPromisePosRateCmd);
        }else {
            res = medicalReportResultApplication.queryMedPromisePositiveRate(medPromisePosRateCmd);
        }
        return Response.buildSuccessResult(res);
    }
}
