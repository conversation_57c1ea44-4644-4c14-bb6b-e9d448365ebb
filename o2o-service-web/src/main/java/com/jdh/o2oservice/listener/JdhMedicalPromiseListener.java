package com.jdh.o2oservice.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.WaveEntry;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.health.xfyl.merchant.export.dto.MerchantStoreConfigDTO;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderFullApplication;
import com.jdh.o2oservice.base.constatnt.CacheConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedPromiseHistory;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseIdentifier;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedPromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.model.UnBindReportBo;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.core.domain.report.rpc.ReportCenterRpc;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.medicalpromise.cmd.DirectCallMedicalPromiseSubmitCmd;
import com.jdh.o2oservice.export.trade.cmd.JdOrderFullSaveCmd;
import com.jdh.o2oservice.listener.util.DBFieldChangeEventUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 21:27
 * @Desc :
 */
@Slf4j
@Service("jdhMedicalPromiseListener")
public class JdhMedicalPromiseListener implements MessageListener {

    /**
     * jdOrderFullApplication
     */
    @Autowired
    private JdOrderFullApplication jdOrderFullApplication;

    /**
     * verticalBusinessRepository
     */
    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * redisLockUtil
     */
    @Autowired
    private RedisLockUtil redisLockUtil;

    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    @Autowired
    private ProviderStoreExportServiceRpc providerStoreExportServiceRpc;

    @Autowired
    private PromiseRepository promiseRepository;

    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    @Autowired
    private MedPromiseHistoryRepository medPromiseHistoryRepository;

    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * dbFieldChangeEventUtil
     */
    @Resource
    DBFieldChangeEventUtil dbFieldChangeEventUtil;


    /**
     * 序列化器
     */
    private static final MessageDeserialize<Message> DESERIALIZE = new JMQMessageDeserialize();

    /**
     * 线程池工厂
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    @Autowired
    private ReportCenterRpc reportCenterRpc;

    @Autowired
    private MedicalReportRepository medicalReportRepository;

    /**
     * ducc
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * onMessage
     *
     * @param messages 消息
     * @throws Exception Exception
     */
    @Override
    @JmqListener(id = "jdhReachStoreConsumer", topics = {"${topics.jdhReachStoreConsumer.jdhMedicalPromiseBinlakeTopic}"})
    public void onMessage(List<Message> messages) throws Exception {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        //将从JMQ消费者客户端获取的Binlake消费数据进行解析
        List<EntryMessage> entryMessages = DESERIALIZE.deserialize(messages);
        String dbConfig = duccConfig.getDbFieldChangeEventConfig();
        for (EntryMessage entryMessage : entryMessages) {
            if (null == entryMessage.getRowChange()) {
                continue;
            }
            if (!entryMessage.getEntryType().equals(WaveEntry.EntryType.ROWDATA)) {
                continue;
            }
            List<WaveEntry.RowData> rowDatas = entryMessage.getRowChange().getRowDatasList();
            String tableName = entryMessage.getHeader().getTableName();
            for (WaveEntry.RowData rowData : rowDatas) {
                try {
                    //组装参数
                    JdOrderFullSaveCmd cmd = convert2JdOrderFullSaveCmd(rowData);
                    log.info("jdhMedicalPromiseListener -> onMessage , cmd={}", JSON.toJSONString(cmd));

                    //异步删除
                    executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
                        JdOrderFullSaveCmd beforeCmd = convert2BeforeJdOrderFullSaveCmd(rowData);
                        //如果之前是已完成，之后是已作废，则同步档案解绑
                        if (MedicalPromiseStatusEnum.COMPLETED.getStatus().toString().equals(beforeCmd.getMedicalPromiseStatus())
                                && MedicalPromiseStatusEnum.INVALID.getStatus().toString().equals(cmd.getMedicalPromiseStatus())
                        ){
                            MedicalReport medicalReport = medicalReportRepository.getByMedicalPromiseId(Long.valueOf(beforeCmd.getMedicalPromiseId()));
                            if (Objects.nonNull(medicalReport.getReportCenterId())){
                                UnBindReportBo unBindReportBo = new UnBindReportBo();
                                unBindReportBo.setId(Long.valueOf(medicalReport.getReportCenterId()));
                                unBindReportBo.setPin(medicalReport.getUserPin());
                                unBindReportBo.setPatientId(medicalReport.getPatientId());
                                reportCenterRpc.unbindReport(unBindReportBo);
                            }
                        }

                    });


                    JdhVerticalBusiness verticalBusiness = verticalBusinessRepository.find(cmd.getVerticalCode());
                    log.info("jdhMedicalPromiseListener -> onMessage , verticalBusiness={}", JSON.toJSONString(verticalBusiness));

                    if(Objects.nonNull(verticalBusiness) &&
                            (BusinessModeEnum.SELF_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.ANGEL_CARE.getCode().equals(verticalBusiness.getBusinessModeCode())
                                    || BusinessModeEnum.SELF_TEST_TRANSPORT.getCode().equals(verticalBusiness.getBusinessModeCode()))){

                        // 表字段变更事件
                        dbFieldChangeEventUtil.dbFieldChange(dbConfig, tableName, rowData);

                        //判断是否需要走实验室迁移逻辑
                        if((BusinessModeEnum.SELF_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                                        || BusinessModeEnum.ANGEL_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())
                                        || BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode().equals(verticalBusiness.getBusinessModeCode()))){
                            if(WaveEntry.EventType.UPDATE.equals(entryMessage.getHeader().getEventType())){
                                //新增检测单,要判断实验室是否已迁移
                                try{
                                    log.info("判断是否走实验室迁移逻辑");
                                    //this.migrationStation(cmd);
                                }catch (Exception ex){
                                    log.error("处理实验室逻辑异常",ex);
                                }
                            }
                        }

                        //如果是逻辑删除
                        if(StrUtil.isNotBlank(cmd.getYn()) && cmd.getYn().equals("0")){
                            String lockKey = CacheConstant.RELOAD_FULL_ORDER_LOCK_KEY_PREFIX + cmd.getPromiseId();
                            String exceptValue = UUID.randomUUID().toString();
                            log.info("JdhMedicalPromiseListener deleteById lockKey:{} , exceptValue:{}", lockKey, exceptValue);
                            try {
                                boolean lock = redisLockUtil.tryLock(lockKey, exceptValue, NumConstant.NUM_5);
                                if (lock) {
                                    log.info("JdhMedicalPromiseListener -> onMessage , deleteById={}", JSON.toJSONString(cmd));
                                    jdOrderFullApplication.deleteById(Collections.singletonList(cmd.getId()));
                                    continue;
                                }else {
                                    throw new RuntimeException("数据同步es 获取分布式锁失败，重试");
                                }
                            } catch (Exception e) {
                                log.error("JdOrderFullApplicationImpl reloadFullOrderInfo exception", e);
                                throw e;
                            } finally {
                                redisLockUtil.unLock(lockKey, exceptValue);
                            }

                        }
                        jdOrderFullApplication.reloadFullOrderInfo(Long.parseLong(cmd.getPromiseId()));
                    }
                }catch (Exception e){
                    log.error("jdhMedicalPromiseListener -> onMessage error", e);
                    throw e;
                }
            }
        }
    }

    /**
     * 处理实验室迁移逻辑
     * @param cmd
     */
    private void migrationStation(JdOrderFullSaveCmd cmd) {
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(Long.parseLong(cmd.getId())).build());
        String originStationId = medicalPromise.getStationId();
        //查询实验室数据
        StoreInfoBo storeInfoBo = providerStoreExportServiceRpc.queryByStoreId(medicalPromise.getStationId());
        JdhPromise jdhPromise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(medicalPromise.getPromiseId()).build());
        if(StringUtils.isEmpty(storeInfoBo.getStoreConfig())){
            log.info("storeInfoBo.getStoreConfig() 为空");
            return;
        }
        MerchantStoreConfigDTO merchantStoreConfigDTO = JSON.parseObject(storeInfoBo.getStoreConfig(),MerchantStoreConfigDTO.class);
        if (merchantStoreConfigDTO != null && Boolean.TRUE.equals(merchantStoreConfigDTO.getMigrationSwitch()) && Boolean.TRUE.equals(merchantStoreConfigDTO.getCreateMigration())) {
            if (TimeUtils.localDateTimeToDate(jdhPromise.getAppointmentTime().getAppointmentStartTime()).after(merchantStoreConfigDTO.getCreateMigrationTimeBegin())
                    && TimeUtils.localDateTimeToDate(jdhPromise.getAppointmentTime().getAppointmentStartTime()).before(merchantStoreConfigDTO.getCreateMigrationTimeEnd())) {
                if(StringUtils.isNotEmpty(medicalPromise.getStationId())&&!medicalPromise.getStationId().equals(merchantStoreConfigDTO.getTargetJdStoreId())){
                    //实验室id不为空,且当前实验室id不等于目标实验室id,执行迁移实验室的逻辑
                    log.info("migrationStation 执行迁移实验室的逻辑");
                    medicalPromise.setStationId(merchantStoreConfigDTO.getTargetJdStoreId());
                    medicalPromiseRepository.save(medicalPromise);
                    //保存检测单操作历史
                    this.saveMedPromiseHistory(medicalPromise,merchantStoreConfigDTO,originStationId);
                    Boolean result = medicalPromiseApplication.directCallMedicalPromiseToStation(DirectCallMedicalPromiseSubmitCmd.builder()
                            .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                            .directCall(YnStatusEnum.YES.getCode())
                            .extJson(storeInfoBo.getStoreConfig()).build());

                    MedicalPromise snapshot = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).build());
                    //发送事件
                    if(result){
                        eventCoordinator.publish(EventFactory.newDefaultEvent(MedicalPromise.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).build(), MedPromiseEventTypeEnum.MED_PROMISE_STATION_MIGRATION,
                                MedicalPromiseEventBody.builder()
                                        .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                                        .beforeStatus(medicalPromise.getStatus())
                                        .status(snapshot.getStatus())
                                        .stationId(merchantStoreConfigDTO.getTargetJdStoreId())
                                        .beforeStationId(originStationId)
                                        .specimenCode(snapshot.getSpecimenCode())
                                        .verticalCode(snapshot.getVerticalCode())
                                        .serviceType(snapshot.getServiceType())
                                        .version(snapshot.getVersion())
                                        .build()));
                    }
                }
            }
        }
    }

    /**
     * 保存检测单操作历史
     * @param medicalPromise
     */
    private void saveMedPromiseHistory(MedicalPromise medicalPromise,MerchantStoreConfigDTO merchantStoreConfigDTO,String originStationId){
        MedPromiseHistory medPromiseHistory = new MedPromiseHistory();
        medPromiseHistory.setEventCode("暂无");
        medPromiseHistory.setEventDesc("暂无");
        medPromiseHistory.setAfterStatus(medicalPromise.getStatus());
        medPromiseHistory.setAfterStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromise.getStatus()));
        medPromiseHistory.setBeforeStatus(medicalPromise.getStatus());
        medPromiseHistory.setBeforeStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromise.getStatus()));
        medPromiseHistory.setProviderId(medicalPromise.getProviderId());
        medPromiseHistory.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        medPromiseHistory.setExtend(JsonUtil.toJSONString(medicalPromise));
        medPromiseHistory.setVersion(medicalPromise.getVersion());
        medPromiseHistory.setDesc("命中实验室迁移规则,实验室"+originStationId+"迁移至"+merchantStoreConfigDTO.getTargetJdStoreId());
        medPromiseHistory.setOperator("binlake");

        medPromiseHistoryRepository.save(medPromiseHistory);
    }


    /**
     * convert2JdOrderFullSaveCmd
     *
     * @param rowData 行数据
     * @return {@link JdOrderFullSaveCmd}
     */
    private JdOrderFullSaveCmd convert2JdOrderFullSaveCmd(WaveEntry.RowData rowData) {
        JdOrderFullSaveCmd cmd = new JdOrderFullSaveCmd();
        for (WaveEntry.Column column : rowData.getAfterColumnsList()) {
            if ("promise_id".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                cmd.setPromiseId(column.getValue());
            } else if ("yn".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                cmd.setYn(column.getValue());
            } else if ("vertical_code".equals(column.getName()) && StrUtil.isNotBlank(column.getValue())) {
                cmd.setVerticalCode(column.getValue());
            } else if ("medical_promise_id".equals(column.getName()) && StrUtil.isNotBlank(column.getValue())) {
                cmd.setId(column.getValue());
            }else if ("status".equals(column.getName()) && StrUtil.isNotBlank(column.getValue())) {
                cmd.setMedicalPromiseStatus(column.getValue());
            }
        }
        return cmd;
    }

    /**
     * convert2JdOrderFullSaveCmd
     *
     * @param rowData 行数据
     * @return {@link JdOrderFullSaveCmd}
     */
    private JdOrderFullSaveCmd convert2BeforeJdOrderFullSaveCmd(WaveEntry.RowData rowData) {
        JdOrderFullSaveCmd cmd = new JdOrderFullSaveCmd();
        for (WaveEntry.Column column : rowData.getBeforeColumnsList()) {
            if ("promise_id".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                cmd.setPromiseId(column.getValue());
            } else if ("yn".equals(column.getName()) && StringUtils.isNotBlank(column.getValue())) {
                cmd.setYn(column.getValue());
            } else if ("vertical_code".equals(column.getName()) && StrUtil.isNotBlank(column.getValue())) {
                cmd.setVerticalCode(column.getValue());
            } else if ("medical_promise_id".equals(column.getName()) && StrUtil.isNotBlank(column.getValue())) {
                cmd.setMedicalPromiseId(column.getValue());
            }else if ("report_status".equals(column.getName()) && StrUtil.isNotBlank(column.getValue())) {
                cmd.setReportStatus(Integer.valueOf(column.getValue()));
            }else if ("status".equals(column.getName()) && StrUtil.isNotBlank(column.getValue())) {
                cmd.setMedicalPromiseStatus(column.getValue());
            }
        }
        return cmd;
    }
}
