package com.jdh.o2oservice.listener.promisego;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.common.enums.RiskAssessmentStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessment;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.RiskAssessmentRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentQuery;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.EtaResultBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/15
 */
@Component
@Slf4j
public class PromiseGoEtaListener implements MessageListener {




    /**
     * 自动注入的风险评估仓库实例，用于查询和更新风险评估信息。
     */
    @Autowired
    private RiskAssessmentRepository riskAssessmentRepository;


    /**
     * @param messages
     * @throws Exception
     */
    @JmqListener(id= "jdhReachStoreConsumer", topics = {"${topics.jdhReachStoreConsumer.etaResultSyncTopic}"})
    @Override
    @LogAndAlarm
    public void onMessage(List<Message> messages) throws Exception {

        if (messages == null || messages.isEmpty()) {
            return;
        }


        for(Message message : messages){
            log.info("PromiseGoEtaListener->message={}", JsonUtil.toJSONString(message));
            this.processMessage(message);
        }

    }



    /**
     * 处理消息并解析 ETA 结果。
     * @param message 消息对象，包含需要解析的 ETA 结果文本。
     */
    @LogAndAlarm
    private void processMessage(Message message){
        String text = message.getText();
        log.info("PromiseGoEtaListener->processMessage,text={}",JsonUtil.toJSONString(text));

        EtaResultBO etaResultBO = JSON.parseObject(text, EtaResultBO.class, Feature.SupportAutoType);
        log.info("PromiseGoEtaListener->processMessage,etaResultBO={}",JsonUtil.toJSONString(etaResultBO));

        //判断是否为评估师评估阶段，如果不是，return

        if (!StringUtil.equals("riskAssessment",etaResultBO.getStageCode())){
            return;
        }



        List<RiskAssessment> riskAssessments = riskAssessmentRepository.queryRiskAssessmentList(RiskAssessmentQuery.builder().riskLevel(CommonConstant.THREE).promiseId(Long.valueOf(etaResultBO.getAggregateId())).build());
        if (CollectionUtil.isEmpty(riskAssessments)){
            return;
        }
        log.info("PromiseGoEtaListener->processMessage,riskAssessments={}",JsonUtil.toJSONString(riskAssessments));



        RiskAssessment riskAssessment = riskAssessments.get(0);

        RiskAssessment updateRiskAssessment = new RiskAssessment();
        updateRiskAssessment.setId(riskAssessment.getId());


        Date estimateBeginTime = etaResultBO.getEstimateBeginTime();

        Date deadline = estimateBeginTime;

        if (Objects.nonNull(etaResultBO.getDuration())){
            deadline = DateUtil.offsetSecond(deadline,etaResultBO.getDuration().intValue());
        }

        updateRiskAssessment.setAssessmentDeadlineTime(deadline);

        Set<Integer> finallyAssStatus = RiskAssessmentStatusEnum.getFinallyAssStatus();
        if (!finallyAssStatus.contains(riskAssessment.getRiskAssessmentStatus())
                && DateUtil.compare(new Date(), deadline) > 0
        ){
            updateRiskAssessment.setAssessmentTimeoutStatus(CommonConstant.ONE);
        }

        riskAssessmentRepository.save(updateRiskAssessment);

    }




}
