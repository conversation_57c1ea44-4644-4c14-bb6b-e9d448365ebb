package com.jdh.o2oservice.listener.handler;

import com.alibaba.fastjson.JSON;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelWorkQcAlarmEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.export.angelpromise.cmd.QcAlarmCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @ClassName QcAlarmHandler
 * @Description
 * <AUTHOR>
 * @Date 2025/6/11 15:16
 **/
@Slf4j
@Component
public class QcAlarmHandler extends AbstractHandler<QcAlarmCmd> implements MapAutowiredKey {

    @Value("${topics.jdhReachStoreConsumer.qcAlarmTopic}")
    private String handlerTopic;

    /** */
    @Resource
    private AngelWorkRepository angelWorkRepository;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;


    @Override
    public String getMapKey() {
        return handlerTopic;
    }

    @Override
    public boolean preFilterOfDiscardMessage(Message message) {
        //消息检查
        if(Objects.isNull(message) || StringUtils.isBlank(message.getText())){
            return true;
        }
        return false;
    }

    @Override
    public QcAlarmCmd analysisMessage(Message message) {
        List<QcAlarmCmd> qcAlarmCmds = JSON.parseArray(message.getText(), QcAlarmCmd.class);
        if (CollectionUtils.isEmpty(qcAlarmCmds)) {
            return null;
        }
        //过滤，只取质控点报警的消息内容
        Optional<QcAlarmCmd> optional = qcAlarmCmds.stream().filter(qcAlarmCmd -> StringUtils.equals("siteAlarm", qcAlarmCmd.getAlarmType())).findFirst();
        return optional.orElse(null);
    }

    @Override
    public boolean filterOfDiscardMessage(QcAlarmCmd qcAlarmCmd) {
        log.info("QcAlarmHandler -> filterOfDiscardMessage, qcAlarmCmd={}", JSON.toJSONString(qcAlarmCmd));
        return Objects.isNull(qcAlarmCmd);
    }

    @Override
    public boolean transferToYf(Message message, QcAlarmCmd qcAlarmCmd) {
        // 判断当前pin是否在转投预发白名单
        return false;
    }

    @Override
    public void dealMessage(QcAlarmCmd qcAlarmCmd) {
        log.info("QcAlarmHandler -> dealMessage, qcAlarmCmd={}", JSON.toJSONString(qcAlarmCmd));
        if (StringUtils.isBlank(qcAlarmCmd.getPromiseId())) {
            log.warn("QcAlarmHandler -> dealMessage, 无履约单号，不处理，qcAlarmCmd={}", JSON.toJSONString(qcAlarmCmd));
            return;
        }
        //查询服务者工单
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseId(Long.valueOf(qcAlarmCmd.getPromiseId()));
        angelWorkDBQuery.setStatusList(AngelWorkStatusEnum.getValidStatus());
        angelWorkDBQuery.setCreateTimeOrderByAsc(true);
        List<AngelWork> list = angelWorkRepository.findList(angelWorkDBQuery);
        if(CollectionUtils.isEmpty(list)){
            log.warn("QcAlarmHandler -> dealMessage, 工单信息不存在，qcAlarmCmd={}", JSON.toJSONString(qcAlarmCmd));
            return;
        }
        AngelWork angelWork = list.get(0);

        AngelWorkQcAlarmEventBody body = new AngelWorkQcAlarmEventBody();
        BeanUtils.copyProperties(qcAlarmCmd, body);
        eventCoordinator.publish(EventFactory.newDefaultEvent(angelWork, AngelWorkEventTypeEnum.ANGEL_WORK_QC_ALARM, body));
    }
}