package com.jdh.o2oservice.web.controller.admin.dispatch;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.application.dispatch.service.DispatchTeamApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.base.util.UserPinContext;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.dispatch.cmd.*;
import com.jdh.o2oservice.export.dispatch.dto.*;
import com.jdh.o2oservice.export.dispatch.query.DispatchDetailForManRequest;
import com.jdh.o2oservice.export.dispatch.query.DispatchTeamListRequest;
import com.jdh.o2oservice.export.dispatch.query.DispatchTeamRequest;
import com.jdh.o2oservice.export.dispatch.query.DispatchWaitingReceiveListRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * DispatchController
 *
 * <AUTHOR>
 * @date 2024/05/03
 */
@RestController
@RequestMapping("/dispatch")
public class DispatchController {

    /**
     * 派单 application
     */
    @Autowired
    private DispatchApplication dispatchApplication;

    /**
     *
     */
    @Autowired
    private DispatchTeamApplication dispatchTeamApplication;

    /**
     * 重新 派单
     *
     * @param cmd cmd
     * @return {@link Response}<{@link Boolean}>
     */
    @RequestMapping(value = "/reDispatch", method = RequestMethod.POST)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.dispatch.DispatchController.reDispatch")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "重新派单", recordParamBizIdExpress = {"args[0].promiseId","args[0].dispatchId"})
    public Response<Boolean> reDispatch(@RequestBody ReDispatchCmd cmd){
        cmd.setOperator(LoginContext.getLoginContext().getPin());
        cmd.setRoleType(NumConstant.NUM_4);
        return ResponseUtil.buildSuccResponse(dispatchApplication.reDispatch(cmd));
    }


    /**
     * 定向 派单
     *
     * @param cmd cmd
     * @return {@link Response}<{@link Boolean}>
     */
    @RequestMapping(value = "/targetDispatch", method = RequestMethod.POST)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.dispatch.DispatchController.targetDispatch")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "定向派单", recordParamBizIdExpress = {"args[0].promiseId","args[0].dispatchId"})
    public Response<Boolean> targetDispatch(@RequestBody TargetDispatchCmd cmd){
        cmd.setOperator(LoginContext.getLoginContext().getPin());
        cmd.setRoleType(NumConstant.NUM_4);
        DispatchTargetStatusDto targetStatusDto = dispatchApplication.targetDispatch(cmd);
        if (Objects.equals(targetStatusDto.getTargetResult(), Boolean.FALSE)) {
            return ResponseUtil.buildErrResponse(targetStatusDto.getErrorCode(), targetStatusDto.getDescription());
        }
        return ResponseUtil.buildSuccResponse(targetStatusDto.getTargetResult());
    }

    /**
     * 定向 派单
     *
     * @param cmd cmd
     * @return {@link Response}<{@link Boolean}>
     */
    @RequestMapping(value = "/receiveTransferDispatch", method = RequestMethod.POST)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.dispatch.DispatchController.receiveTransferDispatch")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "接单护士转单", recordParamBizIdExpress = {"args[0].promiseId","args[0].dispatchId"})
    public Response<Boolean> receiveTransferDispatch(@RequestBody TargetDispatchCmd cmd){
        cmd.setOperator(LoginContext.getLoginContext().getPin());
        cmd.setRoleType(NumConstant.NUM_4);
        DispatchTargetStatusDto targetStatusDto = dispatchApplication.receiveTransferDispatch(cmd);
        if (Objects.equals(targetStatusDto.getTargetResult(), Boolean.FALSE)) {
            return ResponseUtil.buildErrResponse(targetStatusDto.getErrorCode(), targetStatusDto.getDescription());
        }
        return ResponseUtil.buildSuccResponse(targetStatusDto.getTargetResult());
    }

    /**
     * 查询派单明细
     * @param request
     * @return
     */
    @RequestMapping(value = "/queryDispatchDetailList", method = RequestMethod.POST)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.dispatch.DispatchController.queryDispatchDetailList")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询派单列表")
    public Response<PageDto<JdhDispatchForManDTO>> queryDispatchDetailList(@RequestBody DispatchDetailForManRequest request){
        PageDto<JdhDispatchForManDTO> res = dispatchApplication.queryDispatchListForMan(request);
        return ResponseUtil.buildSuccResponse(res);
    }

    /**
     * 查询派单小队列表
     * @param request
     * @return
     */
    @RequestMapping(value = "/queryDispatchTeamPage", method = RequestMethod.POST)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.dispatch.DispatchController.queryDispatchTeamPage")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询派单小队列表")
    public Response<PageDto<JdhDispatchTeamDTO>> queryDispatchTeamPage(@RequestBody DispatchTeamListRequest request){
        PageDto<JdhDispatchTeamDTO> res = dispatchTeamApplication.queryDispatchTeamPage(request);
        return ResponseUtil.buildSuccResponse(res);
    }

    /**
     * 查询派单小队明细
     * @param request
     * @return
     */
    @RequestMapping(value = "/queryDispatchTeamDetail", method = RequestMethod.POST)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.dispatch.DispatchController.queryDispatchTeamDetail")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询派单小队明细")
    public Response<JdhDispatchTeamDTO> queryDispatchTeamDetail(@RequestBody DispatchTeamRequest request){
        JdhDispatchTeamDTO dispatchTeamDetail = dispatchTeamApplication.queryDispatchTeamDetail(request);
        return ResponseUtil.buildSuccResponse(dispatchTeamDetail);
    }

    /**
     * 保存派单小队
     * @param saveDispatchTeamCmd
     * @return
     */
    @RequestMapping(value = "/saveDispatchTeam", method = RequestMethod.POST)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.dispatch.DispatchController.saveDispatchTeam")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "保存派单小队")
    public Response<Boolean> saveDispatchTeam(@Validated @RequestBody SaveDispatchTeamCmd saveDispatchTeamCmd){
        String operator = LoginContext.getLoginContext().getPin();
        saveDispatchTeamCmd.setOperator(operator);
        Boolean result = dispatchTeamApplication.saveDispatchTeam(saveDispatchTeamCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 保存派单小队技能
     * @param saveDispatchTeamSkillCmd
     * @return
     */
    @RequestMapping(value = "/saveDispatchTeamSkill", method = RequestMethod.POST)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.dispatch.DispatchController.saveDispatchTeamSkill")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "保存派单小队技能")
    public Response<SaveDispatchTeamDTO> saveDispatchTeamSkill(@Validated @RequestBody SaveDispatchTeamSkillCmd saveDispatchTeamSkillCmd){
        String operator = LoginContext.getLoginContext().getPin();
        saveDispatchTeamSkillCmd.setOperator(operator);
        SaveDispatchTeamDTO saveDispatchTeamDTO = dispatchTeamApplication.saveDispatchTeamSkill(saveDispatchTeamSkillCmd);
        return ResponseUtil.buildSuccResponse(saveDispatchTeamDTO);
    }

    /**
     * 删除派单小队技能
     * @param deleteDispatchTeamSkillCmd
     * @return
     */
    @RequestMapping(value = "/deleteDispatchTeamSkill", method = RequestMethod.POST)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.dispatch.DispatchController.deleteDispatchTeamSkill")
    @OperationLog(operationType = OpTypeEnum.DEL, operationDesc = "删除派单小队技能")
    public Response<Boolean> deleteDispatchTeamSkill(@Validated @RequestBody DeleteDispatchTeamSkillCmd deleteDispatchTeamSkillCmd){
        String operator = LoginContext.getLoginContext().getPin();
        deleteDispatchTeamSkillCmd.setOperator(operator);
        Boolean result = dispatchTeamApplication.deleteDispatchTeamSkill(deleteDispatchTeamSkillCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 保存派单小队服务者
     * @param saveDispatchTeamAngelCmd
     * @return
     */
    @RequestMapping(value = "/saveDispatchTeamAngel", method = RequestMethod.POST)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.dispatch.DispatchController.saveDispatchTeamAngel")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "保存派单小队服务者")
    public Response<SaveDispatchTeamDTO> saveDispatchTeamAngel(@Validated @RequestBody SaveDispatchTeamAngelCmd saveDispatchTeamAngelCmd){
        String operator = LoginContext.getLoginContext().getPin();
        saveDispatchTeamAngelCmd.setOperator(operator);
        SaveDispatchTeamDTO saveDispatchTeamDTO = dispatchTeamApplication.saveDispatchTeamAngel(saveDispatchTeamAngelCmd);
        return ResponseUtil.buildSuccResponse(saveDispatchTeamDTO);
    }

    /**
     * 删除派单小队服务者
     * @param deleteDispatchTeamAngelCmd
     * @return
     */
    @RequestMapping(value = "/deleteDispatchTeamAngel", method = RequestMethod.POST)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.dispatch.DispatchController.deleteDispatchTeamAngel")
    @OperationLog(operationType = OpTypeEnum.DEL, operationDesc = "删除派单小队服务者")
    public Response<Boolean> deleteDispatchTeamAngel(@Validated @RequestBody DeleteDispatchTeamAngelCmd deleteDispatchTeamAngelCmd){
        String operator = LoginContext.getLoginContext().getPin();
        deleteDispatchTeamAngelCmd.setOperator(operator);
        Boolean result = dispatchTeamApplication.deleteDispatchTeamAngel(deleteDispatchTeamAngelCmd);
        return ResponseUtil.buildSuccResponse(result);
    }
}
