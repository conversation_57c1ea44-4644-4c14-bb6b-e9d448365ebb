package com.jdh.o2oservice.test.appliacation.dispatch;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jd.fastjson.JSON;
import com.jd.jmq.common.message.Message;
import com.jd.matrix.core.biz.service.trace.ServiceTraceUtils;
import com.jd.matrix.core.domain.flow.AbstractDomainFlow;
import com.jd.matrix.core.domain.flow.DomainFlowNode;
import com.jd.matrix.core.domain.flow.engine.DomainFlowContext;
import com.jd.matrix.core.domain.flow.engine.IDomainFlowEngine;
import com.jdh.o2oservice.StartApplication;
import com.jdh.o2oservice.application.dispatch.convert.DispatchApplicationConverter;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.AngelDispatchPriceConfig;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.DispatchDetailTypeEnum;
import com.jdh.o2oservice.common.enums.DispatchTypeEnum;
import com.jdh.o2oservice.common.ext.O2oBusinessIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.context.AngelDispatchContext;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.flow.DispatchFlowDelegate;
import com.jdh.o2oservice.core.domain.dispatch.model.DispatchDetailGroupCount;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatch;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchDetail;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchDetailGroupCountQuery;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchDetailPageRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDto;
import com.jdh.o2oservice.export.dispatch.cmd.AngelDispatchCmd;
import com.jdh.o2oservice.export.dispatch.cmd.AngelDispatchOperationCmd;
import com.jdh.o2oservice.export.dispatch.cmd.DispatchAngelDetail;
import com.jdh.o2oservice.export.dispatch.cmd.DispatchCallbackCmd;
import com.jdh.o2oservice.export.dispatch.dto.AngelDispatchOperationDto;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchDetailDto;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchHistoryDto;
import com.jdh.o2oservice.export.dispatch.query.DispatchHistoryListRequest;
import com.jdh.o2oservice.listener.dispatch.NethpTriageDispatchListener;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @ClassName DispatchApplicationTest
 * @Description
 * <AUTHOR>
 * @Date 2024/5/11 16:22
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = StartApplication.class)
public class DispatchApplicationTest {

    /**
     * dispatchRepository
     */
    @Resource
    private DispatchRepository dispatchRepository;

    /**
     * dispatchApplication
     */
    @Resource
    private DispatchApplication dispatchApplication;

    /**
     * jdOrderApplication
     */
    @Resource
    private JdOrderApplication jdOrderApplication;

    /**
     * ducc
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * nethpTriageDispatchListener
     */
    @Resource
    private NethpTriageDispatchListener nethpTriageDispatchListener;

    /**
     * dispatchFlowDelegate
     */
    @Resource
    private DispatchFlowDelegate dispatchFlowDelegate;

    /**
     * findDispatchDetailGroupCount
     */
    @Test
    public void findDispatchDetailGroupCount() {
        List<DispatchDetailGroupCount> list =
                dispatchRepository.findDispatchDetailGroupCount(DispatchDetailGroupCountQuery.builder().column("dispatch_detail_type").dispatchId(1111L).build());
        log.info("list={}", JSON.toJSONString(list));
    }

    /**
     * findDispatchDetailPage
     */
    @Test
    public void findDispatchDetailPage() {
        DispatchDetailPageRepQuery repQuery = DispatchDetailPageRepQuery.builder().dispatchId(1111L).build();
        repQuery.setPageSize(2);
        repQuery.setPageNum(2);
        Page<JdhDispatchDetail> page = dispatchRepository.findDispatchDetailPage(repQuery);
        log.info("list={}", JSON.toJSONString(page));
    }

    /**
     *
     */
    @Test
    public void angelDispatch() {
        Boolean result = dispatchApplication.angelDispatch(AngelDispatchCmd.builder()
                .dispatchId(154942275321882L)
                //.userPin()
                .voucherId(154864458399753L)
                .promiseId(154864458399754L)
                .build());
        log.info("DispatchApplicationTest -> angelDispatch result={}", result);
    }

    /**
     *
     */
    @Test
    public void onMessage(){
        Message message = new Message();
        message.setText("{\"businessId\":\"320230523395169459\",\"businessType\":3,\"diagId\":22182724126658,\"eventContent\":\"{\\\"tenantType\\\":\\\"JD8888\\\",\\\"redispatchTime\\\":30,\\\"assignResultPin\\\":\\\"jd_JjblgNXDSOVy\\\",\\\"orderId\\\":271693229426,\\\"created\\\":\\\"2023-05-23 10:16:42\\\",\\\"departmentCode\\\":\\\"7300033\\\",\\\"assignResult\\\":\\\"jd_JjblgNXDSOVy\\\",\\\"assignType\\\":2,\\\"tirageId\\\":220230523409300397,\\\"billNo\\\":320230523395169459,\\\"diagId\\\":22182724126658,\\\"status\\\":1,\\\"diagType\\\":40032,\\\"businessScene\\\":618}\",\"eventTime\":\"2023-05-23 10:16:43\",\"eventType\":\"TRIAGE_ASSIGN_RECORD_CREATE\",\"operateMan\":\"11.27.122.150\",\"operateSource\":\"MQ\",\"orderId\":271693229426,\"serviceType\":1,\"tenantType\":\"JD8888\",\"userPin\":\"jd_6807135a4560f\"}");
        nethpTriageDispatchListener.onMessage(Lists.newArrayList(message));
    }

    /**
     *
     */
    @Test
    public void getAngelDispatchPriceConfigMap(){
        Map<String, AngelDispatchPriceConfig> angelDispatchPriceConfigMap = duccConfig.getAngelDispatchPriceConfigMap();
        AngelDispatchPriceConfig dispatchPriceConfig = JSON.parseObject(JSON.toJSONString(angelDispatchPriceConfigMap.get("default")), AngelDispatchPriceConfig.class);
        log.info("DispatchCompletedAction -> execute, dispatchPriceConfig={}", JSON.toJSONString(dispatchPriceConfig));
    }

    /**
     *
     */
    @Test
    public void queryDispatchHistoryListByPromiseId(){
        DispatchHistoryListRequest request = new DispatchHistoryListRequest();
        request.setDispatchId(154942275321882L);
        //request.setPromiseId();
        List<JdhDispatchHistoryDto> list = dispatchApplication.queryDispatchHistoryListByPromiseId(request);
        log.info("DispatchCompletedAction -> queryDispatchHistoryList, list={}", JSON.toJSONString(list));
    }

    /**
     *
     */
    @Test
    public void dispatch2AngelWorkDto() {
        JdhDispatch dispatch = JSON.parseObject("{\"aggregateCode\":\"DISPATCH\",\"branch\":\"yfb\",\"createUser\":\"德古拉的复兴\",\"dispatchId\":155596379128375,\"dispatchRound\":1,\"dispatchStatus\":1,\"dispatchType\":2,\"domainCode\":\"DISPATCH\",\"freeze\":0,\"id\":20,\"identifier\":{\"dispatchId\":155596379128375},\"outDispatchId\":\"\",\"promiseId\":155596378079287,\"serviceInfo\":{\"appointmentTime\":{\"appointmentEndTime\":\"2024-05-28T19:00:00\",\"appointmentStartTime\":\"2024-05-28T18:00:00\",\"dateType\":2,\"isImmediately\":false},\"businessModeCode\":\"angelTest\",\"gisPoint\":{\"address\":\"北京大兴区亦庄地区京东总部2号楼C座\",\"analysisType\":\"TENCENT_GIS\",\"latitude\":39.785059,\"longitude\":116.561359,\"reliability\":100},\"patients\":[{\"name\":\"测试\",\"patientAge\":33,\"patientGender\":1,\"patientId\":1192964528658,\"promisePatientId\":155596378079242,\"serviceItems\":[{\"itemId\":154640350445591,\"itemName\":\"测试0506\",\"itemSkillList\":[{\"angelSkillCode\":\"155097740869705\",\"itemId\":154640350445591}],\"materialPackages\":[{\"materialPackageId\":154330607910921,\"requiredFlag\":1},{\"materialPackageId\":154639235285063,\"requiredFlag\":2}],\"serviceId\":100106308492}],\"userPhone\":\"***********\"},{\"name\":\"京东测试订单\",\"patientAge\":45,\"patientGender\":1,\"patientId\":12826172485415,\"promisePatientId\":155596378079257,\"serviceItems\":[{\"itemId\":154640350445591,\"itemName\":\"测试0506\",\"itemSkillList\":[{\"$ref\":\"$.jdhDispatch.serviceInfo.patients[0].serviceItems[0].itemSkillList[0]\"}],\"materialPackages\":[{\"materialPackageId\":154330607910921,\"requiredFlag\":1},{\"materialPackageId\":154639235285063,\"requiredFlag\":2}],\"serviceId\":100106308492}],\"userPhone\":\"***********\"}],\"serviceDuration\":10,\"sourceVoucherId\":\"296097468929\"},\"serviceLocation\":{\"serviceLocationDetail\":\"北京大兴区亦庄地区京东总部2号楼C座\"},\"serviceType\":\"test\",\"userPin\":\"德古拉的复兴\",\"version\":1,\"verticalCode\":\"xfylHomeTest\",\"voucherId\":155596378079241}", JdhDispatch.class);
        JdhDispatchDetailDto detailDto = JSON.parseObject("{\"angelCharge\":\"350.00\",\"angelChargeDesc\":\"￥350.00\",\"angelId\":155134956929049,\"angelName\":\"测试扣扣凡\",\"appointmentTime\":{\"appointmentEndTime\":\"2024-05-28 19:00\",\"appointmentStartTime\":\"2024-05-28 18:00\",\"dateType\":2,\"isImmediately\":false,\"serviceDuration\":10},\"businessModeCode\":\"angelTest\",\"dispatchDetailId\":155596380700713,\"dispatchDetailStatus\":2,\"dispatchDetailStatusDesc\":\"已派单\",\"dispatchDetailType\":2,\"dispatchId\":155596379128375,\"dispatchType\":2,\"materialPackageAggregatedDesc\":\"无耗材×4\",\"outAngelId\":\"406213962402\",\"patients\":[{\"name\":\"测**\",\"patientAge\":33,\"patientGender\":1,\"patientId\":1192964528658,\"promisePatientId\":155596378079242,\"serviceItems\":[{\"itemId\":154640350445591,\"itemName\":\"测试0506\",\"serviceId\":100106308492}]},{\"name\":\"京**\",\"patientAge\":45,\"patientGender\":1,\"patientId\":12826172485415,\"promisePatientId\":155596378079257,\"serviceItems\":[{\"itemId\":154640350445591,\"itemName\":\"测试0506\",\"serviceId\":100106308492}]}],\"promiseId\":155596378079287,\"remainingDuration\":454,\"serviceItemAggregatedDesc\":\"测试0506\",\"serviceItemSize\":1,\"serviceLocation\":{\"distance\":1125,\"duration\":6,\"latitude\":39.785059,\"longitude\":116.561359,\"serviceLocationDetail\":\"北京大兴区亦庄地区京东总部2号楼C座\"},\"serviceType\":\"test\",\"sourceVoucherId\":\"296097468929\",\"verticalCode\":\"xfylHomeTest\",\"voucherId\":155596378079241}", JdhDispatchDetailDto.class);
        AngelWorkDto angelWorkDto = DispatchApplicationConverter.INS.dispatch2AngelWorkDto(detailDto, dispatch);
        log.info("dispatch2AngelWorkDto -> angelWorkDto={}", JSON.toJSONString(angelWorkDto));
    }

    /**
     *
     */
    @Test
    public void angelDispatchSwitchOperate(){
        AngelDispatchOperationCmd angelDispatchOperationCmd = JSON.parseObject("{\"dispatchDetailId\":155663852372346111,\"operation\":1,\"userPin\":\"黑色v星期九\"}", AngelDispatchOperationCmd.class);
        AngelDispatchOperationDto angelDispatchOperationDto = dispatchApplication.angelDispatchOperate(angelDispatchOperationCmd);
        log.info("angelDispatchSwitchOperate -> angelDispatchOperationDto={}", JSON.toJSONString(angelDispatchOperationDto));
    }

    /**
     * isProcessingOrder
     */
    @Test
     public void isProcessingOrder() {
        Boolean processingOrder = jdOrderApplication.isProcessingOrder(111L);
        log.info("isProcessingOrder={}", processingOrder);
    }

    /**
     * dispatch
     */
    @Test
    public void dispatch(){
        AngelDispatchContext dispatchContext = new AngelDispatchContext();
        dispatchContext.setVerticalCode("xfylHomeTest");
        dispatchContext.setServiceType("test");
        dispatchContext.initVertical();
        Boolean b = dispatchFlowDelegate.executeDispatch(dispatchContext);
    }

    /**
     * 组装消息体
     * @param args
     */
    public static void main(String[] args) {
        /*AtomicInteger i = new AtomicInteger(1);
        RetryTemplate RETRY_TEMPLATE = RetryTemplate.builder().maxAttempts(2).fixedBackoff(200).build();
        Integer execute = RETRY_TEMPLATE.execute(retryContext -> {
            i.getAndDecrement();
            return 1 / i.get();
        });
        System.out.println(execute);*/
        DispatchCallbackCmd cmd = new DispatchCallbackCmd();
        cmd.setDispatchId(154942275321882L);
        cmd.setDispatchStatus(JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus());
        cmd.setDispatchType(DispatchTypeEnum.IMMEDIATELY);
        cmd.setEventTime(new Date());
        cmd.setExpireDate(TimeUtils.addDays(cmd.getEventTime(), 7));

        DispatchAngelDetail dispatchAngelDetail = new DispatchAngelDetail();
        dispatchAngelDetail.setAngelId(154635651252249L);
        dispatchAngelDetail.setOutAngelId("");
        dispatchAngelDetail.setAngelName("测试服务者6");
        dispatchAngelDetail.setDispatchDetailType(DispatchDetailTypeEnum.ASSIGN.getType());
        dispatchAngelDetail.setExpireDate(cmd.getExpireDate());

        cmd.setAngelDetailList(Lists.newArrayList(dispatchAngelDetail));
        System.out.println(com.alibaba.fastjson.JSON.toJSONString(cmd));
    }
}