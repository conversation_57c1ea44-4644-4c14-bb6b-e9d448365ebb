package com.jdh.o2oservice.common.result.request;

import lombok.Data;

/**
 * 抽象请求参数
 * @author: yang<PERSON><PERSON>
 * @date: 2022/8/25 10:30 下午
 * @version: 1.0
 */
@Data
public abstract class  AbstractRequest {

    /** 客户端名称，不可为空 */
    protected String appName;
    /** 客户端ip，不可为空 */
    protected String clientIp;
    /** 授权的key */
    protected String accessKey;
    /** (垂直)业务身份 */
    protected String verticalCode;
    /** serviceId */
    protected String serviceId;
    /** 服务类型 */
    protected String serviceType;
    /**
     * 用户使用环境
     * ["jdhapp","jdhapp","jdheapp","jdmeapp","miniprogram","wxwork","wexin","qq","h5"]
     *  京东app    健康app   E企健康    京me      小程序         企业微信  微信     qq   h5
     */
    protected String envType;
    /** pin */
    protected String userPin;

    /**
     * 来自cookie中的unpl
     */
    private String unpl;
    /**
     * user agent 端
     */
    private Integer ua;

    /**
     * 微信openId
     */
    private String openId;

    /**
     * callWxType:
     */
    private String callWxType;

    /**
     * roleType
     */
    private String roleType;

    /**
     * channelName
     */
    private String channelName;

    /**
     * PartnerSource下的子渠道号
     */
    private String saleChannelId;
}
