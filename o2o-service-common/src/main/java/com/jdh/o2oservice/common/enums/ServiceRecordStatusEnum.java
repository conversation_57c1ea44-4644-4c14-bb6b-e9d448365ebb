package com.jdh.o2oservice.common.enums;

import lombok.Getter;

@Getter
public enum ServiceRecordStatusEnum {

    INIT(0, "初始"),

    FINISH(1, "完成"),

    CANCEL(2, "取消"),
    EDIT(3, "编辑"),
    HIGH_RISK(-1, "评估结果高风险"),
    ;

    ServiceRecordStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    private Integer status;

    private String desc;

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
