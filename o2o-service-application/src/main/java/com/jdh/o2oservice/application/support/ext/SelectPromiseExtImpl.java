package com.jdh.o2oservice.application.support.ext;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.AngelTypeEnum;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelProfessionRel;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.product.enums.ProductAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.event.PromiseModifyEventBody;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportAggregateEnum;
import com.jdh.o2oservice.core.domain.support.reach.ext.ReachServiceSelectDataExt;
import com.jdh.o2oservice.core.domain.support.reach.ext.param.SelectReachDataParam;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.export.angelpromise.dto.AngelTrackDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelTrackQuery;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 触达领域扩展点，查询触达任务需要的实体，查询履约单
 * @author: yangxiyu
 * @date: 2024/4/21 7:54 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class SelectPromiseExtImpl extends AbstractSelectPromise implements ReachServiceSelectDataExt {
    /**
     * 服务者地址
     */
    private static final String ANGEL_TRACK = "angelTrack";
    /** 履约单过期日期 */
    private static final String PROMISE_EXPIRE_DATE = "promiseExpireDate";
    /**
     * 扩展信息
     */
    private static final String EXTEND_MAP = "extendMap";

    private static final String PROFESSION_IDENTIFY = "angelProfession";

    /**
     * 预约开始时间戳，使用场景
     * （1）根据预约时间和当前时间戳判断是否发短信
     *
     */
    private static final String APPOINTMENT_START_TIMESTAMP = "appointmentStartTimestamp";
    /**
     *
     */
    private static final String LAST_MODIFY_APPOINTMENT_TIME = "lastModifyAppointmentTime";
    /**
     *
     */
    private static final String SUBMIT_ORDER_TIME = "submitOrderTime";

    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;
    @Resource
    private AngelWorkApplication angelWorkApplication;
    @Resource
    private PromiseHistoryRepository promiseHistoryRepository;

    @Resource
    private VoucherRepository voucherRepository;

    @Resource
    private JdOrderRepository jdOrderRepository;
    @Resource
    private ProductApplication productApplication;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelRepository angelRepository;

    /**
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> selectData(SelectReachDataParam param) {

        Map<String, Object> res = Maps.newHashMap();
        if(DomainEnum.PROMISE.getCode().equals(param.getDomainCode())&& PromiseAggregateEnum.VOUCHER.getCode().equals(param.getAggregateCode())){
            JdhVoucher jdhVoucher = voucherRepository.find(JdhVoucherIdentifier.builder().voucherId(Long.parseLong(param.getAggregateId())).build());
            res.put(PromiseAggregateEnum.VOUCHER.getCode(), jdhVoucher);
            return  res;
        }else{
            JdhPromise promise = find(param.getDomainCode(), param.getAggregateCode(), param.getAggregateId());
            if (Objects.isNull(promise)){
                return null;
            }
            JdhVerticalBusiness verticalBusiness = verticalBusinessRepository.find(promise.getVerticalCode());
            res.put(PromiseAggregateEnum.PROMISE.getCode(), promise);
            res.put(SupportAggregateEnum.VERTICAL_BUSINESS.getCode(), verticalBusiness);


            // 查询基础商品信息
            if (Objects.nonNull(promise.findBasicService())){
                Long skuId = promise.findBasicService().getServiceId();
                JdhSkuRequest jdhSkuRequest = new JdhSkuRequest();
                jdhSkuRequest.setSkuId(skuId);
                jdhSkuRequest.setQuerySkuCoreData(true);
                jdhSkuRequest.setQueryServiceItem(true);
                JdhSkuDto jdhSkuDto = productApplication.queryJdhSkuInfo(jdhSkuRequest);
                res.put(ProductAggregateEnum.PRODUCT_SKU.getCode(), jdhSkuDto);
            }

            // promise扩展字段单独填充
            List<JdhPromiseExtend> promiseExtends = promise.getPromiseExtends();
            if (CollectionUtils.isNotEmpty(promiseExtends)){
                Map<String, String> extendMap = Maps.newHashMap();
                for (JdhPromiseExtend extend : promiseExtends) {
                    extendMap.put(extend.getAttribute(), extend.getValue());
                }
                res.put(EXTEND_MAP, extendMap);
            }
            homeElements(promise, verticalBusiness, res);
            timeElements(promise, res);

            //查询工单信息
            AngelWork lastAngelWork = angelWorkRepository.findLastAngelWork(promise.getPromiseId());
            if(Objects.isNull(lastAngelWork) || StringUtils.isBlank(lastAngelWork.getAngelId())) {
                return res;
            }
            JdhAngelRepQuery jdhAngelRepQuery = new JdhAngelRepQuery();
            jdhAngelRepQuery.setAngelId(Long.valueOf(lastAngelWork.getAngelId()));
            JdhAngel jdhAngel = angelRepository.queryAngelDetail(jdhAngelRepQuery);
            if(Objects.isNull(jdhAngel) || CollectionUtils.isEmpty(jdhAngel.getJdhAngelProfessionRelList())){
                return res;
            }
            JdhAngelProfessionRel jdhAngelProfessionRel = jdhAngel.getJdhAngelProfessionRelList().get(0);
            res.put(PROFESSION_IDENTIFY, jdhAngelProfessionRel);
        }
        return res;
    }

    @Override
    public String functionId() {
        return "selectPromise";
    }

    /**
     * 到家服务需要填充的元素
     * @param promise
     * @param verticalBusiness
     */
    private void homeElements(JdhPromise promise , JdhVerticalBusiness verticalBusiness,  Map<String, Object> res){

        AngelTrackQuery angelTrackQuery = new AngelTrackQuery();
        angelTrackQuery.setPromiseId(promise.getPromiseId());
        try {
            AngelTrackDto track = null;
            if (Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_CARE.getCode())
                    || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST.getCode())
                    || Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode())){
                angelTrackQuery.setAngelType(AngelTypeEnum.NURSE.getType());
                track = angelWorkApplication.getTransferTrack(angelTrackQuery);
            }else if(Objects.equals(verticalBusiness.getBusinessModeCode(), BusinessModeEnum.SELF_TEST.getCode())){
                angelTrackQuery.setAngelType(AngelTypeEnum.DELIVERY.getType());
                track = angelWorkApplication.getTransferTrack(angelTrackQuery);
            }
            if (Objects.nonNull(track)) {
                res.put(ANGEL_TRACK, track);
            }
        }catch (Throwable e){
            log.warn("SelectPromiseExtImpl->getTransferTrack error", e.getMessage());
        }
    }


    /**
     * 时间相关，预约时间，过期时间等
     * @param promise
     * @param res
     */
    private void timeElements(JdhPromise promise,  Map<String, Object> res){
        // 添加履约单过期时间
        LocalDateTime expireDate = promise.getExpireDate();
        if (Objects.nonNull(expireDate)){
            res.put(PROMISE_EXPIRE_DATE, expireDate.format(TimeFormat.SHORT_PATTERN_LINE.formatter));
        }
        // 预约开始时间戳
        if (Objects.nonNull(promise.getAppointmentTime()) && Objects.nonNull(promise.getAppointmentTime().getAppointmentStartTime())){
            long startTime = promise.getAppointmentTime().getAppointmentStartTime().atZone(ZoneOffset.systemDefault()).toInstant().getEpochSecond();
            res.put(APPOINTMENT_START_TIMESTAMP, startTime);
        }
        // 如果是改约失败，获取本次修改预约提交的期望时间
        if (Objects.equals(promise.getPromiseStatus(), JdhPromiseStatusEnum.MODIFY_FAIL.getStatus())){
            JdhPromiseHistory history = promiseHistoryRepository.findLastEvent(promise.getPromiseId(), JdhPromiseStatusEnum.MODIFY_ING.getStatus());
            if (history.getVersion() < promise.getVersion()){
                String extend = history.getExtend();
                PromiseModifyEventBody modifyTime = JSON.parseObject(extend, PromiseModifyEventBody.class);
                res.put(LAST_MODIFY_APPOINTMENT_TIME, modifyTime);
            }
        }

        // 创建时间
        if (Objects.nonNull(promise.getCreateTime())){
            String submitDay = TimeUtils.dateToLocalDateTime(promise.getCreateTime()).format(TimeFormat.SHORT_PATTERN_LINE.formatter);
            res.put(SUBMIT_ORDER_TIME, submitDay);
        }
    }

    public static void main(String[] args) {
//        PromiseModifyEventBody modifyTime = JSON.parseObject("{\"afterStatus\":5,\"afterTime\":{\"appointmentEndTime\":\"2024-08-27T00:00:00\",\"appointmentStartTime\":\"2024-08-27T00:00:00\",\"dateType\":1},\"beforeStatus\":3,\"beforeTime\":{\"appointmentEndTime\":\"2024-08-28T00:00:00\",\"appointmentStartTime\":\"2024-08-28T00:00:00\",\"dateType\":1,\"isImmediately\":false}}\t", PromiseModifyEventBody.class);
//        String expressionStr = "lastModifyAppointmentTime.afterTime#formatDesc";
//        DynamicParse expression = new DynamicParse();
//        expression.setParseExpression(expressionStr);
//        expression.setParseType(2);
//
//        Map<String, Object> data = Maps.newHashMap();
//        data.put("lastModifyAppointmentTime", modifyTime);
//
//        Object value = expression.parse(data);
//        System.out.println(Objects.toString(value));


        JdhPromise promise = new JdhPromise();

        List<PromiseService> services = Lists.newArrayList();
        PromiseService service1 = new PromiseService();
        service1.setServiceName("项目1");
        services.add(service1);

        PromiseService service2 = new PromiseService();
        service2.setServiceName("项目2");
        services.add(service2);

        promise.setServices(services);

        Map<String, Object> map = Maps.newHashMap();
        map.put("promise", promise);
        System.out.println(BeanUtil.getProperty(map, "promise.services[0].serviceName").toString());
        System.out.println(BeanUtil.getProperty(map, "promise.services.size").toString());

    }

}
