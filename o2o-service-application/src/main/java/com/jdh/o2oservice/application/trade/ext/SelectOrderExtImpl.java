package com.jdh.o2oservice.application.trade.ext;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.core.domain.support.reach.ext.ReachServiceSelectDataExt;
import com.jdh.o2oservice.core.domain.support.reach.ext.param.SelectReachDataParam;
import com.jdh.o2oservice.core.domain.trade.enums.OrderAggregateCode;
import com.jdh.o2oservice.core.domain.trade.enums.OrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.vo.JdSkuRelationInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 查询订单信息
 * @author: yangxiyu
 * @date: 2024/5/30 4:26 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class SelectOrderExtImpl implements ReachServiceSelectDataExt {

    private static final String ORDER_EXTEND = "orderExtend";
    /**
     *
     */
    @Resource
    private JdOrderRepository jdOrderRepository;
    @Resource
    private JdOrderItemRepository jdOrderItemRepository;
    @Resource
    private ProductApplication productApplication;

    /**
     * 入参是履约单ID
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> selectData(SelectReachDataParam param) {
        Map<String, Object> res = Maps.newHashMap();

        Long orderId  = Long.valueOf(param.getAggregateId());
        JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(orderId));
        List<JdOrderItem> items = jdOrderItemRepository.listByOrderId(jdOrder.getOrderId());

        jdOrder.setJdOrderItemList(items);
        res.put(OrderAggregateCode.ORDER.getCode(), jdOrder);

        // 订单扩展信息
        Map<String, Object> extend = Maps.newHashMap();
        extend.put(OrderExtTypeEnum.SKU_WARE_TYPE.getType(), jdOrder.findWareType());

        List<JdSkuRelationInfoVo> relationInfoVos = jdOrder.findAllMainSku();
        if (CollectionUtils.isNotEmpty(relationInfoVos)){
            String mainSkuName = null;
            for (JdSkuRelationInfoVo relationInfoVo : relationInfoVos) {
                if (StringUtils.isNotBlank(relationInfoVo.getMainSkuName())){
                    mainSkuName = relationInfoVo.getMainSkuName();
                    break;
                }
            }
            extend.put(OrderExtTypeEnum.MAIN_SKU_NAME.getType(), mainSkuName);
        }
        res.put(ORDER_EXTEND, extend);
        return res;
    }

    @Override
    public String functionId() {
        return "selectOrder";
    }

    public static void main(String[] args) {
        Map<String, Object> res = Maps.newHashMap();

        JdOrder jdOrder = new JdOrder();
        List<JdOrderItem> items = Lists.newArrayList();
        JdOrderItem item = new JdOrderItem();
        item.setSkuName("商品");
        items.add(item);
        jdOrder.setJdOrderItemList(items);
        res.put("order", jdOrder);
        Object o = BeanUtil.getProperty(res, "order.jdOrderItemList[0].skuName");
        System.out.println(o);
    }
}
