package com.jdh.o2oservice.application.support.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.support.convert.ReachApplicationConverter;
import com.jdh.o2oservice.application.support.service.PromisegoApplication;
import com.jdh.o2oservice.application.support.service.ReachApplication;
import com.jdh.o2oservice.base.annotation.AutoTestSupport;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.LabAggStatusEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.WsEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseIdentifier;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.provider.rpc.QuickCheckThirdExportServiceRpc;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.basic.model.User;
import com.jdh.o2oservice.core.domain.support.basic.rpc.XfylDelayServiceRpc;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.LabPromisegoBo;
import com.jdh.o2oservice.core.domain.support.reach.context.*;
import com.jdh.o2oservice.core.domain.support.reach.factory.ReachFactory;
import com.jdh.o2oservice.core.domain.support.reach.model.*;
import com.jdh.o2oservice.core.domain.support.reach.repository.db.*;
import com.jdh.o2oservice.core.domain.support.reach.repository.query.JdhReachMessageDbQuery;
import com.jdh.o2oservice.core.domain.support.reach.rpc.WebSocketMessageRpc;
import com.jdh.o2oservice.core.domain.support.reach.rpc.bo.SendSocketMsgBO;
import com.jdh.o2oservice.core.domain.support.reach.service.ReachDomainService;
import com.jdh.o2oservice.core.domain.support.reach.valueobject.JdhReachMsgBoxGroup;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.export.support.command.*;
import com.jdh.o2oservice.export.support.dto.ReachMessageDto;
import com.jdh.o2oservice.export.support.query.ReachMsgBoxQuery;
import com.jdh.o2oservice.export.support.query.SendLaboratorySocketMsgRequest;
import com.jdh.o2oservice.export.via.dto.MessagePageDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 触达应用服务实现
 * <AUTHOR>
 * @date 2023-12-25-7:34 下午
 */
@Service
@Slf4j
@Lazy
public class ReachApplicationImpl implements ReachApplication {
    /**
     * reachDomainService
     */
    @Resource
    private ReachDomainService reachDomainService;

    /**
     * reachRuleRepository
     */
    @Resource
    private ReachRuleRepository reachRuleRepository;

    /**
     * promiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;

    /**
     * promiseHistoryRepository
     */
    @Resource
    private PromiseHistoryRepository promiseHistoryRepository;

    /**
     * voucherRepository
     */
    @Resource
    private VoucherRepository voucherRepository;

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;
    /** */
    @Resource
    private JdhReachTriggerRepository jdhReachTriggerRepository;
    /** */
    @Resource
    private JdhReachTaskRepository jdhReachTaskRepository;
    /** */
    @Resource
    private JdhReachTemplateRepository jdhReachTemplateRepository;
    /** */
    @Resource
    private JdhReachMessageTypeRepository jdhReachMessageTypeRepository;
    /** */
    @Resource
    private JdhReachMessageRepository jdhReachMessageRepository;


    @Resource
    private JdhReachConfigRepository jdhReachConfigRepository;

    /**
     * xfylDelayServiceRpc
     */
    @Resource
    private XfylDelayServiceRpc xfylDelayServiceRpc;

    /**
     * reachStoreProducer
     */
    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    /**
     *
     */
    @Value("${topics.reach.task.topic}")
    private String taskTopic;

    /**
     * medicalReportRepository
     */
    @Autowired
    private MedicalReportRepository medicalReportRepository;

    /**
     * medicalPromiseRepository
     */
    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * webSocketMessageRpc
     */
    @Autowired
    private WebSocketMessageRpc webSocketMessageRpc;

    /**
     * 线程池
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * promisegoApplication
     */
    @Autowired
    private PromisegoApplication promisegoApplication;

    @Resource
    private QuickCheckThirdExportServiceRpc quickCheckThirdExportServiceRpc;

    /**
     * 发送短信验证码
     *
     * @param sendCommand 发送命令
     * @return {@link Boolean}
     */
    @Override
    public Boolean sendSmsCode(SmsSendCommand sendCommand) {
        SmsCodeSendContext ctx = new SmsCodeSendContext();
        BeanUtils.copyProperties(sendCommand,ctx);
        ctx.setPhoneNumber(new PhoneNumber(sendCommand.getPhone()));
        ctx.initVertical();

        return reachDomainService.sendSmsCode(ctx);
    }

    @Override
    public Boolean reach(ReachCommand reachCommand) {
        log.info("ReachEventSubscriber -> promiseReach reachCommand:{}", JSON.toJSONString(reachCommand));
        if(reachCommand == null || reachCommand.getPromiseId() == null || StringUtils.isBlank(reachCommand.getEventCode())){
            log.warn("[ReachApplicationImpl-reach]invalid params");
            return Boolean.FALSE;
        }

        //获取事件的触达配置
        String eventCode = reachCommand.getEventCode();
        ReachRule reachRule = reachRuleRepository.findByEvent(eventCode);
        log.info("ReachEventSubscriber ->reach reachRule:{}", JSON.toJSONString(reachRule));
        if(reachRule == null || !reachRule.couldReach()){
            log.info("[ReachApplicationImpl-reach]non valid reachRule, eventCode:{}", eventCode);
            return false;
        }

        //触达
        ReachContext reachContext = new ReachContext();
        BeanUtils.copyProperties(reachCommand, reachContext);
        reachContext.setReachRule(reachRule);
        reachContext.initVertical();
        //设置必要数据（预约单、履约单、预约记录等）
        attachDomainData(reachContext);
        log.info("ReachEventSubscriber -> reach reachContext:{}", JSON.toJSONString(reachContext));
        return reachDomainService.sendReach(reachContext);
    }

    /**
     * 提交触达任务
     * @param event
     */
    @Override
    @LogAndAlarm(jKey="com.jdh.o2oservice.application.support.service.impl.ReachApplicationImpl.submitTask")
    @AutoTestSupport(whiteKey = "aggregateId", skip = true)
    public void submitTask(Event event) {
        List<JdhReachTrigger> triggers = jdhReachTriggerRepository.find(event);
        if (CollectionUtils.isEmpty(triggers)) {
            log.info("ReachApplicationImpl-> not match trigger");
            return;
        }
        Set<Long> templateIds = triggers.stream().map(JdhReachTrigger::getTemplateId).collect(Collectors.toSet());
        log.info("ReachApplicationImpl-> submitTask templateIds={}", JSON.toJSONString(templateIds));
        List<JdhReachTemplate> templates = jdhReachTemplateRepository.findList(templateIds);
        List<JdhReachTask> tasks = reachDomainService.initTask(triggers, event, templates);

        log.info("ReachApplicationImpl-> submitTask tasks={}", JSON.toJSONString(tasks));


        // 保存task
        if (CollectionUtils.isNotEmpty(tasks)) {
            jdhReachTaskRepository.batchSave(tasks);

            reachDomainService.submit(tasks);
        }
    }

    /**
     * 执行触达任务，不能事务注解@Transactional，因为涉及批量的RPC调用，会导致大事务；当前操作不需要保证强一致，极端情况消息没发送成功不影响业务流转
     * 消息发送失败也只是没有主动触达到，护士在消息盒子是可以看到消息的。
     * （1）查询task
     * （2）根据task构建上下文
     * （3）构建消息
     * （4）发送消息
     * （5）保存任务和发送失败的消息
     * @param taskId
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.ReachApplicationImpl.executeTask")
    public void executeTask(Long taskId) {
        JdhReachTask task = jdhReachTaskRepository.find(new JdhReachTaskIdentifier(taskId));

        try {
            ExecuteReachTaskContext context = new ExecuteReachTaskContext(task);
            // 构建消息
            reachDomainService.buildReachMessage(context);
            // 保存消息
            jdhReachMessageRepository.batchSave(context.getMessages());
            // 发送消息
            reachDomainService.sendReachMessage(context);
            // 保存任务
            task.executeSuccess();
            jdhReachTaskRepository.save(task);
            jdhReachMessageRepository.batchAddFailMsg(context.getSendFailMsg());
        }catch (Exception e){
            log.error("ReachApplicationImpl->executeTask error task={}", task,  e);
            if (Objects.nonNull(task)) {
                task.setStatus(JdhReachTask.FAIL_STATUS);
                task.setErrorInfo(e.getMessage());
                try{
                    jdhReachTaskRepository.save(task);
                }catch (Exception innerE){
                    log.error("ReachApplicationImpl->executeTask error",  e);
                }
            }
        }
    }

    /**
     * 查询消息盒子
     *
     * @param reachMsgBoxQuery
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MessagePageDto<ReachMessageDto> queryMsgBoxPage(ReachMsgBoxQuery reachMsgBoxQuery) {
        MessagePageDto<ReachMessageDto> rstDto = new MessagePageDto<>();

        //获取ducc配置的消息分组信息
        JdhReachMsgBoxGroup jdhReachMsgBoxGroup = jdhReachConfigRepository.findBoxGroup(reachMsgBoxQuery.getMessageGroupNo());
        if(Objects.isNull(jdhReachMsgBoxGroup)){
            log.error("[ReachApplicationImpl.queryMsgBoxPage],ducc配置错误!");
            throw new BusinessException(BusinessErrorCode.CUSTOM_ERROR_CODE, "ducc config error!");
        }


        //判断查询的消息是否是直接变更为已读
        if(jdhReachMsgBoxGroup.checkIsRead()){
            //未读消息直接设置成已读
            int updateNum = jdhReachMessageRepository.batchUpdateReadStatus(reachMsgBoxQuery.getUserPin(), reachMsgBoxQuery.getMessageGroupNo());
            log.info("[ReachApplicationImpl.queryMsgBoxPage],group更新已读状态{}条, reachMsgBoxQuery={}", updateNum, JSON.toJSONString(reachMsgBoxQuery));
        }


        //查询其他分组的数量
        AtomicInteger unReadCount = new AtomicInteger();
        Map<String, Integer> groupCount =  jdhReachMessageRepository.findCount(reachMsgBoxQuery.getUserPin());
        if (Objects.nonNull(groupCount)){
            groupCount.forEach((k, v) -> {
                unReadCount.getAndAdd(v);
            });
        }

        JdhReachMessageDbQuery jdhReachMessageDbQuery = JdhReachMessageDbQuery.builder()
                .userPin(reachMsgBoxQuery.getUserPin())
                .messageGroupNo(jdhReachMsgBoxGroup.getGroupNo())
                .cursor(reachMsgBoxQuery.getCursor())
                .offset(reachMsgBoxQuery.getOffset())
                .build();
        MessagePageDto<JdhReachMessage> groupDetailMessage = jdhReachMessageRepository.listByCursor(jdhReachMessageDbQuery);
        if(Objects.isNull(groupDetailMessage) || CollectionUtils.isEmpty(groupDetailMessage.getList())){
            log.info("[ReachApplicationImpl.queryMsgBoxPage],没有查到消息盒子的数据!");
            rstDto.setDefaultMessageGroup(reachMsgBoxQuery.getMessageGroupNo());
            rstDto.setGroupUnreadCount(groupCount);
            rstDto.setTotalUnreadCount(unReadCount.get());
            rstDto.setHasNextPage(Boolean.FALSE);
            return rstDto;
        }else{
            log.info("[ReachApplicationImpl.queryMsgBoxPage],存在消息数据!");
            //结果
            List<ReachMessageDto> reachMessageDtos = Lists.newArrayList();
            List<JdhReachMessage> list = groupDetailMessage.getList();
            list.forEach(msg -> {
                ReachMessageDto reachMessageDto = ReachApplicationConverter.instance.convertToReachMessageDto(msg);
                reachMessageDto.setMessageGroup(reachMsgBoxQuery.getMessageGroupNo());
                reachMessageDtos.add(reachMessageDto);
            });
            rstDto.setList(reachMessageDtos);
            rstDto.setTotalPage(groupDetailMessage.getTotalPage());
            rstDto.setTotalCount(groupDetailMessage.getTotalCount());
            rstDto.setHasNextPage(groupDetailMessage.getHasNextPage());

            rstDto.setDefaultMessageGroup(reachMsgBoxQuery.getMessageGroupNo());
            rstDto.setGroupUnreadCount(groupCount);
            rstDto.setTotalUnreadCount(unReadCount.get());
            if (CollectionUtils.isNotEmpty(list)){
                rstDto.setNextCursor(list.get(list.size() - 1).getTimestamp());
            }
            return rstDto;
        }




    }


    /**
     * 查询未读消息数量
     *
     * @param reachMsgBoxQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public Integer getUnreadCount(ReachMsgBoxQuery reachMsgBoxQuery) {
        Map<String, Integer> mapCount = jdhReachMessageRepository.findCount(reachMsgBoxQuery.getUserPin());
        if (Objects.isNull(mapCount)){
            return 0;
        }
        Integer count = 0;
        for (Integer value : mapCount.values()) {
            if (Objects.nonNull(value)){
                count += value;
            }
        }
        return count;
    }

    /**
     * 消息已读事件上报
     *
     * @param readCommand
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean readMessage(ReachMsgReadCommand readCommand) {
        AssertUtils.hasText(readCommand.getUserPin(), SystemErrorCode.DATA_AUTHORITY);
        AssertUtils.hasText(readCommand.getAppId(), SystemErrorCode.DATA_AUTHORITY);

        ReadingMessageBO messageBO = new ReadingMessageBO();
        messageBO.setMessageId(readCommand.getMessageId());
        messageBO.setUserPin(readCommand.getUserPin());
        messageBO.setAppId(readCommand.getAppId());
        reachDomainService.readMessage(messageBO);
        return Boolean.TRUE;
    }

    /**
     * 创建触达模板
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @LogAndAlarm
    @Transactional(rollbackFor = Exception.class)
    public Boolean createReachTemplate(CreateReachTemplateCmd cmd) {
        CreateReachTemplateContext ctx = ReachApplicationConverter.instance.createReachTemplateCmd2Ctx(cmd);
        log.info("ReachApplicationImpl createReachTemplate ctx:{}",JSON.toJSONString(ctx));
        JdhReachTemplate jdhReachTemplate = ReachFactory.createJdhReachTemplate(ctx);
        log.info("ReachApplicationImpl createReachTemplate jdhReachTemplate:{}",JSON.toJSONString(jdhReachTemplate));
        Integer effectRow = jdhReachTemplateRepository.save(jdhReachTemplate);
        return NumConstant.NUM_1.equals(effectRow);
    }

    /**
     * 更新触达模板
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @LogAndAlarm
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateReachTemplate(UpdateReachTemplateCmd cmd) {
        JdhReachTemplate jdhReachTemplate = jdhReachTemplateRepository.find(JdhReachTemplateIdentifier.builder().templateId(cmd.getTemplateId()).build());
        log.info("ReachApplicationImpl updateReachTemplate before jdhReachTemplate:{}",JSON.toJSONString(jdhReachTemplate));
        UpdateReachTemplateContext ctx = ReachApplicationConverter.instance.updateReachTemplateCmd2Ctx(cmd);
        jdhReachTemplate.update(ctx);
        log.info("ReachApplicationImpl updateReachTemplate after jdhReachTemplate:{}",JSON.toJSONString(jdhReachTemplate));
        Integer effectRow = jdhReachTemplateRepository.save(jdhReachTemplate);
        return NumConstant.NUM_1.equals(effectRow);
    }


    /**
     * deleteReachTemplate
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @LogAndAlarm
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteReachTemplate(DeleteReachTemplateCmd cmd) {
        JdhReachTemplate jdhReachTemplate = jdhReachTemplateRepository.find(JdhReachTemplateIdentifier.builder().templateId(cmd.getTemplateId()).build());
        log.info("ReachApplicationImpl deleteReachTemplate before jdhReachTemplate:{}",JSON.toJSONString(jdhReachTemplate));
        jdhReachTemplate.delete();
        log.info("ReachApplicationImpl deleteReachTemplate after jdhReachTemplate:{}",JSON.toJSONString(jdhReachTemplate));
        Integer effectRow = jdhReachTemplateRepository.save(jdhReachTemplate);
        return NumConstant.NUM_1.equals(effectRow);
    }

    /**
     * sendSmsCodeByUniqueId
     *
     * @param smsSendUniqueCommand smsSendUniqueCommand
     * @return
     */
    @Override
    public Boolean sendSmsCodeByUniqueId(SmsSendUniqueCommand smsSendUniqueCommand) {
        log.info("ReachApplicationImpl->sendSmsCodeByUniqueId={}",JsonUtil.toJSONString(smsSendUniqueCommand));

        SmsCodeSendContext ctx = new SmsCodeSendContext();
        BeanUtils.copyProperties(smsSendUniqueCommand,ctx);
        ctx.initVertical();
        if (StringUtils.equals("popReport",smsSendUniqueCommand.getScene())){
            log.info("ReachApplicationImpl->sendSmsCodeByUniqueId2={}",JsonUtil.toJSONString(smsSendUniqueCommand));
            MedicalReport medicalReport = medicalReportRepository.getById(Long.valueOf(smsSendUniqueCommand.getUniqueId()));
            JdhPromise jdhPromise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(medicalReport.getPromiseId()).build());
            log.info("ReachApplicationImpl->jdhPromise={}",JsonUtil.toJSONString(jdhPromise));
            //TODO 确认pop预约是否必有预约人手机号
            String phone = jdhPromise.getPatients().get(0).getPhoneNumber().getPhone();
            ctx.setPhoneNumber(new PhoneNumber(phone));
            ctx.setUniqueRedisStr(smsSendUniqueCommand.getUniqueId()+"_"+smsSendUniqueCommand.getUserPin());
        }else {
            ctx.setPhoneNumber(new PhoneNumber(smsSendUniqueCommand.getPhone()));
        }
        return reachDomainService.sendSmsCodeByUniqueId(ctx);
    }

    /**
     * 实验室端长连接
     * @param request
     * @return
     */
    @Override
    public Boolean sendLaboratorySocketMsg(SendLaboratorySocketMsgRequest request) {
        try {
            log.info("ReachApplicationImpl sendLaboratorySocketMsg request={}", JSON.toJSONString(request));
            if (Objects.isNull(request.getStationId())){
                log.info("ReachApplicationImpl sendLaboratorySocketMsg stationId empty");
                return false;
            }
            List<String> userPinList = quickCheckThirdExportServiceRpc.quickCheckStoreUser(request.getStationId());
            if (CollectionUtils.isEmpty(userPinList)){
                log.info("ReachApplicationImpl sendLaboratorySocketMsg userPinList empty");
                return false;
            }

            List<CompletableFuture<?>> futureAll = new ArrayList<>();
            userPinList.forEach(userPin->{
                futureAll.add(CompletableFuture.supplyAsync(() -> {
                    try {
                        SendSocketMsgBO wsMsg = new SendSocketMsgBO();
                        wsMsg.setAppId("10011");
                        wsMsg.setUserNo(request.getStationId()+"_"+userPin);
                        wsMsg.setSendType(2);
                        String msgBusinessId = request.getStationId()+"_"+userPin+"_"+request.getWsEventType()+"_"+System.currentTimeMillis();
                        wsMsg.setMsgBusinessId(msgBusinessId);
                        Map<String,Object> customerJsonDataMap = new HashMap<>();
                        customerJsonDataMap.put("stationId", request.getStationId());
                        customerJsonDataMap.put("wsEventType", request.getWsEventType());
                        customerJsonDataMap.put("data", request.getData());
                        wsMsg.setCustomerJsonData(JSON.toJSONString(customerJsonDataMap));
                        return webSocketMessageRpc.sendSocketMsg(wsMsg);
                    } catch (Exception e) {
                        log.error("ReachApplicationImpl sendLaboratorySocketMsg ws error e", e);
                    }
                    return false;
                },executorPoolFactory.get(ThreadPoolConfigEnum.WS_POOL)));
            });
            log.info("ReachApplicationImpl sendLaboratorySocketMsg end");
            // 等待全部执行完成
            //CompletableFuture.allOf(futureAll.toArray(new CompletableFuture[0])).join();
            return true;
        } catch (Exception e) {
            log.error("ReachApplicationImpl sendLaboratorySocketMsg error e", e);
            return false;
        }
    }

    /**
     * 实验室端数据超时通知（待收样、检测）
     *
     * @param event 事件
     */
    @Override
    public void labReach(Event event) {
        log.info("ReachApplicationImpl labTimeoutReach event={}", JSON.toJSONString(event));
        String eventCode = event.getEventCode();
        MedicalPromise medicalPromise = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(Long.parseLong(event.getAggregateId())).build());

        SendLaboratorySocketMsgRequest socketMsgRequest = SendLaboratorySocketMsgRequest
                .builder()
                .stationId(medicalPromise.getStationId())
                .build();

        Map<String, Object> data = new HashMap<>();

        //实验室收样 事件
        if(MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE.getCode().equals(eventCode)){
            socketMsgRequest.setWsEventType(WsEventTypeEnum.LAB_RECEIVE.getType());
            // 您有一个样本已送达，请在15分钟内完成收样”；（15分钟是eta配置的收样时间）
            LabPromisegoBo labPromisegoBo = promisegoApplication.queryLabPromisego(medicalPromise.getMedicalPromiseId(), LabAggStatusEnum.WAITING_TEST);
            if(Objects.nonNull(labPromisegoBo) && Objects.nonNull(labPromisegoBo.getCurrScript())){
                data.put("msg","您有一个样本已送达，请在" + (int)Math.ceil((double)labPromisegoBo.getCurrScript().getDuration()/60) + "分钟内完成收样");
            }
        }

        //实验室检测超时 事件
        if(MedPromiseEventTypeEnum.MED_PROMISE_TESTING_TIMEOUT.getCode().equals(eventCode)){
            socketMsgRequest.setWsEventType(WsEventTypeEnum.LAB_TESTING_TIMEOUT.getType());
            //您有*个样本检测超时
            data.put("msg","您有一个样本检测超时，请及时处理");
        }

        //实验室收样超时 事件
        if(MedPromiseEventTypeEnum.MED_PROMISE_WAITING_TESTING_TIMEOUT.getCode().equals(eventCode)){
            socketMsgRequest.setWsEventType(WsEventTypeEnum.LAB_WAITING_TEST_TIMEOUT.getType());
            //您有一个样本收样超时，请及时收样
            data.put("msg","您有一个样本收样超时，请及时收样");
        }
        socketMsgRequest.setData(data);
        this.sendLaboratorySocketMsg(socketMsgRequest);
    }


    /**
     * deleteReachTrigger
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @LogAndAlarm
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteReachTrigger(DeleteReachTriggerCmd cmd) {
        JdhReachTrigger trigger = jdhReachTriggerRepository.find(JdhReachTriggerIdentifier.builder().triggerId(cmd.getTriggerId()).build());
        log.info("ReachApplicationImpl deleteReachTrigger before trigger:{}",JSON.toJSONString(trigger));
        trigger.delete();
        log.info("ReachApplicationImpl deleteReachTrigger after trigger:{}",JSON.toJSONString(trigger));
        Integer effectRow = jdhReachTriggerRepository.save(trigger);
        return NumConstant.NUM_1.equals(effectRow);
    }

    /**
     * updateReachTrigger
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @LogAndAlarm
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateReachTrigger(UpdateReachTriggerCmd cmd) {
        JdhReachTrigger trigger = jdhReachTriggerRepository.find(JdhReachTriggerIdentifier.builder().triggerId(cmd.getTriggerId()).build());
        log.info("ReachApplicationImpl updateReachTrigger before trigger:{}",JSON.toJSONString(trigger));

        UpdateReachTriggerContext ctx = new UpdateReachTriggerContext();
        BeanUtil.copyProperties(cmd,ctx);
        log.info("ReachApplicationImpl updateReachTrigger before ctx:{}",JSON.toJSONString(ctx));

        trigger.update(ctx);

        log.info("ReachApplicationImpl updateReachTrigger after trigger:{}",JSON.toJSONString(trigger));

        Integer effectRow = jdhReachTriggerRepository.save(trigger);
        return NumConstant.NUM_1.equals(effectRow);
    }

    /**
     * createReachTrigger
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    public Boolean createReachTrigger(CreateReachTriggerCmd cmd) {
        CreateReachTriggerContext ctx = new CreateReachTriggerContext();
        BeanUtil.copyProperties(cmd,ctx);
        log.info("ReachApplicationImpl createReachTrigger before ctx:{}",JSON.toJSONString(ctx));

        JdhReachTrigger jdhReachTrigger = ReachFactory.createJdhReachTrigger(ctx);

        log.info("ReachApplicationImpl createReachTrigger after trigger:{}",JSON.toJSONString(jdhReachTrigger));

        Integer effectRow = jdhReachTriggerRepository.save(jdhReachTrigger);
        return NumConstant.NUM_1.equals(effectRow);
    }


    /**
     * 设置必要数据
     * @param reachContext
     */
    private void attachDomainData(ReachContext reachContext) {
        Long promiseId = reachContext.getPromiseId();
        Long voucherId = null;
        Long orderId = null;

        //查询预约单数据
        if (promiseId != null) {
            JdhPromise jdhPromise = promiseRepository.find(new JdhPromiseIdentifier(promiseId));
            reachContext.setPromise(ReachApplicationConverter.instance.jdhPromiseToReachPromise(jdhPromise));
            voucherId = Objects.nonNull(jdhPromise) ? jdhPromise.getVoucherId() : voucherId;
            if(Objects.nonNull(reachContext.getPromise()) && CollectionUtils.isNotEmpty(reachContext.getPromise().getPatients())) {
                User user = reachContext.getPromise().getPatients().get(0);
                if (Objects.nonNull(user) && Objects.nonNull(user.getPhoneNumber())){
                    reachContext.setPhone(user.getPhoneNumber().getPhone());
                }
            }
            //查询预约记录（预约单不为空）,查询当前状态的前两个状态
            if (Objects.nonNull(jdhPromise)) {
                reachContext.setPromiseHistoryList(Lists.newArrayList());
                //查询预约记录
                JdhPromiseHistory lastEvent = promiseHistoryRepository.findLastEvent(jdhPromise.getPromiseId(), jdhPromise.getPromiseStatus());
                if (Objects.nonNull(lastEvent)) {
                    //查询前一个状态记录
                    jdhPromise.setPromiseStatus(lastEvent.getBeforeStatus());
                    JdhPromiseHistory event = promiseHistoryRepository.findLastEvent(jdhPromise.getPromiseId(), jdhPromise.getPromiseStatus());
                    if (Objects.nonNull(event)) {
                        reachContext.getPromiseHistoryList().add(ReachApplicationConverter.instance.jdhPromiseHistoryToReachPromiseHistory(event));
                    }
                    reachContext.getPromiseHistoryList().add(ReachApplicationConverter.instance.jdhPromiseHistoryToReachPromiseHistory(lastEvent));
                }
            }
        }
        //查询服务单数据
        if (voucherId != null) {
            JdhVoucher jdhVoucher = voucherRepository.find(new JdhVoucherIdentifier(voucherId));
            reachContext.setVoucher(ReachApplicationConverter.instance.jdhVoucherToReachVoucher(jdhVoucher));
            orderId = Objects.isNull(orderId) && Objects.nonNull(jdhVoucher) ?
                    StringUtil.isNotBlank(jdhVoucher.getExtend().getOrderId()) ? Long.valueOf(jdhVoucher.getExtend().getOrderId()) :
                            Long.valueOf(jdhVoucher.getSourceVoucherId()) : voucherId;
        }
        //查询订单数据
        if (orderId != null) {
            JdOrder jdOrder = new JdOrder();
            jdOrder.setOrderId(orderId);
            //查询订单信息
            JdOrder orderDetail = jdOrderRepository.findOrderDetail(jdOrder);
            reachContext.setOrder(ReachApplicationConverter.instance.jdOrderToReachOrder(orderDetail));
        }
    }

}
