package com.jdh.o2oservice.application.angelpromise.service;
import com.jdh.o2oservice.export.angelpromise.cmd.SubmitAngelServiceRecordCmd;
import com.jdh.o2oservice.export.angelpromise.dto.AngelServiceRecordDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelServiceRecordQuestionGroupDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelTaskDto;
import com.jdh.o2oservice.export.angelpromise.dto.SubmitAngelServiceRecordDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordFlowQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordQuery;

import java.util.List;

/**
 * 护理单
 */
public interface AngelServiceRecordApplication {

    /**
     * 提交护理单
     * @param cmd
     * @return
     */
    SubmitAngelServiceRecordDto submitAngelServiceRecord(SubmitAngelServiceRecordCmd cmd);

    /**
     * 查询服务者服务项详情
     * @param query
     * @return
     */
    AngelServiceRecordDto queryAngelServiceRecordFlow(AngelServiceRecordFlowQuery query);

    /**
     * 校验是否开启了护理单配置
     * @param cmd
     * @return
     */
    Boolean checkAngelServiceRecordConfig(AngelServiceRecordQuery cmd);

    /**
     * 校验护理单是否已完成
     * @param cmd
     * @return
     */
    Boolean checkAngelServiceRecordFinish(AngelServiceRecordQuery cmd);

    /**
     * 通过taskId查询护理单相关pin
     * @param cmd
     * @return
     */
    AngelTaskDto queryAngelTaskDtoByTaskId(AngelServiceRecordFlowQuery cmd);

    /**
     * 查询节点配置数据
     * @param serviceItemIdList
     * @param businessMode
     * @param serviceType
     * @return
     */
    List<AngelServiceRecordQuestionGroupDto> queryQuestionGroupConfigList(List<Long> serviceItemIdList, String businessMode, String serviceType);
}
