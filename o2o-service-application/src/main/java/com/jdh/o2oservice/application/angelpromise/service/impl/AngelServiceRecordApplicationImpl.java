package com.jdh.o2oservice.application.angelpromise.service.impl;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.application.angelpromise.convert.AngelPromiseTaskApplicationConverter;
import com.jdh.o2oservice.application.angelpromise.convert.AngelServiceRecordApplicationConverter;
import com.jdh.o2oservice.application.angelpromise.service.AngelServiceRecordApplication;
import com.jdh.o2oservice.application.product.QuestionExtApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.base.util.TextUtil;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelServiceRecordContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelServiceRecordFlowRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelServiceRecordRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelServiceRecordDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelServiceRecordFlowDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelServiceRecordDomainService;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.securitynumber.enums.SecurityNumberBizCallTypeEnum;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.angelpromise.cmd.SubmitAngelServiceRecordCmd;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordFlowQuery;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordQuery;
import com.jdh.o2oservice.export.product.dto.QuestionGroupDto;
import com.jdh.o2oservice.export.product.query.QueryQuesByGroupCode;
import com.jdh.o2oservice.export.support.dto.PdfSignatureResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 护理单
 */
@Service
@Slf4j
public class AngelServiceRecordApplicationImpl implements AngelServiceRecordApplication {

    @Resource
    private AngelServiceRecordDomainService angelServiceRecordDomainService;

    @Resource
    private QuestionExtApplication questionExtApplication;

    @Resource
    private AngelServiceRecordRepository angelServiceRecordRepository;

    @Resource
    private AngelServiceRecordFlowRepository angelServiceRecordFlowRepository;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private TradeApplication tradeApplication;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private AngelTaskRepository angelTaskRepository;

    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    @Resource
    private PromiseRepository promiseRepository;

    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    private RedisLockUtil redisLockUtil;

    /**
     * 提交护理单
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelServiceRecordApplicationImpl.submitAngelServiceRecord")
    public SubmitAngelServiceRecordDto submitAngelServiceRecord(SubmitAngelServiceRecordCmd cmd) {
        String lockKey = "";
        String uuid = "";
        try {
            if (Objects.isNull(cmd) || Objects.isNull(cmd.getTaskId()) || StringUtils.isBlank(cmd.getFlowCode())
                    || CollectionUtils.isEmpty(cmd.getAnswerQuestionDTOList())){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);
            }

            RedisKeyEnum redisKeyEnum = RedisKeyEnum.SUBMIT_ANGEL_SERVICE_RECORD_KEY;
            lockKey = RedisKeyEnum.getRedisKey(redisKeyEnum, cmd.getTaskId(), cmd.getFlowCode());
            uuid = UUID.randomUUID().toString();
            if (!redisLockUtil.tryLock(lockKey, uuid, redisKeyEnum.getExpireTime(), redisKeyEnum.getExpireTimeUnit())) {
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_SERVICE_RECORD_REPEAT_CREATE);
            }

            AngelTask angelTask = angelTaskRepository.find(AngelTaskIdentifier.builder().taskId(cmd.getTaskId()).build());
            if (Objects.isNull(angelTask)){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_TASK_NOT_EXIST);
            }

            // 查询护理单
            AngelServiceRecord angelServiceRecord = this.queryAngelServiceRecordByTaskId(cmd.getTaskId());
            if (Objects.isNull(angelServiceRecord)){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_SERVICE_RECORD_NOT_EXIST);
            }

            if (Arrays.asList(ServiceRecordStatusEnum.HIGH_RISK.getStatus(), ServiceRecordStatusEnum.CANCEL.getStatus()).contains(angelServiceRecord.getStatus())){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_SERVICE_RECORD_NOT_ALLOWED_OPERATE);
            }

            AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelServiceRecord.getWorkId()).build());

            // todo 权限校验
           /* if (!cmd.getUserPin().equals(angelWork.getAngelPin())){
                throw new SystemException(SystemErrorCode.DATA_AUTHORITY);
            }*/

            // 查询节点配置数据
            JdhVerticalBusiness verticalBusiness = verticalBusinessRepository.find(angelWork.getVerticalCode());
            List<Long> serviceItemIdList = JSON.parseArray(angelServiceRecord.getServiceItemIdList(), Long.class);
            List<AngelServiceRecordQuestionGroupDto> allQuestionGroupConfigList = this.queryQuestionGroupConfigList(serviceItemIdList, verticalBusiness.getBusinessModeCode(), angelWork.getServiceType());

            // 构建节点数据
            AngelServiceRecordQuestionGroupDto serviceRecordQuestionGroup = this.buildServiceRecordQuestionGroup(cmd, allQuestionGroupConfigList);

            // 节点code去重
            List<AngelServiceRecordQuestionGroupDto> repeatAllQuestionGroupConfigList  = allQuestionGroupConfigList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(AngelServiceRecordQuestionGroupDto::getCode))), ArrayList::new));

            // 节点排序 1 2 3...
            repeatAllQuestionGroupConfigList.sort(Comparator.comparing(AngelServiceRecordQuestionGroupDto::getSort));
            log.info("AngelServiceRecordApplicationImpl submitAngelServiceRecord repeatAllQuestionGroupConfigList={}", JSON.toJSONString(repeatAllQuestionGroupConfigList));

            // 配置节点code
            List<String> repeatConfigFlowCodeList = repeatAllQuestionGroupConfigList.stream().map(AngelServiceRecordQuestionGroupDto::getCode).collect(Collectors.toList());
            log.info("AngelServiceRecordApplicationImpl submitAngelServiceRecord repeatConfigFlowCodeList={}", JSON.toJSONString(repeatConfigFlowCodeList));

            // 判断是否存在高危选项
            Boolean highRiskOption = this.checkHighRisk(serviceRecordQuestionGroup);
            log.info("AngelServiceRecordApplicationImpl submitAngelServiceRecord highRiskOption={}", highRiskOption);

            // 高风险提示
            SubmitAngelServiceRecordDto highRiskTipResult = this.serviceRecordHighRiskTip(cmd, highRiskOption);
            log.info("AngelServiceRecordApplicationImpl submitAngelServiceRecord highRiskTipResult={}", JSON.toJSONString(highRiskTipResult));
            if (Objects.nonNull(highRiskTipResult)){
                return highRiskTipResult;
            }
            JdhPromise jdhPromise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(angelWork.getPromiseId()).build());
            log.info("AngelServiceRecordApplicationImpl submitAngelServiceRecord jdhPromise={}", JSON.toJSONString(jdhPromise));
            String serviceName = TextUtil.joinPropertyLimit(jdhPromise.getServices(), "serviceName", "、", 2, "...");

            AngelServiceRecordContext context = AngelServiceRecordContext.builder()
                    .id(angelServiceRecord.getId())
                    .recordId(angelServiceRecord.getRecordId())
                    .taskId(cmd.getTaskId())
                    .promisePatientId(Long.valueOf(angelTask.getPatientId()))
                    .promiseId(angelWork.getPromiseId())
                    .highRiskOption(highRiskOption)
                    .flowCode(serviceRecordQuestionGroup.getCode())
                    .flowName(serviceRecordQuestionGroup.getName())
                    .sortId(serviceRecordQuestionGroup.getSort())
                    .detail(JSON.toJSONString(serviceRecordQuestionGroup))
                    .answerQuestionDTOList(cmd.getAnswerQuestionDTOList())
                    .configFlowCodeNum(repeatConfigFlowCodeList.size())
                    .lastFlowNode(repeatConfigFlowCodeList.get(repeatConfigFlowCodeList.size()-1))
                    .serviceName(serviceName)
                    .build();
            return angelServiceRecordDomainService.submitAngelServiceRecord(context);
        } finally {
            redisLockUtil.unLock(lockKey, uuid);
        }
    }

    /**
     * 查询护理单项详情
     * @param query
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelServiceRecordApplicationImpl.queryAngelServiceRecordFlow")
    public AngelServiceRecordDto queryAngelServiceRecordFlow(AngelServiceRecordFlowQuery query) {
        if(Objects.isNull(query) || Objects.isNull(query.getTaskId())){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);
        }

        // 查询服务记录
        AngelServiceRecord angelServiceRecord = this.queryAngelServiceRecordByTaskId(query.getTaskId());
        if (Objects.isNull(angelServiceRecord)){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_SERVICE_RECORD_NOT_EXIST);
        }

        //点击编辑按钮后查询进入固定节点
        if(query.getQueryType() != null && query.getQueryType().equals(1)){
            query.setFlowCode(QuestionGroupTypeEnum.SUPPLYVERIFICATION.getCode());
            angelServiceRecord.setStatus(ServiceRecordStatusEnum.EDIT.getStatus());
            this.updateAngelServiceRecordStatusById(angelServiceRecord);
        }else if(angelServiceRecord.getStatus().equals(ServiceRecordStatusEnum.EDIT.getStatus()) && StringUtils.isEmpty(query.getFlowCode())){
            query.setFlowCode(angelServiceRecord.getLastFlowNode());
        }

        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelServiceRecord.getWorkId()).build());

        // todo 权限校验
//        if (!query.getUserPin().equals(angelWork.getAngelPin())){
//            throw new SystemException(SystemErrorCode.DATA_AUTHORITY);
//        }

        // 查询业务模式
        JdhVerticalBusiness verticalBusiness = verticalBusinessRepository.find(angelWork.getVerticalCode());


        // 构建所有节点数据
        AngelServiceRecordDto recordDto = this.buildServiceRecordDto(query, angelServiceRecord,verticalBusiness.getBusinessModeCode(),angelWork);

        log.info("AngelServiceRecordApplicationImpl queryAngelServiceRecordFlow recordDto={}", recordDto);

        return recordDto;
    }

    /**
     * 校验是否开启了护理单配置
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelServiceRecordApplicationImpl.checkAngelServiceRecordConfig")
    public Boolean checkAngelServiceRecordConfig(AngelServiceRecordQuery cmd) {
        JSONObject obj = JSON.parseObject(duccConfig.getAngelServiceRecordConfig());
        if (!obj.getBoolean("serviceRecordSwitch")){
            return false;
        }
        AngelServiceRecordDBQuery query = AngelServiceRecordDBQuery.builder()
                .workId(cmd.getWorkId())
                .build();
        List<AngelServiceRecord> angelServiceRecordList = angelServiceRecordRepository.findList(query);
        log.info("AngelServiceRecordApplicationImpl checkAngelServiceRecordConfig angelServiceRecordList={}", JSON.toJSONString(angelServiceRecordList));
        return CollectionUtils.isNotEmpty(angelServiceRecordList);
    }

    /**
     * 校验护理单是否已完成
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelServiceRecordApplicationImpl.checkAngelServiceRecordConfig")
    public Boolean checkAngelServiceRecordFinish(AngelServiceRecordQuery cmd) {
        AngelServiceRecordDBQuery query = AngelServiceRecordDBQuery.builder()
                .workId(cmd.getWorkId())
                .build();
        List<AngelServiceRecord> angelServiceRecordList = angelServiceRecordRepository.findList(query);
        log.info("AngelServiceRecordApplicationImpl checkAngelServiceRecordFinish angelServiceRecordList={}", JSON.toJSONString(angelServiceRecordList));
        if (CollectionUtils.isEmpty(angelServiceRecordList)){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_SERVICE_RECORD_NOT_EXIST);
        }
        // 多人场景，可能是一个人高危、一个人完成
        List<AngelServiceRecord> finishServiceRecordList = angelServiceRecordList.stream().filter(s -> Arrays.asList(ServiceRecordStatusEnum.FINISH.getStatus()
                        , ServiceRecordStatusEnum.HIGH_RISK.getStatus()).contains(s.getStatus())).collect(Collectors.toList());
        log.info("AngelServiceRecordApplicationImpl checkAngelServiceRecordFinish finishServiceRecordList={}", JSON.toJSONString(finishServiceRecordList));
        return finishServiceRecordList.size() == angelServiceRecordList.size();
    }

    /**
     * 判断是否存在高危选项
     * @param serviceRecordQuestionGroup
     * @return
     */
    private Boolean checkHighRisk(AngelServiceRecordQuestionGroupDto serviceRecordQuestionGroup) {
        for (AngelServiceRecordQuestionDto serviceRecordQuestion : serviceRecordQuestionGroup.getQuestionDTOS()) {
            if (Objects.nonNull(serviceRecordQuestion.getHighRisk()) && NumConstant.NUM_1.equals(serviceRecordQuestion.getHighRisk())){
                if (StringUtils.isBlank(serviceRecordQuestion.getExtJson())){
                    continue;
                }
                List<QuestionOptionDto> questionOptionList = JSON.parseArray(serviceRecordQuestion.getExtJson(), QuestionOptionDto.class);
                for (QuestionOptionDto questionOption : questionOptionList) {
                    if (NumConstant.NUM_1.equals(serviceRecordQuestion.getType())){// 单选
                        // 命中高危选项
                        if (NumConstant.NUM_1.equals(questionOption.getHighRisk()) && StringUtils.isNotBlank(serviceRecordQuestion.getAnswerValue())
                                && serviceRecordQuestion.getAnswerValue().equals(questionOption.getValue())){
                            log.info("AngelServiceRecordApplicationImpl checkHighRisk single hit");
                            return true;
                        }
                    }else if (NumConstant.NUM_2.equals(serviceRecordQuestion.getType())){// 多选
                        // 命中高危选项
                        if (NumConstant.NUM_1.equals(questionOption.getHighRisk()) && StringUtils.isNotBlank(serviceRecordQuestion.getAnswerValue())){
                            List<String> answerValueList = Arrays.asList(serviceRecordQuestion.getAnswerValue().split(","));
                            if (answerValueList.contains(questionOption.getValue())){
                                log.info("AngelServiceRecordApplicationImpl checkHighRisk multiple choice hit");
                                return true;
                            }
                        }
                    }

                }
            }
        }
        return false;
    }

    /**
     * 查询护理单护士pin和患者pin
     * @param query
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelServiceRecordApplicationImpl.queryAngelTaskDtoByTaskId")
    public AngelTaskDto queryAngelTaskDtoByTaskId(AngelServiceRecordFlowQuery query) {
        AngelTaskDto angelTaskDto = this.queryPatientServiceInfo(query.getTaskId());
        log.info("AngelServiceRecordApplicationImpl queryAngelTaskDtoByTaskId angelTaskDto={}", JSON.toJSONString(angelTaskDto));
        return angelTaskDto;
    }

    /**
     * 构建节点数据
     * @param cmd
     * @param allQuestionGroupConfigList
     * @return
     */
    private AngelServiceRecordQuestionGroupDto buildServiceRecordQuestionGroup(SubmitAngelServiceRecordCmd cmd, List<AngelServiceRecordQuestionGroupDto> allQuestionGroupConfigList) {
        List<AngelServiceRecordQuestionGroupDto> serviceRecordQuestionGroups = new ArrayList<>();
        for (AngelServiceRecordQuestionGroupDto f : allQuestionGroupConfigList) {
            if (cmd.getFlowCode().equals(f.getCode())){
                serviceRecordQuestionGroups.add(f);
            }
        }
        log.info("AngelServiceRecordApplicationImpl buildServiceRecordQuestionGroup serviceRecordQuestionGroups={}", JSON.toJSONString(serviceRecordQuestionGroups));


        // 题去重
        List<AngelServiceRecordQuestionDto> repeatServiceRecordQuestions = this.filterDuplicateQuestions(serviceRecordQuestionGroups,cmd.getFlowCode());

        log.info("buildServiceRecordQuestionGroup before repeatServiceRecordQuestions={}", JSON.toJSONString(repeatServiceRecordQuestions));

        repeatServiceRecordQuestions.sort(Comparator.comparing(AngelServiceRecordQuestionDto::getSort));
        log.info("buildServiceRecordQuestionGroup sort repeatServiceRecordQuestions={}", JSON.toJSONString(repeatServiceRecordQuestions));

        // 题答案按照题code分组
        Map<String, ServiceRecordAnswerQuestionDTO> submitAnswerQuestionMap = cmd.getAnswerQuestionDTOList().stream().collect(Collectors.toMap(ServiceRecordAnswerQuestionDTO::getQuesCode
                , Function.identity(), (key1, key2) -> key2));
        log.info("buildServiceRecordQuestionGroup sort submitAnswerQuestionMap={}", JSON.toJSONString(submitAnswerQuestionMap));

        // 填充题答案
        repeatServiceRecordQuestions.forEach(r->{
            ServiceRecordAnswerQuestionDTO submitAnswerQuestion = submitAnswerQuestionMap.get(r.getQuesCode());
            if (Objects.nonNull(submitAnswerQuestion) && StringUtils.isNotBlank(submitAnswerQuestion.getAnswerValue())){
                r.setAnswerValue(submitAnswerQuestion.getAnswerValue());
            }
        });

        AngelServiceRecordQuestionGroupDto serviceRecordQuestionGroup = serviceRecordQuestionGroups.get(0);
        serviceRecordQuestionGroup.setQuestionDTOS(repeatServiceRecordQuestions);
        log.info("buildServiceRecordQuestionGroup serviceRecordQuestionGroup={}", JSON.toJSONString(serviceRecordQuestionGroup));
        return serviceRecordQuestionGroup;
    }

    /**
     * 查询节点配置数据
     * @param serviceItemIdList
     * @param businessMode
     * @param serviceType
     * @return
     */
    public List<AngelServiceRecordQuestionGroupDto> queryQuestionGroupConfigList(List<Long> serviceItemIdList, String businessMode, String serviceType) {
        // 节点数据
        List<AngelServiceRecordQuestionGroupDto> questionGroupConfigList = new ArrayList<>();
        serviceItemIdList.forEach(serviceItemId->{
            QueryQuesByGroupCode quesByGroupCode = new QueryQuesByGroupCode();
            quesByGroupCode.setServiceItemId(serviceItemId);
            quesByGroupCode.setBusinessMode(businessMode);
            quesByGroupCode.setServiceType(serviceType);
            // 查询题节点数据
            List<QuestionGroupDto> questionGroupDtoList = questionExtApplication.queryQuesByGroupCode(quesByGroupCode);
            log.info("queryQuestionGroupConfigList res quesByGroupCode={}, questionGroupDtoList={}", JSON.toJSONString(quesByGroupCode), JSON.toJSONString(questionGroupDtoList));
            if (CollectionUtils.isNotEmpty(questionGroupDtoList)){
                questionGroupDtoList.forEach(p->{
                    if (NumConstant.NUM_1.equals(p.getShow())){
                        questionGroupConfigList.add(AngelServiceRecordApplicationConverter.INS.convertToServiceRecordQuestionGroupDto(p));
                    }
                });
            }
        });
        log.info("queryQuestionGroupConfigList questionGroupConfigList={}", JSON.toJSONString(questionGroupConfigList));
        return questionGroupConfigList;
    }

    /**
     * 构建节点数据
     * @param query
     * @param angelServiceRecord
     * @return
     */
    private AngelServiceRecordDto buildServiceRecordDto(AngelServiceRecordFlowQuery query, AngelServiceRecord angelServiceRecord, String businessMode, AngelWork angelWork) {
        String serviceType = angelWork.getServiceType();
        //初始化返回对象
        AngelServiceRecordDto angelServiceRecordDto = new AngelServiceRecordDto();
        angelServiceRecordDto.setServiceRecordStatus(angelServiceRecord.getStatus());
        angelServiceRecordDto.setTaskId(query.getTaskId());
        angelServiceRecordDto.setWorkStatus(angelWork.getWorkStatus());
        //初始化返回的节点集合
        List<AngelServiceRecordQuestionGroupDto> angelServiceRecordQuestionGroupDtoList = new ArrayList<>();
        // 获取服务项目ID
        List<Long> serviceItemIdList = JSON.parseArray(angelServiceRecord.getServiceItemIdList(), Long.class);
        // 查询护理单已完成节点
        List<AngelServiceRecordFlow> angelServiceRecordFlows = this.queryAngelServiceRecordFlowByRecordId(angelServiceRecord.getRecordId());
        // 保存所有服务项目的节点数据（题目），方便之后对于题目进行合并去重
        Map<String,List<AngelServiceRecordQuestionGroupDto>> questionGroupDtoMap = new HashMap<>();
        serviceItemIdList.forEach(serviceItemId->{
            QueryQuesByGroupCode quesByGroupCode = new QueryQuesByGroupCode();
            quesByGroupCode.setServiceItemId(serviceItemId);
            quesByGroupCode.setBusinessMode(businessMode);
            quesByGroupCode.setServiceType(serviceType);
            // 查询题节点数据
            this.buildServiceRecordQuestionGroupDto(questionGroupDtoMap,angelServiceRecordFlows,quesByGroupCode);
        });

        //节点去重后重新排序
        List<AngelServiceRecordQuestionGroupDto> questionGroupDtos = new ArrayList<>();
        questionGroupDtoMap.forEach((k,q)->{
            questionGroupDtos.addAll(q);
        });
        List<AngelServiceRecordQuestionGroupDto> angelServiceRecordQuestionGroupDtos = questionGroupDtos.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(AngelServiceRecordQuestionGroupDto::getCode))), ArrayList::new));
        angelServiceRecordQuestionGroupDtos.sort(Comparator.comparing(AngelServiceRecordQuestionGroupDto::getSort));

        // 节点位置定位，如果没有传节点code，默认根据用户操作的最后一个节点定位到下一个节点
        if(StringUtils.isEmpty(query.getFlowCode())) {
            String nextFlowCode = getNextFlowCode(angelServiceRecordQuestionGroupDtos,angelServiceRecord);
            query.setFlowCode(nextFlowCode);
        }

        //查询服务信息
        boolean isFinish = false;
        AngelTaskDto angelTaskDto = this.queryPatientServiceInfo(query.getTaskId());
        if(angelServiceRecord.getStatus().equals(ServiceRecordStatusEnum.FINISH.getStatus())
                || angelServiceRecord.getStatus().equals(ServiceRecordStatusEnum.HIGH_RISK.getStatus())
                || angelWork.getWorkStatus().equals(AngelWorkStatusEnum.COMPLETED.getType())
                || angelWork.getWorkStatus().equals(AngelWorkStatusEnum.REFUNDED.getType())
                || angelWork.getWorkStatus().equals(AngelWorkStatusEnum.CANCEL.getType())){
            if(CollectionUtils.isNotEmpty(angelServiceRecordQuestionGroupDtos)){
                angelServiceRecordQuestionGroupDtos.get(0).setAngelServiceInfoDto(angelTaskDto);
                isFinish = true;
            }
        }
        //循环处理节点数据
        for (AngelServiceRecordQuestionGroupDto angelServiceRecordQuestionGroupDto:angelServiceRecordQuestionGroupDtos) {
            if (angelServiceRecordQuestionGroupDto != null) {
                if(!isFinish) {
                    // 节点特殊处理：添加被服务者信息
                    JSONObject obj = JSON.parseObject(duccConfig.getAngelServiceRecordConfig());
                    List<String> addAngelServiceInfoFlowCode = JSON.parseArray(obj.getString("addAngelServiceInfoFlowCode"), String.class);
                    if (addAngelServiceInfoFlowCode.contains(angelServiceRecordQuestionGroupDto.getCode())) {
                        angelServiceRecordQuestionGroupDto.setAngelServiceInfoDto(angelTaskDto);
                    }
                }
                // 服务前评估节点特殊处理：添加按钮控制
                if (angelServiceRecordQuestionGroupDto.getCode().equals(QuestionGroupTypeEnum.PRESERVICEASSESSMENT.getCode())
                        && angelWork.getWorkStatus() < AngelWorkStatusEnum.SERVICING.getType()) {
                    angelServiceRecordQuestionGroupDto.setIsClick(false);
                    angelServiceRecordQuestionGroupDto.setButtonName("服务开始后才可以填写");
                }

                // 根据节点状态组装节点数据
                if (StringUtils.isEmpty(query.getFlowCode()) && angelServiceRecordQuestionGroupDto.getStatus().equals(ServiceRecordQuestionGroupStatusEnum.INIT.getStatus())) {
                    query.setFlowCode(angelServiceRecordQuestionGroupDto.getCode());
                    angelServiceRecordQuestionGroupDto.setStatus(ServiceRecordQuestionGroupStatusEnum.PROCESS.getStatus());
                    // 提去重
                    List<AngelServiceRecordQuestionDto> filterRepeatQuestionList = this.filterDuplicateQuestions(questionGroupDtoMap, angelServiceRecordQuestionGroupDto);
                    angelServiceRecordQuestionGroupDto.setQuestionDTOS(filterRepeatQuestionList);
                } else if (StringUtils.isNotEmpty(query.getFlowCode()) && query.getFlowCode().equals(angelServiceRecordQuestionGroupDto.getCode())) {
                    if (angelServiceRecordQuestionGroupDto.getStatus().equals(ServiceRecordQuestionGroupStatusEnum.INIT.getStatus())) {
                        // 提去重
                        List<AngelServiceRecordQuestionDto> filterRepeatQuestionList = this.filterDuplicateQuestions(questionGroupDtoMap, angelServiceRecordQuestionGroupDto);
                        angelServiceRecordQuestionGroupDto.setQuestionDTOS(filterRepeatQuestionList);
                    }
                    angelServiceRecordQuestionGroupDto.setStatus(ServiceRecordQuestionGroupStatusEnum.PROCESS.getStatus());
                }else{
                    if (angelServiceRecordQuestionGroupDto.getStatus().equals(ServiceRecordQuestionGroupStatusEnum.INIT.getStatus())) {
                        // 提去重
                        List<AngelServiceRecordQuestionDto> filterRepeatQuestionList = this.filterDuplicateQuestions(questionGroupDtoMap, angelServiceRecordQuestionGroupDto);
                        angelServiceRecordQuestionGroupDto.setQuestionDTOS(filterRepeatQuestionList);
                    }
                }
                //避免按钮文案出现空的情况进行兜底，兜底文案“下一步”
                if (StringUtils.isEmpty(angelServiceRecordQuestionGroupDto.getButtonName())) {
                    angelServiceRecordQuestionGroupDto.setButtonName("下一步");
                }
                angelServiceRecordQuestionGroupDtoList.add(angelServiceRecordQuestionGroupDto);
            }
        }
        angelServiceRecordDto.setQuestionGroupDtoList(angelServiceRecordQuestionGroupDtoList);
        return angelServiceRecordDto;
    }


    private List<AngelServiceRecordQuestionDto> filterDuplicateQuestions(List<AngelServiceRecordQuestionGroupDto> questionGroupDtos,String code) {
        Map<String,List<AngelServiceRecordQuestionGroupDto>> questionGroupDtoMap = new HashMap<>();
        questionGroupDtoMap.put(code,questionGroupDtos);
        AngelServiceRecordQuestionGroupDto angelServiceRecordQuestionGroupDto = new AngelServiceRecordQuestionGroupDto();
        angelServiceRecordQuestionGroupDto.setCode(code);
        return filterDuplicateQuestions(questionGroupDtoMap,angelServiceRecordQuestionGroupDto);
    }
    /**
     * 对节点下重复题目进行去重
     * @param angelServiceRecordQuestionGroupDto
     * @param questionGroupDtoMap
     * @return
     */
    private List<AngelServiceRecordQuestionDto> filterDuplicateQuestions(Map<String,List<AngelServiceRecordQuestionGroupDto>> questionGroupDtoMap,AngelServiceRecordQuestionGroupDto angelServiceRecordQuestionGroupDto){
        List<AngelServiceRecordQuestionGroupDto> questionGroupList = questionGroupDtoMap.get(angelServiceRecordQuestionGroupDto.getCode());
        // 提去重
        if (QuestionGroupTypeEnum.PRESERVICESIGNATURE.getCode().equals(angelServiceRecordQuestionGroupDto.getCode()) ||
                QuestionGroupTypeEnum.SIGNCONFIRM.getCode().equals(angelServiceRecordQuestionGroupDto.getCode())){
            Map<String,List<AngelServiceRecordQuestionDto>> questionMap = new HashMap<>();
            //对相同题目分组
            questionGroupList.forEach(questionGroup->{
                if(CollectionUtils.isNotEmpty(questionGroup.getQuestionDTOS())){
                    questionGroup.getQuestionDTOS().forEach(q -> {
                        List<AngelServiceRecordQuestionDto> questionDtos = questionMap.computeIfAbsent(q.getQuesCode(),k -> new ArrayList<>());
                        questionDtos.add(q);
                    });
                }
            });
            List<AngelServiceRecordQuestionDto> filterRepeatQuestionList = new ArrayList<>();
            // 签字题，题目去重的同时需要合并题目中的签字文件
            questionMap.forEach((k,questionDtos) -> {
                AngelServiceRecordQuestionDto dto = questionDtos.get(0);
                JSONArray value = new JSONArray();
                JSONObject positionJson = new JSONObject();
                Set<String> fileNames = new HashSet<>();
                questionDtos.forEach(q -> {
                    List<PdfSignatureResult> files = JSONArray.parseArray(q.getValue(),PdfSignatureResult.class);
                    // 循环合并所有文件
                    files.forEach(file -> {
                        if(!fileNames.contains(file.getFileName())){
                            fileNames.add(file.getFileName());
                            value.add(file);
                        }
                    });
                    // 合并所有文件坐标
                    if(StringUtils.isNotEmpty(q.getExtJson())){
                        String position = JSON.parseObject(q.getExtJson()).getString("position");
                        if(StringUtils.isNotEmpty(position)){
                            JSONObject jsonObject = JSONObject.parseObject(position);
                            positionJson.putAll(jsonObject);
                        }
                    }
                });
                dto.setValue(value.toJSONString());
                if(StringUtils.isNotEmpty(dto.getExtJson())){
                    JSONObject extJson = JSON.parseObject(dto.getExtJson());
                    extJson.put("position",positionJson.toJSONString());
                    dto.setExtJson(extJson.toJSONString());
                }else{
                    JSONObject extJson = new JSONObject();
                    extJson.put("position",positionJson.toJSONString());
                    dto.setExtJson(extJson.toJSONString());
                }
                filterRepeatQuestionList.add(dto);
            });
            filterRepeatQuestionList.sort(Comparator.comparing(AngelServiceRecordQuestionDto::getSort));
            log.info("AngelServiceRecordApplicationImpl filterDuplicateQuestions questionGroup={}", JSON.toJSONString(filterRepeatQuestionList));
            return filterRepeatQuestionList;
        }else{
            // 题
            List<AngelServiceRecordQuestionDto> filterRepeatQuestionList = new ArrayList<>();
            Set<String> codes = new HashSet<>();
            questionGroupList.forEach(questionGroup->{
                if(CollectionUtils.isNotEmpty(questionGroup.getQuestionDTOS())){
                    questionGroup.getQuestionDTOS().forEach(q -> {
                        if(!codes.contains(q.getQuesCode())){
                            codes.add(q.getQuesCode());
                            filterRepeatQuestionList.add(q);
                        }
                    });
                }
            });
            filterRepeatQuestionList.sort(Comparator.comparing(AngelServiceRecordQuestionDto::getSort));
            log.info("AngelServiceRecordApplicationImpl filterDuplicateQuestions questionGroup={}", JSON.toJSONString(filterRepeatQuestionList));
            return filterRepeatQuestionList;
        }
    }

    /**
     * 根据用户操作的最后一个节点定位到下一个节点
     * @param angelServiceRecordQuestionGroupDtos
     * @param angelServiceRecord
     * @return
     */
    private String getNextFlowCode(List<AngelServiceRecordQuestionGroupDto> angelServiceRecordQuestionGroupDtos,AngelServiceRecord angelServiceRecord){
        for (int i = 0; i < angelServiceRecordQuestionGroupDtos.size(); i++) {
            AngelServiceRecordQuestionGroupDto angelServiceRecordQuestionGroupDto = angelServiceRecordQuestionGroupDtos.get(i);
            if (angelServiceRecordQuestionGroupDto != null
                    && StringUtils.isNotEmpty(angelServiceRecord.getLastFlowNode())
                    && angelServiceRecord.getLastFlowNode().equals(angelServiceRecordQuestionGroupDto.getCode())
                    && i < angelServiceRecordQuestionGroupDtos.size()-1) {
                return angelServiceRecordQuestionGroupDtos.get(i+1).getCode();
            }
        }
        return null;
    }

    /**
     * 构建服务项目节点和题目数据，方便之后去重
     * @param questionGroupDtoMap
     * @return
     */
    private void buildServiceRecordQuestionGroupDto(Map<String,List<AngelServiceRecordQuestionGroupDto>> questionGroupDtoMap,List<AngelServiceRecordFlow> angelServiceRecordFlows,QueryQuesByGroupCode quesByGroupCode){
        // 查询题节点数据
        List<QuestionGroupDto> productQuestionGroupList = questionExtApplication.queryQuesByGroupCode(quesByGroupCode);
        if (CollectionUtils.isNotEmpty(productQuestionGroupList)){
            productQuestionGroupList.forEach(productQuestionGroup -> {
                // 将所有需要显示的节点的数据保存到map里
                if (productQuestionGroup.getShow().equals(NumConstant.NUM_1)){
                    List<AngelServiceRecordQuestionGroupDto> questionGroupList = questionGroupDtoMap.computeIfAbsent(productQuestionGroup.getCode(),k->new ArrayList<>());
                    AngelServiceRecordQuestionGroupDto questionGroupDto = AngelServiceRecordApplicationConverter.INS.convertToServiceRecordQuestionGroupDto(productQuestionGroup);
                    questionGroupDto.setStatus(ServiceRecordQuestionGroupStatusEnum.INIT.getStatus());
                    for(AngelServiceRecordFlow angelServiceRecordFlow:angelServiceRecordFlows){
                        if(questionGroupDto.getCode().equals(angelServiceRecordFlow.getFlowCode())){
                            questionGroupDto.setStatus(ServiceRecordQuestionGroupStatusEnum.FINISH.getStatus());
                            AngelServiceRecordQuestionGroupDto dto = JSONObject.parseObject(angelServiceRecordFlow.getDetail(),AngelServiceRecordQuestionGroupDto.class);
                            questionGroupDto.setQuestionDTOS(dto.getQuestionDTOS());
                            break;
                        }
                    }
                    questionGroupList.add(questionGroupDto);
                }
            });
        }
    }

    /**
     * 高风险提示
     * @param cmd
     * @param highRiskOption
     * @return
     */
    private SubmitAngelServiceRecordDto serviceRecordHighRiskTip(SubmitAngelServiceRecordCmd cmd, Boolean highRiskOption) {
        if (BooleanUtil.isTrue(highRiskOption)
                && (Objects.isNull(cmd.getHighRiskConfirm()) || BooleanUtil.isFalse(cmd.getHighRiskConfirm()))){
            JSONObject serviceRecordConfigObj = JSON.parseObject(duccConfig.getAngelServiceRecordConfig());
            // 接单评估
            if (QuestionGroupTypeEnum.PRERECEIVEASSESSMENT.getCode().equals(cmd.getFlowCode())){
                SubmitAngelServiceRecordDto result = new SubmitAngelServiceRecordDto();
                result.setTaskId(cmd.getTaskId());
                result.setServiceRecordStatus(ServiceRecordStatusEnum.HIGH_RISK.getStatus());
                result.setHighRiskTip(serviceRecordConfigObj.getString("receiveOrderHighRiskTip"));
                return result;
            }
            // 服务前评估
            if (QuestionGroupTypeEnum.PRESERVICEASSESSMENT.getCode().equals(cmd.getFlowCode())){
                SubmitAngelServiceRecordDto result = new SubmitAngelServiceRecordDto();
                result.setTaskId(cmd.getTaskId());
                result.setServiceRecordStatus(ServiceRecordStatusEnum.HIGH_RISK.getStatus());
                result.setHighRiskTip(serviceRecordConfigObj.getString("serviceBeforeHighRiskTip"));
                return result;
            }
        }
        return null;
    }


    /**
     * 根据任务ID查询护理单
     * @param taskId
     * @return
     */
    private AngelServiceRecord queryAngelServiceRecordByTaskId(Long taskId) {
        AngelServiceRecordDBQuery angelServiceRecordDBQuery = AngelServiceRecordDBQuery.builder()
                .taskId(taskId)
                .build();
        List<AngelServiceRecord> angelServiceRecordList = angelServiceRecordRepository.findList(angelServiceRecordDBQuery);
        log.info("queryAngelServiceRecordByTaskId angelServiceRecordList={}", JSON.toJSONString(angelServiceRecordList));
        if (CollectionUtils.isNotEmpty(angelServiceRecordList)){
            return angelServiceRecordList.get(0);
        }
        return null;
    }

    /**
     * 根据任务ID查询护理单
     * @param record
     * @return
     */
    private boolean updateAngelServiceRecordStatusById(AngelServiceRecord record) {
        AngelServiceRecord angelServiceRecord = AngelServiceRecord.builder()
                .id(record.getId())
                .recordId(record.getRecordId())
                .status(ServiceRecordStatusEnum.EDIT.getStatus())
                .lastFlowNode(QuestionGroupTypeEnum.SUPPLYVERIFICATION.getCode())
                .build();
        int result = angelServiceRecordRepository.save(angelServiceRecord);
        log.info("queryAngelServiceRecordByTaskId result={}", result);
        if (result > 0){
            return true;
        }
        return false;
    }

    /**
     * 根据recordId查询服务记录
     * @param recordId
     * @return
     */
    private List<AngelServiceRecordFlow> queryAngelServiceRecordFlowByRecordId(Long recordId) {
        AngelServiceRecordFlowDBQuery angelServiceRecordFlowDBQuery = AngelServiceRecordFlowDBQuery.builder()
                .recordId(recordId)
                .build();
        List<AngelServiceRecordFlow> angelServiceRecordFlows = angelServiceRecordFlowRepository.findList(angelServiceRecordFlowDBQuery);
        log.info("queryAngelServiceRecordByTaskId angelServiceRecordFlows={}", JSON.toJSONString(angelServiceRecordFlows));
        if (CollectionUtils.isNotEmpty(angelServiceRecordFlows)){
            return angelServiceRecordFlows;
        }
        return new ArrayList<>();
    }

    /**
     * 查询用户服务信息
     * @param taskId
     * @return
     */
    public AngelTaskDto queryPatientServiceInfo(Long taskId){
        // 查询服务者任务单
        AngelTask angelTask = angelTaskRepository.find(AngelTaskIdentifier.builder().taskId(taskId).build());
        log.info("queryServiceInfo angelTask={}", JSON.toJSONString(angelTask));

        // 查询服务者工单
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelTask.getWorkId()).build());
        log.info("queryServiceInfo angelWork={}", JSON.toJSONString(angelWork));

        List<AngelTaskDto> angelTaskList = AngelPromiseTaskApplicationConverter.INS.convert2TaskDto(Collections.singletonList(angelTask));
        log.info("queryServiceInfo angelTaskList={}", JSON.toJSONString(angelTaskList));
        AngelTaskDto task = angelTaskList.get(0);

        // 年龄处理
        if(StringUtils.isNotBlank(task.getPatientAge())){
            task.setPatientAge(task.getPatientAge().concat("岁"));
        }

        // 脱敏处理
        task.setPatientName(new UserName(task.getPatientName()).mask());

        // 服务者打给被服务人
        List<Integer> workStatusList = Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType(),AngelWorkStatusEnum.SERVICING.getType());
        if (ServiceTypeEnum.TEST.getServiceType().equals(angelWork.getServiceType())){
            workStatusList = Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(),AngelWorkStatusEnum.WAIT_SERVICE.getType()
                    ,AngelWorkStatusEnum.SERVICING.getType(),AngelWorkStatusEnum.SERVICED.getType(),AngelWorkStatusEnum.DELIVERING.getType());
        }
        if (workStatusList.contains(angelWork.getWorkStatus())){
            AngelWorkCallDto angelWorkCall = new AngelWorkCallDto();
            angelWorkCall.setPromiseId(angelWork.getPromiseId());
            angelWorkCall.setBizCallType(SecurityNumberBizCallTypeEnum.ANGEL_TO_SERVICED.getCode());
            angelWorkCall.setPromisePatientId(task.getPatientId());
            task.setAngelWorkCall(angelWorkCall);
        }

        MedicalPromiseListQuery medicalPromiseListQuery = MedicalPromiseListQuery.builder()
                .promisePatientId(Long.valueOf(angelTask.getPatientId()))
                .build();
        List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        if (CollectionUtils.isNotEmpty(medicalPromiseList)){
            List<String> serviceItemNameList = medicalPromiseList.stream().map(MedicalPromise::getServiceItemName).distinct().collect(Collectors.toList());
            task.setServiceItemNameList(serviceItemNameList);
            task.setUserPin(medicalPromiseList.get(0).getUserPin());
        }
        task.setAngelPin(angelWork.getAngelPin());
        return task;
    }

}
