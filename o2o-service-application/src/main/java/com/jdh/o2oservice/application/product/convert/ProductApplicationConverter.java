package com.jdh.o2oservice.application.product.convert;

import com.jdh.market.core.promo.coupon.dto.CouponInfoDTO;
import com.jdh.o2oservice.base.ducc.model.DiscountFeeAction;
import com.jdh.o2oservice.base.ducc.model.DiscountFeeDisplay;
import com.jdh.o2oservice.core.domain.product.bo.*;
import com.jdh.o2oservice.core.domain.product.event.ProducBaseEventBody;
import com.jdh.o2oservice.core.domain.product.model.*;
import com.jdh.o2oservice.core.domain.product.repository.query.ProductServiceGoodsQuery;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.AddressDetailBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.SkuInfoAndStyleBO;
import com.jdh.o2oservice.export.product.cmd.CreateJdhSkuCmd;
import com.jdh.o2oservice.export.product.cmd.UpdateJdhSkuCmd;
import com.jdh.o2oservice.export.product.cmd.UpdateJdhSkuSaleStatusCmd;
import com.jdh.o2oservice.export.product.dto.*;
import com.jdh.o2oservice.export.product.query.JdhSkuPageRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuServiceItemRelPageRequest;
import com.jdh.o2oservice.export.product.query.ProductServiceGoodsListRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * @ClassName ProductApplicationConverter
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 15:50
 **/
@Mapper
public interface ProductApplicationConverter {

    ProductApplicationConverter instance = Mappers.getMapper(ProductApplicationConverter.class);

    ProductServiceGoodsQuery serviceGoodsListRequest2Query(ProductServiceGoodsListRequest request);

    List<ProviderServiceGoodsDto> serviceGoodsEntity2Dto(List<ProductServiceGoods> list);

    List<SkuCouponInfoDto> convertToSkuCouponInfoDtoList(List<CouponInfoDTO> couponInfoList);

    List<IndicatorItemDto> convertToIndicatorItemDtoList(List<IndicatorItem> couponInfoList);

    /**
     * 商品中台skuBo转京东健康商品对象
     *
     * @param rpcSkuBO bo
     * @return dto
     */
    JdhSkuDto convertToJdhSkuDto(RpcSkuBO rpcSkuBO);
    
    /**
     * 商品中台skuBo转京东健康商品对象
     *
     * @param jdhSku jdhSku
     * @return dto
     */
    @Mapping(target = "serviceResourceType", expression = "java(com.alibaba.fastjson.JSON.parseArray(jdhSku.getServiceResourceType(), Integer.class))")
    @Mapping(target = "dayTimeFrame", expression = "java(com.alibaba.fastjson.JSON.parseArray(jdhSku.getDayTimeFrame(), String.class))")
    @Mapping(target = "stationAssignType", expression = "java(com.alibaba.fastjson.JSON.parseArray(jdhSku.getStationAssignType(), Integer.class))")
    @Mapping(target = "tags", expression = "java(com.alibaba.fastjson.JSON.parseArray(jdhSku.getTags(), String.class))")
    @Mapping(target = "serviceNotice", expression = "java(com.alibaba.fastjson.JSON.parseArray(jdhSku.getServiceNotice(), com.jdh.o2oservice.export.product.dto.JdhSkuServiceNoticeDto.class))")
    @Mapping(target = "minAge", expression = "java(com.jdh.o2oservice.base.util.AgeUtil.getArrayIndex(jdhSku.getAgeRange(), 0))")
    @Mapping(target = "maxAge", expression = "java(com.jdh.o2oservice.base.util.AgeUtil.getArrayIndex(jdhSku.getAgeRange(), 1))")
    @Mapping(target = "ageRange", expression = "java(com.alibaba.fastjson.JSON.parseArray(jdhSku.getAgeRange(), Integer.class))")
    @Mapping(target = "genderLimit", expression = "java(com.alibaba.fastjson.JSON.parseArray(jdhSku.getGenderLimit(), Integer.class))")
    @Mapping(target = "customerConfirmType", expression = "java(com.alibaba.fastjson.JSON.parseArray(jdhSku.getCustomerConfirmType(), Integer.class))")
    @Mapping(target = "serviceRecordType", expression = "java(com.alibaba.fastjson.JSON.parseArray(jdhSku.getServiceRecordType(), Integer.class))")
    @Mapping(target = "serviceProcessImgId", expression = "java(jdhSku.getServiceProcessImg())")
    @Mapping(target = "tutorialCarousel", expression = "java(org.apache.commons.lang3.StringUtils.isNotBlank(jdhSku.getTutorialCarousel()) ? com.alibaba.fastjson.JSON.parseArray(jdhSku.getTutorialCarousel(), Long.class) : null)")
    JdhSkuDto convertJdhSkuToJdhSkuDto(JdhSku jdhSku);
    
    /**
     * 商品中台skuBo转京东健康商品对象
     *
     * @param createJdhSkuCmd createJdhSkuCmd
     * @return dto
     */
    @Mapping(target = "serviceResourceType", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(createJdhSkuCmd.getServiceResourceType()) ? com.alibaba.fastjson.JSON.toJSONString(createJdhSkuCmd.getServiceResourceType()) : null)")
    @Mapping(target = "dayTimeFrame", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(createJdhSkuCmd.getDayTimeFrame()) ? com.alibaba.fastjson.JSON.toJSONString(createJdhSkuCmd.getDayTimeFrame()) : null)")
    @Mapping(target = "stationAssignType", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(createJdhSkuCmd.getStationAssignType()) ? com.alibaba.fastjson.JSON.toJSONString(createJdhSkuCmd.getStationAssignType()) : null)")
    @Mapping(target = "tags", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(createJdhSkuCmd.getTags()) ? com.alibaba.fastjson.JSON.toJSONString(createJdhSkuCmd.getTags().stream().map(String::trim).distinct().collect(java.util.stream.Collectors.toList())) : null)")
    @Mapping(target = "serviceNotice", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(createJdhSkuCmd.getServiceNotice()) ? com.alibaba.fastjson.JSON.toJSONString(createJdhSkuCmd.getServiceNotice()) : null)")
    @Mapping(target = "customerConfirmType", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(createJdhSkuCmd.getCustomerConfirmType()) ? com.alibaba.fastjson.JSON.toJSONString(createJdhSkuCmd.getCustomerConfirmType()) : null)")
    @Mapping(target = "serviceRecordType", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(createJdhSkuCmd.getServiceRecordType()) ? com.alibaba.fastjson.JSON.toJSONString(createJdhSkuCmd.getServiceRecordType()) : null)")
    @Mapping(target = "ageRange", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(createJdhSkuCmd.getAgeRange()) ? com.jdh.o2oservice.base.util.AgeUtil.parseToJson(createJdhSkuCmd.getAgeRange()) : null)")
    @Mapping(target = "genderLimit", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(createJdhSkuCmd.getGenderLimit()) ? com.alibaba.fastjson.JSON.toJSONString(createJdhSkuCmd.getGenderLimit()) : null)")
    @Mapping(target = "serviceProcessImg", expression = "java(createJdhSkuCmd.getServiceProcessImgId())")
    @Mapping(target = "tutorialCarousel", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(createJdhSkuCmd.getTutorialCarousel()) ? com.alibaba.fastjson.JSON.toJSONString(createJdhSkuCmd.getTutorialCarousel()) : null)")
    JdhSku cmdToJdhSku(CreateJdhSkuCmd createJdhSkuCmd);
    

    @Mapping(target = "serviceResourceType", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(updateJdhSkuCmd.getServiceResourceType()) ? com.alibaba.fastjson.JSON.toJSONString(updateJdhSkuCmd.getServiceResourceType()) : null)")
    @Mapping(target = "dayTimeFrame", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(updateJdhSkuCmd.getDayTimeFrame()) ? com.alibaba.fastjson.JSON.toJSONString(updateJdhSkuCmd.getDayTimeFrame()) : null)")
    @Mapping(target = "stationAssignType", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(updateJdhSkuCmd.getStationAssignType()) ? com.alibaba.fastjson.JSON.toJSONString(updateJdhSkuCmd.getStationAssignType()) : null)")
    @Mapping(target = "tags", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(updateJdhSkuCmd.getTags()) ? com.alibaba.fastjson.JSON.toJSONString(updateJdhSkuCmd.getTags().stream().map(String::trim).distinct().collect(java.util.stream.Collectors.toList())) : null)")
    @Mapping(target = "serviceNotice", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(updateJdhSkuCmd.getServiceNotice()) ? com.alibaba.fastjson.JSON.toJSONString(updateJdhSkuCmd.getServiceNotice()) : null)")
    @Mapping(target = "customerConfirmType", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(updateJdhSkuCmd.getCustomerConfirmType()) ? com.alibaba.fastjson.JSON.toJSONString(updateJdhSkuCmd.getCustomerConfirmType()) : null)")
    @Mapping(target = "serviceRecordType", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(updateJdhSkuCmd.getServiceRecordType()) ? com.alibaba.fastjson.JSON.toJSONString(updateJdhSkuCmd.getServiceRecordType()) : null)")
    @Mapping(target = "ageRange", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(updateJdhSkuCmd.getAgeRange()) ? com.jdh.o2oservice.base.util.AgeUtil.parseToJson(updateJdhSkuCmd.getAgeRange()) : null)")
    @Mapping(target = "genderLimit", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(updateJdhSkuCmd.getGenderLimit()) ? com.alibaba.fastjson.JSON.toJSONString(updateJdhSkuCmd.getGenderLimit()) : null)")
    @Mapping(target = "serviceProcessImg", expression = "java(updateJdhSkuCmd.getServiceProcessImgId())")
    @Mapping(target = "tutorialCarousel", expression = "java(cn.hutool.core.collection.CollUtil.isNotEmpty(updateJdhSkuCmd.getTutorialCarousel()) ? com.alibaba.fastjson.JSON.toJSONString(updateJdhSkuCmd.getTutorialCarousel()) : null)")
    JdhSku cmdToJdhSku(UpdateJdhSkuCmd updateJdhSkuCmd);
    
    /**
     * 商品中台skuBo转京东健康商品对象
     *
     * @param updateJdhSkuCmd updateJdhSkuCmd
     * @return dto
     */
    JdhSku cmdToJdhSku(UpdateJdhSkuSaleStatusCmd updateJdhSkuCmd);
    JdhSkuDto dtoToDto(JdhSkuDto jdhSkuDto);


    SkuInfoAndStyleDTO skuInfoAndStyleBO2DTO(SkuInfoAndStyleBO skuInfoAndStyleBO);


    ProductLimitBuyDTO productLimitBuy2DTO(ProductLimitBuyBO productLimitStrategy);


    ProductDefaultSkuDTO productDefaultSkuBO2DTO(ProductDefaultSkuBO productDefaultSkuBO);

    List<ProductDefaultSkuDTO> productDefaultSkuBO2DTOList(List<ProductDefaultSkuBO> productDefaultSkuDTOS);

    List<ProductCarouselFileDTO> productCarouselFileBO2DTOList(List<ProductCarouselFileBO> productCarouselFileBoList);

    List<ProductDetailCustomIconConfigDTO> productDetailCustomIconConfigBO2DTO(List<ProductDetailCustomIconConfigBO> customConfigButtons);

    ProductDetailBottomBannerDTO productDetailBottomBannerBO2DTO(ProductDetailBottomBannerBO productDetailBottomBannerDTO);

    @Mapping(source = "userAddress.addressId",target = "userAddress.id")
    ProductInfoDTO ProductInfoBO2DTO(ProductInfoBO productInfo);

    /**
     * 对象转换
     *
     * @param request request
     * @return dto
     */
    JdhSku pageRequestToJdhSku(JdhSkuPageRequest request);

    /**
     * 对象转换
     *
     * @param jdhSkuRel jdhSkuRel
     * @return dto
     */
    @Mapping(target = "skuShortDesc", expression = "java(jdhSkuRel.getSkuShortDesc())")
    @Mapping(target = "skuFullDesc", expression = "java(jdhSkuRel.getSkuFullDesc())")
    @Mapping(target = "skuTagDesc", expression = "java(jdhSkuRel.getSkuTagDesc())")
    JdhSkuRelDto modelToDto(JdhSkuRel jdhSkuRel);

    /**
     * 对象转换
     *
     * @param jdhSkuRel jdhSkuRel
     * @return dto
     */
    List<JdhSkuRelDto> modelToDtoList(List<JdhSkuRel> jdhSkuRel);

    Map<Long, List<JdhSkuRelDto>> modelToDtoMap(Map<Long, List<JdhSkuRel>> jdhSkuRelMap);

    @Mapping(source = "addressId", target = "id")
    List<UserAddressDetailDTO> convertAddressDetailBO2DTO(List<AddressDetailBO> list);

    /**
     * 对象转换
     *
     * @param model model
     * @return dto
     */
    JdhSkuItemRelDto modelToDto(JdhSkuItemRel model);

    /**
     * 对象转换
     *
     * @param model model
     * @return dto
     */
    List<JdhSkuItemRelDto> modelToDto(List<JdhSkuItemRel> model);

    /**
     * 对象转换
     *
     * @param request request
     * @return dto
     */
    JdhSkuItemRel requestToModel(JdhSkuServiceItemRelPageRequest request);
    /**
     * 对象转换
     *
     * @param model model
     * @return dto
     */
    EvaluateInfoDTO modelToDto(EvaluateInfoBO model);
    /**
     * 对象转换
     *
     * @param skuQuesttion4Result
     * @return dto
     */
    QueryQuestionDTO modelToDto(SkuQuesttion4Result skuQuesttion4Result);

    ProducBaseEventBody convertProducBaseEventBody(JdhSku sku);

    DiscountFeeDisplayDto convertDiscountFeeDisplayDto(DiscountFeeDisplay display);
}