package com.jdh.o2oservice.application.support.service;

import com.jdh.o2oservice.export.support.command.DistributeFeedbackCmd;
import com.jdh.o2oservice.export.user.cmd.SubmitUserFeedbackCmd;
import com.jdh.o2oservice.export.user.dto.UserFeedbackAggregationDTO;
import com.jdh.o2oservice.export.user.query.UserFeedbackRequest;

/**
 * @ClassName UserFeedbackApplication
 * @Description
 * <AUTHOR>
 * @Date 2025/1/17 13:47
 **/
public interface UserFeedbackApplication {

    /**
     * 分配反馈问题（履约单维度）
     * @param cmd
     */
    void distributeFeedback(DistributeFeedbackCmd cmd);

    /**
     * 查询用户反馈数据
     * @param request
     * @return
     */
    UserFeedbackAggregationDTO queryUserFeedback(UserFeedbackRequest request);

    /**
     * 提交用户反馈数据
     * @param cmd
     * @return
     */
    UserFeedbackAggregationDTO submitUserFeedback(SubmitUserFeedbackCmd cmd);
}