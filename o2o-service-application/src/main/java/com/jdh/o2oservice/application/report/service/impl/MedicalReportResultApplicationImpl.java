package com.jdh.o2oservice.application.report.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.esotericsoftware.minlog.Log;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.medicalpromise.convert.MedicalPromiseConvert;
import com.jdh.o2oservice.application.product.service.ProductServiceIndicatorApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.report.convert.MedicalReportIndicatorConvert;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportResultApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.product.model.ServiceItem;
import com.jdh.o2oservice.core.domain.product.model.ServiceItemIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceIndicatorRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemIndicatorRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.report.bo.MedReportIndicatorQueryBO;
import com.jdh.o2oservice.core.domain.report.bo.MedicalReportPageBO;
import com.jdh.o2oservice.core.domain.report.bo.MedicalReportQueryBO;
import com.jdh.o2oservice.core.domain.report.bo.SyncReportToCenterResBo;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicator;
import com.jdh.o2oservice.core.domain.report.model.ReportCenterReport;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportIndicatorRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.core.domain.report.service.ReportCenterDomainService;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseReportCmd;
import com.jdh.o2oservice.export.product.dto.ServiceIndicatorDto;
import com.jdh.o2oservice.export.product.query.JdhIndicatorExactQuery;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.report.cmd.*;
import com.jdh.o2oservice.export.report.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * MedicalReportResultApplication
 * <AUTHOR>
 * @date 2024-11-13 13:21
 */
@Service
@Slf4j
public class MedicalReportResultApplicationImpl implements MedicalReportResultApplication {


    /**
     * 医疗报告指标仓库，用于管理和操作报告指标数据。
     */
    @Autowired
    private MedicalReportIndicatorRepository medicalReportIndicatorRepository;

    /**
     * Ducc配置类，用于获取和设置Ducc相关的配置信息。
     */
    @Autowired
    private DuccConfig duccConfig;

    /**
     * 产品服务指标应用程序接口，用于查询和操作指标数据。
     */
    @Autowired
    private ProductServiceIndicatorApplication productServiceIndicatorApplication;

    /**
     * 执行器池工厂，用于创建和管理线程池。
     */
    @Autowired
    private ExecutorPoolFactory executorPoolFactory;
    
    /**
     * 医疗承诺仓库，用于管理和操作医疗承诺数据。
     */
    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * 医疗报告应用程序接口，用于查询和操作医疗报告数据。
     */
    @Autowired
    private MedicalReportApplication medicalReportApplication;


    /**
     * 提供履约相关服务的接口，用于查询和操作履约相关数据。
     */
    @Resource
    private PromiseRepository jdhPromiseRepository;
    
    /**
     * 地址Rpc服务，用于获取和解析用户地址信息。
     */
    @Autowired
    private AddressRpc addressRpc;
    
    /**
     * 文件管理服务，用于下载和管理文件。
     */
    @Autowired
    private FileManageService fileManageService;

    /**
     * 医疗报告仓库，用于管理和操作医疗报告数据。
     */
    @Autowired
    private MedicalReportRepository medicalReportRepository;

    /**
     * JDH服务项目指标仓库，用于管理和操作服务项目指标数据。
     */
    @Autowired
    private JdhServiceItemIndicatorRepository jdhServiceItemIndicatorRepository;


    /**
     * JDH服务项目指标仓库，用于管理和操作服务项目指标数据。
     */
    @Autowired
    private JdhServiceIndicatorRepository jdhServiceIndicatorRepository;

    /**
     * JDH服务项目仓库，用于管理和操作服务项目指标数据。
     */
    @Autowired
    private JdhServiceItemRepository jdhServiceItemRepository;

    /**
     * 医疗承诺应用程序接口，用于查询和操作医疗承诺数据。
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    @Autowired
    private PromiseApplication promiseApplication;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * 报告中心领域服务，提供与报告中心相关的业务逻辑操作。
     */
    @Autowired
    private ReportCenterDomainService reportCenterDomainService;

    /**
     * reachStoreProducer
     */
    @JmqProducer(name = "pdfToJpgProvider")
    private MessageProducer pdfToJpgProvider;

    @Value("${topics.report.deal}")
    private String pdfToJpgTopic;



    @Override
    public Boolean analyseReportIndicators(Long medicalPromiseId) {
        log.info("MedicalReportResultApplicationImpl->analyseReportIndicators,medicalPromiseId:{}", JSONUtil.toJsonStr(medicalPromiseId));

        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(
                MedicalPromiseRepQuery.builder()
                        .medicalPromiseId(medicalPromiseId)
                        .build()
        );
        log.info("MedicalReportResultApplicationImpl->analyseReportIndicators,medicalPromiseDTO:{}", JSONUtil.toJsonStr(medicalPromise));
        if (Objects.isNull(medicalPromise)){
            return Boolean.TRUE;
        }
        packReportIndicator(medicalPromise);
        return Boolean.TRUE;
    }

    /**
     * 保存报告指标。
     * @param reportIndicatorCmds 报告指标命令列表。
     * @return 保存结果。
     */
    @Override
    public Boolean saveReportIndicators(List<ReportIndicatorCmd> reportIndicatorCmds) {

        List<MedicalReportIndicator> medicalReportIndicators = MedicalReportIndicatorConvert.INSTANCE.convertList(reportIndicatorCmds);

        Boolean res = medicalReportIndicatorRepository.saveMedicalReportIndicators(medicalReportIndicators);

        return res;
    }

    /**
     * 刷新医疗报告指标ID。
     * @param medicalReportIndicatorFlushCmd 包含要刷新的医疗报告指标ID的命令对象。
     * @return 是否成功刷新指标ID。
     */
    @Override
    public Boolean flushReportIndicatorId(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd) {
        Integer startPage = medicalReportIndicatorFlushCmd.getStartPage();
        Integer pageSize = medicalReportIndicatorFlushCmd.getPageSize();
        for (int i = startPage; i <= medicalReportIndicatorFlushCmd.getEndPage(); i++) {


            PageDto<MedicalReportIndicator> pageDto = medicalReportIndicatorRepository.pageMedicalReportIndicators(i, pageSize);
            log.info("MedicalReportResultApplicationImpl->pageDto={}", JSONUtil.toJsonStr(pageDto));

            if (pageDto == null || CollectionUtils.isEmpty(pageDto.getList())) {
                break;
            }

            List<MedicalReportIndicator> list = pageDto.getList();

            Map<String, String> indicatorNameMap = duccConfig.getIndicatorNameMap();
            Set<String> formatName = Sets.newHashSet();

            for (MedicalReportIndicator medicalReportIndicator : list) {
                if (indicatorNameMap.containsKey(medicalReportIndicator.getIndicatorName())) {
                    formatName.add(indicatorNameMap.get(medicalReportIndicator.getIndicatorName()));
                }
            }
            log.info("MedicalReportResultApplicationImpl->formatName={}", JSONUtil.toJsonStr(formatName));

            if (CollectionUtils.isEmpty(formatName)) {
                continue;
            }

            List<ServiceIndicatorDto> serviceIndicatorDtos = productServiceIndicatorApplication.queryIndicatorExactList(JdhIndicatorExactQuery.builder().firstIndicatorCategory(medicalReportIndicatorFlushCmd.getFirstIndicatorCategory()).secondIndicatorCategory(medicalReportIndicatorFlushCmd.getSecondIndicatorCategory()).indicatorName(formatName).indicatorType(CommonConstant.TWO).build());
            log.info("MedicalReportResultApplicationImpl->serviceIndicatorDtos={}", JSONUtil.toJsonStr(serviceIndicatorDtos));
            if (CollectionUtils.isEmpty(serviceIndicatorDtos)) {
                continue;
            }


            Map<String, String> nameToId = serviceIndicatorDtos.stream().collect(Collectors.toMap(ServiceIndicatorDto::getIndicatorName, ServiceIndicatorDto::getIndicatorId));

            List<MedicalReportIndicator> medicalReportIndicators = Lists.newArrayList();

            for (MedicalReportIndicator medicalReportIndicator : list) {
                if (indicatorNameMap.containsKey(medicalReportIndicator.getIndicatorName())) {
                    String name = indicatorNameMap.get(medicalReportIndicator.getIndicatorName());
                    if (nameToId.containsKey(name)) {
                        medicalReportIndicator.setIndicatorId(Long.valueOf(nameToId.get(name)));
                        medicalReportIndicators.add(medicalReportIndicator);
                    }
                }
            }
            Log.info("MedicalReportResultApplicationImpl->medicalReportIndicators={}", JSONUtil.toJsonStr(medicalReportIndicators));

            medicalReportIndicatorRepository.updateMedicalReportIndicators(medicalReportIndicators);

        }

        return Boolean.TRUE;

    }

    /**
     * 刷新医疗报告指标。
     * @param medicalReportIndicatorFlushCmd 包含刷新指标的命令对象。
     * @return 刷新结果，true 表示成功，false 表示失败。
     */
    @Override
    public Boolean flushReportIndicator(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd) {

        Integer startPage = medicalReportIndicatorFlushCmd.getStartPage();
        Integer pageSize = medicalReportIndicatorFlushCmd.getPageSize();
        for (int i = startPage; i <= medicalReportIndicatorFlushCmd.getEndPage(); i++) {
            log.info("MedicalReportResultApplicationImpl->page={},time={}", startPage, System.currentTimeMillis());
            MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
            medicalPromiseListQuery.setPageNum(i);
            medicalPromiseListQuery.setPageSize(pageSize);
            medicalPromiseListQuery.setMedicalPromiseId(medicalReportIndicatorFlushCmd.getMedicalPromiseId());
            medicalPromiseListQuery.setReportStatus(CommonConstant.ONE);
            medicalPromiseListQuery.setOrderBy("asc");
            PageDto<MedicalPromise> medicalPromisePageDto = medicalPromiseRepository.queryMedicalPromisePage(medicalPromiseListQuery);
            if (medicalPromisePageDto == null || CollectionUtils.isEmpty(medicalPromisePageDto.getList())) {
                break;
            }
            executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(() -> {
                for (MedicalPromise medicalPromise : medicalPromisePageDto.getList()) {
                    log.info("MedicalReportResultApplicationImpl->flushReportIndicator,medicalPromiseId={},id={}", medicalPromise.getMedicalPromiseId().toString(), medicalPromise.getId());
                    try {
                        packReportIndicator(medicalPromise);
                    } catch (Exception e) {
                        log.info("MedicalReportResultApplicationImpl->packReportIndicator,error,id={}", medicalPromise.getId(), e);
                    }
                }
            });
            log.info("MedicalReportResultApplicationImpl->end,time={}", System.currentTimeMillis());
        }

        return Boolean.TRUE;
    }

    /**
     * 刷新医疗报告指标的检查时间。
     *
     * @param medicalReportIndicatorFlushCmd 包含要刷新的医疗报告指标信息的命令对象。
     * @return 刷新操作是否成功。
     */
    @Override
    public Boolean flushCheckTime(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd) {
        Integer startPage = medicalReportIndicatorFlushCmd.getStartPage();
        Integer pageSize = medicalReportIndicatorFlushCmd.getPageSize();
        for (int i = startPage; i <= medicalReportIndicatorFlushCmd.getEndPage(); i++) {


            PageDto<MedicalReportIndicator> pageDto = medicalReportIndicatorRepository.pageReportIndicatorsNoCheckTime(i, pageSize);
            log.info("MedicalReportResultApplicationImpl->flushCheckTime,pageDto={}", JSONUtil.toJsonStr(pageDto));

            if (pageDto == null || CollectionUtils.isEmpty(pageDto.getList())) {
                break;
            }

            List<MedicalReportIndicator> list = pageDto.getList();

            Set<Long> reportId = list.stream().map(p->Long.valueOf(p.getReportId())).collect(Collectors.toSet());

            List<MedicalReport> medicalReports = medicalReportRepository.selectByCondition(MedicalReportQueryBO.builder().ids(reportId).build());

            if (CollectionUtils.isEmpty(medicalReports)) {
                continue;
            }


            Map<Long, Date> idToCreateTime = medicalReports.stream().collect(Collectors.toMap(MedicalReport::getId, MedicalReport::getCreateTime));

            List<MedicalReportIndicator> medicalReportIndicators = Lists.newArrayList();

            for (MedicalReportIndicator medicalReportIndicator : list) {
                if (idToCreateTime.containsKey(Long.valueOf(medicalReportIndicator.getReportId()))){
                    medicalReportIndicator.setCheckTime(medicalReportIndicator.getCreateTime());
                    medicalReportIndicators.add(medicalReportIndicator);
                }
            }

            medicalReportIndicatorRepository.updateMedicalReportIndicators(medicalReportIndicators);

        }
        return Boolean.TRUE;
    }

/**
 * 计算并返回医保承诺履行率DTO对象，包括阳性率信息。
 *
 * @param medPromisePosRateCmd 查询条件对象，包含服务项ID等信息。
 * @return 医保承诺履行率DTO对象，包括阳性率信息。
 */
@Override
public MedPromisePositiveRateDTO queryMedPromisePositiveRate(MedPromisePosRateCmd medPromisePosRateCmd) {
    ServiceItem serviceItem = jdhServiceItemRepository.find(ServiceItemIdentifier.builder().itemId(medPromisePosRateCmd.getServiceItemId()).build());

    if (Objects.isNull(serviceItem)){
        return null;
    }

    // 创建并初始化MedPromisePositiveRateDTO对象
    MedPromisePositiveRateDTO medPromisePositiveRateDTO = new MedPromisePositiveRateDTO();
    medPromisePositiveRateDTO.setServiceItemId(medPromisePosRateCmd.getServiceItemId());
    medPromisePositiveRateDTO.setAppointmentNum(CommonConstant.ZERO);
    medPromisePositiveRateDTO.setServiceItemName(serviceItem.getItemName());

//    // 查询服务项与指标的关联关系
//    List<ServiceItemIndicatorRel> serviceItemIndicatorRels = jdhServiceItemIndicatorRepository.queryItemIndicatorList(
//            ServiceItemQueryContext.builder().itemId(medPromisePosRateCmd.getServiceItemId()).build());
//    log.info("MedicalReportApplicationImpl.queryMedPromisePosPosRate -> serviceItemIndicatorRels={}",
//            JSON.toJSONString(serviceItemIndicatorRels));
//
//    // 如果没有关联关系，直接返回null
//    if (CollectionUtils.isEmpty(serviceItemIndicatorRels)) {
//        return null;
//    }
//
//    // 提取指标ID集合
//    Set<Long> indicatorIds = serviceItemIndicatorRels.stream().map(ServiceItemIndicatorRel::getIndicatorId).collect(Collectors.toSet());
//
//    // 根据指标ID查询指标信息
//    List<Indicator> indicators = jdhServiceIndicatorRepository.queryIndicatorList(
//            ServiceIndicatorQueryContext.builder().indicatorIds(indicatorIds).build());
//    log.info("MedicalReportApplicationImpl->queryMedPromisePosPosRate->indicators={}",
//            JSON.toJSONString(indicators));
//
//    // 如果没有指标信息，直接返回null
//    if (CollectionUtils.isEmpty(indicators)) {
//        return null;
//    }

    // 创建并初始化MedPromiseIndicatorPosRateDTO对象列表
    List<MedPromiseIndicatorPosRateDTO> detailList = Lists.newArrayList();
//    for (Indicator indicator : indicators) {
//        MedPromiseIndicatorPosRateDTO indicatorPosRateDTO = new MedPromiseIndicatorPosRateDTO();
//        indicatorPosRateDTO.setIndicatorId(indicator.getIndicatorId());
//        indicatorPosRateDTO.setIndicatorName(indicator.getIndicatorName());
//        indicatorPosRateDTO.setPositiveRate(CommonConstant.ZERO_STR);
//        detailList.add(indicatorPosRateDTO);
//    }
    medPromisePositiveRateDTO.setDetailList(detailList);

    // 将查询条件对象转换为MedReportIndicatorQueryBO对象
    MedReportIndicatorQueryBO medReportIndicatorQueryBO = MedicalReportIndicatorConvert.INSTANCE.convert(medPromisePosRateCmd);

    // 根据MedReportIndicatorQueryBO对象查询医疗报告指标信息
    List<MedicalReportIndicator> medicalReportIndicators = medicalReportIndicatorRepository.listMedicalReportIndicators(medReportIndicatorQueryBO);
    log.info("MedicalReportApplicationImpl.queryMedPromisePosPosRateBefore -> medicalReportIndicators={}",
            JSON.toJSONString(medicalReportIndicators));

    // 如果没有医疗报告指标信息，直接返回MedPromisePositiveRateDTO对象
    if (CollectionUtils.isEmpty(medicalReportIndicators)) {
        return medPromisePositiveRateDTO;
    }

    //获取统计的报告份数
    int size = medicalReportIndicators.stream().collect(Collectors.groupingBy(MedicalReportIndicator::getReportId)).size();
    medPromisePositiveRateDTO.setAppointmentNum(size);

    Map<String, List<MedicalReportIndicator>> nameToList = medicalReportIndicators.stream().collect(Collectors.groupingBy(MedicalReportIndicator::getIndicatorName));

    nameToList.forEach((name, list) -> {
        MedPromiseIndicatorPosRateDTO medPromiseIndicatorPosRateDTO = new MedPromiseIndicatorPosRateDTO();
        medPromiseIndicatorPosRateDTO.setIndicatorName(name);
        medPromiseIndicatorPosRateDTO.setIndicatorNum(list.size());

        List<MedicalReportIndicator> normalIndicator = list.stream().filter(p -> Objects.equals(CommonConstant.ZERO, p.getAbnormalMarkType())).collect(Collectors.toList());
        int normalNum = org.apache.commons.collections4.CollectionUtils.isNotEmpty(normalIndicator)? normalIndicator.size() : CommonConstant.ZERO;
        String plainString = new BigDecimal(list.size() - normalNum).divide(new BigDecimal(list.size()), 2, RoundingMode.HALF_UP).toPlainString();
        medPromiseIndicatorPosRateDTO.setPositiveRate(plainString);

        detailList.add(medPromiseIndicatorPosRateDTO);

    });



//    // 过滤掉指标ID为空的医疗报告指标信息
//    medicalReportIndicators.removeIf(p -> Objects.isNull(p.getIndicatorId()));
//    log.info("MedicalReportApplicationImpl.queryMedPromisePosPosRateAfter -> medicalReportIndicators={}",
//            JSON.toJSONString(medicalReportIndicators));
//
//    // 如果过滤后没有医疗报告指标信息，直接返回MedPromisePositiveRateDTO对象
//    if (CollectionUtils.isEmpty(medicalReportIndicators)) {
//        return medPromisePositiveRateDTO;
//    }



//    // 按照指标ID分组医疗报告指标信息
//    Map<Long, List<MedicalReportIndicator>> indicatorToList = medicalReportIndicators.stream().collect(Collectors.groupingBy(MedicalReportIndicator::getIndicatorId));
//
//    // 计算每个指标的阳性率，并存储在Map中
//    Map<Long, String> indicatorIdToRate = Maps.newHashMap();
//    indicatorToList.forEach((indicator, list) -> {
//        List<MedicalReportIndicator> normalIndicator = list.stream().filter(p -> Objects.equals(CommonConstant.ZERO, p.getAbnormalMarkType())).collect(Collectors.toList());
//        int normalNum = org.apache.commons.collections4.CollectionUtils.isNotEmpty(normalIndicator)? normalIndicator.size() : CommonConstant.ZERO;
//        String plainString = new BigDecimal(list.size() - normalNum).divide(new BigDecimal(list.size()), 2, RoundingMode.HALF_UP).toPlainString();
//        indicatorIdToRate.put(indicator, plainString);
//    });
//    log.info("MedicalReportApplicationImpl.indicatorIdToRate={}", JSON.toJSONString(indicatorIdToRate));
//
//
//    // 将计算得到的阳性率信息更新到MedPromiseIndicatorPosRateDTO对象列表中
//    for (MedPromiseIndicatorPosRateDTO medPromiseIndicatorPosRateDTO : detailList) {
//        if (indicatorIdToRate.containsKey(medPromiseIndicatorPosRateDTO.getIndicatorId())) {
//            medPromiseIndicatorPosRateDTO.setPositiveRate(indicatorIdToRate.get(medPromiseIndicatorPosRateDTO.getIndicatorId()));
//        }
//    }

    // 返回包含阳性率信息的MedPromisePositiveRateDTO对象
    return medPromisePositiveRateDTO;
}

    /**
     * @param fixStructReportCmd
     * @return
     */
    @Override
    public Boolean fixStructReport(FixStructReportCmd fixStructReportCmd) {

        Map<String, String> indicatorNameToValueMap = duccConfig.getIndicatorNameToValueMap();

        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder()
                .medicalPromiseIds(fixStructReportCmd.getMedicalPromiseIds())
                .stationId(fixStructReportCmd.getStationId())
                .reportStartTime(fixStructReportCmd.getStartTime())
                .reportEndTime(fixStructReportCmd.getEndTime())
                .reportStatus(CommonConstant.ONE)
                .status(MedicalPromiseStatusEnum.COMPLETED.getStatus())
                .build()
        );
        log.info("MedicalReportResultApplicationImpl->medicalPromises={}",JsonUtil.toJSONString(medicalPromises));

        if (CollectionUtils.isEmpty(medicalPromises)) {
            return Boolean.TRUE;
        }

        Map<Long, MedicalPromise> medicalPromiseMap = medicalPromises.stream().collect(Collectors.toMap(MedicalPromise::getMedicalPromiseId, p -> p));
        List<Long> medicalPromiseIds = medicalPromises.stream().map(MedicalPromise::getMedicalPromiseId).collect(Collectors.toList());

        List<MedicalReport> medicalReports = medicalReportRepository.getByMedicalPromiseIdList(medicalPromiseIds);

        log.info("MedicalReportResultApplicationImpl->medicalReports={}",JsonUtil.toJSONString(medicalReports));

        for (MedicalReport medicalReport : medicalReports) {

            InputStream inputStream = fileManageService.get(medicalReport.getSourceOss());
            String structReportJson = new BufferedReader(new InputStreamReader(inputStream)).lines().collect(Collectors.joining("\n"));
            if (StringUtils.isBlank(structReportJson)) {
                continue;
            }
            StructQuickReportContentDTO reportContentDTO = com.jd.fastjson.JSON.parseObject(structReportJson, StructQuickReportContentDTO.class);
            log.info("MedicalReportResultApplicationImpl->reportContentDTO={}",JsonUtil.toJSONString(reportContentDTO));

            Boolean change = Boolean.FALSE;
            List<StructQuickReportResultDTO> reportResult = reportContentDTO.getReportResult();

            for (StructQuickReportResultDTO structQuickReportResultDTO : reportResult) {
                if (CollectionUtil.isNotEmpty(structQuickReportResultDTO.getIndicators())){
                    for (StructQuickReportResultIndicatorDTO structQuickReportResultIndicatorDTO : structQuickReportResultDTO.getIndicators()){
                        if (structQuickReportResultIndicatorDTO.getNormalRangeValue().contains("<")){
                            if (indicatorNameToValueMap.containsKey(structQuickReportResultIndicatorDTO.getIndicatorName())){
                                change = Boolean.TRUE;
                                structQuickReportResultIndicatorDTO.setNormalRangeValue(indicatorNameToValueMap.get(structQuickReportResultIndicatorDTO.getIndicatorName()));
                            }
                        }
                    }
                }
            }

            if (change){
                String jsonString = JsonUtil.toJSONString(reportContentDTO);
                log.info("MedicalReportResultApplicationImpl->jsonString={}",jsonString);
                MedicalPromiseReportCmd medicalPromiseReportCmd = new MedicalPromiseReportCmd();
                medicalPromiseReportCmd.setJdStructReportStr(jsonString);
                medicalPromiseReportCmd.setStructReportStr(jsonString);
                MedicalPromise medicalPromise = medicalPromiseMap.get(medicalReport.getMedicalPromiseId());

                medicalPromiseReportCmd.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
                medicalPromiseReportCmd.setChannelType(medicalPromise.getProviderId());
                medicalPromiseReportCmd.setMedicalType(CommonConstant.ONE);
                medicalPromiseReportCmd.setReportType(CommonConstant.ONE);
                medicalPromiseReportCmd.setReportStatus(CommonConstant.ONE);
                medicalPromiseApplication.pushMedicalPromiseReport(medicalPromiseReportCmd);
            }


        }


        return null;
    }

    /**
     * 模拟完成患者 SKU 订单
     *
     * @param promisePatientSkuFinishCmd 包含完成患者 SKU 订单所需信息的命令对象
     * @return 是否成功模拟完成患者 SKU 订单
     */
    @Override
    public Boolean mockPromisePatientSkuFinish(PromisePatientSkuFinishCmd promisePatientSkuFinishCmd) {

        if (CollectionUtil.isNotEmpty(promisePatientSkuFinishCmd.getMedicalPromiseIds())){

            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().medicalPromiseIds(promisePatientSkuFinishCmd.getMedicalPromiseIds()).build());
            if (CollectionUtil.isEmpty(medicalPromises)){
                log.info("mockPromisePatientSkuFinish->medicalPromises,is null");
                return Boolean.TRUE;
            }

            List<String> dealList = Lists.newArrayList();

            Map<Long, List<MedicalPromise>> promiseToList = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromise::getPromiseId));

            promiseToList.forEach((promiseId,list)->{

                Map<Long, List<MedicalPromise>> ppidToList = list.stream().collect(Collectors.groupingBy(MedicalPromise::getPromisePatientId));

                ppidToList.forEach((ppid,mps)->{

                    Map<String, List<MedicalPromise>> ppidSkuToList = mps.stream().collect(Collectors.groupingBy(MedicalPromise::getServiceId));

                    ppidSkuToList.forEach((sku,list2)->{
                        list2.sort(Comparator.comparing(MedicalPromise::getMedicalPromiseId));
                        MedicalPromise first = list2.get(0);
                        MedicalPromiseEventBody eventBody = MedicalPromiseConvert.INSTANCE.medicalPromise2MedicalPromiseEventBody(first);
                        eventBody.setAppointmentId(first.getPromisePatientId()+"_"+ first.getServiceId());
                        eventBody.setSourceVoucherId(promiseApplication.findSourceVoucherIdByPromiseId(first.getPromiseId()));
                        eventCoordinator.publish(EventFactory.newDefaultEvent(first, MedPromiseEventTypeEnum.MED_PROMISE_SKU_PATIENT_ALL_GENERATE_REPORT,eventBody));
                        List<String> collect = list2.stream().map(p->String.valueOf(p.getMedicalPromiseId())).collect(Collectors.toList());
                        dealList.addAll(collect);
                        log.info("mockPromisePatientSkuFinish->success,promiseId={},promisePatientId={},serviceId={},mplist={}",promiseId,ppid,sku,String.join(",",collect));
                    });
                });


            });

            log.info("param={},dealList={}",JsonUtil.toJSONString(promisePatientSkuFinishCmd.getMedicalPromiseIds()),JsonUtil.toJSONString(dealList));

        }

        return Boolean.TRUE;
    }

    /**
     * 刷新报告中心ID。
     *
     * @param fixStructReportCmd 包含报告中心ID的FixStructReportCmd对象。
     * @return 如果操作成功，则返回true；否则返回false。
     */
    @Override
    public Boolean flushReportCenterId(FixStructReportCmd fixStructReportCmd) {
        List<List<Long>> partition = com.google.common.collect.Lists.partition(fixStructReportCmd.getMedicalPromiseIds(), 500);

        for (List<Long> partitionist : partition) {

            try {
                log.info("flushReportCenterId->partitionistSize={}",partitionist.size());
                List<MedicalReport> medicalReports = medicalReportRepository.getByMedicalPromiseIdList(partitionist);

                if (CollectionUtils.isEmpty(medicalReports)){
                    continue;
                }

                medicalReports.removeIf(p->StringUtils.isNotBlank(p.getReportCenterId()));

                if (CollectionUtils.isEmpty(medicalReports)){
                    continue;
                }

                log.info("flushReportCenterId->medicalReportsAfter.size={}",medicalReports.size());

                for (MedicalReport medicalReport : medicalReports) {
                    medicalReport.setReportCenterId(String.valueOf(medicalReport.getMedicalPromiseId()));
                    medicalReportRepository.updateByCondition(medicalReport);
                }

            }catch (Exception e){
                log.error("flushReportCenterId->error,partitionist first={}", partitionist.get(0).toString(),e);
            }

        }

        return Boolean.TRUE;
    }

    /**
     * 通过检测单同步报告中心的数据。
     *
     * @param fixStructReportCmd 报告中心数据的固定结构体命令。
     * @return 同步操作是否成功。
     */
    @Override
    public Boolean syncReportCenterByMp(FixStructReportCmd fixStructReportCmd) {


        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().medicalPromiseIds(fixStructReportCmd.getMedicalPromiseIds()).build());

        if (CollectionUtil.isEmpty(medicalPromises)) {
            log.info("syncReportCenterByMp->medicalPromises,is null");
            return Boolean.FALSE;
        }


        List<MedicalReport> medicalReports = medicalReportRepository.getByMedicalPromiseIdList(fixStructReportCmd.getMedicalPromiseIds());

        if (CollectionUtils.isEmpty(medicalReports)) {
            log.info("syncReportCenterByMp->medicalReports,is null");
            return Boolean.FALSE;
        }

        Map<Long, MedicalPromise> mpIdToMp = medicalPromises.stream().collect(Collectors.toMap(MedicalPromise::getMedicalPromiseId,p->p));
        Map<Long, MedicalReport> mpIdToReport = medicalReports.stream().collect(Collectors.toMap(MedicalReport::getMedicalPromiseId,p->p));


        for (Long medicalPromiseId : fixStructReportCmd.getMedicalPromiseIds()) {

            log.info("syncReportCenterByMp->medicalPromiseId={}",medicalPromiseId);

            MedicalPromise medicalPromise = mpIdToMp.get(medicalPromiseId);
            MedicalReport medicalReport = mpIdToReport.get(medicalPromiseId);

            if (Objects.isNull(medicalPromise) || Objects.isNull(medicalReport)) {
                continue;
            }

            ReportCenterReport reportCenterReport = new ReportCenterReport();

            //设置档案id
            PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
            promiseIdRequest.setPromiseId(medicalPromise.getPromiseId());
            PromiseDto promiseDto = promiseApplication.findPromiseByPromiseId(promiseIdRequest);


            reportCenterReport.setUserPin(medicalPromise.getUserPin());
            reportCenterReport.setPatientId(medicalReport.getPatientId());
            reportCenterReport.setReportSource(medicalReport.getReportSource());
            reportCenterReport.setReportType(medicalReport.getReportType());
            if (StringUtils.isBlank(medicalReport.getFileMd5())){
                //设置FileMd5
                ObjectMetadata objectMetadata = fileManageService.getObjectMetadata(medicalReport.getReportOss());
                if(log.isInfoEnabled()) {
                    log.info("syncReportCenterByMp,objectMetadata = {} ", JSON.toJSONString(objectMetadata));
                }
                medicalReport.setFileMd5(objectMetadata.getETag());
            }

            reportCenterReport.setFileMd5(medicalReport.getFileMd5());
            reportCenterReport.setReportOss(medicalReport.getReportOss());
            reportCenterReport.setStructReportOss(medicalReport.getStructReportOss());
            reportCenterReport.setPatientName(promiseDto.getPatients().get(0).getUserName().getName());
            reportCenterReport.setPromiseId(medicalReport.getPromiseId());
            reportCenterReport.setExaminationTime(DateUtil.formatDate(medicalPromise.getCheckTime(),CommonConstant.YMDHMS));
            reportCenterReport.setServiceItemName(medicalPromise.getServiceItemName());


            SyncReportToCenterResBo syncReportToCenterResBo = reportCenterDomainService.syncMedicalReportToReportCenter(reportCenterReport);

            log.info("syncReportCenterByMp,syncReportToCenterResBo = {} ", syncReportToCenterResBo);
            if (StringUtils.equals("0000",syncReportToCenterResBo.getCode()) || StringUtils.equals("400015",syncReportToCenterResBo.getCode())){
                if(StringUtils.isNotBlank(syncReportToCenterResBo.getReportCenterId())) {
                    medicalReport.setReportCenterId(syncReportToCenterResBo.getReportCenterId());
                    medicalReportRepository.update(medicalReport);
                }
            }

        }


        return Boolean.TRUE;
    }

    /**
     * 修复空白报告
     *
     * @param fixStructReportCmd 报告中心数据的固定结构体命令。
     * @return 同步操作是否成功。
     */
    @Override
    public Boolean fixEmptyJpg(FixStructReportCmd fixStructReportCmd) {

        MedicalReportPageBO medicalReportPageBO = new MedicalReportPageBO();
        medicalReportPageBO.setStartTime(fixStructReportCmd.getStartTime());
        medicalReportPageBO.setEndTime(fixStructReportCmd.getEndTime());
        medicalReportPageBO.setPageSize(fixStructReportCmd.getPageSize());
        medicalReportPageBO.setNeedReportJpg(Boolean.TRUE);
        for (int i = 1; i <= 1000; i++) {
            medicalReportPageBO.setPageNum(i);
            PageDto<MedicalReport> medicalReportPageDto = medicalReportRepository.pageMedicalReport(medicalReportPageBO);
            if (Objects.isNull(medicalReportPageDto) || CollectionUtils.isEmpty(medicalReportPageDto.getList())){
                break;
            }
            List<MedicalReport> list = medicalReportPageDto.getList();

            List<Long> medPromiseIds = list.stream().map(MedicalReport::getMedicalPromiseId).collect(Collectors.toList());

            log.info("fixEmptyJpg->medPromiseIds={}",JsonUtil.toJSONString(medPromiseIds));

            if (fixStructReportCmd.getNeedFlushJpg()){
                Integer contentLengthMin = duccConfig.getContentLengthMin();
                for (MedicalReport medicalReport : list) {
                    try {
                        ObjectMetadata objectMetadata = fileManageService.getObjectMetadata(medicalReport.getReportJpgOss());
                        long contentLength = objectMetadata.getContentLength();
                        if (contentLength <= contentLengthMin) {
                            log.info("fixEmptyJpg->medicalReport,needFlush,id={},contentLength={},oss={}", medicalReport.getId(), contentLength, medicalReport.getReportJpgOss());
                            MedicalReportSaveCmd medicalReportSaveCmd = new MedicalReportSaveCmd();
                            medicalReportSaveCmd.setMedicalPromiseId(medicalReport.getMedicalPromiseId());
                            medicalReportSaveCmd.setReportOss(medicalReport.getReportOss());
                            medicalReportSaveCmd.setUserPin(medicalReport.getUserPin());
                            Message message = new Message(pdfToJpgTopic, JsonUtil.toJSONString(medicalReportSaveCmd));
                            pdfToJpgProvider.send(message);

                        }
                    }catch (Exception e){
                        log.error("fixEmptyJpg->medicalReport,error,id={},oss={}", medicalReport.getId(), medicalReport.getReportJpgOss(),e);
                    }
                }
            }
        }





        return null;
    }

    /**
     * 刷新报告ct值
     *
     * @param fixStructReportCmd 报表命令结构体，包含报表的相关信息。
     * @return 是否成功刷新报表计数器值。
     */
    @Override
    @LogAndAlarm
    public Boolean flushReportCtValue(FixStructReportCmd fixStructReportCmd) {
        //查询报告

        Long offset = fixStructReportCmd.getOffset();
        Long offsetEnd = fixStructReportCmd.getOffsetEnd();
        while (offset < offsetEnd){

            log.info("flushReportCtValue->offset={},offsetEnd={}", offset, offsetEnd);

            List<MedicalReport> medicalReports = medicalReportRepository.selectByCondition(
                    MedicalReportQueryBO.builder()
                            .offset(offset)
                            .offsetEnd(offset + fixStructReportCmd.getPageSize())
                            .build()
            );


            offset = offset + fixStructReportCmd.getPageSize();

            if (CollectionUtils.isEmpty(medicalReports)){
                continue;
            }

            executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{

                Set<String> reportIds = medicalReports.stream().map(p->String.valueOf(p.getId())).collect(Collectors.toSet());

                MedReportIndicatorQueryBO medReportIndicatorQueryBO = new MedReportIndicatorQueryBO();
                medReportIndicatorQueryBO.setReportIds(reportIds);
                List<MedicalReportIndicator> medicalReportIndicators = medicalReportIndicatorRepository.listMedicalReportIndicators(medReportIndicatorQueryBO);
                if (CollectionUtils.isEmpty(medicalReportIndicators)){
                    return;
                }

                Map<String, List<MedicalReportIndicator>> reportIdToIndicator = medicalReportIndicators.stream().collect(Collectors.groupingBy(MedicalReportIndicator::getReportId));

                for (MedicalReport medicalReport : medicalReports) {

                    if (!reportIdToIndicator.containsKey(String.valueOf(medicalReport.getId()))){
                        continue;
                    }


                    String structReportStr = getStructReportStr(medicalReport.getStructReportOss());

                    if (StringUtils.isBlank(structReportStr)){
                        continue;
                    }

                    StructQuickReportContentDTO structQuickReportContentDTO = JsonUtil.parseObject(structReportStr, StructQuickReportContentDTO.class);
                    if (Objects.isNull(structQuickReportContentDTO)) {
                        continue ;
                    }

                    StructQuickReportResultDTO structQuickReportResultDTO = structQuickReportContentDTO.getReportResult().get(0);
                    List<StructQuickReportResultIndicatorDTO> indicators = structQuickReportResultDTO.getIndicators();

                    StructQuickReportResultIndicatorDTO resultIndicatorDTO = indicators.stream().filter(p -> StringUtil.isNotBlank(p.getCtValue())).findFirst().orElse(null);
                    if (Objects.isNull(resultIndicatorDTO)){
                        continue;
                    }

                    indicators.removeIf(p->StringUtil.isBlank(p.getCtValue()));

                    Map<String, String> nameToCtValue = indicators.stream().collect(Collectors.toMap(StructQuickReportResultIndicatorDTO::getIndicatorName, StructQuickReportResultIndicatorDTO::getCtValue));

                    List<MedicalReportIndicator> reportIndicators = reportIdToIndicator.get(String.valueOf(medicalReport.getId()));

                    for (MedicalReportIndicator medicalReportIndicator : reportIndicators) {
                        String indicatorName = medicalReportIndicator.getIndicatorName();
                        if (nameToCtValue.containsKey(indicatorName)){
                            medicalReportIndicator.setCtValue(nameToCtValue.get(indicatorName));
                        }
                    }
                    medicalReportIndicatorRepository.updateMedicalReportIndicators(reportIndicators);
                }

                log.info("flushReportCtValue->deal end id ={}",medicalReports.get(medicalReports.size()-1).getId().toString());

            });


        }




        return Boolean.TRUE;
    }

    /**
     * 阳性率
     *
     * @param medPromisePosRateCmd 查询条件对象
     * @return 医保承诺履行率DTO对象
     */
    @Override
    public MedPromisePositiveRateDTO queryMedPromisePositiveRateNew(MedPromisePosRateCmd medPromisePosRateCmd) {

        //查项目
        ServiceItem serviceItem = jdhServiceItemRepository.find(ServiceItemIdentifier.builder().itemId(medPromisePosRateCmd.getServiceItemId()).build());
        if (Objects.isNull(serviceItem)){
            return null;
        }

        // 创建并初始化MedPromisePositiveRateDTO对象
        MedPromisePositiveRateDTO medPromisePositiveRateDTO = new MedPromisePositiveRateDTO();
        medPromisePositiveRateDTO.setServiceItemId(medPromisePosRateCmd.getServiceItemId());
        medPromisePositiveRateDTO.setAppointmentNum(CommonConstant.ZERO);
        medPromisePositiveRateDTO.setServiceItemName(serviceItem.getItemName());
        // 创建并初始化MedPromiseIndicatorPosRateDTO对象列表
        List<MedPromiseIndicatorPosRateDTO> detailList = Lists.newArrayList();
        medPromisePositiveRateDTO.setDetailList(detailList);


        //查检测单
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(
                MedicalPromiseListQuery.builder().stationId(medPromisePosRateCmd.getStationId()).serviceItemId(String.valueOf(medPromisePosRateCmd.getServiceItemId())).reportStatus(CommonConstant.ONE).reportStartTime(medPromisePosRateCmd.getStartTime()).reportEndTime(medPromisePosRateCmd.getEndTime()).build()
        );
        if (CollectionUtils.isEmpty(medicalPromises)){
            return medPromisePositiveRateDTO;
        }


        //查报告
        List<Long> mpIds = medicalPromises.stream().map(MedicalPromise::getMedicalPromiseId).collect(Collectors.toList());
        List<MedicalReport> medicalReports = medicalReportRepository.getByMedicalPromiseIdList(mpIds);
        if (CollectionUtils.isEmpty(medicalReports)){
            return medPromisePositiveRateDTO;
        }


        //查报告结果
        Set<String> mrIds = medicalReports.stream().map(p->String.valueOf(p.getId())).collect(Collectors.toSet());
        MedReportIndicatorQueryBO bo = new MedReportIndicatorQueryBO();
        bo.setReportIds(mrIds);
        List<MedicalReportIndicator> medicalReportIndicators = medicalReportIndicatorRepository.listMedicalReportIndicators(bo);
        if (CollectionUtils.isEmpty(medicalReportIndicators)){
            return medPromisePositiveRateDTO;
        }

        //获取统计的报告份数
        int size = medicalReportIndicators.stream().collect(Collectors.groupingBy(MedicalReportIndicator::getReportId)).size();
        medPromisePositiveRateDTO.setAppointmentNum(size);

        Map<String, List<MedicalReportIndicator>> nameToList = medicalReportIndicators.stream().collect(Collectors.groupingBy(MedicalReportIndicator::getIndicatorName));

        nameToList.forEach((name, list) -> {
            MedPromiseIndicatorPosRateDTO medPromiseIndicatorPosRateDTO = new MedPromiseIndicatorPosRateDTO();
            medPromiseIndicatorPosRateDTO.setIndicatorName(name);
            medPromiseIndicatorPosRateDTO.setIndicatorNum(list.size());

            List<MedicalReportIndicator> normalIndicator = list.stream().filter(p -> Objects.equals(CommonConstant.ZERO, p.getAbnormalMarkType())).collect(Collectors.toList());
            int normalNum = org.apache.commons.collections4.CollectionUtils.isNotEmpty(normalIndicator)? normalIndicator.size() : CommonConstant.ZERO;
            String plainString = new BigDecimal(list.size() - normalNum).divide(new BigDecimal(list.size()), 2, RoundingMode.HALF_UP).toPlainString();
            medPromiseIndicatorPosRateDTO.setPositiveRate(plainString);

            detailList.add(medPromiseIndicatorPosRateDTO);

        });

        return medPromisePositiveRateDTO;
    }

    /**
     * @param medicalReportIndicatorFlushCmd
     * @return
     */
    @Override
    public Boolean flushReportIndicatorIdOnly(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd) {
        Integer pageSize = Objects.nonNull(medicalReportIndicatorFlushCmd.getPageSize()) ? medicalReportIndicatorFlushCmd.getPageSize() : CommonConstant.ONE_HUNDRED;
        Integer offset = medicalReportIndicatorFlushCmd.getStartId();
        //最多循环1000次
        int query = CommonConstant.ZERO;
        Map<String, String> nameToId = duccConfig.getStationIndicatorNameToId();
        while (offset < medicalReportIndicatorFlushCmd.getEndId() && query <= CommonConstant.NUMBER_THOUSAND){
            log.info("flushReportIndicatorIdOnly->offset={},endId={},query={}",offset,medicalReportIndicatorFlushCmd.getEndId(),query);
            Integer endId = offset+pageSize;
            try {
                List<MedicalReportIndicator> medicalReportIndicators = medicalReportIndicatorRepository.listNoIndicatorId(offset, endId);
                if (CollectionUtils.isNotEmpty(medicalReportIndicators)){
                    log.info("flushReportIndicatorIdOnly->medicalReportIndicators.size={}",medicalReportIndicators.size());
                    for (MedicalReportIndicator medicalReportIndicator : medicalReportIndicators){
                        String indicatorId = nameToId.get(medicalReportIndicator.getIndicatorName());
                        if (StringUtil.isNotBlank(indicatorId)){
                            medicalReportIndicator.setIndicatorId(Long.valueOf(indicatorId));
                            medicalReportIndicatorRepository.saveIndicatorId(medicalReportIndicator);
                        }
                    }
                }
            }catch (Exception e){
                log.info("flushReportIndicatorIdOnly,error,startId={},endId={},query={}",offset,endId,query);
            }
            offset = endId;
            query++;
        }
        return Boolean.TRUE;
    }

    /**
     * 下载结构化报告字符串
     *
     * @param jssUrl
     * @return
     */
    private String getStructReportStr(String jssUrl) {
        if (StringUtils.isBlank(jssUrl)) {
            return null;
        }
        log.info("MedicalReportApplicationImpl.getStructReportStr -> jssUrl={}", jssUrl);
        InputStream inputStream = fileManageService.get(jssUrl);
        String result = new BufferedReader(new InputStreamReader(inputStream))
                .lines().collect(Collectors.joining("\n"));
        log.info("MedicalReportApplicationImpl.getStructReportStr -> result={}", result);
        return result;
    }

    /**
     * 将 StructQuickReportResultDTO 和 BaseAddressBo 转换为 ReportIndicatorCmd 列表。
     * @param indicatorDTOS indicatorDTOS。
     * @return 转换后的 ReportIndicatorCmd 列表。
     */
    private List<ReportIndicatorCmd> convert(List<StructQuickReportResultIndicatorDTO> indicatorDTOS, MedicalPromise medicalPromise, MedicalReportDTO medicalReportDTO, JdhPromise jdhPromise,  JdhPromisePatient jdhPromisePatient){
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(indicatorDTOS)){
            return null;
        }

        //省
        Integer provinceCode = jdhPromise.getStore().getProvinceCode();
        //市
        Integer cityCode = jdhPromise.getStore().getCityCode();
        //区
        Integer districtCode = jdhPromise.getStore().getDistrictCode();
        //镇
        Integer townCode = jdhPromise.getStore().getTownCode();

        List<ReportIndicatorCmd> reportIndicatorCmdList = new ArrayList<>();

        for (StructQuickReportResultIndicatorDTO indicatorDTO : indicatorDTOS) {
            ReportIndicatorCmd reportIndicatorCmd = convert(indicatorDTO);
            packOtherInfo(medicalPromise, medicalReportDTO, jdhPromise, jdhPromisePatient, reportIndicatorCmd, provinceCode, cityCode, districtCode, townCode);
            reportIndicatorCmdList.add(reportIndicatorCmd);
        }

        return reportIndicatorCmdList;
    }

    /**
     * 将医疗承诺、医疗报告、就诊承诺、就诊承诺患者、报告指标命令等信息打包到报告指标命令中。
     * @param medicalPromise 医疗承诺对象
     * @param medicalReportDTO 医疗报告DTO对象
     * @param jdhPromise 就诊承诺对象
     * @param jdhPromisePatient 就诊承诺患者对象
     * @param reportIndicatorCmd 报告指标命令对象
     * @param provinceCode 省份代码
     * @param cityCode 城市代码
     * @param districtCode 区县代码
     * @param townCode 镇代码
     */
    private  void packOtherInfo(MedicalPromise medicalPromise, MedicalReportDTO medicalReportDTO, JdhPromise jdhPromise, JdhPromisePatient jdhPromisePatient, ReportIndicatorCmd reportIndicatorCmd, Integer provinceCode, Integer cityCode, Integer districtCode, Integer townCode) {
        reportIndicatorCmd.setReportId(String.valueOf(medicalReportDTO.getId()));
        reportIndicatorCmd.setVerticalCode(medicalPromise.getVerticalCode());
        reportIndicatorCmd.setServiceType(medicalPromise.getServiceType());
        reportIndicatorCmd.setStationId(medicalPromise.getStationId());
        if (Objects.nonNull(provinceCode)){
            reportIndicatorCmd.setPatientProvince(Long.valueOf(provinceCode));
        }
        if (Objects.nonNull(cityCode)){
            reportIndicatorCmd.setPatientCity(Long.valueOf(cityCode));
        }
        if (Objects.nonNull(districtCode)){
            reportIndicatorCmd.setPatientCounty(Long.valueOf(districtCode));
        }
        if (Objects.nonNull(townCode)){
            reportIndicatorCmd.setPatientTown(Long.valueOf(townCode));
        }
        reportIndicatorCmd.setPatientAddress(jdhPromise.getStore().getStoreAddr());
        reportIndicatorCmd.setPatientId(jdhPromisePatient.getPatientId());
        reportIndicatorCmd.setPromisePatientId(jdhPromisePatient.getPromisePatientId());
        reportIndicatorCmd.setCheckTime(Objects.nonNull(medicalPromise.getCheckTime()) ? medicalPromise.getCheckTime() : medicalPromise.getReportTime());
        reportIndicatorCmd.setServiceItemId(medicalPromise.getServiceItemId());
    }


    /**
     * 将 StructQuickReportResultIndicatorDTO 对象转换为 ReportIndicatorCmd 对象。
     * @param indicatorDTO 需要转换的 StructQuickReportResultIndicatorDTO 对象。
     * @return 转换后的 ReportIndicatorCmd 对象。
     */
    private ReportIndicatorCmd convert(StructQuickReportResultIndicatorDTO indicatorDTO){
        if (Objects.isNull(indicatorDTO)) {
            return null;
        }
        ReportIndicatorCmd reportIndicatorCmd = new ReportIndicatorCmd();
        if (StringUtils.isNoneBlank(indicatorDTO.getIndicatorNo())){
            reportIndicatorCmd.setIndicatorId(Long.valueOf(indicatorDTO.getIndicatorNo()));
        }
        reportIndicatorCmd.setIndicatorName(indicatorDTO.getIndicatorName());
        reportIndicatorCmd.setNormalRangeValue(indicatorDTO.getNormalRangeValue());
        reportIndicatorCmd.setUnit(indicatorDTO.getUnit());
        reportIndicatorCmd.setIndicatorResult(indicatorDTO.getValue());
        reportIndicatorCmd.setAbnormalMarkType(Integer.valueOf(indicatorDTO.getAbnormalType()));
        reportIndicatorCmd.setCtValue(indicatorDTO.getCtValue());
        return reportIndicatorCmd;
    }

    /**
     * 打包并保存医疗报告指标数据。
     * @param medicalPromise 医疗承诺对象，包含医疗承诺ID和患者ID。
     */
    private void packReportIndicator(MedicalPromise medicalPromise){
        com.jdh.o2oservice.export.report.query.MedicalPromiseRequest medicalPromiseRequest = new com.jdh.o2oservice.export.report.query.MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        MedicalReportDTO medicalReportDTO = medicalReportApplication.getByMedicalPromiseId(medicalPromiseRequest);
        String structReportStr = getStructReportStr(medicalReportDTO.getStructReportOss());
        if (StringUtil.isBlank(structReportStr)){
            return ;
        }
        StructQuickReportContentDTO structQuickReportContentDTO = JsonUtil.parseObject(structReportStr, StructQuickReportContentDTO.class);
        if (Objects.isNull(structQuickReportContentDTO)) {
            return ;
        }

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(structQuickReportContentDTO.getReportResult())){
            return ;
        }

        //解析用户4级地址
        JdhPromise jdhPromise = jdhPromiseRepository.find(
                JdhPromiseIdentifier.builder()
                        .promiseId(medicalPromise.getPromiseId())
                        .build()
        );
        //TODO storeAddr是否是用户上门地址？
        JdhPromisePatient jdhPromisePatient = jdhPromise.getPatients().stream().filter(p -> Objects.equals(p.getPromisePatientId(), medicalPromise.getPromisePatientId())).findFirst().orElse(null);
        log.info("MedicalReportResultApplicationImpl->analyseReportIndicators,jdhPromisePatient={}", JSON.toJSONString(jdhPromisePatient));
        List<StructQuickReportResultDTO> reportResult = structQuickReportContentDTO.getReportResult();
        for (StructQuickReportResultDTO structQuickReportResultDTO : reportResult) {
            List<ReportIndicatorCmd> convert = convert(structQuickReportResultDTO.getIndicators(), medicalPromise, medicalReportDTO, jdhPromise,jdhPromisePatient);
            log.info("MedicalReportResultApplicationImpl->analyseReportIndicators,convert:{}", JSON.toJSONString(convert));
            this.saveReportIndicators(convert);
        }
    }
}
