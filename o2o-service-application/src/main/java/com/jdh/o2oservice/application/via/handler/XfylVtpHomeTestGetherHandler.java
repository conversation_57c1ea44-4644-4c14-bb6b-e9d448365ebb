package com.jdh.o2oservice.application.via.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherIdentifier;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherItem;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhVoucherStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.enums.ViaBtnCodeEnum;
import com.jdh.o2oservice.core.domain.support.via.enums.ViaFloorEnum;
import com.jdh.o2oservice.core.domain.support.via.enums.ViaFormTypeEnum;
import com.jdh.o2oservice.core.domain.support.via.enums.ViaPageEnum;
import com.jdh.o2oservice.core.domain.support.via.model.*;
import com.jdh.o2oservice.core.domain.trade.enums.OrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.vo.AddressInfoValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentInfoValueObject;
import com.jdh.o2oservice.export.product.dto.GroupUserAddressDTO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.UserAddressDetailDTO;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.OrderUserAddressQuery;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderExtDTO;
import com.jdh.o2oservice.export.trade.query.OrderDetailParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * xfyl vtp 次卡场景 预约聚合页
 *
 * <AUTHOR>
 * @date 2024/08/29
 */
@Slf4j
@Service
public class XfylVtpHomeTestGetherHandler extends AbstractViaDataFillHandler implements MapAutowiredKey {

    /**
     * 可选地址列表分组
     */
    private static final String CAN_SELECT_ADDRESS_GROUP = "canSelect";

    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;

    /**
     * voucherRepository
     */
    @Resource
    private VoucherRepository voucherRepository;

    /**
     * productApplication
     */
    @Autowired
    private ProductApplication productApplication;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * tradeApplication
     */
    @Autowired
    private TradeApplication tradeApplication;

    /**
     * jdhSkuRepository
     */
    @Resource
    private JdhSkuRepository jdhSkuRepository;

    /**
     * 可预约预约单状态集合
     */
    private static List<Integer> CAN_APPOINTMENT_STATUS = Arrays.asList(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus(),JdhPromiseStatusEnum.CANCEL_SUCCESS.getStatus());

    /**
     * verifyVoucherBaseInfo
     *
     * @param jdhVoucher jdhVoucher
     * @param ctx        ctx
     */
    private void verifyVoucherBaseInfo(JdhVoucher jdhVoucher,FillViaConfigDataContext ctx){
        if(Objects.isNull(jdhVoucher)){
            throw new BusinessException(SupportErrorCode.VIA_FLOOR_VOUCHER_ID_NOT_EXIT);
        }
        if(JdhFreezeEnum.FREEZE.getStatus().equals(jdhVoucher.getFreeze())){
            throw new BusinessException(SupportErrorCode.VIA_FLOOR_VOUCHER_FREEZE_ERROR);
        }
        if(JdhVoucherStatusEnum.INVALID.getStatus().equals(jdhVoucher.getStatus())){
            throw new BusinessException(SupportErrorCode.VIA_FLOOR_VOUCHER_INVALID_ERROR);
        }
        if(!ctx.getUserPin().equals(jdhVoucher.getUserPin())){
            throw new BusinessException(SupportErrorCode.VIA_FLOOR_VOUCHER_ID_NOT_EXIT);
        }
    }

    /**
     * detectPromise
     * 本次是voucher,promise一起创建的逻辑。
     * 后续看情况，调整为voucher创建后，在提交预约的时候在创建promise。这里的逻辑就变成只校验卡次数相关逻辑
     *
     * @param jdhPromiseList jdhPromiseList
     * @param jdhVoucher     jdhVoucher
     * @return {@link JdhPromise}
     */
    private JdhPromise detectPromise(JdhVoucher jdhVoucher,List<JdhPromise> jdhPromiseList){
        if(CollUtil.isEmpty(jdhPromiseList)){
            throw new BusinessException(SupportErrorCode.VIA_PROMISE_INFO_NOT_EXIT);
        }
        //3、过滤出没有使用的次
        List<JdhPromise> waitPromiseList = jdhPromiseList.stream().filter(ele -> CAN_APPOINTMENT_STATUS.contains(ele.getPromiseStatus())
                && JdhFreezeEnum.UN_FREEZE.getStatus().equals(ele.getFreeze())).collect(Collectors.toList());
        if(CollUtil.isEmpty(waitPromiseList)){
            throw new BusinessException(SupportErrorCode.VIA_FLOOR_NOT_FOUND_WAIT_PROMISE);
        }
        //4、排个序
        waitPromiseList.sort(Comparator.comparingLong(JdhPromise::getPromiseId));

        return waitPromiseList.get(NumConstant.NUM_0);
    }

    /**
     * 检查服务是否开启爆单
     *
     * @param skuDtoMap sku dto地图
     */
    private void checkService(Map<Long, JdhSkuDto> skuDtoMap){
        if(CollUtil.isEmpty(skuDtoMap)){
            throw new BusinessException(SupportErrorCode.VIA_FLOOR_NOT_FOUND_SKU_INFO);
        }
        for (Map.Entry<Long, JdhSkuDto> jdhSkuDtoEntry : skuDtoMap.entrySet()) {
            JdhSkuDto jdhSkuDto = jdhSkuDtoEntry.getValue();
            // 售卖状态 0-不可售卖 1-可售卖,仅控制是否可下单,非上下架
            Integer saleStatus = jdhSkuDto.getSaleStatus();
            if(NumConstant.NUM_0.equals(saleStatus)){
                throw new BusinessException(SupportErrorCode.VIA_FLOOR_NOT_SUPPORT_APPOINTMENT);
            }
        }
    }

    /**
     * 填充数据
     *
     * @param ctx ctx
     */
    @Override
    public void handle(FillViaConfigDataContext ctx) {
        log.info("XfylVtpHomeTestGetherHandler handle ctx={}", JSON.toJSONString(ctx));
        String voucherId = ctx.getVoucherId();
        AssertUtils.hasText(voucherId, SupportErrorCode.VIA_FLOOR_VOUCHER_ID_NOT_EXIT);

        //1、查询voucher信息
        JdhVoucher jdhVoucher = voucherRepository.find(JdhVoucherIdentifier.builder().voucherId(Long.parseLong(voucherId)).build());
        //校验voucher信息。越权、冻结，作废 等
        verifyVoucherBaseInfo(jdhVoucher,ctx);

        //可预约的sku集合
        List<Long> skuNoList = jdhVoucher.getVoucherItemList().stream().map(JdhVoucherItem::getServiceId).collect(Collectors.toList());

        JdhSkuListRequest skuListRequest = new JdhSkuListRequest();
        skuListRequest.setSkuIdList(new HashSet<>(skuNoList));
        List<JdhSku> jdhSkus = jdhSkuRepository.queryMultiSku(skuListRequest);
        ctx.getViaConfig().setSkuServiceType(jdhSkus.get(0).getServiceType());

        //商品开启爆单开关
//        Map<Long, JdhSkuDto> skuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder().skuIdList(CollUtil.newHashSet(skuNoList)).build());
//        checkService(skuDtoMap);

        //2、根据voucherId查询 promiseId，本次都是1次卡
        List<JdhPromise> jdhPromiseList = promiseRepository.findList(PromiseRepQuery.builder().voucherIds(Arrays.asList(jdhVoucher.getVoucherId())).build());
        //查找本次可用的promise单据
        JdhPromise jdhPromise = detectPromise(jdhVoucher,jdhPromiseList);


        // ========================== 楼层信息 =======================
        ViaConfig viaConfig = ctx.getViaConfig();
        List<ViaFloorInfo> floorList = viaConfig.getFloorList();

        //异步组装楼层
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (ViaFloorInfo viaFloorInfo : floorList) {
            //1、地址信息 appointmentAddressInfo
            if (ViaFloorEnum.VTP_PROMISE_GATHER_ADDRESS_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> dealAddressInfoFloorInfo(ctx,viaFloorInfo,jdhVoucher,skuNoList),
                        executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestGetherHandler dealFloorList dealAddressInfoFloorInfo exception",exception);
                    return null;
                }));
            }
            //2、预约信息 appointmentCommonInfo
            if (ViaFloorEnum.VTP_PROMISE_GATHER_COMMON_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> dealCommonInfoFloorInfo(ctx,viaFloorInfo,jdhVoucher,skuNoList,jdhSkus),
                        executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestGetherHandler dealFloorList dealCommonInfoFloorInfo exception",exception);
                    return null;
                }));
            }

            //3、备注信息 appointmentRemarkInfo 无需逻辑处理
            if (ViaFloorEnum.VTP_PROMISE_GATHER_REMARK_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> dealAppointmentRemarkInfoFloorInfo(viaFloorInfo,jdhVoucher,skuNoList,jdhSkus,ctx),
                        executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestGetherHandler dealFloorList dealAppointmentRemarkInfoFloorInfo exception",exception);
                    return null;
                }));
            }
            //4、温馨提示 kindlyTip 无需逻辑处理
//            if (ViaFloorEnum.VTP_PROMISE_GATHER_KINDLY_TIP.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
//
//            }
            //5、底部按钮 footerButtons
            if (ViaFloorEnum.VTP_PROMISE_GATHER_FOOTER_BUTTONS.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> dealFooterButtons(ctx,viaFloorInfo,jdhVoucher,jdhPromise,skuNoList),
                        executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("XfylVtpHomeTestGetherHandler dealFloorList dealFooterButtons exception",exception);
                    return null;
                }));
            }
        }
        //等待所有楼层信息组装完毕
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * dealAddressInfoFloorInfo
     *
     * @param viaFloorInfo viaFloorInfo
     * @param jdhVoucher   jdhVoucher
     * @param skuNoList    skuNoList
     */
    private void dealAddressInfoFloorInfo(FillViaConfigDataContext ctx,
                                          ViaFloorInfo viaFloorInfo,
                                          JdhVoucher jdhVoucher,
                                          List<Long> skuNoList){
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        ViaFloorConfig viaFloorConfig = floorConfigList.get(NumConstant.NUM_0);
        List<ViaFormItem> formItemList = viaFloorConfig.getFormItemList();
        ViaFormItem viaFormItem = formItemList.get(0);

        ViaActionInfo action = viaFormItem.getAction();
        Map<String, Object> actionCommonParams = new HashMap<>();
        actionCommonParams.put("verticalCode",jdhVoucher.getVerticalCode());
        actionCommonParams.put("serviceType",jdhVoucher.getServiceType());
        actionCommonParams.put("envType",ctx.getEnvType());
        actionCommonParams.put("skuNoList",skuNoList);
        action.setParams(actionCommonParams);

        try {
            Long orderAddressId = null;
            JdOrderDTO orderDetail = tradeApplication.getOrderDetail(OrderDetailParam.builder().orderId(jdhVoucher.getSourceVoucherId()).build());
            log.info("XfylVtpHomeTestGetherHandler dealAddressInfoFloorInfo orderDetail:{}",JSON.toJSONString(orderDetail));
            if(Objects.nonNull(orderDetail)){
                List<JdOrderExtDTO> jdOrderExtList = orderDetail.getJdOrderExtList();
                if(CollUtil.isNotEmpty(jdOrderExtList)){
                    for (JdOrderExtDTO jdOrderExtDTO : jdOrderExtList) {
                        String extType = jdOrderExtDTO.getExtType();
                        if(OrderExtTypeEnum.APPOINTMENT_INFO.getType().equals(extType)){
                            String extContext = jdOrderExtDTO.getExtContext();
                            OrderAppointmentInfoValueObject orderAppointmentInfoValueObject = JSON.parseObject(extContext, OrderAppointmentInfoValueObject.class);
                            AddressInfoValueObject addressInfo = orderAppointmentInfoValueObject.getAddressInfo();
                            log.info("XfylVtpHomeTestGetherHandler dealAddressInfoFloorInfo orderDetail 中 addressInfo:{}",JSON.toJSONString(addressInfo));
                            orderAddressId = addressInfo.getId();
                            break;
                        }
                    }
                }
            }

            //如果下单时的收货地址取到了,判断下单的收货地址，是否可使用
            if(Objects.nonNull(orderAddressId)){
                OrderUserAddressQuery orderUserAddressQuery = new OrderUserAddressQuery();
                orderUserAddressQuery.setUserPin(ctx.getUserPin());
                orderUserAddressQuery.setSkuNoList(skuNoList.stream().map(String::valueOf).collect(Collectors.toList()));
                Map<String, GroupUserAddressDTO> dtoMap = productApplication.listGroupAddress(orderUserAddressQuery);
                if(MapUtil.isNotEmpty(dtoMap) && dtoMap.containsKey(CAN_SELECT_ADDRESS_GROUP)){
                    GroupUserAddressDTO canSelect = dtoMap.get(CAN_SELECT_ADDRESS_GROUP);
                    Collection<UserAddressDetailDTO> canSelectList = canSelect.getList();
                    if(CollUtil.isNotEmpty(canSelectList)){
                        //copy orderAddressId to effective scope
                        Long finalOrderAddressId = orderAddressId;
                        canSelectList.stream()
                                .filter(ele -> finalOrderAddressId.equals(ele.getAddressId())).findFirst()
                                .ifPresent(userAddressDetailDTO -> viaFormItem.setValue(JSON.toJSONString(userAddressDetailDTO)));

                    }
                }
            }

        }catch (Exception e){
            log.error("XfylVtpHomeTestGetherHandler dealAddressInfoFloorInfo fillAddressInfo Error",e);
        }

    }

    /**
     * dealCommonInfoFloorInfo
     *
     *
     * @param ctx          ctx
     * @param viaFloorInfo viaFloorInfo
     * @param jdhVoucher   jdhVoucher
     * @param skuNoList    skuNoList
     */
    private void dealAppointmentRemarkInfoFloorInfo(
                                         ViaFloorInfo viaFloorInfo,
                                         JdhVoucher jdhVoucher,
                                         List<Long> skuNoList,
                                         List<JdhSku> jdhSkus,FillViaConfigDataContext ctx){
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        ViaFloorConfig viaFloorConfig = floorConfigList.get(NumConstant.NUM_0);
        List<ViaFormItem> formItemList = viaFloorConfig.getFormItemList();
        for (ViaFormItem viaFormItem : formItemList) {
            String formType = viaFormItem.getFormType();
            if (ViaFormTypeEnum.APPOINTMENT_REMARK.getType().equals(formType)){
                if (CollectionUtils.isNotEmpty(jdhSkus)){
                    JdhSku jdhSku = jdhSkus.get(0);
                    viaFormItem.setPlaceholder(productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), ctx.getScene(), viaFormItem.getPlaceholder()));
                }
            }
        }
    }


    /**
     * dealCommonInfoFloorInfo
     *
     *
     * @param ctx          ctx
     * @param viaFloorInfo viaFloorInfo
     * @param jdhVoucher   jdhVoucher
     * @param skuNoList    skuNoList
     */
    private void dealCommonInfoFloorInfo(FillViaConfigDataContext ctx,
                                         ViaFloorInfo viaFloorInfo,
                                         JdhVoucher jdhVoucher,
                                         List<Long> skuNoList,
                                         List<JdhSku> jdhSkus){
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        ViaFloorConfig viaFloorConfig = floorConfigList.get(NumConstant.NUM_0);
        List<ViaFormItem> formItemList = viaFloorConfig.getFormItemList();
        log.info("XfylVtpHomeTestGetherHandler dealCommonInfoFloorInfo formItemList={}", JSON.toJSONString(formItemList));
        for (ViaFormItem viaFormItem : formItemList) {
            ViaActionInfo action = viaFormItem.getAction();
            Map<String, Object> actionCommonParams = new HashMap<>();
            actionCommonParams.put("verticalCode",jdhVoucher.getVerticalCode());
            actionCommonParams.put("serviceType",jdhVoucher.getServiceType());
            actionCommonParams.put("envType",ctx.getEnvType());
            actionCommonParams.put("skuNoList",skuNoList);
            action.setParams(actionCommonParams);

            String formType = viaFormItem.getFormType();
            if (ViaFormTypeEnum.INTENDED_NURSE.getType().equals(formType)){
                if (CollectionUtils.isNotEmpty(jdhSkus)){
                    JdhSku jdhSku = jdhSkus.get(0);
                    viaFormItem.setFormName(productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), ctx.getScene(), viaFormItem.getFormName()));
                    viaFormItem.setTips(productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), ctx.getScene(), viaFormItem.getTips()));
                }
            }
        }
    }

    /**
     * dealFooterButtons
     *
     * @param ctx          ctx
     * @param viaFloorInfo viaFloorInfo
     * @param jdhVoucher   jdhVoucher
     * @param jdhPromise   jdhPromise
     * @param skuNoList    skuNoList
     */
    private void dealFooterButtons(FillViaConfigDataContext ctx,ViaFloorInfo viaFloorInfo,JdhVoucher jdhVoucher,JdhPromise jdhPromise,List<Long> skuNoList){
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        ViaFloorConfig viaFloorConfig = floorConfigList.get(NumConstant.NUM_0);
        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
        for (ViaBtnInfo btnInfo : btnList) {
            if(ViaBtnCodeEnum.BUY_FIRST_SUBMIT_APPOINT_BTN.getCode().equals(btnInfo.getCode())){
                 ViaActionInfo action = btnInfo.getAction();
                Map<String, Object> actionCommonParams = new HashMap<>();
                actionCommonParams.put("verticalCode",jdhVoucher.getVerticalCode());
                actionCommonParams.put("serviceType",jdhVoucher.getServiceType());
                actionCommonParams.put("envType",ctx.getEnvType());
                actionCommonParams.put("promiseId",jdhPromise.getPromiseId());

                ViaActionInfo nextAction = action.getNextAction();
                String nextActionUrl = nextAction.getUrl();
                nextAction.setUrl(MessageFormat.format(nextActionUrl,jdhPromise.getPromiseId().toString()));

                List<Map<String,Object>> services = new ArrayList<>();
                for (Long skuNo : skuNoList) {
                    Map<String,Object> service = new HashMap<>();
                    service.put("promiseId",jdhPromise.getPromiseId());
                    service.put("serviceId",skuNo);
                    services.add(service);
                }
                actionCommonParams.put("services",services);
                action.setParams(actionCommonParams);
            }
        }

    }

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return ViaPageEnum.APPOINT_GETHER.getScene() + "_" + BusinessModeEnum.ANGEL_TEST.getCode();
    }
}
