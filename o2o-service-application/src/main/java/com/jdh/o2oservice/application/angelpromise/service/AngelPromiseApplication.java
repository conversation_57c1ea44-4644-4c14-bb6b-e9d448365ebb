package com.jdh.o2oservice.application.angelpromise.service;

import com.jdh.o2oservice.export.angelpromise.cmd.*;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.*;

import java.util.List;

/**
 * AngelPromiseApplication
 * <AUTHOR>
 * @date 2024-05-23 21:39
 */
public interface AngelPromiseApplication {

    /**
     * 查询工单信息（仅工单）
     *
     * @param angelWorkQuery 入参
     * @return AngelWorkDetailDto
     */
    AngelWorkDetailDto queryAngelWork(AngelWorkQuery angelWorkQuery);

    /**
     * 查询工单列表或者最近一次的无效工单
     *
     * @param angelWorkQuery 工单参数
     * @return AngelWorkDetailDto
     */
    AngelWorkDetailDto queryWorkListOrRecently(AngelWorkQuery angelWorkQuery);

    /**
     * 服务者工单详情
     * @param detailQuery 入参
     * @return AngelWorkDto
     */
    AngelWorkDto queryAngelWorkDetail(AngelWorkDetailQuery detailQuery);

    /**
     * 服务者工单详情页
     * @param detailQuery 入参
     * @return AngelWorkDto
     */
    AngelWorkDto queryAngelWorkDetailForPage(AngelWorkDetailQuery detailQuery);

    /**
     * 工单详情页列表
     * @param angelWorkListQuery 入参
     * @return  List
     */
    List<AngelWorkDto> queryAngelWorkList(AngelWorkListQuery angelWorkListQuery);

    /**
     * 工单详情页列表页
     * @param angelWorkListQuery 入参
     * @return AngelWorkListDto
     */
    AngelWorkListDto queryAngelWorkListForPage(AngelWorkListQuery angelWorkListQuery);

    /**
     * 查工单详情页配置模板
     * @param templateQuery 入参
     * @return List
     */
    List<AngelWorkShowTemplateDto> queryAngelWorkShowTemplate(AngelWorkShowTemplateQuery templateQuery);

    /**
     * 送达
     *
     * @param deliverCmd 入参
     * @return Boolean
     */
    Boolean deliver(DeliverCmd deliverCmd);

    /**
     * 护士到家后验证码
     * @param codeVerificationCmd 入参
     * @return Boolean
     */
    Boolean codeVerification(AngelWorkCodeVerificationCmd codeVerificationCmd);

    /**
     * 获取服务者预计费用
     * @param query 入参
     * @return List
     */
    List<AngelPlanChargeDto> queryAngelPlanCharge(AngelPlanChargeQuery query);

    /**
     * 检验项绑定样本条码
     * @param bindSpecimenCodeCmd 入参
     * @return Boolean
     */
    Boolean bindSpecimenCode(AngelWorkBindSpecimenCodeCmd bindSpecimenCodeCmd);

    /**
     * 查询枚举列表
     * @param enumQuery 入参
     * @return List
     */
    List<AngelWorkEnumDto> queryEnum(AngelWorkEnumQuery enumQuery);

    /**
     * 确认检测任务信息
     * @param cmd  入参
     * @return Boolean
     */
    Boolean confirmTaskInfo(ConfirmTaskInfoCmd cmd);

    /**
     * 提交服务任务完成信息
     * @param cmd 入参
     * @return Boolean
     */
    Boolean submitWorkCompleteInfo(SubmitWorkCompleteInfoCmd cmd);

    /**
     * 提交服务单录音
     * @param cmd 入参
     * @return Boolean
     */
    Boolean submitSoundRecording(SubmitSoundRecordingCmd cmd);

    /**
     * 护士取消工单判断是否超过3小时不能取消
     *
     * @param angelWorkQuery 入参
     * @return Boolean
     */
    Boolean checkCancelTimeout(AngelWorkQuery angelWorkQuery);

    /**
     * 查询工单实验室列表
     * @param detailQuery 入参
     * @return AngelWorkDto
     */
    List<AngelWorkSpecimenDto> queryAngelWorkSpecimens(AngelWorkDetailQuery detailQuery);

    /**
     * 护士点击已到达
     * @param submitArrivedCmd
     * @return
     */
    AngelArrivedDTO submitArrived(SubmitArrivedCmd submitArrivedCmd);

    /**
     * 护士点击开始服务
     * @param submitInServiceCmd
     * @return
     */
    AngelInServiceDTO submitInService(SubmitInServiceCmd submitInServiceCmd);

    /**
     * 查询服务单外呼记录
     * @param param
     * @return
     */
    AngelCallRecordsDTO queryCallRecords(AngelWorkDetailQuery param);


    /**
     * 根据查询条件获取工单基本信息
     * @param angelWorkDetailQuery 查询条件对象
     * @return 工单基本信息DTO
     */
    AngelWorkDto queryAngelWorkBaseInfo(AngelWorkDetailQuery angelWorkDetailQuery);


    /**
     * 提交服务任务完成信息
     * @param cmd 入参
     * @return Boolean
     */

    Boolean updateSoundRecording(SubmitSoundRecordingCmd cmd);
}
