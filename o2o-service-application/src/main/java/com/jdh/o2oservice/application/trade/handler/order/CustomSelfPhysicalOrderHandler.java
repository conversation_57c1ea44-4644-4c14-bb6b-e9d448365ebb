package com.jdh.o2oservice.application.trade.handler.order;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.AviatorEvaluator;
import com.jdh.o2oservice.base.constatnt.DateConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.AppointmentInfoDetail;
import com.jdh.o2oservice.export.trade.dto.CustomOrderInfoDTO;
import com.jdh.o2oservice.export.trade.dto.CustomOrderProcessTrackDTO;
import com.jdh.o2oservice.export.trade.dto.CustomOrderServiceMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description 自定义消费医疗自营体检订单（ordertype=200&sendpay294=4）
 * https://joyspace.jd.com/pages/JHRRSsFaHZMD4nsLVoOA
 * @Date 2024/9/19 下午3:07
 * <AUTHOR>
 **/
@Component
@Slf4j
public class CustomSelfPhysicalOrderHandler extends AbstractCustomOrderHandler implements CustomOrderStrategy{

    @Resource
    private DuccConfig duccConfig;

    @Override
    public String getKey() {
        return CustomOrderStrategy.XFYL_SELF_PHYSICAL_ORDER;
    }

    @Override
    public CustomOrderInfoDTO handle(CustomOrderHandlerContext context) {
        log.info("CustomSelfPhysicalOrderHandler handle context={}", JSON.toJSONString(context));
        // 自定义订单信息
        CustomOrderInfoDTO res = new CustomOrderInfoDTO();
        res.setOrderId(context.getOrderId());
        JSONObject customOrderInfoConfig = JSON.parseObject(duccConfig.getCustomOrderInfoConfig());
        // 标签
        res.setLabelInfo(this.buildLabelInfo(ServiceTypeEnum.PHYSICAL.getServiceType(), customOrderInfoConfig));
        log.info("CustomSelfPhysicalOrderHandler handle labelInfo res={}", JSON.toJSONString(res));

        AppointmentInfoDetail appointmentInfoDetail = selectAppointmentInfoDetail(context, context.getOrderId());
        log.info("CustomSelfPhysicalOrderHandler handle appointmentInfoDetail={}", JSON.toJSONString(appointmentInfoDetail));
        if (appointmentInfoDetail == null){
            return res;
        }

        // orderStatus 预约状态 0-待预约 1-预约失败 2-预约中 3-预约成功 4-已到检 5-已完成(过期) 6-退款中 7-退款失败 8-退款完成 9-待付款 11-已取消 12-取消中
        // 有效期
        if (!Arrays.asList(9, 11, 8).contains(appointmentInfoDetail.getOrderStatus())){
            try {
                CustomOrderServiceMessageDTO serviceMessageDTO = new CustomOrderServiceMessageDTO();
                Date date = new SimpleDateFormat(DateConstant.YMDHMS2).parse(appointmentInfoDetail.getExpireDate());
                serviceMessageDTO.setMsg("有效期至:" + TimeUtils.dateTimeToStr(date, TimeFormat.SHORT_PATTERN_LINE));
                res.setServiceMessages(Collections.singletonList(serviceMessageDTO));
            } catch (ParseException e) {
                log.error("CustomSelfPhysicalOrderHandler handle expireDate error");
            }
        }

        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus", appointmentInfoDetail.getOrderStatus());
        log.info("CustomSelfPhysicalOrderHandler handle param={}", JSON.toJSONString(param));

        // 服务流程配置
        JSONObject processTrackObj = customOrderInfoConfig.getJSONObject("processTrack");
        // 自营体检订单配置
        JSONArray orderStatusArray =  processTrackObj.getJSONObject("XFYLSelfPhysicalOrder").getJSONArray("orderStatus");
        for (int i=0; i<orderStatusArray.size(); i++) {
            JSONObject orderStatusObj = orderStatusArray.getJSONObject(i);
            if ((Boolean) AviatorEvaluator.compile(orderStatusObj.getString("statusExpression"), Boolean.TRUE).execute(param)){
                log.info("CustomSelfPhysicalOrderHandler handle orderStatusObj={}", JSON.toJSONString(orderStatusObj));
                res.setProcessTrack(buildProcessTrack(appointmentInfoDetail, orderStatusObj));
            }
        }
        return res;
    }

    private CustomOrderProcessTrackDTO buildProcessTrack(AppointmentInfoDetail appointmentInfoDetail, JSONObject orderStatusObj) {
        CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSON.toJSONString(orderStatusObj), CustomOrderProcessTrackDTO.class);
        processTrack.setUrl(MessageFormat.format(processTrack.getUrl(), String.valueOf(appointmentInfoDetail.getOrderId()), String.valueOf(appointmentInfoDetail.getJdAppointmentId())));
        // 预约人姓名
        String patientName = "*"+appointmentInfoDetail.getUserName().substring(1);
        // 预约时间
        String appointmentTime = TimeUtils.dateTimeToStr(new Date(Long.parseLong(appointmentInfoDetail.getAppointmentDate())), TimeFormat.SHORT_PATTERN_DOT);
        // 门店名称
        String storeName = appointmentInfoDetail.getStoreName();
        processTrack.setMessage(String.format(processTrack.getMessage(), patientName, appointmentTime, storeName));
        return processTrack;
    }

    /**
     * orderStatus 预约状态 0-待预约 1-预约失败 2-预约中 3-预约成功 4-已到检 5-已完成(过期) 6-退款中 7-退款失败 8-退款完成 9-待付款 11-已取消 12-取消中
     *
     * 一单多件时，服务流程展示优先级：待预约>预约失败>预约中>预约成功>退款中>已到检＞已出报告>已完成>退款完成（该优先级与一单多件的订单状态优先级保持一致）
     * 若优先级排序后，最高优先级存在同状态的预约单，则展示预约时间最近的
     * 若同状态且预约时间都相同，则按照系统返回的顺序展示即可
     * @param context
     * @param orderId
     * @return
     */
    private AppointmentInfoDetail selectAppointmentInfoDetail(CustomOrderHandlerContext context, Long orderId) {
        List<AppointmentInfoDetail> appointmentInfoDetailList = context.getOrderIdMappingAppointmentInfoDetailMap().get(orderId);
        appointmentInfoDetailList.forEach(app->{
            if (NumConstant.NUM_1.equals(app.getReportStatus())){
                app.setOrderStatus(NumConstant.NUM_100);
            }
        });

        Map<String, AppointmentInfoDetail> appointMap = appointmentInfoDetailList.stream().collect(Collectors.toMap(AppointmentInfoDetail::getJdAppointmentId
                , Function.identity(), (key1, key2) -> key2));

        Map<Integer, AppointmentInfoDetail> valueAppointMap = new HashMap<>();

        Map<String, Integer> sortAppointMap = new HashMap<>();

        for (AppointmentInfoDetail app : appointmentInfoDetailList) {
            if (NumConstant.NUM_0.equals(app.getOrderStatus())){// 待预约
                if (checkAppointmentDateSort(valueAppointMap, sortAppointMap, NumConstant.NUM_9, app)){
                    sortAppointMap.put(app.getJdAppointmentId(),NumConstant.NUM_9);
                    valueAppointMap.put(NumConstant.NUM_9, app);
                    continue;
                }
            }
            if (NumConstant.NUM_1.equals(app.getOrderStatus())){// 预约失败
                if (checkAppointmentDateSort(valueAppointMap, sortAppointMap, NumConstant.NUM_8, app)){
                    sortAppointMap.put(app.getJdAppointmentId(),NumConstant.NUM_8);
                    valueAppointMap.put(NumConstant.NUM_8, app);
                    continue;
                }
            }
            if (NumConstant.NUM_2.equals(app.getOrderStatus())){// 预约中
                if (checkAppointmentDateSort(valueAppointMap, sortAppointMap, NumConstant.NUM_7, app)){
                    sortAppointMap.put(app.getJdAppointmentId(),NumConstant.NUM_7);
                    valueAppointMap.put(NumConstant.NUM_7, app);
                    continue;
                }
            }
            if (NumConstant.NUM_3.equals(app.getOrderStatus())){// 预约成功
                if (checkAppointmentDateSort(valueAppointMap, sortAppointMap, NumConstant.NUM_6, app)){
                    sortAppointMap.put(app.getJdAppointmentId(),NumConstant.NUM_6);
                    valueAppointMap.put(NumConstant.NUM_6, app);
                    continue;
                }
            }
            if (NumConstant.NUM_6.equals(app.getOrderStatus())){// 退款中
                if (checkAppointmentDateSort(valueAppointMap, sortAppointMap, NumConstant.NUM_5, app)){
                    sortAppointMap.put(app.getJdAppointmentId(),NumConstant.NUM_5);
                    valueAppointMap.put(NumConstant.NUM_5, app);
                    continue;
                }
            }
            if (NumConstant.NUM_4.equals(app.getOrderStatus())){// 已到检
                if (checkAppointmentDateSort(valueAppointMap, sortAppointMap, NumConstant.NUM_4, app)){
                    sortAppointMap.put(app.getJdAppointmentId(),NumConstant.NUM_4);
                    valueAppointMap.put(NumConstant.NUM_4, app);
                    continue;
                }
            }
            if (NumConstant.NUM_100.equals(app.getOrderStatus())){// 已出报告
                if (checkAppointmentDateSort(valueAppointMap, sortAppointMap, NumConstant.NUM_3, app)){
                    sortAppointMap.put(app.getJdAppointmentId(),NumConstant.NUM_3);
                    valueAppointMap.put(NumConstant.NUM_3, app);
                    continue;
                }
            }
            if (NumConstant.NUM_5.equals(app.getOrderStatus())){// 已完成
                if (checkAppointmentDateSort(valueAppointMap, sortAppointMap, NumConstant.NUM_2, app)){
                    sortAppointMap.put(app.getJdAppointmentId(),NumConstant.NUM_2);
                    valueAppointMap.put(NumConstant.NUM_2, app);
                    continue;
                }
            }
            if (NumConstant.NUM_8.equals(app.getOrderStatus())){// 退款完成
                if (checkAppointmentDateSort(valueAppointMap, sortAppointMap, NumConstant.NUM_1, app)){
                    sortAppointMap.put(app.getJdAppointmentId(),NumConstant.NUM_1);
                    valueAppointMap.put(NumConstant.NUM_1, app);
                    continue;
                }
            }
        }

        log.info("AbstractCustomOrderHandler selectAppointmentInfoDetail sortAppointMap={}", JSON.toJSONString(sortAppointMap));
        if (MapUtils.isNotEmpty(sortAppointMap)){
            String jdAppointmentId = getStringKeyWithMaxValue(sortAppointMap);
            log.info("AbstractCustomOrderHandler selectAppointmentInfoDetail jdAppointmentId={}", jdAppointmentId);
            if (jdAppointmentId != null){
                return appointMap.get(jdAppointmentId);
            }
        }
        return null;
    }


    public Boolean checkAppointmentDateSort(Map<Integer, AppointmentInfoDetail> valueAppointMap, Map<String, Integer> sortAppointMap, Integer valueStatus,  AppointmentInfoDetail app){
        AppointmentInfoDetail existApp = valueAppointMap.get(valueStatus);
        if (existApp == null){
            return true;
        }
        if (existApp.getAppointmentDate() == null || app.getAppointmentDate() == null){
            return true;
        }
        if (Long.parseLong(app.getAppointmentDate()) < Long.parseLong(existApp.getAppointmentDate())){
            sortAppointMap.remove(existApp.getJdAppointmentId());
            valueAppointMap.remove(valueStatus);
            return true;
        }
        return false;
    }

    public static String getStringKeyWithMaxValue(Map<String, Integer> map) {
        String maxKey = null;
        int maxValue = Integer.MIN_VALUE;
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            if (entry.getValue() > maxValue) {
                maxKey = entry.getKey();
                maxValue = entry.getValue();
            }
        }
        return maxKey;
    }


}
