package com.jdh.o2oservice.application.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.application.product.QuestionExtApplication;
import com.jdh.o2oservice.application.product.convert.QuestionApplicationConverter;
import com.jdh.o2oservice.application.product.service.QuestionServiceApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.enums.QuestionGroupTypeEnum;
import com.jdh.o2oservice.common.enums.QuestionTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.product.bo.JdhQuestionBO;
import com.jdh.o2oservice.core.domain.product.context.BindCareFormContext;
import com.jdh.o2oservice.core.domain.product.context.JdhQuestionContext;
import com.jdh.o2oservice.core.domain.product.model.*;
import com.jdh.o2oservice.core.domain.product.repository.cmd.QuestionRemoveDbCmd;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhGroupQuesRelRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhItemQuesGroupRelRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhQuestionRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.QuestionDbQuery;
import com.jdh.o2oservice.core.domain.product.service.JdhQuestionDomainService;
import com.jdh.o2oservice.core.domain.product.service.question.QuestionTypeAbstract;
import com.jdh.o2oservice.core.domain.product.service.question.QuestionTypeInterface;
import com.jdh.o2oservice.export.product.cmd.BindCareFormCmd;
import com.jdh.o2oservice.export.product.cmd.QuestionRemoveCmd;
import com.jdh.o2oservice.export.product.cmd.QuestionSaveCmd;
import com.jdh.o2oservice.export.product.dto.CareFormDTO;
import com.jdh.o2oservice.export.product.dto.JdhQuestionDto;
import com.jdh.o2oservice.export.product.dto.QuestionDTO;
import com.jdh.o2oservice.export.product.dto.QuestionGroupDto;
import com.jdh.o2oservice.export.product.query.CareFormDetailQuery;
import com.jdh.o2oservice.export.product.query.QueryQuesByGroupCode;
import com.jdh.o2oservice.export.product.query.QuestionDetailQuery;
import com.jdh.o2oservice.export.product.query.QuestionPageQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Slf4j
@Service
public class QuestionServiceApplicationImpl implements QuestionServiceApplication, QuestionExtApplication {

    @Autowired
    private JdhQuestionDomainService jdhQuestionDomainService;

    @Autowired
    private JdhGroupQuesRelRepository jdhGroupQuesRelRepository;


    @Autowired
    private JdhQuestionRepository jdhQuestionRepository;


    @Resource
    private Cluster jimClient;

    @Autowired
    private JdhServiceItemRepository jdhServiceItemRepository;

    private String CAREFORMCACHE = "careformcache_%s";

    @Autowired
    private JdhItemQuesGroupRelRepository jdhItemQuesGroupRelRepository;

    @Resource
    private DuccConfig duccConfig;


    @Override
    @LogAndAlarm
    public JdhQuestionDto findDetail(QuestionDetailQuery questionDetailQuery){
        JdhQuestionBO jdhQuestionBO = jdhQuestionDomainService.findDetail(questionDetailQuery.getQuesId());
        return QuestionApplicationConverter.INS.toJdhQuestionDto(jdhQuestionBO);
    }

    @Override
    @LogAndAlarm
    public Boolean remove(QuestionRemoveCmd questionRemoveCmd){
        QuestionRemoveDbCmd questionRemoveDbCmd =QuestionApplicationConverter.INS.toQuestionRemoveDbCmd(questionRemoveCmd);
        return jdhQuestionDomainService.remove(questionRemoveDbCmd);
    }


    @Override
    @LogAndAlarm
    public Boolean saveOrUpdate(QuestionSaveCmd questionSaveCmd){
        JdhQuestionContext jdhQuestionContext = QuestionApplicationConverter.INS.toJdhQuestionContext(questionSaveCmd);
        return jdhQuestionDomainService.saveOrUpdate(jdhQuestionContext);
    }


    @Override
    @LogAndAlarm
    public PageDto<QuestionDTO> findPage(QuestionPageQuery questionPageQuery){
        QuestionDbQuery questionDbQuery = QuestionApplicationConverter.INS.toQuestionDbQuery(questionPageQuery);
        Page<JdhQuestionBO> page = jdhQuestionDomainService.findPage(questionDbQuery);
        return QuestionApplicationConverter.INS.toPage(page);
    }

    /**
     * 保存护理单配置
     * @param bindCareFormCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean bindCareForm(BindCareFormCmd bindCareFormCmd) {
        BindCareFormContext bindCareFormContext = QuestionApplicationConverter.INS.toBindCareFormContext(bindCareFormCmd);
        return jdhQuestionDomainService.bindCareForm(bindCareFormContext);
    }

    /**
     * 运营B端查询题配置
     * @param careFormDetailQuery
     * @return
     */
    @LogAndAlarm
    @Override
    public CareFormDTO careFormDetail(CareFormDetailQuery careFormDetailQuery) {

        //查询所有节点和题关系
        List<JdhGroupQuesRel> jdhGroupQuesRels =  jdhGroupQuesRelRepository.findByItemId(careFormDetailQuery.getServiceItemId());
        if(CollectionUtils.isEmpty(jdhGroupQuesRels)){
            log.info("jdhGroupQuesRels 为空,逻辑终止");
            return null;
        }
        JdhItemQuesGroupRel jdhItemQuesGroupRel = new JdhItemQuesGroupRel();
        jdhItemQuesGroupRel.setServiceItemId(careFormDetailQuery.getServiceItemId());
        List<JdhItemQuesGroupRel> jdhItemQuesGroupRels = jdhItemQuesGroupRelRepository.findList(jdhItemQuesGroupRel);
        if(CollectionUtils.isEmpty(jdhItemQuesGroupRels)){
            log.info("jdhItemQuesGroupRels 为空,逻辑终止");
            return null;
        }
        //查询所有题详情
        QuestionDbQuery questionDbQuery = new QuestionDbQuery();
        questionDbQuery.setQuesIds(jdhGroupQuesRels.stream().map(t->Long.parseLong(t.getQuesId())).collect(Collectors.toList()));
        List<JdhQuestion> jdhQuestions = jdhQuestionRepository.findList(questionDbQuery);

        Map<String,List<JdhGroupQuesRel>> groupMap = jdhGroupQuesRels.stream().collect(Collectors.groupingBy(JdhGroupQuesRel::getGroupCode));
        Map<String,Map<String,Object>> groupMapTemp = new HashMap<>();
        groupMap.forEach((k,v)->{
            Map<String,Object> map = toMap(v,jdhQuestions.stream().collect(Collectors.groupingBy(JdhQuestion::getQuesId)));
            map.put("show",jdhItemQuesGroupRels.stream().filter(t->t.getGroupCode().equals(k)).findFirst().orElse(new JdhItemQuesGroupRel()).getShow());
            groupMapTemp.put(k,map);
        });

        ServiceItem serviceItem = jdhServiceItemRepository.find(ServiceItemIdentifier.builder().itemId(careFormDetailQuery.getServiceItemId()).build());
        CareFormDTO cacheCareFormDTO = JSON.parseObject(serviceItem.getCareRecordConfig(),CareFormDTO.class);

        CareFormDTO careFormDTO = JSON.parseObject(JSON.toJSONString(groupMapTemp),CareFormDTO.class);
        log.info("QuestionServiceApplicationImpl.careFormDetail careFormDTO={},cacheCareFormDTO={} ",JSON.toJSONString(careFormDTO),JSON.toJSONString(cacheCareFormDTO));
        QuestionApplicationConverter.INS.update(careFormDTO,cacheCareFormDTO);
        return cacheCareFormDTO;
    }

    /**
     * 给运营端多返回一些字段 比如 题库列表
     * @param jdhGroupQuesRels
     * @param quesMap
     * @return
     */
    private Map<String,Object> toMap(List<JdhGroupQuesRel> jdhGroupQuesRels,Map<Long,List<JdhQuestion>> quesMap){
        Map<String,Object> result = new HashMap<>();
        for (JdhGroupQuesRel jdhGroupQuesRel:jdhGroupQuesRels) {
            JSONObject jsonObject = JSONObject.parseObject(jdhGroupQuesRel.getExtJson());
            if(jsonObject!=null){
                JdhQuestion jdhQuestion = quesMap.get(Long.parseLong(jdhGroupQuesRel.getQuesId())).stream().findFirst().orElse(null);
                result.put(jdhGroupQuesRel.getQuesCode(),jsonObject.get("value"));
                QuestionTypeEnum questionTypeEnum =QuestionTypeEnum.getByCode(jdhQuestion.getType());
                if(questionTypeEnum!=null&& StringUtils.isNotEmpty(questionTypeEnum.getBeanName())){
                    QuestionTypeInterface questionCodeInterface = (QuestionTypeInterface)SpringUtil.getBean(questionTypeEnum.getBeanName());
                    Map<String,?> map = questionCodeInterface.toMap(jdhGroupQuesRel);
                    if(map!=null&&!map.isEmpty()){
                        result.putAll(questionCodeInterface.toMap(jdhGroupQuesRel));
                    }
                }
            }
        }
        return result;
    }

    /**
     * 护士端查询题配置
     * @param queryQuesByGroupCode
     * @return
     */
    @LogAndAlarm
    @Override
    public List<QuestionGroupDto> queryQuesByGroupCode(QueryQuesByGroupCode queryQuesByGroupCode) {

        List<QuestionGroupDto> questionGroupDtos = new ArrayList<>();

        try {
            //查询全部节点
            JdhItemQuesGroupRel jdhItemQuesGroupRelQuery = new JdhItemQuesGroupRel();
            jdhItemQuesGroupRelQuery.setServiceItemId(queryQuesByGroupCode.getServiceItemId());
            List<JdhItemQuesGroupRel> jdhItemQuesGroupRels = jdhItemQuesGroupRelRepository.findList(jdhItemQuesGroupRelQuery);

            //根据groupCode过滤数据
            if(StringUtils.isNotEmpty(queryQuesByGroupCode.getGroupCode())){
                jdhItemQuesGroupRels = jdhItemQuesGroupRels.stream().filter(t->t.getGroupCode().equals(queryQuesByGroupCode.getGroupCode())).collect(Collectors.toList());
            }

            //查询节点和题关系
            List<JdhGroupQuesRel> jdhGroupQuesRels =  jdhGroupQuesRelRepository.findByItemId(queryQuesByGroupCode.getServiceItemId());
            if(CollectionUtils.isEmpty(jdhGroupQuesRels)){
                log.info("QuestionServiceApplicationImpl-queryQuesByGroupCode jdhGroupQuesRels 为空,逻辑终止!!!");
                return null;
            }

            //查询题详情
            QuestionDbQuery questionDbQuery = new QuestionDbQuery();
            questionDbQuery.setQuesIds(jdhGroupQuesRels.stream().map(t->Long.parseLong(t.getQuesId())).collect(Collectors.toList()));
            List<JdhQuestion> jdhQuestions = jdhQuestionRepository.findList(questionDbQuery);
            Map<Long,List<JdhQuestion>> localQuestion = jdhQuestions.stream().collect(Collectors.groupingBy(JdhQuestion::getQuesId));
            jdhItemQuesGroupRels.forEach(jdhItemQuesGroupRel ->{
                //维护节点数据
                QuestionGroupDto questionGroupDto = JSON.parseObject(jdhItemQuesGroupRel.getGroupSnapshot(),QuestionGroupDto.class);
                questionGroupDto.setShow(jdhItemQuesGroupRel.getShow());
                questionGroupDtos.add(questionGroupDto);
                //过滤出当前节点和题关系
                List<JdhGroupQuesRel> currentNodeQuesRel = jdhGroupQuesRels.stream().filter(jdhGroupQuesRel -> jdhGroupQuesRel.getGroupCode().equals(jdhItemQuesGroupRel.getGroupCode())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(currentNodeQuesRel)){
                    log.info("QuestionServiceApplicationImpl-queryQuesByGroupCode currentNodeQuesRel 为空,逻辑终止!!!");
                    return;
                }
                List<QuestionDTO> questionDTOS = new ArrayList<>();
                //维护节点和题关系
                questionGroupDto.setQuestionDTOS(questionDTOS);
                //解析出对应的题id
                currentNodeQuesRel.forEach(jdhGroupQuesRel -> {
                    JdhQuestion question = localQuestion.get(Long.parseLong(jdhGroupQuesRel.getQuesId())).stream().findFirst().orElse(null);
                    QuestionTypeEnum questionTypeEnum = QuestionTypeEnum.getByCode(question.getType());
                    if(questionTypeEnum!=null&&StringUtils.isNotEmpty(questionTypeEnum.getBeanName())){
                        QuestionTypeAbstract questionTypeAbstract = (QuestionTypeAbstract)SpringUtil.getBean(questionTypeEnum.getBeanName());
                        //因为题(evaluatePoint)有一些共享的,所以这里要传入一个新的question对象;

                        List<JdhQuestion> questions = questionTypeAbstract.process(jdhGroupQuesRel,JSON.parseObject(JSON.toJSONString(question),JdhQuestion.class));
                        if(CollectionUtils.isNotEmpty(questions)){
                            questionDTOS.addAll(QuestionApplicationConverter.INS.toJdhQuestionDtos(questions));
                        }
                    }else{
                        questionDTOS.add(QuestionApplicationConverter.INS.toJdhQuestionDto(question));
                    }
                });
            });
            log.info("QuestionServiceApplicationImpl.queryQuesByGroupCode questionGroupDtos={}",JSON.toJSONString(questionGroupDtos));
            this.serviceRecordImageUpload(queryQuesByGroupCode, questionGroupDtos);

            this.serviceRecordPdfSignature(questionGroupDtos);
        } catch (Exception e) {
            log.error("QuestionServiceApplicationImpl.queryQuesByGroupCode error e", e);
        }
        return questionGroupDtos;
    }

    private void serviceRecordPdfSignature(List<QuestionGroupDto> questionGroupDtos){
        for (QuestionGroupDto questionGroupDto : questionGroupDtos) {
            // 签字
            if (QuestionGroupTypeEnum.PRESERVICESIGNATURE.getCode().equals(questionGroupDto.getCode()) ||
                    QuestionGroupTypeEnum.SIGNCONFIRM.getCode().equals(questionGroupDto.getCode())){
                List<QuestionDTO> newQuestionDTOS = new ArrayList<>();
                String value = "";
                Integer required = null;
                for (QuestionDTO questionDTO : questionGroupDto.getQuestionDTOS()) {
                    if (questionDTO.getType().equals(4)){
                        value = questionDTO.getValue();
                        required = questionDTO.getRequired();
                    }
                }
                for (QuestionDTO questionDTO : questionGroupDto.getQuestionDTOS()) {
                    if (questionDTO.getType().equals(5)){
                        questionDTO.setValue(value);
                        if(required != null) {
                            questionDTO.setRequired(required);
                        }
                        newQuestionDTOS.add(questionDTO);
                    }
                }
                questionGroupDto.setQuestionDTOS(newQuestionDTOS);
            }
        }
    }

    private void serviceRecordImageUpload(QueryQuesByGroupCode queryQuesByGroupCode, List<QuestionGroupDto> questionGroupDtos) {
        try {
            if (StringUtils.isNotBlank(queryQuesByGroupCode.getBusinessMode()) && StringUtils.isNotBlank(queryQuesByGroupCode.getServiceType())
                    && CollectionUtils.isNotEmpty(questionGroupDtos)){
                log.info("QuestionServiceApplicationImpl serviceRecordImageUpload before questionGroupDtos={}", JSON.toJSONString(questionGroupDtos));
                // 规则入参
                Map<String, Object> param = new HashMap<>();
                param.put("businessMode", queryQuesByGroupCode.getBusinessMode());
                param.put("serviceType", queryQuesByGroupCode.getServiceType());
                // 匹配的图片上传问题id
                List<String> imageUploadQuesCodeList = new ArrayList<>();
                // 服务记录图片上传配置
                JSONArray arr = JSON.parseArray(duccConfig.getServiceRecordImageUploadConfig());
                for (int i = 0; i < arr.size(); i++) {
                    JSONObject obj = arr.getJSONObject(i);
                    if ((Boolean) AviatorEvaluator.compile(obj.getString("statusExpression"), Boolean.TRUE).execute(param)) {
                        imageUploadQuesCodeList = JSONObject.parseArray(obj.getString("imageUploadQuesCodeList"), String.class);
                        break;
                    }
                }
                log.info("QuestionServiceApplicationImpl serviceRecordImageUpload imageUploadQuesCodeList={}", JSON.toJSONString(imageUploadQuesCodeList));

                if (CollectionUtils.isEmpty(imageUploadQuesCodeList)){
                    return;
                }

                for (QuestionGroupDto questionGroupDto : questionGroupDtos) {
                    // 记录上传
                    if (QuestionGroupTypeEnum.RECORDUPLOAD.getCode().equals(questionGroupDto.getCode())){
                        List<QuestionDTO> newQuestionDTOS = new ArrayList<>();
                        for (QuestionDTO questionDTO : questionGroupDto.getQuestionDTOS()) {
                            if (imageUploadQuesCodeList.contains(questionDTO.getQuesCode())){
                                newQuestionDTOS.add(questionDTO);
                            }
                        }
                        questionGroupDto.setQuestionDTOS(newQuestionDTOS);
                    }
                }
                log.info("QuestionServiceApplicationImpl serviceRecordImageUpload after questionGroupDtos={}", JSON.toJSONString(questionGroupDtos));
            }
        } catch (Exception e) {
            log.error("QuestionServiceApplicationImpl serviceRecordImageUpload error e", e);
        }
    }
}
