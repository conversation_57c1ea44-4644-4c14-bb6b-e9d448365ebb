package com.jdh.o2oservice.application.via.handler.floor;


import com.alibaba.fastjson.JSON;
import com.googlecode.aviator.AviatorEvaluator;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.ServiceSurveyConfig;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.feedback.JdhUserServiceSurvey;
import com.jdh.o2oservice.core.domain.support.feedback.repository.db.UserServiceSurveyRepository;
import com.jdh.o2oservice.core.domain.support.feedback.repository.query.JdhUserServiceSurveyQuery;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.model.ViaActionInfo;
import com.jdh.o2oservice.core.domain.support.via.model.ViaFloorConfig;
import com.jdh.o2oservice.core.domain.support.via.model.ViaFloorInfo;
import com.jdh.o2oservice.core.domain.support.via.model.ViaStatusMapping;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/2
 * @description 用户问卷调查楼层
 */
@Slf4j
@Component
public class UserServiceSurveyFloor implements Floor {

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private UserServiceSurveyRepository userServiceSurveyRepository;

    @Override
    public void handleData(FillViaConfigDataContext ctx,Map<String, Object> sourceData ,ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping, JdhPromise jdhPromise, JdOrderDTO jdOrder, List<JdhPromiseHistory> promiseHistories, List<MedicalPromiseDTO> medicalPromiseList) {
        log.info("UserServiceSurveyFloor.handleData viaFloorInfo={}", JSON.toJSONString(viaFloorInfo));
        JdhUserServiceSurveyQuery jdhUserServiceSurveyQuery = new JdhUserServiceSurveyQuery();
        jdhUserServiceSurveyQuery.setPromiseId(jdhPromise.getPromiseId());
        List<JdhUserServiceSurvey> jdhUserServiceSurveys = userServiceSurveyRepository.query(jdhUserServiceSurveyQuery);
        if(CollectionUtils.isNotEmpty(jdhUserServiceSurveys)){
            log.info("UserServiceSurveyFloor.handleData 用户已维护nps问卷,该楼层不在展示!!!");
            viaFloorInfo.setRemove(true);
            return;
        }
        if(!JdhPromiseStatusEnum.COMPLETE.getStatus().equals(jdhPromise.getPromiseStatus())){
            log.info("UserServiceSurveyFloor.handleData 履约单状态未完成,nps问卷楼层不展示!!!");
            viaFloorInfo.setRemove(true);
            return;
        }
        ServiceSurveyConfig serviceSurveyConfig = duccConfig.getServiceSurveyConfig();

        List<ServiceSurveyConfig.SubOption> subOptions = serviceSurveyConfig.getSubOptions();
        Map<String, Object> map = new HashMap<>();
        map.put("jdhPromise", jdhPromise);
        serviceSurveyConfig.setSubOptions(subOptions.stream().filter(t -> (boolean) AviatorEvaluator.compile(t.getHitRule(), Boolean.TRUE).execute(map)).collect(Collectors.toList()));

        ViaFloorConfig viaFloorConfig = new ViaFloorConfig();
        viaFloorConfig.setValue(JSON.toJSONString(serviceSurveyConfig));
        ViaActionInfo action = viaFloorInfo.getFloorConfigList().get(0).getAction();
        action.init(sourceData);
        viaFloorConfig.setAction(action);
        viaFloorInfo.setFloorConfigList(Collections.singletonList(viaFloorConfig));
    }
}

