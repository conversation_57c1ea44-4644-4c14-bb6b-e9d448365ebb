package com.jdh.o2oservice.application.trade.handler.order;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessment;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssDetailBaseDTO;
import com.jdh.o2oservice.export.trade.dto.CustomOrderInfoDTO;
import com.jdh.o2oservice.export.trade.dto.CustomOrderProcessTrackDTO;
import com.jdh.o2oservice.export.trade.dto.CustomOrderTransferInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 自定义消费医疗虚拟订单（ordertype=4&sendpay352-354=099）
 * https://joyspace.jd.com/pages/JHRRSsFaHZMD4nsLVoOA
 * @Date 2024/8/31 下午11:03
 * <AUTHOR>
 **/
@Component
@Slf4j
public class CustomXFYLVirtualOrderHandler extends AbstractCustomOrderHandler implements CustomOrderStrategy {

    @Resource
    private DuccConfig duccConfig;

    @Autowired
    private ProductApplication productApplication;

    @Override
    public String getKey() {
        return CustomOrderStrategy.XFYL_VTP_VIRTUAL_ORDER;
    }

    @Override
    public CustomOrderInfoDTO handle(CustomOrderHandlerContext receipt) {
        log.info("CustomXFYLVirtualOrderHandler handle receipt={}", JSON.toJSONString(receipt));
        if (!careTestServiceType.contains(receipt.getOrder().getServiceType())){
            log.info("CustomXFYLVirtualOrderHandler handle serviceType no mate");
            return null;
        }
        // 构建标签
        CustomOrderInfoDTO res = this.buildCustomOrderLabelInfo(receipt.getOrder(), JSON.parseObject(duccConfig.getCustomOrderInfoConfig()));
        // 履约单
        List<JdhPromise> promiseList = receipt.getSourceVoucherIdMappingPromiseMap().get(receipt.getOrderIdMappingSourceVoucherIdMap().get(receipt.getOrder().getOrderId()));
        log.info("CustomXFYLVirtualOrderHandler handle promiseList={}", JSON.toJSONString(promiseList));
        if (CollectionUtils.isEmpty(promiseList)){
            return res;
        }
        // 待预约的预约单
        List<JdhPromise> waitPromiseList = promiseList.stream().filter(p -> waitPromiseStatus.contains(p.getPromiseStatus())).collect(Collectors.toList());
        log.info("CustomXFYLVirtualOrderHandler handle waitPromiseList={}, orderStatusId={}", JSON.toJSONString(waitPromiseList), receipt.getOrderStatusId());
        // 护士上门护理
        JSONObject nurseVisitCareObj = JSON.parseObject(duccConfig.getVtpHomeCareOrderList()).getJSONObject("processTrack");
        // 护士上门检测
        JSONObject nurseVisitTestObj = JSON.parseObject(duccConfig.getVtpHomeTestOrderList()).getJSONObject("processTrack");
        // 业务身份
        JdhVerticalBusiness verticalBusiness = receipt.getVerticalBusinessMap().get(receipt.getOrder().getVerticalCode());

        // 支付成功，产码成功，没有在行履约单
        if (CollectionUtils.isNotEmpty(waitPromiseList)){
            log.info("CustomXFYLVirtualOrderHandler handle no waitPromise orderId={}", receipt.getOrder().getOrderId());
            res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(waitPromiseList.get(0))));
        }

        // 退款中
        if (OrderStatusEnum.ORDER_REFUNDING.getStatus().equals(receipt.getOrder().getOrderStatus())){
            log.info("CustomXFYLVirtualOrderHandler handle 退款中 orderId={}", receipt.getOrder().getOrderId());
            res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(promiseList.get(0))));
        }

        if (BusinessModeEnum.ANGEL_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())){
            log.info("CustomXFYLVirtualOrderHandler handle 护士上门检测 orderId={}", receipt.getOrder().getOrderId());
            // 护士上门检测
            this.buildNurseVisitTestInfo(receipt, res, nurseVisitTestObj);

        }else if (BusinessModeEnum.ANGEL_CARE.getCode().equals(verticalBusiness.getBusinessModeCode())){
            log.info("CustomXFYLVirtualOrderHandler handle 护士上门护理 orderId={}", receipt.getOrder().getOrderId());
            // 护士上门护理
            this.buildNurseVisitCareInfo(receipt, res, nurseVisitCareObj);

        }
        return res;
    }

    /**
     * 构建护士上门护理信息
     * @param receipt
     * @param res
     * @param nurseVisitCareObj
     */
    private void buildNurseVisitCareInfo(CustomOrderHandlerContext receipt, CustomOrderInfoDTO res, JSONObject nurseVisitCareObj) {
        // 履约单
        JdhPromise promise = this.selectProcessTrackOptimalPromise(receipt);
        log.info("CustomXFYLVirtualOrderHandler buildNurseVisitCareInfo promise={}", JSON.toJSONString(promise));
        if (Objects.isNull(promise)){
            return;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus",receipt.getOrder().getOrderStatus());
        param.put("promiseStatus",promise.getPromiseStatus());
        param.put("medPromiseStatusList", this.getMedicalPromiseStatusByPromiseId(receipt, promise.getPromiseId()));
        List<RiskAssessment> riskAssessmentList = receipt.getPromiseIdMappingRiskAssessmentMap().get(promise.getPromiseId());
        param.put("riskAssessmentStatus", CollectionUtils.isEmpty(riskAssessmentList) ? null : riskAssessmentList.get(0).getRiskAssessmentStatus());
        param.put("highRiskAssessmentFlag", highRiskFlag(promise));
        log.info("CustomXFYLVirtualOrderHandler buildNurseVisitCareInfo param={}", JSON.toJSONString(param));

        // 定列透传自定义对象
        CustomOrderTransferInfoDTO customOrderTransferInfoDTO = new CustomOrderTransferInfoDTO();
        buildCustomOrderTransferInfoAllServiceNodeInfoDTO(nurseVisitCareObj, customOrderTransferInfoDTO);

        if ((Boolean) AviatorEvaluator.compile(nurseVisitCareObj.getJSONObject("waitRiskAssessment").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitCareInfo 待评估 orderId={}", receipt.getOrder().getOrderId());
            JSONObject waitNurseReceiveOrder = nurseVisitCareObj.getJSONObject("waitRiskAssessment");
            res.setProcessTrack(this.buildWaitRiskAssessmentV2(receipt, promise, waitNurseReceiveOrder));
            buildCustomOrderTransferInfoDTO(waitNurseReceiveOrder, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitCareObj.getJSONObject("waitNurseReceiveOrder").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitCareInfo 待护士接单 orderId={}", receipt.getOrder().getOrderId());
            JSONObject waitNurseReceiveOrder = nurseVisitCareObj.getJSONObject("waitNurseReceiveOrder");
            CustomOrderProcessTrackDTO processTrack = this.buildWaitNurseReceiveOrderProcessTrackV2(receipt, receipt.getPromiseIdMappingPromiseHistoryMap().get(promise.getPromiseId()), promise, waitNurseReceiveOrder);
            res.setProcessTrack(processTrack);
            res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(promise)));
            buildUrl(processTrack, promise, customOrderTransferInfoDTO);
            buildCustomOrderTransferInfoDTO(waitNurseReceiveOrder, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitCareObj.getJSONObject("waitNurseVisit").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitCareInfo 待护士上门 orderId={}", receipt.getOrder().getOrderId());
            JSONObject waitNurseVisit = nurseVisitCareObj.getJSONObject("waitNurseVisit");
            CustomOrderProcessTrackDTO processTrack = this.buildWaitNurseVisitProcessTrackV2(receipt, promise, waitNurseVisit);
            res.setProcessTrack(processTrack);
            res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(promise)));
            buildUrl(processTrack, promise, customOrderTransferInfoDTO);
            buildCustomOrderTransferInfoDTO(waitNurseVisit, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitCareObj.getJSONObject("nurseVisitProgress").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitCareInfo 护士上门中 orderId={}", receipt.getOrder().getOrderId());
            JSONObject waitNurseVisit = nurseVisitCareObj.getJSONObject("nurseVisitProgress");
            CustomOrderProcessTrackDTO processTrack = this.buildNurseVisitProgressProcessTrackV2(receipt, promise, waitNurseVisit);
            res.setProcessTrack(processTrack);
            res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(promise)));
            buildUrl(processTrack, promise, customOrderTransferInfoDTO);
            buildCustomOrderTransferInfoDTO(waitNurseVisit, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitCareObj.getJSONObject("waitServiceProgress").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitCareInfo 护士服务中 orderId={}", receipt.getOrder().getOrderId());
            JSONObject waitServiceProgress = nurseVisitCareObj.getJSONObject("waitServiceProgress");
            CustomOrderProcessTrackDTO processTrack = this.buildWaitServiceProgressProcessTrackV2(waitServiceProgress);
            res.setProcessTrack(processTrack);
            res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(promise)));
            buildUrl(processTrack, promise, customOrderTransferInfoDTO);
            buildCustomOrderTransferInfoDTO(waitServiceProgress, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitCareObj.getJSONObject("serviceFinish").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitCareInfo 服务完成 orderId={}", receipt.getOrder().getOrderId());
            List<JdhPromise> promiseList = receipt.getSourceVoucherIdMappingPromiseMap().get(receipt.getOrderIdMappingSourceVoucherIdMap().get(receipt.getOrder().getOrderId()));
            if (CollectionUtils.isNotEmpty(promiseList)){
                // 仅当订单内最后一次服务用完时，才展示服务已完成
                boolean allPromiseCompleteFlag = promiseList.stream().allMatch(p -> Arrays.asList(JdhPromiseStatusEnum.COMPLETE.getStatus(), JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus())
                        .contains(p.getPromiseStatus()));
                if (allPromiseCompleteFlag){
                    log.info("CustomXFYLVirtualOrderHandler buildNurseVisitCareInfo all 服务完成 orderId={}", receipt.getOrder().getOrderId());
                    JSONObject serviceFinish = nurseVisitCareObj.getJSONObject("serviceFinish");
                    CustomOrderProcessTrackDTO processTrack = this.buildServiceFinishProcessTrackHome(serviceFinish);
                    res.setProcessTrack(processTrack);
                    res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(promise)));
                    buildUrl(processTrack, promise, customOrderTransferInfoDTO);
                    buildCustomOrderTransferInfoDTO(serviceFinish, res, customOrderTransferInfoDTO, promise);
                }
            }
        }
        res.setTransferInfo(customOrderTransferInfoDTO);

        this.customOrderReplaceWords(receipt, res,"xfylVtpHomeCareOrderList");
    }

    /**
     * 构建跳转地址
     *
     * @param processTrack
     * @param promise
     */
    private void buildUrl(CustomOrderProcessTrackDTO processTrack, JdhPromise promise, CustomOrderTransferInfoDTO customOrderTransferInfoDTO) {
        String orderDetailUrl = MessageFormat.format(processTrack.getUrl(), String.valueOf(promise.getPromiseId()));
        processTrack.setUrl(orderDetailUrl);
        customOrderTransferInfoDTO.setJumpDetailUrl(orderDetailUrl);
    }

    /**
     * 构建护士上门检测信息
     * @param receipt
     * @param res
     * @param nurseVisitTestObj
     */
    private void buildNurseVisitTestInfo(CustomOrderHandlerContext receipt, CustomOrderInfoDTO res, JSONObject nurseVisitTestObj) {
        // 履约单
        JdhPromise promise = this.selectProcessTrackOptimalPromise(receipt);
        log.info("CustomXFYLVirtualOrderHandler buildNurseVisitTestInfo promise={}", JSON.toJSONString(promise));
        if (Objects.isNull(promise)){
            return;
        }

        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus",receipt.getOrder().getOrderStatus());
        param.put("promiseStatus",promise.getPromiseStatus());
        param.put("medPromiseStatusList", this.getMedicalPromiseStatusByPromiseId(receipt, promise.getPromiseId()));
        log.info("CustomXFYLVirtualOrderHandler buildNurseVisitTestInfo param={}", JSON.toJSONString(param));

        // 定列透传自定义对象
        CustomOrderTransferInfoDTO customOrderTransferInfoDTO = new CustomOrderTransferInfoDTO();
        buildCustomOrderTransferInfoAllServiceNodeInfoDTO(nurseVisitTestObj, customOrderTransferInfoDTO);

        if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("waitNurseReceiveOrder").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitTestInfo 待护士接单 orderId={}", receipt.getOrder().getOrderId());
            JSONObject waitNurseReceiveOrder = nurseVisitTestObj.getJSONObject("waitNurseReceiveOrder");
            CustomOrderProcessTrackDTO processTrack = this.buildWaitNurseReceiveOrderProcessTrack(receipt.getPromiseIdMappingPromiseHistoryMap().get(promise.getPromiseId()), promise, waitNurseReceiveOrder);
            buildUrl(processTrack, promise, customOrderTransferInfoDTO);
            res.setProcessTrack(processTrack);
            res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(promise)));
            buildCustomOrderTransferInfoDTO(waitNurseReceiveOrder, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("waitNurseVisit").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitTestInfo 待护士上门 orderId={}", receipt.getOrder().getOrderId());
            JSONObject waitNurseVisit = nurseVisitTestObj.getJSONObject("waitNurseVisit");
            CustomOrderProcessTrackDTO processTrack = this.buildWaitNurseVisitProcessTrack(receipt.getPromiseIdMappingAngelWorkMap().get(promise.getPromiseId()), promise, waitNurseVisit);
            buildUrl(processTrack, promise, customOrderTransferInfoDTO);
            res.setProcessTrack(processTrack);
            res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(promise)));
            buildCustomOrderTransferInfoDTO(waitNurseVisit, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("waitServiceProgress").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitTestInfo 护士服务中 orderId={}", receipt.getOrder().getOrderId());
            JSONObject waitServiceProgress = nurseVisitTestObj.getJSONObject("waitServiceProgress");
            Long skuId = receipt.getOrderIdMappingOrderItemMap().get(receipt.getOrder().getOrderId()).get(0).getSkuId();
            JdhSkuDto jdhSkuDto = querySkuInfo(skuId);
            CustomOrderProcessTrackDTO processTrack = this.buildWaitServiceProgressProcessTrack(receipt.getPromiseIdMappingPromiseHistoryMap().get(promise.getPromiseId()), receipt.getPromiseIdMappingAngelWorkMap().get(promise.getPromiseId()), promise, jdhSkuDto.getServiceDuration(), waitServiceProgress);
            buildUrl(processTrack, promise, customOrderTransferInfoDTO);
            res.setProcessTrack(processTrack);
            res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(promise)));
            buildCustomOrderTransferInfoDTO(waitServiceProgress, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("underInspection").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitTestInfo 送检中 orderId={}", receipt.getOrder().getOrderId());
            JSONObject underInspection = nurseVisitTestObj.getJSONObject("underInspection");
            CustomOrderProcessTrackDTO processTrack = this.buildUnderInspectionProcessTrack(receipt.getPromiseIdMappingPromiseHistoryMap().get(promise.getPromiseId()), promise, underInspection);
            buildUrl(processTrack, promise, customOrderTransferInfoDTO);
            res.setProcessTrack(processTrack);
            res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(promise)));
            buildCustomOrderTransferInfoDTO(underInspection, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("laboratoryTesting").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitTestInfo 实验室检测中 orderId={}", receipt.getOrder().getOrderId());
            JSONObject laboratoryTesting = nurseVisitTestObj.getJSONObject("laboratoryTesting");
            CustomOrderProcessTrackDTO processTrack = this.buildLaboratoryTestingProcessTrack(promise, receipt.getPromiseIdMappingMedicalPromiseMap().get(promise.getPromiseId()), laboratoryTesting);
            buildUrl(processTrack, promise, customOrderTransferInfoDTO);
            res.setProcessTrack(processTrack);
            res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(promise)));
            buildCustomOrderTransferInfoDTO(laboratoryTesting, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("viewReport").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitTestInfo 报告已生成 orderId={}", receipt.getOrder().getOrderId());
            JSONObject viewReport = nurseVisitTestObj.getJSONObject("viewReport");
            CustomOrderProcessTrackDTO processTrack = this.buildLaboratoryTestingProcessTrack(promise, receipt.getPromiseIdMappingMedicalPromiseMap().get(promise.getPromiseId()), viewReport);
            res.setProcessTrack(processTrack);
            buildUrl(processTrack, promise, customOrderTransferInfoDTO);
            buildCustomOrderTransferInfoDTO(viewReport, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("serviceFinish").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLVirtualOrderHandler buildNurseVisitTestInfo 服务已完成 orderId={}", receipt.getOrder().getOrderId());
            List<JdhPromise> promiseList = receipt.getSourceVoucherIdMappingPromiseMap().get(receipt.getOrderIdMappingSourceVoucherIdMap().get(receipt.getOrder().getOrderId()));
            if (CollectionUtils.isNotEmpty(promiseList)){
                List<JdhPromise> promiseCompleteList = promiseList.stream().filter(p-> Arrays.asList(JdhPromiseStatusEnum.COMPLETE.getStatus(), JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus())
                        .contains(p.getPromiseStatus())).collect(Collectors.toList());
                // 仅当订单内最后一次服务用完时，才展示服务已完成
                if (promiseList.size() == promiseCompleteList.size()){
                    log.info("CustomXFYLVirtualOrderHandler buildNurseVisitTestInfo all 服务已完成 orderId={}", receipt.getOrder().getOrderId());
                    JSONObject serviceFinish = nurseVisitTestObj.getJSONObject("serviceFinish");
                    CustomOrderProcessTrackDTO processTrack =this.buildServiceFinishProcessTrackHome(serviceFinish);
                    res.setProcessTrack(processTrack);
                    buildUrl(processTrack, promise, customOrderTransferInfoDTO);
                    buildCustomOrderTransferInfoDTO(serviceFinish, res, customOrderTransferInfoDTO, promise);
                }
            }
        }
        res.setTransferInfo(customOrderTransferInfoDTO);
    }


    /**
     * 查询SKU信息
     * @param skuId
     * @return
     */
    private JdhSkuDto querySkuInfo(Long skuId){
        try {
            Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder()
                    .querySkuCoreData(Boolean.TRUE)
                    //.queryServiceItem(Boolean.TRUE)
                    .skuIdList(Sets.newHashSet(skuId))
                    .build());
            log.info("CustomXFYLVirtualOrderHandler querySkuInfo skuId={}, jdhSkuDtoMap={}", skuId, JSON.toJSONString(jdhSkuDtoMap));
            return jdhSkuDtoMap.get(skuId);
        }catch (Exception e){
            log.info("CustomXFYLVirtualOrderHandler handle querySkuInfo exception",e);
            return null;
        }
    }

}
