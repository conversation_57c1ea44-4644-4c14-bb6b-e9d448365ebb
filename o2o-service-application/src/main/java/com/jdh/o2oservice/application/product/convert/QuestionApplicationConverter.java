package com.jdh.o2oservice.application.product.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.product.bo.JdhQuestionBO;
import com.jdh.o2oservice.core.domain.product.context.BindCareFormContext;
import com.jdh.o2oservice.core.domain.product.context.JdhQuestionContext;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.repository.cmd.QuestionRemoveDbCmd;
import com.jdh.o2oservice.core.domain.product.repository.query.QuestionDbQuery;
import com.jdh.o2oservice.export.product.cmd.BindCareFormCmd;
import com.jdh.o2oservice.export.product.cmd.QuestionRemoveCmd;
import com.jdh.o2oservice.export.product.cmd.QuestionSaveCmd;
import com.jdh.o2oservice.export.product.dto.CareFormDTO;
import com.jdh.o2oservice.export.product.dto.JdhQuestionDto;
import com.jdh.o2oservice.export.product.dto.QuestionDTO;
import com.jdh.o2oservice.export.product.dto.careform.*;
import com.jdh.o2oservice.export.product.query.QuestionPageQuery;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Mapper(nullValuePropertyMappingStrategy= NullValuePropertyMappingStrategy.IGNORE)
public interface QuestionApplicationConverter {

    QuestionApplicationConverter INS = Mappers.getMapper(QuestionApplicationConverter.class);

    JdhQuestionDto toJdhQuestionDto(JdhQuestionBO jdhQuestionBO);

    JdhQuestionContext toJdhQuestionContext(QuestionSaveCmd questionSaveCmd);

    QuestionDbQuery toQuestionDbQuery(QuestionPageQuery questionPageQuery);
    @Mapping(target = "totalPage", source = "pages")
    @Mapping(target = "pageNum", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "totalCount", source = "total")
    @Mapping(target = "list", source = "records")
    PageDto<QuestionDTO> toPage(Page<JdhQuestionBO> page);

    QuestionRemoveDbCmd toQuestionRemoveDbCmd(QuestionRemoveCmd questionRemoveCmd);

    BindCareFormContext toBindCareFormContext(BindCareFormCmd bindCareFormCmd);

    void update(CareFormDTO careFormDTO, @MappingTarget CareFormDTO cacheCareFormDTO);

    void update(PreReceiveAssessmentDto preReceiveAssessmentDto,@MappingTarget PreReceiveAssessmentDto preReceiveAssessmentDto1);

    void update(PreServiceAssessmentDto preServiceAssessmentDto, @MappingTarget PreServiceAssessmentDto preServiceAssessmentDto1);

    void update(PreServiceSignatureDto preServiceSignatureDto, @MappingTarget PreServiceSignatureDto preServiceSignatureDto1);

    void update(ServiceRecordDto serviceRecordDto, @MappingTarget ServiceRecordDto serviceRecordDto1);

    void update(HealthEduDto healthEduDto, @MappingTarget HealthEduDto healthEduDto1);

    void update(SignConfirmDto signConfirmDto, @MappingTarget SignConfirmDto signConfirmDto1);

    QuestionDTO toJdhQuestionDto(JdhQuestion question);

    List<QuestionDTO> toJdhQuestionDtos(List<JdhQuestion> questions);
}
