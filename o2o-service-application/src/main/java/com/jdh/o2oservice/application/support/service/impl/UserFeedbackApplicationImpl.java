package com.jdh.o2oservice.application.support.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.support.convert.UserFeedbackConverter;
import com.jdh.o2oservice.application.support.service.UserFeedbackApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.support.feedback.JdhFeedbackConfig;
import com.jdh.o2oservice.core.domain.support.feedback.JdhFeedbackConfigDictAdaptor;
import com.jdh.o2oservice.core.domain.support.feedback.JdhUserFeedback;
import com.jdh.o2oservice.core.domain.support.feedback.JdhUserFeedbackIdentifier;
import com.jdh.o2oservice.core.domain.support.feedback.enums.UserFeedbackErrorCode;
import com.jdh.o2oservice.core.domain.support.feedback.repository.db.UserFeedbackRepository;
import com.jdh.o2oservice.core.domain.support.feedback.repository.query.JdhFeedbackConfigDbQuery;
import com.jdh.o2oservice.core.domain.support.feedback.repository.query.JdhFeedbackDbQuery;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.support.command.DistributeFeedbackCmd;
import com.jdh.o2oservice.export.user.cmd.SubmitUserFeedbackCmd;
import com.jdh.o2oservice.export.user.dto.UserFeedbackAggregationDTO;
import com.jdh.o2oservice.export.user.dto.UserFeedbackDTO;
import com.jdh.o2oservice.export.user.query.UserFeedbackRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName UserFeedbackApplicationImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/1/17 13:47
 **/
@Slf4j
@Component
public class UserFeedbackApplicationImpl implements UserFeedbackApplication {

    /**
     * userFeedbackRepository
     */
    @Resource
    private UserFeedbackRepository userFeedbackRepository;

    /**
     *
     */
    @Resource
    private VerticalBusinessRepository businessRepository;

    /**
     * promiseApplication
     */
    @Resource
    private PromiseApplication promiseApplication;

    @Autowired
    private ProductApplication productApplication;

    /**
     * 分配反馈问题（履约单维度）
     * @param cmd
     */
    @Override
    @LogAndAlarm
    public void distributeFeedback(DistributeFeedbackCmd cmd) {
        if(Objects.isNull(cmd) || Objects.isNull(cmd.getPromiseId())) {
            log.info("UserFeedbackApplicationImpl -> distributeFeedback error, 未获取到履约单号");
            return;
        }
        //根据PromiseId查询履约单
        PromiseDto promise = promiseApplication.findPromiseByPromiseId(PromiseIdRequest.builder().promiseId(cmd.getPromiseId()).build());
        if (Objects.isNull(promise)) {
            log.info("UserFeedbackApplicationImpl -> distributeFeedback error, 未获取到履约单信息, cmd={}", JSON.toJSONString(cmd));
            return;
        }
        //查询当前单据对应的（业务模式 + 业务身份）
        JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(promise.getVerticalCode());
        if(Objects.isNull(jdhVerticalBusiness)) {
            log.info("UserFeedbackApplicationImpl -> distributeFeedback error, 未获取到业务身份信息, promise={}", JSON.toJSONString(promise));
            return;
        }
        //幂等校验 查询数据库判断当前单据是否已经分配过题目，如果已分配则不处理
        List<JdhUserFeedback> jdhUserFeedbackList = userFeedbackRepository.queryUserFeedbackList(JdhFeedbackDbQuery.builder().userPin(promise.getUserPin()).promiseId(promise.getPromiseId()).businessScene(cmd.getFeedbackScene()).build());
        if (CollectionUtils.isNotEmpty(jdhUserFeedbackList)) {
            log.info("UserFeedbackApplicationImpl -> distributeFeedback error, 当前单据已经分配过题目，不处理, promise={}", JSON.toJSONString(promise));
            return;
        }

        //获取题目配置（业务模式 + 业务身份）
        List<JdhFeedbackConfigDictAdaptor> list = userFeedbackRepository.queryUserFeedbackBusinessModeConfigList(
                JdhFeedbackConfigDbQuery.builder().feedbackScene(cmd.getFeedbackScene()).businessMode(jdhVerticalBusiness.getBusinessModeCode())
                        .verticalCode(jdhVerticalBusiness.getVerticalCode()).build());
        log.info("UserFeedbackApplicationImpl -> distributeFeedback adaptorList={}", JSON.toJSONString(list));
        if (CollectionUtils.isEmpty(list)) {
            log.info("UserFeedbackApplicationImpl -> distributeFeedback error, 未获取到业务身份对应题库配置不处理, jdhVerticalBusiness={}", JSON.toJSONString(jdhVerticalBusiness));
            return;
        }
        JdhFeedbackConfigDictAdaptor jdhFeedbackConfigDictAdaptor = list.get(0);
        //开始分配题目
        List<JdhUserFeedback> saveList = new ArrayList<>();
        //1.归堆分配
        if (Objects.equals(jdhFeedbackConfigDictAdaptor.getDistributeType(), 1)) {
            //问题分堆
            Map<String, List<JdhFeedbackConfig>> groupMap = jdhFeedbackConfigDictAdaptor.getQuestionList().stream().collect(Collectors.groupingBy(JdhFeedbackConfig::getGroup));
            //分堆后每堆的选题策略
            Map<String, String> groupDistributeExpressionMap = jdhFeedbackConfigDictAdaptor.getGroupDistributeExpressionMap();

            //根据group排序
            Set<String> keySet = groupMap.keySet();
            List<String> keyList = keySet.stream().sorted().collect(Collectors.toList());
            //选取反馈问题
            for (String key : keyList) {
                List<JdhFeedbackConfig> jdhFeedbackConfigs = groupMap.get(key);
                String expressionStr = groupDistributeExpressionMap.get(key);
                log.info("UserFeedbackApplicationImpl -> distributeFeedback 选取反馈问题, list={}, expression={}", JSON.toJSONString(jdhFeedbackConfigs), expressionStr);
                // 随机打乱列表
                Collections.shuffle(jdhFeedbackConfigs);
                Map<String, Object> map = new HashMap<>();
                map.put("selectedObjects", jdhFeedbackConfigs);
                // 使用AviatorEvaluator执行某个表达式（根据需要填写表达式）
                Expression expression = AviatorEvaluator.compile(expressionStr, true);
                Object execute = expression.execute(map);

                List<JdhFeedbackConfig> hitQuestionList = Arrays.asList((JdhFeedbackConfig[]) execute);
                List<JdhUserFeedback> result = UserFeedbackConverter.INSTANCE.convertJdhUserFeedback(hitQuestionList, promise, cmd);
                log.info("UserFeedbackApplicationImpl -> distributeFeedback 选取反馈问题结果, result={}", JSON.toJSONString(result));
                saveList.addAll(result);
            }

        } else {
            log.info("UserFeedbackApplicationImpl -> distributeFeedback 不支持的分配类型, jdhFeedbackConfigDictAdaptor={}", JSON.toJSONString(jdhFeedbackConfigDictAdaptor));
            return;
        }
        log.info("UserFeedbackApplicationImpl -> distributeFeedback, saveList={}", JSON.toJSONString(saveList));
        feedbackReplaceWords(jdhVerticalBusiness, promise, saveList);
        userFeedbackRepository.batchSave(saveList);
    }

    private void feedbackReplaceWords(JdhVerticalBusiness jdhVerticalBusiness, PromiseDto promise, List<JdhUserFeedback> saveList) {
        try {
            String businessModeCode = jdhVerticalBusiness.getBusinessModeCode();
            Long skuId = promise.getServices().get(0).getServiceId();
            Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder()
                    .querySkuCoreData(Boolean.TRUE)
                    .queryServiceItem(Boolean.TRUE)
                    .skuIdList(Sets.newHashSet(skuId))
                    .build());
            JdhSkuDto jdhSkuDto = jdhSkuDtoMap.get(skuId);
            saveList.forEach(s->{
                s.setFeedbackQuestionConfig(productApplication.replaceWordsByServiceType(jdhSkuDto.getServiceType(), businessModeCode, s.getFeedbackQuestionConfig()));
            });
        } catch (Exception e) {
            log.error("UserFeedbackApplicationImpl feedbackReplaceWords error e", e);
        }
    }

    /**
     * 查询用户反馈数据
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public UserFeedbackAggregationDTO queryUserFeedback(UserFeedbackRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getPromiseId()) || StringUtils.isBlank(request.getUserPin())
                || StringUtils.isBlank(request.getBusinessScene())) {
            return null;
        }

        //履约单对应的用户反馈列表数据, 反馈场景：serviceStandardFeedback-服务标准化反馈
        List<JdhUserFeedback> jdhUserFeedbackList = userFeedbackRepository.queryUserFeedbackList(JdhFeedbackDbQuery.builder().userPin(request.getUserPin()).promiseId(request.getPromiseId()).businessScene(request.getBusinessScene()).build());
        if (CollectionUtils.isEmpty(jdhUserFeedbackList)) {
            log.info("UserFeedbackApplicationImpl queryUserFeedback jdhUserFeedbackList is null or empty");
            return null;
        }
        UserFeedbackAggregationDTO result = new UserFeedbackAggregationDTO();
        List<UserFeedbackDTO> list = new ArrayList<>();
        Integer currentIndex = null;
        int index = 1;
        for (JdhUserFeedback userFeedback : jdhUserFeedbackList) {
            if (Objects.isNull(currentIndex) && Objects.equals(userFeedback.getFeedbackStatus(), 0)) {
                currentIndex = index;
            }
            list.add(UserFeedbackConverter.INSTANCE.convertUserFeedbackDTO(userFeedback));
            index++;
        }
        index = Math.min(index, jdhUserFeedbackList.size());
        if (Objects.isNull(currentIndex)) {
            currentIndex = index;
        }
        result.setCurrentIndex(currentIndex);
        result.setList(list);
        return result;
    }

    /**
     * 提交用户反馈数据
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm
    @Transactional(rollbackFor = Exception.class)
    public UserFeedbackAggregationDTO submitUserFeedback(SubmitUserFeedbackCmd cmd) {
        //校验必填参数
        if (Objects.isNull(cmd) || StringUtils.isBlank(cmd.getUserPin()) || Objects.isNull(cmd.getFeedbackId())
                || StringUtils.isBlank(cmd.getContent())) {
            throw new BusinessException(UserFeedbackErrorCode.MISSING_NECESSARY_ARG);
        }
        //查询用户反馈数据
        JdhUserFeedback userFeedback = userFeedbackRepository.find(JdhUserFeedbackIdentifier.builder().feedbackId(cmd.getFeedbackId()).build());
        if (Objects.isNull(userFeedback)) {
            throw new BusinessException(UserFeedbackErrorCode.MISSING_NECESSARY_ARG);
        }
        String tips = null;
        if (Objects.equals(cmd.getContent(), userFeedback.getContent()) && Objects.equals(cmd.getContentType(), userFeedback.getContentType())) {
            log.info("UserFeedbackApplicationImpl -> submitUserFeedback 反馈结果已提交无需重复提交, cmd={}", JSON.toJSONString(cmd));
            tips = "已收到您的反馈～";
        } else {
            //提交反馈结果
            log.info("UserFeedbackApplicationImpl -> submitUserFeedback 提交反馈结果, 枚举值类型 cmd={}", JSON.toJSONString(cmd));
            JdhUserFeedback updateUserFeedback = userFeedback.copyInstance();
            updateUserFeedback.setContentType(cmd.getContentType());
            updateUserFeedback.setContent(cmd.getContent());
            updateUserFeedback.setFeedbackStatus(1);
            updateUserFeedback.setUpdateUser(cmd.getUserPin());
            userFeedbackRepository.save(updateUserFeedback);
            userFeedbackRepository.batchSaveOperateHistory(Lists.newArrayList(UserFeedbackConverter.INSTANCE.convertJdhUserFeedbackOperateHistory(userFeedback, updateUserFeedback)));
        }
        //返回最新的反馈列表数据
        UserFeedbackAggregationDTO aggregationDTO = queryUserFeedback(UserFeedbackRequest.builder().userPin(userFeedback.getUserPin()).promiseId(userFeedback.getPromiseId()).businessScene(userFeedback.getBusinessScene()).build());
        aggregationDTO.setTips(tips);
        return aggregationDTO;
    }
}