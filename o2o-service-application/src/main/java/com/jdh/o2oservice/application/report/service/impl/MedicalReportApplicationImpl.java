package com.jdh.o2oservice.application.report.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.medicalpromise.service.MedPromiseHistoryApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceIndicatorApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.report.convert.MedicalReportIndicatorConvert;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.application.support.service.UserInfoApplication;
import com.jdh.o2oservice.application.support.service.UserMarketApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CacheConstant;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.*;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.NumberUtils;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.IndicatorTagEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.UserMarketSceneEnum;
import com.jdh.o2oservice.common.enums.UserMarketTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.AppointTypeFormatEnum;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.report.bo.MedReportIndicatorQueryBO;
import com.jdh.o2oservice.core.domain.report.bo.MedicalReportQueryBO;
import com.jdh.o2oservice.core.domain.report.bo.UserIdentityBO;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportChangeLog;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicator;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportIndicatorRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.QuickDrugConfigRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.QuickDrugConfigRepository2;
import com.jdh.o2oservice.core.domain.report.rpc.UserIdentityRpc;
import com.jdh.o2oservice.core.domain.support.basic.model.BaseDomainErrorCode;
import com.jdh.o2oservice.core.domain.support.file.context.PutFileResult;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.core.domain.support.netrx.bo.DpRelationCheckBo;
import com.jdh.o2oservice.core.domain.support.netrx.rpc.NetRxRpc;
import com.jdh.o2oservice.core.domain.support.patient.model.Patient;
import com.jdh.o2oservice.core.domain.support.patient.repository.PatientRepository;
import com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo.AddCartResultBo;
import com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo.QuickCheckMedicineBo;
import com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo.QuickCheckResultBo;
import com.jdh.o2oservice.core.domain.support.quickcheckmedicine.bo.RxNewPrescriptAddCartBo;
import com.jdh.o2oservice.core.domain.support.quickcheckmedicine.rpc.QuickCheckMedicineRpc;
import com.jdh.o2oservice.core.domain.support.reach.constant.ReachConstant;
import com.jdh.o2oservice.core.domain.support.usermarket.bo.NethpRxSkuExtendBO;
import com.jdh.o2oservice.core.domain.trade.bo.InspectSheetInfoBO;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.InspectionQueryRpc;
import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseHistoryDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedPromiseHistoryRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.ServiceIndicatorDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.JdhIndicatorQuery;
import com.jdh.o2oservice.export.product.query.ServiceItemIndicatorQuery;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.report.cmd.MedicalReportSaveCmd;
import com.jdh.o2oservice.export.report.cmd.ReportShareConfigBO;
import com.jdh.o2oservice.export.report.dto.*;
import com.jdh.o2oservice.export.report.query.*;
import com.jdh.o2oservice.export.support.command.RxNewPrescriptAddCartCmd;
import com.jdh.o2oservice.export.support.dto.UserMarketDTO;
import com.jdh.o2oservice.export.support.query.QuickCheckMedicineRequest;
import com.jdh.o2oservice.export.support.query.UserMarketRequest;
import com.jdh.o2oservice.export.trade.dto.JdOrderDTO;
import com.jdh.o2oservice.export.trade.query.OrderDetailParam;
import com.jdh.o2oservice.infrastructure.repository.db.convert.MedicalReportConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.MedicalReportIndicatorMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.MedicalReportMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.MedicalReportIndicatorPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.MedicalReportPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-04-18 14:49
 * @Desc : 医学检测报告
 */
@Slf4j
@Service
public class MedicalReportApplicationImpl implements MedicalReportApplication {
    /**
     * 用户点击报告
     */
    private static final String REPORT_READ_CLICK_EVENT_TOPIC = "report_read_click_event";
    /**
     * jimRedisService
     */
    @Resource
    private Cluster jimClient;
    /**
     * medicalPromiseApplication
     */
    @Autowired
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * generateIdFactory
     */
    @Autowired
    private GenerateIdFactory generateIdFactory;

    /**
     * medicalReportRepository
     */
    @Autowired
    private MedicalReportRepository medicalReportRepository;

    /**
     * patientRepository
     */
    @Autowired
    private PatientRepository patientRepository;

    /**
     * tradeApplication
     */
    @Autowired
    private TradeApplication tradeApplication;

    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;
    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;

    /**
     * DUCC
     */
    @Autowired
    private DuccConfig duccConfig;
    /**
     * 文件管理领域服务
     */
    @Autowired
    private FileManageService fileManageService;
    /**
     * inspectionQueryRpc
     */
    @Autowired
    private InspectionQueryRpc inspectionQueryRpc;
    /**
     * 指标服务
     */
    @Autowired
    private ProductServiceIndicatorApplication productServiceIndicatorApplication;
    /**
     * productServiceItemApplication
     */
    @Autowired
    private ProductServiceItemApplication productServiceItemApplication;
    /**
     * medPromiseHistoryApplication
     */
    @Autowired
    private MedPromiseHistoryApplication medPromiseHistoryApplication;
    /**
     * reachStoreProducer,jmq2
     */
    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    @Autowired
    private JdhFileRepository jdhFileRepository;

    @Autowired
    private PromiseApplication promiseApplication;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private MedicalReportIndicatorMapper medicalReportIndicatorMapper;
    @Resource
    private MedicalReportMapper medicalReportMapper;

    /**
     * 提供线程池工厂，用于创建和管理线程池。
     */
    @Autowired
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * 用户信息应用程序接口，用于获取用户相关信息。
     */
    @Autowired
    private UserInfoApplication userInfoApplication;

    @Autowired
    private UserIdentityRpc userIdentityRpc;

    /**
     * 用户营销信息
     */
    @Autowired
    private UserMarketApplication userMarketApplication;

    /**
     * 快速检查药品的远程过程调用接口，用于生成跳转链接。
     */
    @Autowired
    private QuickCheckMedicineRpc quickCheckMedicineRpc;

    /**
     * 远程过程调用接口，用于查询网上药店处方单信息。
     */
    @Autowired
    private NetRxRpc netRxRpc;

    /**
     * 一键购药仓储
     */
    @Autowired
    private QuickDrugConfigRepository quickDrugConfigRepository;

    @Autowired
    private QuickDrugConfigRepository2 quickDrugConfigRepository2;

    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;




    /**
     * medicalReportIndicatorRepository
     */
    @Autowired
    private MedicalReportIndicatorRepository medicalReportIndicatorRepository;

    @Override
    public SaveMedicalReportResultDTO save(MedicalReportSaveCmd medicalReportSaveCmd) {
        log.info("[MedicalReportApplicationImpl->save], medicalReportSaveCmd={}", JSON.toJSONString(medicalReportSaveCmd));
        SaveMedicalReportResultDTO resultDto = new SaveMedicalReportResultDTO();
        MedicalReport medicalReport = convert2MedicalReport(medicalReportSaveCmd);
        if (StringUtil.isNotBlank(medicalReportSaveCmd.getReportJpgUrl())){
            String fileName = medicalReportSaveCmd.getMedicalPromiseId().toString()+"_"+DateUtil.format(new Date(),CommonConstant.YMDHMSSS2)+".jpg";
            PutFileResult putFileResult = fileManageService.transferPut(fileName, medicalReportSaveCmd.getReportJpgUrl(), FileManageServiceImpl.FolderPathEnum.REPORT, ContentTypeEnum.JPG.getValue());
            if (Objects.nonNull(putFileResult)){
                medicalReport.setReportJpgOss(putFileResult.getFilePath());
            }
        }

        Date now = new Date();
        //按检测单去查询报告是否存在
        Long reportId = getReportIdByMedicalPromiseId(medicalReportSaveCmd.getMedicalPromiseId());
        if (reportId == null) {//新增
            //添加锁，防止并发插入
            long i = jimClient.incr(CacheConstant.MEDICAL_PROMISE_ID_REPORT_ID_PREFIX + medicalReportSaveCmd.getMedicalPromiseId());
            jimClient.expire(CacheConstant.MEDICAL_PROMISE_ID_REPORT_ID_PREFIX + medicalReportSaveCmd.getMedicalPromiseId(), 5, TimeUnit.SECONDS);
            if (i > 1) {
                log.error("报告并发新增触发锁机制，新增失败 medicalReportSaveCmd={}", JSON.toJSONString(medicalReportSaveCmd));
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR, "报告并发新增" + medicalReportSaveCmd.getMedicalPromiseId());
            }
            //对标旧逻辑 com.jd.health.medical.examination.service.report.impl.ReportInputServiceImpl#inputReport
            checkInsertParam(medicalReportSaveCmd);
            Long id = generateIdFactory.getId();//生成全局唯一id

            medicalReport.setId(id);
            medicalReport.setCreateTime(now);
            medicalReport.setUpdateTime(now);
            medicalReport.setYn(1);
            //生成报告ID，兼容旧逻辑
            reportId = medicalReportRepository.insert(medicalReport);
            jimClient.del(CacheConstant.MEDICAL_PROMISE_ID_REPORT_ID_PREFIX + medicalReportSaveCmd.getMedicalPromiseId());
            if (reportId <= 0) {
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR, "新增报告失败");
            }
            //添加缓存，防止主从不同步，影响查询准确性
            jimClient.setEx(CacheConstant.MEDICAL_PROMISE_ID_REPORT_ID_PREFIX + medicalReportSaveCmd.getMedicalPromiseId(), reportId.toString(), 1, TimeUnit.DAYS);
            resultDto.setReportId(reportId);
            resultDto.setRes(Boolean.TRUE);
        } else {
            // 更新前记录旧的报告信息
            MedicalReport oldReport = null;
            // 只有报告oss文件更新,才记录报告变更记录
            if (StringUtils.isNotBlank(medicalReportSaveCmd.getReportOss()) || StringUtils.isNotBlank(medicalReportSaveCmd.getSourceOss()) || StringUtils.isNotBlank(medicalReportSaveCmd.getStructReportOss())) {
                oldReport = medicalReportRepository.getByMedicalPromiseId(medicalReportSaveCmd.getMedicalPromiseId());
            }
            //对标旧逻辑 com.jd.health.medical.examination.facade.impl.ThirdDataExportServiceImpl#pushReport
            checkUpdateParam(medicalReportSaveCmd);
//            if(!medicalReportSaveCmd.getUserPin().equals(oldReport.getUserPin())){
//                log.error("[MedicalReportApplicationImpl->save], userPin is ILLEGAL_ARG_ERROR new={}|old={}", medicalReportSaveCmd.getUserPin(),oldReport.getUserPin());
//                throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR, "用户pin不匹配");
//            }
            medicalReport.setId(reportId);
            medicalReport.setUpdateTime(now);
            boolean success = medicalReportRepository.update(medicalReport);
            if (!success) {
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR, "更新失败");
            }
            if (oldReport != null) {
                medicalReportRepository.insertMedicalReportChangeLog(MedicalReportConverter.INS.reportBoToChangeLogBo(oldReport));
            }
            resultDto.setRes(Boolean.TRUE);
        }
        log.info("[MedicalReportApplicationImpl->save], result={}", JSON.toJSONString(resultDto));
        return resultDto;
    }


    @Override
    public MedicalReportSummaryDTO queryListByOuterOrderId(OuterOrderRequest request) {
        MedicalReportSummaryDTO resultDTO = new MedicalReportSummaryDTO();
        resultDTO.setPatientId(request.getPatientId());
        //根据互医生检测单id，查询对应的父亲订单id
        Long parentOrderId = jdOrderRepository.queryParentOrderIdByPartnerSourceOrderId(request.getOuterOrderId(), request.getUserPin());
        if (parentOrderId == null) {
            log.error("MedicalReportApplicationImpl.queryListByOuterOrderId parentOrderId isNull request={}", JSON.toJSONString(request));
//            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR, "您无权查看其他人的检验单报告信息~");
            return resultDTO;
        }
        List<Long> promiseIdList = getByOrder(parentOrderId, request.getUserPin());
        if (CollectionUtils.isEmpty(promiseIdList)){
            return resultDTO;
        }
        //根据查询有哪些检测项，哪些检测项目已出
        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseIdList(promiseIdList);
        medicalPromiseListRequest.setInvalid(false);
        List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
        log.info("MedicalReportApplicationImpl.queryListByOuterOrderId medicalPromiseListRequest={} medicalPromiseDTOList={}", JSON.toJSONString(medicalPromiseListRequest), JSON.toJSONString(medicalPromiseDTOList));
        if (CollectionUtils.isEmpty(medicalPromiseDTOList)) {
            log.error("MedicalReportApplicationImpl.queryListByOuterOrderId orderList isEmpty request={} medicalPromiseListRequest={}", JSON.toJSONString(request), JSON.toJSONString(medicalPromiseListRequest));
            return resultDTO;
        }
        //过滤已出的报告列表
        medicalPromiseDTOList = medicalPromiseDTOList.stream().filter(o -> MedicalPromiseStatusEnum.isReportOut(o.getStatus())).collect(Collectors.toList());

        List<PromiseMedicalReportDTO> reportDtoList = Lists.newArrayList();
        resultDTO.setReportDtoList(reportDtoList);
        for (MedicalPromiseDTO promiseDTO : medicalPromiseDTOList) {
            PromiseMedicalReportDTO promiseMedicalReportDTO = new PromiseMedicalReportDTO();
            promiseMedicalReportDTO.setMedicalPromiseId(promiseDTO.getMedicalPromiseId());
            promiseMedicalReportDTO.setStatus(promiseDTO.getStatus());
            promiseMedicalReportDTO.setServiceItemName(promiseDTO.getServiceItemName());
            reportDtoList.add(promiseMedicalReportDTO);
        }
        List<Long> medicalPromiseIdList = medicalPromiseDTOList.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(medicalPromiseIdList)) {
            return resultDTO;
        }


        MedPromiseHistoryRequest medPromise = new MedPromiseHistoryRequest();
        medPromise.setMedicalPromiseIds(Sets.newHashSet(medicalPromiseIdList));
        medPromise.setAfterStatus(MedicalPromiseStatusEnum.COLLECTED.getStatus());
        List<MedPromiseHistoryDTO> medPromiseHistoryDTOList = medPromiseHistoryApplication.queryMedPromiseHistoryList(medPromise);
        log.info("MedicalReportApplicationImpl.queryListByOuterOrderId medicalPromiseIdList={} medPromiseHistoryDTOList={}", JSON.toJSONString(medicalPromiseIdList), JSON.toJSONString(medPromiseHistoryDTOList));
        Map<Long, List<MedPromiseHistoryDTO>> historyMap = medPromiseHistoryDTOList.stream().collect(Collectors.groupingBy(MedPromiseHistoryDTO :: getMedicalPromiseId));

        List<MedicalReport> reportList = medicalReportRepository.getByMedicalPromiseIdList(medicalPromiseIdList);
        if (request.getPatientId() != null) {
            reportList = reportList.stream().filter(o -> o.getPatientId() == null || o.getPatientId().equals(request.getPatientId())).collect(Collectors.toList());
        }
        log.info("MedicalReportApplicationImpl.queryListByOuterOrderId medicalPromiseIdList={} reportList={}", JSON.toJSONString(medicalPromiseIdList), JSON.toJSONString(reportList));
        if (CollectionUtils.isEmpty(reportList)) {
            return resultDTO;
        }
        // key:medicalPromiseId  value:reportId
        Map<Long, Long> medicalPromiseIdMap = reportList.stream().collect(Collectors.toMap(MedicalReport::getMedicalPromiseId, MedicalReport::getId, (key1, key2) -> key2));
        for (PromiseMedicalReportDTO promiseDTO : reportDtoList) {
            if(CollectionUtils.isNotEmpty(historyMap.get(promiseDTO.getMedicalPromiseId()))){
                promiseDTO.setCheckTime(historyMap.get(promiseDTO.getMedicalPromiseId()).get(0).getCreateTime());
            }
            if (medicalPromiseIdMap.get(promiseDTO.getMedicalPromiseId()) == null) {
                continue;
            }
            promiseDTO.setReportId(medicalPromiseIdMap.get(promiseDTO.getMedicalPromiseId()).toString());
        }

        if (Boolean.TRUE.equals(request.getReportDetail())){
            log.info("病假单-判断是否开具病假单");
            MedReportIndicatorQueryBO medReportIndicatorQueryBO = new MedReportIndicatorQueryBO();
            Set<String> mpIds = reportDtoList.stream().map(PromiseMedicalReportDTO::getReportId).collect(Collectors.toSet());
            medReportIndicatorQueryBO.setReportIds(mpIds);
            medReportIndicatorQueryBO.setAbnormalOnly(Boolean.TRUE);
            List<MedicalReportIndicator> medicalReportIndicators = medicalReportIndicatorRepository.listMedicalReportIndicators(medReportIndicatorQueryBO);
            if(CollectionUtils.isEmpty(medicalReportIndicators)){
                return resultDTO;
            }
            resultDTO.setAllAbnormalIndicatorDTOList(MedicalReportIndicatorConvert.INSTANCE.convertResultIndicator(medicalReportIndicators));

            log.info("病假单-判断是否开具病假单 medicalReportIndicators={}",JSON.toJSONString(medicalReportIndicators));
            //判断是否开具病假单
            MedicalReport medicalReport = reportList.stream().min(Comparator.comparing(MedicalReport::getCreateTime)).orElse(null);
            Date dayOfTomorrow = TimeUtils.getDateEnding(TimeUtils.addDays(medicalReport.getCreateTime(),duccConfig.getSickCertExpiredDayNum()));
            //查出符合条件的报告
            List<Long> reportIds = reportList.stream().filter(t->t.getCreateTime().getTime()>=medicalReport.getCreateTime().getTime()&&t.getCreateTime().getTime()<dayOfTomorrow.getTime()&&dayOfTomorrow.getTime()>System.currentTimeMillis()).map(MedicalReport::getId).collect(Collectors.toList());
            long abnormalNum = medicalReportIndicators.stream().filter(t->reportIds.contains(Long.parseLong(t.getReportId()))).count();
            if(abnormalNum>0){
                resultDTO.setOpenSickCert(true);
                resultDTO.setSickCertEndDay(dayOfTomorrow);
            }
        }

        return resultDTO;
    }

    /**
     * 根据订单，查询履约单id列表
     *
     * @return
     */
    private List<Long> getByOrder(Long parentOrderId, String userPin) {
        if (parentOrderId == null) {
            return Lists.newArrayList();
        }
//        String orderId = null;
//        //根据订单id，查询父订单
//        OrderDetailParam param = new OrderDetailParam();
//        param.setOrderId(orderId);
//        param.setPin(userPin);
//        JdOrderDTO jdOrder =tradeApplication.getOrderDetail(param);
//        //根据父订单，查询履约单id
//        String sourceVoucherId = Objects.nonNull(jdOrder.getParentId()) && jdOrder.getParentId() > 0 ? jdOrder.getParentId().toString() : jdOrder.getOrderId().toString();
        JdhPromise jdhPromise = promiseRepository.findPromise(PromiseRepQuery.builder().sourceVoucherId(String.valueOf(parentOrderId)).userPin(userPin).build());
        log.info("MedicalReportApplicationImpl.getByOrder parentOrderId={} userPin={} jdhPromise={}", parentOrderId, userPin, JSON.toJSONString(jdhPromise));
        //履约单列表
        if (Objects.isNull(jdhPromise)){
            return null;
        }
        List<Long> promiseIdList = Lists.newArrayList(jdhPromise.getPromiseId());
        return promiseIdList;
    }

    @Override
    public MedicalReportUrlDTO queryUrl(MedicalReportRequest request) {
        MedicalReport medicalReport = null;
        if (Objects.nonNull(request.getReportId())){
            medicalReport = medicalReportRepository.getById(request.getReportId());
        }else if (Objects.nonNull(request.getMedicalPromiseId())){
            medicalReport = medicalReportRepository.getByMedicalPromiseId(request.getMedicalPromiseId());
        }

        if (!request.getUserPin().equals(medicalReport.getUserPin())) {

            DpRelationCheckBo bo = DpRelationCheckBo.builder()
                    .loginPin(request.getUserPin())
                    .patientId(medicalReport.getPatientId())
                    .tenantType("JD8888")
                    .checkType(CommonConstant.ONE)
                    .build();
            Boolean res = netRxRpc.checkDoctorPatientRelationForHealthData(bo);
            if (!res) {
                throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR, "用户pin不正确");

            }
        }
        String url = medicalReport.getReportOss();
        //生成临时链接
        String tempUrl = getOssHeepUrl(url);
        MedicalReportUrlDTO res = new MedicalReportUrlDTO();
        res.setReportId(request.getReportId());
        res.setReportUrl(tempUrl);
        if (StringUtils.isNotBlank(tempUrl)) {
            res.setUrlType(tempUrl.substring(tempUrl.lastIndexOf(".") + 1));
        }
        return res;
    }

    @Override
    public MedicalReportOrderDTO queryByOrderId(MedicalReportOrderRequest request) {
        MedicalReportOrderDTO resultDTO = new MedicalReportOrderDTO();


        Boolean combine = Boolean.FALSE;
        JdhPromise jdhPromise = null;
        JdOrderDTO jdOrder = null;
        if (StringUtils.equals(request.getDomainCode(), "promise") && StringUtils.equals(request.getAggregateCode(), "promise")) {
            jdhPromise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(request.getPromiseId()).userPin(request.getUserPin()).build());
        } else {
            //根据订单id，查询父订单id
            OrderDetailParam param = new OrderDetailParam();
            param.setOrderId(request.getOrderId());
            param.setPin(request.getUserPin());
            jdOrder = tradeApplication.getOrderDetail(param);
            log.info("MedicalReportApplicationImpl.queryByOrderId param={} jdOrder={}", JSON.toJSONString(param), JSON.toJSONString(jdOrder));
            if (jdOrder == null) {
                throw new RuntimeException("您无权查看其他人的检验单报告信息~");
            }
            if (PartnerSourceEnum.fromCode(jdOrder.getPartnerSource()) == PartnerSourceEnum.JDH_NETDIAG || PartnerSourceEnum.fromCode(jdOrder.getPartnerSource()) == PartnerSourceEnum.JDH_HOMEDIAG) {
                //互医和家医检验单，只会对应一个患者，所以去掉患者id过滤
                request.setPatientId(null);
                combine = Boolean.TRUE;
            }
            resultDTO.setOrderId(request.getOrderId());
            resultDTO.setOuterOrderId(jdOrder.getPartnerSourceOrderId());
            //根据父订单，查询履约单id
            String sourceVoucherId = Objects.nonNull(jdOrder.getParentId()) && jdOrder.getParentId() > 0 ? jdOrder.getParentId().toString() : jdOrder.getOrderId().toString();
            jdhPromise = promiseRepository.findPromise(PromiseRepQuery.builder().sourceVoucherId(sourceVoucherId).userPin(request.getUserPin()).build());
            log.info("MedicalReportApplicationImpl.queryByOrderId sourceVoucherId={} jdhPromise={}", sourceVoucherId, JSON.toJSONString(jdhPromise));
        }
        if (jdhPromise == null) {
            log.error("MedicalReportApplicationImpl.queryByOrderId request={} jdhPromise is null", JsonUtil.toJSONString(request));
            throw new RuntimeException("您无权查看其他人的检验单报告信息~");
        }
        resultDTO.setPromiseId(jdhPromise.getPromiseId());
        Set<Long> promisePatientId = Sets.newHashSet();
        if (request.getPatientId() != null) {
            List<JdhPromisePatient> JdhPromisePatientList = jdhPromise.getPatients().stream().filter(o -> request.getPatientId().equals(o.getPatientId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(JdhPromisePatientList)) {
                promisePatientId = JdhPromisePatientList.stream().map(JdhPromisePatient::getPromisePatientId).collect(Collectors.toSet());
                resultDTO.setPatientName(JdhPromisePatientList.get(0).getUserName().mask());
            }
        } else {
            if (CollectionUtils.isNotEmpty(jdhPromise.getPatients())) {
                resultDTO.setPatientName(jdhPromise.getPatients().get(0).getUserName().mask());
            }
        }
        //患者id
//        List<Long> promisePatientIdList = Lists.newArrayList(request.getPatientId());
        //根据查询有哪些检测项，哪些检测项目已出
        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseIdList(Lists.newArrayList(jdhPromise.getPromiseId()));
        medicalPromiseListRequest.setInvalid(false);
        List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
        log.info("MedicalReportApplicationImpl.queryByOrderId PatientId={} getPromiseId={} medicalPromiseDTOList={}", request.getPatientId(), jdhPromise.getPromiseId(), JSON.toJSONString(medicalPromiseDTOList));
        if (CollectionUtils.isEmpty(medicalPromiseDTOList)) {
            return resultDTO;
        }
        //查询报告
        List<Long> medicalPromiseIdList = medicalPromiseDTOList.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList());
        List<MedicalReport> reportList = medicalReportRepository.getByMedicalPromiseIdList(medicalPromiseIdList);
        if (request.getPatientId() != null) {
            reportList = reportList.stream().filter(o -> o.getPatientId() == null || o.getPatientId().equals(request.getPatientId())).collect(Collectors.toList());
        }
        log.info("MedicalReportApplicationImpl.queryByOrderId medicalPromiseIdList={} reportList={}", medicalPromiseIdList, JSON.toJSONString(reportList));
        //检测信息,检测指标汇总
        List<PromiseMedicalReportDTO> promiseMedicalReportDtoList = convert2PromiseMedicalReportDTOList(medicalPromiseDTOList, reportList,combine,promisePatientId);
        this.fillIndicatorNum(promiseMedicalReportDtoList);
        resultDTO.setPromiseMedicalReportDtoList(promiseMedicalReportDtoList);
        //检测结论
        TestConclusionDTO testConclusionDTO = getTestConclusionDTO(promiseMedicalReportDtoList);
        resultDTO.setTestConclusionDTO(testConclusionDTO);

        resultDTO.setOuterOrderId(Objects.nonNull(jdOrder) ? jdOrder.getPartnerSourceOrderId() : null);// 查询互医检测单id
        //补全医生楼层
        this.fillReportDoctorDTO(resultDTO, request, jdOrder);
        //补全指标百科卡片
        this.fillVirusEncyclopediaList(resultDTO, promiseMedicalReportDtoList);
        //温馨提示
        Integer partnerSource = null;
        if (Objects.nonNull(jdOrder)){
            partnerSource = jdOrder.getPartnerSource();
        }
        String tips = getTips(request.getOrderId(), request.getUserPin(), PartnerSourceEnum.fromCode(partnerSource));
        resultDTO.setTips(tips);
        //调研链接
        String nps = getNps(jdhPromise.getVerticalCode());
        resultDTO.setNps(nps);
        log.info("MedicalReportApplicationImpl.queryByOrderId promiseMedicalReportDtoList={}",  JSON.toJSONString(promiseMedicalReportDtoList));
        PromiseMedicalReportDTO promiseMedicalReportDTO = promiseMedicalReportDtoList.stream().filter(p -> !Objects.equals(MedicalPromiseStatusEnum.COMPLETED.getStatus(), p.getStatus())).findFirst().orElse(null);
        log.info("MedicalReportApplicationImpl.queryByOrderId promiseMedicalReportDTO={}",  JSON.toJSONString(promiseMedicalReportDTO));

        if (Objects.isNull(promiseMedicalReportDTO)){
            resultDTO.setAllFinish(Boolean.TRUE);
        }else {
            resultDTO.setAllFinish(Boolean.FALSE);
        }
        return resultDTO;
    }

    /**
     * 按订单id查询报告汇总(串+并)
     *
     * @param request
     * @return
     */
    @Override
    public MedicalReportOrderDTO queryByOrderIdNew(MedicalReportOrderRequest request) {

        try (MedicalReportContainer container = new MedicalReportContainer()){

            container.initBaseParam(()->{
                container.getContextData().get().setDuccConfig(duccConfig);
                container.getContextData().get().setRequest(request);
                return Boolean.TRUE;
            }).initOrderPromise(tradeApplication::getOrderDetail,promiseRepository::findPromise,medicalPromiseRepository::findMedicalPromise,medicalReportRepository::selectByCondition,netRxRpc::checkDoctorPatientRelationForHealthData);

            MedicalReportContainer.ReportData reportData = container.getContextData().get();
            if (Objects.isNull(reportData) || Objects.isNull(reportData.getJdhPromise())) {
                return null;
            }
            reportData.setDoctorView(request.getDoctorView());
            //user信息、报告信息、nps、问诊、tips 并行查询
            ExecutorService executorService = executorPoolFactory.get(ThreadPoolConfigEnum.REPORT_C_POOL);
            //检测单
            CompletableFuture medPromiseListFuture = CompletableFuture.supplyAsync(() -> {
                return container.medicalPromiseList(medicalPromiseApplication::queryMedicalPromiseList, reportData);
            }, executorService);
            //问诊
            CompletableFuture doctorInfoFuture = CompletableFuture.supplyAsync(() -> {
                return container.inspectSheet(inspectionQueryRpc::queryInspectSheetBySheetId, reportData);
            }, executorService);

            //用户信息
            CompletableFuture userInfoFuture = CompletableFuture.supplyAsync(() -> {
                return container.userInfo(userInfoApplication::getUserBaseInfoByPin, reportData);
            }, executorService);


//            //用户营销信息
            CompletableFuture userMarketInfoFuture = CompletableFuture.supplyAsync(() -> {
                return container.userMarketInfo(userMarketApplication::queryUserMarketList, reportData);
            }, executorService);

            container.initFutureFunc(medPromiseListFuture,doctorInfoFuture,userInfoFuture,userMarketInfoFuture);
//            container.initFutureFunc(medPromiseListFuture,doctorInfoFuture,userInfoFuture);
            //-----------------
            if (CollectionUtils.isEmpty(reportData.getMedicalPromiseList())){
                log.info("queryByOrderIdNew->reportData.getMedicalPromiseList,isNull");
                return null;
            }
            //-----------------
            //检测单报告
            CompletableFuture medPromiseReportFuture = CompletableFuture.supplyAsync(() -> {
                return container.reportList(medicalReportRepository::getByMedicalPromiseIdList, reportData);
            }, executorService);
            //检测单历史
            CompletableFuture medPromiseHistoryFuture = CompletableFuture.supplyAsync(() -> {
                return container.medicalPromiseHistoryList(medPromiseHistoryApplication::queryMedPromiseHistoryList, reportData);
            }, executorService);
            //项目指标
            CompletableFuture serviceItemFuture = CompletableFuture.supplyAsync(() -> {
                return container.serviceItemList(productServiceItemApplication::queryServiceItemListCondition, reportData);
            }, executorService);
            //处方单查询
            CompletableFuture rxFuture = CompletableFuture.supplyAsync(() -> {
                return container.netRx(netRxRpc::getRxInfoByRxIds,reportData);
            }, executorService);

            container.initFutureFunc(medPromiseReportFuture,medPromiseHistoryFuture,serviceItemFuture,rxFuture);
//            container.initFutureFunc(medPromiseReportFuture,medPromiseHistoryFuture,serviceItemFuture);
            //-----------------
            container.reportFileDownload(fileManageService,executorService,jimClient)
                    .indicator(productServiceIndicatorApplication::queryIndicatorList);
            //-----------------
            MedicalReportOrderDTO resp = container
                    .buildMedPromiseDetailList()
                    .buildMedicineRecommend(quickDrugConfigRepository,quickDrugConfigRepository2)
                    .buildTips()
                    .buildNps()
                    .buildDoctorInfo()
                    .buildQuickDrug()
                    .resp();

            // 报告问诊生添加权益标识
            if (Strings.isNotBlank(request.getUserPin()) && StringUtils.equals("c-order", request.getPath())) {
                UserIdentityBO bo = userIdentityRpc.queryUserIdentity(request.getUserPin());
                if (bo != null && Objects.nonNull(resp.getReportDoctorDTO())) {
                    resp.getReportDoctorDTO().setUserType(bo.getType());
                    String buttonBubbleText = "";
                    if (bo.getType() == 1 && bo.getPlusRightRemainingNum() > 0) {
                        buttonBubbleText = "https://img12.360buyimg.com/imagetools/jfs/t1/256286/10/8070/4621/6777af78Fd87eeadb/73bc991099282043.png";
                    }
                    resp.getReportDoctorDTO().setButtonBubbleText(buttonBubbleText);
                }
            }

            if (Objects.nonNull(resp)){
                //不展示单项总异常结论
                if (!duccConfig.getSingleServiceItemAbnormal()){
                    if (Objects.equals(CommonConstant.ONE,resp.getPromiseMedicalReportDtoList().size())){
                        resp.getTestConclusionDTO().setAbnormalSummaryIndicators(null);
                    }
                }
            }


            return resp;
        }catch (Exception e){
            log.info("MedicalReportApplicationImpl.queryByOrderIdNew error", e);
        }

        return null;
    }



    /**
     * 补全医生楼层
     */
    private void fillReportDoctorDTO(MedicalReportOrderDTO resultDTO, MedicalReportOrderRequest request, JdOrderDTO jdOrder) {
        //当进入报告页的路径为huyi问诊场景，不展示该楼层
        if ("huyi".equals(request.getPath())) {
            return;
        }
        /*
         * 报告解读层
         * ① 根据下单场景（互医检测单/C端交易）配置展示报告楼层图片以及问诊链接；
         * ② 根据进入报告页的路径不同（IM问诊场景/C订单详情场景）配置是否展示该楼层；
         */

        ReportDoctorDTO reportDoctorDTO = new ReportDoctorDTO();
        resultDTO.setReportDoctorDTO(reportDoctorDTO);
         if (Objects.isNull(jdOrder)){
             reportDoctorDTO = getDefaultReportDoctorDTO(null, resultDTO.getUserPin());
             resultDTO.setReportDoctorDTO(reportDoctorDTO);
             return;
        }

        //如果订单来源是家医或互医
        if (PartnerSourceEnum.fromCode(jdOrder.getPartnerSource()) == PartnerSourceEnum.JDH_NETDIAG || PartnerSourceEnum.fromCode(jdOrder.getPartnerSource()) == PartnerSourceEnum.JDH_HOMEDIAG) {
            //查询医生头像、标题、内容说明、服务使用提示、问诊链接
            try {
                InspectSheetInfoBO inspectSheetInfoBO = inspectionQueryRpc.queryInspectSheetBySheetId(request.getUserPin(), new Long(jdOrder.getPartnerSourceOrderId()));
                reportDoctorDTO.setPicUrl(inspectSheetInfoBO.getDoctorHeadPic());
                reportDoctorDTO.setTitle(inspectSheetInfoBO.getDoctorName());
                reportDoctorDTO.setHints(inspectSheetInfoBO.getExpireDate() + "前" + inspectSheetInfoBO.getNumInquiry() + "次免费提问机会");
                reportDoctorDTO.setIntroduction(inspectSheetInfoBO.getHospitalName() + " | " + inspectSheetInfoBO.getDoctorTitle());
                reportDoctorDTO.setLink(inspectSheetInfoBO.getDiagJumpUrl());
                reportDoctorDTO.setDoctorDepartmentName(inspectSheetInfoBO.getSecondDepartmentName());
                reportDoctorDTO.setDoctorTitle(inspectSheetInfoBO.getDoctorTitle());
                reportDoctorDTO.setHospitalName(inspectSheetInfoBO.getHospitalName());
                reportDoctorDTO.setNumInquiry(inspectSheetInfoBO.getNumInquiry());
                reportDoctorDTO.setButtonName("报告解读");
                return;
            } catch (BusinessException e) {
                log.error("MedicalReportApplicationImpl.fillReportDoctorDTO error", e);
            }
        }
        reportDoctorDTO = getDefaultReportDoctorDTO(null, resultDTO.getUserPin());
        resultDTO.setReportDoctorDTO(reportDoctorDTO);
    }

    /**
     * 获取默认链接
     * @return
     */
    public ReportDoctorDTO getDefaultReportDoctorDTO(Long medicalPromiseId, String userPin){
        ReportDoctorDTO reportDoctorDTO = new ReportDoctorDTO();

        if (StringUtils.isNotBlank(userPin)) {
            UserIdentityBO bo = userIdentityRpc.queryUserIdentity(userPin);
            if (bo != null) {
                reportDoctorDTO.setUserType(bo.getType());
                String buttonBubbleText = "";
                if (bo.getType() == 1 && bo.getPlusRightRemainingNum() > 0) {
                    buttonBubbleText = "https://img12.360buyimg.com/imagetools/jfs/t1/256286/10/8070/4621/6777af78Fd87eeadb/73bc991099282043.png";
                }
                reportDoctorDTO.setButtonBubbleText(buttonBubbleText);
            }
        }
        String jsonConfig = duccConfig.getReportDoctorConfig();
        String reportDoctorNewConfig = duccConfig.getReportDoctorNewConfig();
        if (StringUtils.isNotBlank(reportDoctorNewConfig)){
            reportDoctorDTO = JSON.parseObject(reportDoctorNewConfig, ReportDoctorDTO.class);
            if (Objects.nonNull(medicalPromiseId)) {
                String format = MessageFormat.format(reportDoctorDTO.getLink(), medicalPromiseId.toString());
                reportDoctorDTO.setLink(format);
            }
        }else if (StringUtils.isNotBlank(jsonConfig)) {
            reportDoctorDTO = JSON.parseObject(jsonConfig, ReportDoctorDTO.class);
        } else {
            //固定头像、标题、内容说明、问诊链接（若本期无法实现，则配置固定极速问诊链接）
            reportDoctorDTO.setPicUrl("https://jkimg10.360buyimg.com/pop/jfs/t1/230298/36/17694/33798/664d6503F44f51cb5/56128f58c96f2d4a.png");
            reportDoctorDTO.setTitle("京东自营  在线问诊");
            reportDoctorDTO.setIntroduction("京东医生24小时在线，30秒响应");
            reportDoctorDTO.setButtonName("问医生");
            reportDoctorDTO.setLink("https://m.healthjd.com/s/preInquiryNew?quickAnswer=true&sourceId=1&hy_entry=HomeInspection_ResultQuick17");

        }
        return reportDoctorDTO;
    }

    /**
     * 补全指标地址
     *
     * @param promiseMedicalReportDtoList
     */
    private void fillIndicatorNum(List<PromiseMedicalReportDTO> promiseMedicalReportDtoList) {
        if (CollectionUtils.isEmpty(promiseMedicalReportDtoList)) {
            return;
        }
        Set<Long> itemIdset = promiseMedicalReportDtoList.stream().filter(o -> o.getServiceItemId() != null).map(o -> new Long(o.getServiceItemId())).collect(Collectors.toSet());
        ServiceItemQuery serviceItemQuery = new ServiceItemQuery();
        serviceItemQuery.setItemIds(itemIdset);
        List<ServiceItemDto> response = productServiceItemApplication.queryServiceItemList(serviceItemQuery);
        if (CollectionUtils.isEmpty(response)) {
            log.error("MedicalReportApplicationImpl.fillIndicatorNum -> serviceItemQuery={} response={}", JSON.toJSONString(serviceItemQuery), JSON.toJSONString(response));
            return;
        }
        Map<String, Integer> map = response.stream().filter(o -> o.getItemId() != null && o.getServiceIndicatorDtoList() != null).collect(Collectors.toMap(o -> o.getItemId().toString(), o -> o.getServiceIndicatorDtoList().size(), (key1, key2) -> key2));
        for (PromiseMedicalReportDTO reportDTO : promiseMedicalReportDtoList) {
            if (CollectionUtils.isNotEmpty(reportDTO.getReportIndicatorDTOList())) {//如果已出报告，按报告的实际指标数为准
                reportDTO.setIndicatorNum(reportDTO.getReportIndicatorDTOList().size());
                continue;
            }
            reportDTO.setIndicatorNum(map.get(reportDTO.getServiceItemId()));
        }

    }


    //获取调研链接
    private String getNps(String verticalCode) {
        if (AppointTypeFormatEnum.isNures(verticalCode)) {//护士服务调研链接
            return "https://answer.jd.com/jump/?shortCode=cwtCwEVFGuC&surveyId=2079071";
        } else {
            return "https://answer.jd.com/jump/?shortCode=cwtCwEVFGuC&surveyId=2079071";
        }
    }


    //旧版百科 home.service.struct.report.properties
    private void fillVirusEncyclopediaList(MedicalReportOrderDTO resultDTO, List<PromiseMedicalReportDTO> promiseMedicalReportDtoList) {
        if (CollectionUtils.isEmpty(promiseMedicalReportDtoList)) {
            log.error("MedicalReportApplicationImpl.fillVirusEncyclopediaList promiseMedicalReportDtoList isEmpty");
            return;
        }
        Set<VirusEncyclopediaDTO> virusEncyclopediaDTOSet = Sets.newHashSet();
        Set<String> indicatorNameSet = Sets.newHashSet();
        Set<Long> indicatorIdSet = Sets.newHashSet();
        for (PromiseMedicalReportDTO promiseMedicalReportDTO : promiseMedicalReportDtoList) {
            if (promiseMedicalReportDTO == null || CollectionUtils.isEmpty(promiseMedicalReportDTO.getReportIndicatorDTOList())) {
                continue;
            }
            for (ReportIndicatorDTO reportIndicatorDTO : promiseMedicalReportDTO.getReportIndicatorDTOList()) {
                if (reportIndicatorDTO.getIndicatorId() == null) {
                    continue;
                }
                indicatorIdSet.add(reportIndicatorDTO.getIndicatorId());
            }
        }
        if (CollectionUtils.isEmpty(indicatorIdSet)) {
            log.error("MedicalReportApplicationImpl.fillVirusEncyclopediaList indicatorIdSet isEmpty promiseMedicalReportDtoList={}", JSON.toJSONString(promiseMedicalReportDtoList));
            return;
        }
        JdhIndicatorQuery jdhIndicatorQuery = new JdhIndicatorQuery();
        jdhIndicatorQuery.setIndicatorIds(indicatorIdSet);
        jdhIndicatorQuery.setIndicatorType(CommonConstant.TWO);
        List<ServiceIndicatorDto> serviceIndicatorDtoList = productServiceIndicatorApplication.queryIndicatorList(jdhIndicatorQuery);
        log.error("MedicalReportApplicationImpl.fillVirusEncyclopediaList jdhIndicatorQuery={} serviceIndicatorDtoList={}", JSON.toJSONString(jdhIndicatorQuery), JSON.toJSONString(serviceIndicatorDtoList));
        if (CollectionUtils.isEmpty(serviceIndicatorDtoList)) {
            log.error("MedicalReportApplicationImpl.fillVirusEncyclopediaList serviceIndicatorDtoList isEmpty jdhIndicatorQuery={} serviceIndicatorDtoList={}", JSON.toJSONString(jdhIndicatorQuery), JSON.toJSONString(serviceIndicatorDtoList));
            return;
        }
        Map<String, String> indicatorIdMap = serviceIndicatorDtoList.stream().filter(o -> o.getIndicatorId() != null && o.getJdhWikiId() != null).collect(Collectors.toMap(ServiceIndicatorDto::getIndicatorId, ServiceIndicatorDto::getJdhWikiId, (key1, key2) -> key2));
        log.info("MedicalReportApplicationImpl.fillVirusEncyclopediaList indicatorIdMap={}", JSON.toJSONString(indicatorIdMap));
        log.info("MedicalReportApplicationImpl.fillVirusEncyclopediaList promiseMedicalReportDtoList={}", JSON.toJSONString(promiseMedicalReportDtoList));
        for (PromiseMedicalReportDTO promiseMedicalReportDTO : promiseMedicalReportDtoList) {
            if (CollectionUtils.isEmpty(promiseMedicalReportDTO.getReportIndicatorDTOList())) {
                continue;
            }
            for (ReportIndicatorDTO reportIndicatorDTO : promiseMedicalReportDTO.getReportIndicatorDTOList()) {
                log.info("MedicalReportApplicationImpl.fillVirusEncyclopediaList reportIndicatorDTO={}", JSON.toJSONString(reportIndicatorDTO));
                if (reportIndicatorDTO.getIndicatorId() == null) {
                    continue;
                }
                String virusEncyclopediaId = indicatorIdMap.get(reportIndicatorDTO.getIndicatorId().toString());
                log.info("MedicalReportApplicationImpl.fillVirusEncyclopediaList virusEncyclopediaId={}", virusEncyclopediaId);
                if (StringUtils.isBlank(virusEncyclopediaId)) {
                    continue;
                }
                if (!indicatorNameSet.add(reportIndicatorDTO.getIndicatorName())){
                    continue;
                }
                VirusEncyclopediaDTO virusEncyclopediaDTO = new VirusEncyclopediaDTO();
                virusEncyclopediaDTO.setVirusEncyclopediaName(reportIndicatorDTO.getIndicatorName());
                virusEncyclopediaDTO.setVirusEncyclopediaUrl("https://cont.healthjd.com/content/" + virusEncyclopediaId);
                virusEncyclopediaDTO.setVirusEncyclopediaId(virusEncyclopediaId);
                virusEncyclopediaDTOSet.add(virusEncyclopediaDTO);
                reportIndicatorDTO.setVirusEncyclopediaId(virusEncyclopediaId);
            }
        }
        resultDTO.setVirusEncyclopediaDTOSet(virusEncyclopediaDTOSet);
    }


    private TestConclusionDTO getTestConclusionDTO(List<PromiseMedicalReportDTO> promiseMedicalReportDtoList) {
        int anomalyNum = 0;//异常数量
        int pathogensNum = 0;//致病菌数

        TestConclusionDTO result = new TestConclusionDTO();
        result.setTitle("检出{0}项异常");
        result.setContent("包含{0}项条件致病菌");
        if (CollectionUtils.isEmpty(promiseMedicalReportDtoList)) {
            return result;
        }
        for (PromiseMedicalReportDTO promiseMedicalReportDTO : promiseMedicalReportDtoList) {
            if (CollectionUtils.isEmpty(promiseMedicalReportDTO.getReportIndicatorDTOList())) {
                continue;
            }
            Map<String, ReportIndicatorDTO> maps = promiseMedicalReportDTO.getReportIndicatorDTOList().stream().collect(Collectors.toMap(ReportIndicatorDTO::getIndicatorName, each -> each, (value1, value2) -> value1));
            List<ReportIndicatorDTO> distinctAbnormalSummaryList = new ArrayList<>(maps.values());
            for (ReportIndicatorDTO reportIndicatorDTO : distinctAbnormalSummaryList) {
                if (StringUtils.isBlank(reportIndicatorDTO.getAbnormalType())) {
                    continue;
                }
                if (reportIndicatorDTO.getAbnormalType().equals("0")) {//0代表正常
                    continue;
                }
                if (StringUtils.isBlank(reportIndicatorDTO.getTags())) {
                    anomalyNum++;
                    continue;
                }
                anomalyNum++;

                String[] tagArray = reportIndicatorDTO.getTags().split(",");
//                boolean isZymad = false;//是否是条件致病菌
                for (String tag : tagArray) {
                    if (tag.equals("1")) {
                        pathogensNum++;
//                        isZymad = true;
                        break;
                    }
                }
//                if (!isZymad) {
//                    anomalyNum++;
//                }
            }
        }
        result.setAnomalyNum(anomalyNum);
        result.setPathogensNum(pathogensNum);
        if (pathogensNum > 0) {
            result.setTips(duccConfig.getReportTipsMap().get("pathogens"));//文案
//            "条件致病菌是指那些通常存在于人体内外环境中，但日常不会引起疾病的微生物，也叫机会致病菌。在人体免疫系统受损或其他有利条件下，" +
//                    "这些微生物可能会成为病原体导致疾病。如果检测结果显示某种条件致病菌阳性，并不一定表示存在健康问题，但建议咨询医生，" +
//                    "结合其他检查指标和症状表现，分析是否存在感染或相关风险，并按照医生的建议采取适当措施，比如可能包括监测病情、" +
//                    "增强个人卫生习惯或在必要时接受相应的治疗等。"
        }

        return result;
    }

    /**
     * 温馨提示
     * 根据下单场景（互医检测单/C端交易）配置展示温馨提示楼层文案
     *
     * @param orderId
     * @param userPin
     * @return
     */
    private String getTips(String orderId, String userPin, PartnerSourceEnum sourceEnum) {
        //根据下单场景（互医检测单/C端交易）配置展示温馨提示楼层文案
        Map<String, String> reportTipsMap = duccConfig.getReportTipsMap();
          if (Objects.isNull(sourceEnum)){
            return reportTipsMap.get("default");
        }
        if (StringUtils.isNotBlank(reportTipsMap.get(String.valueOf(sourceEnum.getCode())))){
            return reportTipsMap.get(String.valueOf(sourceEnum.getCode()));
        }else {
            return reportTipsMap.get("default");
        }
//
//        if (sourceEnum == PartnerSourceEnum.JDH_NETDIAG) {
//            //互医检测单：本结果仅对本次检测样本负责，供临床参考
//            return "本结果仅对本次检测样本负责，供临床参考";
//        } else if (sourceEnum == PartnerSourceEnum.JDH_HOMEDIAG) {
//            //互医检测单：本结果仅对本次检测样本负责，供临床参考
//            return "本结果仅对本次检测样本负责，供临床参考";
//        } else {
//            //C端交易
//            return "<p>1、该检测结果仅供自我健康咨询参考，不作为诊断、治疗或其他患者管理决策的唯一依据。</p>\n" +
//                    "<p>2、本项目的检测内容为报告中展示的常见病原体，并未涵盖对应项目感染相关的全部病原体；阳性结果表明所检测的病原体存在，不排除存在与其他病原的合并感染，阴性结果并不排除存在感染。</p>\n" +
//                    "<p>3、同其他检测方法一样，本检测存在由于技术、样本等因素所导致的假阴性或假阳性的情况，本检测仅对本次来样负责。</p>";
//        }
    }

    @Override
    public MedicalReportDTO getByMedicalPromiseId(MedicalPromiseRequest request) {
        MedicalReport medicalReport = medicalReportRepository.getByMedicalPromiseId(request.getMedicalPromiseId());
        MedicalReportDTO resDto = convert2MedicalReportDTO(medicalReport);
        return resDto;
    }

    @Override
    public String clickDoctorReadReport(MedicalReportReadDTO dto) {
        try {
            dto.setEventTime(new Date());

            log.info("AngelLocationApplicationImpl -> clickDoctorReadReport dto={}", com.alibaba.fastjson.JSON.toJSONString(dto));
            Message message = new Message(REPORT_READ_CLICK_EVENT_TOPIC, com.alibaba.fastjson.JSON.toJSONString(dto), dto.getUserPin());
            log.info("AngelLocationApplicationImpl-> clickDoctorReadReport message={}", com.alibaba.fastjson.JSON.toJSONString(message));
            reachStoreProducer.send(message);
            if (StringUtils.isBlank(dto.getOuterOrderId())) {//如果没有互医检测单
                ReportDoctorDTO reportDoctorDTO = getDefaultReportDoctorDTO(dto.getMedicalPromiseId(), dto.getUserPin());
                return reportDoctorDTO.getLink();
            }
            try{
                InspectSheetInfoBO inspectSheetInfoBO = inspectionQueryRpc.queryInspectSheetBySheetId(dto.getUserPin(), new Long(dto.getOuterOrderId()));
                if(inspectSheetInfoBO == null || StringUtils.isBlank(inspectSheetInfoBO.getDiagJumpUrl())){
                    //互医研发：范鑫鑫  产品：黄行
                    log.error("MedicalReportApplicationImpl.clickDoctorReadReport 未能正确从互医查出医生信息，请联系互医排查 pin={} OuterOrderId={} inspectSheetInfoBO={}",
                            dto.getUserPin(), dto.getOuterOrderId(), JSON.toJSONString(inspectSheetInfoBO));
                }
                return inspectSheetInfoBO.getDiagJumpUrl();
            }catch (BusinessException e) {
                log.info(" MedicalReportApplicationImpl.clickDoctorReadReport error", e);
            }
            //默认链接做兜底逻辑
            ReportDoctorDTO  reportDoctorDTO = getDefaultReportDoctorDTO(dto.getMedicalPromiseId(), dto.getUserPin());
            return reportDoctorDTO.getLink();
        } catch (Exception e) {
            log.error("MedicalReportApplicationImpl.clickDoctorReadReport", e);
        }
        return "";
    }

    /**
     * 查询报告项目
     *
     * @param param
     * @return
     */
    @Override
    public ServiceItemEncyclopediaDTO queryServiceItemEncyclopedia(ServiceItemEncyclopediaRequest param) {
        ServiceItemIndicatorQuery build = ServiceItemIndicatorQuery.builder().serviceItemIds(Sets.newHashSet(Collections.singleton(param.getServiceItemId()))).build();
        List<ServiceIndicatorDto> serviceIndicatorDtos = productServiceIndicatorApplication.queryServiceItemIndicator(build);
        log.info("MedicalReportApplicationImpl->queryServiceItemEncyclopedia,serviceIndicatorDtos={}",JsonUtil.toJSONString(serviceIndicatorDtos));
        ServiceItemEncyclopediaDTO serviceItemEncyclopediaDTO = new ServiceItemEncyclopediaDTO();
        serviceItemEncyclopediaDTO.setServiceItemId(param.getServiceItemId());
        if (CollectionUtils.isEmpty(serviceIndicatorDtos)){
            return serviceItemEncyclopediaDTO;
        }

        List<VirusEncyclopediaDTO> virusEncyclopediaDTOS = Lists.newArrayList();
        for (ServiceIndicatorDto p : serviceIndicatorDtos){
            if (StringUtils.isBlank(p.getJdhWikiId())){
                continue;
            }
            VirusEncyclopediaDTO virusEncyclopediaDTO = new VirusEncyclopediaDTO();
            virusEncyclopediaDTO.setVirusEncyclopediaId(p.getJdhWikiId());
            virusEncyclopediaDTO.setVirusEncyclopediaUrl("https://cont.healthjd.com/content/"+p.getJdhWikiId());
            virusEncyclopediaDTO.setVirusEncyclopediaName(p.getIndicatorName());
            virusEncyclopediaDTO.setIndicatorId(p.getIndicatorId());
            virusEncyclopediaDTO.setIndicatorNo(p.getIndicatorId());
            virusEncyclopediaDTOS.add(virusEncyclopediaDTO);
        }

        serviceItemEncyclopediaDTO.setVirusEncyclopediaDTOS(virusEncyclopediaDTOS);
        return serviceItemEncyclopediaDTO;
    }

    /**
     * 按订单id查询报告汇总
     *
     * @param medicalReportRequest
     * @return
     */
    @Override
    public MedicalReportOrderDTO queryReportDetailById(MedicalReportRequest medicalReportRequest) {
        MedicalReport byMedicalPromiseId = medicalReportRepository.getByMedicalPromiseId(medicalReportRequest.getMedicalPromiseId());
        if (Objects.isNull(byMedicalPromiseId)){
            return null;
        }
        MedicalReportOrderDTO medicalReportOrderDTO = new MedicalReportOrderDTO();
        if (StringUtils.isNotBlank(byMedicalPromiseId.getReportOss())){
            String publicUrl = fileManageService.getPublicUrl(byMedicalPromiseId.getReportOss(), Boolean.TRUE, DateUtil.offsetMinute(new Date(), 30));
            medicalReportOrderDTO.setReportFileUrl(publicUrl);
        }
        String structReportOss = byMedicalPromiseId.getStructReportOss();
        if (StringUtils.isNotBlank(structReportOss)){
            String structReportJson = getStructReportStr(structReportOss);
            StructQuickReportContentDTO reportContentDTO = JSON.parseObject(structReportJson, StructQuickReportContentDTO.class);
            List<StructQuickReportResultDTO> reportResult = reportContentDTO.getReportResult();
            StructQuickReportResultDTO structQuickReportResultDTO = reportResult.get(0);
            List<StructQuickReportResultIndicatorDTO> indicators = structQuickReportResultDTO.getIndicators();
            List<ReportIndicatorDTO> indicatorDTOS = Lists.newArrayList();
            for (StructQuickReportResultIndicatorDTO indicatorDTO : indicators){
                ReportIndicatorDTO reportIndicatorDTO = new ReportIndicatorDTO();
                reportIndicatorDTO.setIndicatorNo(indicatorDTO.getIndicatorNo());
                reportIndicatorDTO.setIndicatorName(indicatorDTO.getIndicatorName());
                reportIndicatorDTO.setUnit(indicatorDTO.getUnit());
                reportIndicatorDTO.setValue(indicatorDTO.getValue());
                reportIndicatorDTO.setAbnormalType(indicatorDTO.getAbnormalType());
                reportIndicatorDTO.setNormalRangeValue(indicatorDTO.getNormalRangeValue());
                indicatorDTOS.add(reportIndicatorDTO);
            }
            PromiseMedicalReportDTO promiseMedicalReportDTO = new PromiseMedicalReportDTO();
            promiseMedicalReportDTO.setReportIndicatorDTOList(indicatorDTOS);
            medicalReportOrderDTO.setPromiseMedicalReportDtoList(Lists.newArrayList(promiseMedicalReportDTO));
        }
        return medicalReportOrderDTO;
    }

    /**
     * 查询报告分享链接
     *
     * @param request
     * @return
     */
    @Override
    public ReportShareDTO queryReportShare(ReportShareRequest request) {

        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.REPORT_SHARE_REDIS_KEY, String.valueOf(request.getPromiseId()), String.valueOf(request.getPatientId()));
        if (duccConfig.getReportShareCacheSwitch()){
            String res = jimClient.get(redisKey);
            if (StringUtils.isNotBlank(res)){
                log.info("queryReportShare->res={}",JsonUtil.toJSONString(res));
                return JsonUtil.parseObject(res,ReportShareDTO.class);
            }
        }

        JdhPromise jdhPromise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(request.getPromiseId()).build());
        log.info("queryReportShare->jdhPromise={}",JsonUtil.toJSONString(jdhPromise));
        //TODO 判空抛异常
//        if (Objects.isNull(byPromiseId)){
//            throw new BusinessException()
//        }
        List<JdhPromisePatient> patients = jdhPromise.getPatients();
        JdhPromisePatient jdhPromisePatient = patients.stream().filter(p -> Objects.equals(p.getPatientId(), request.getPatientId())).findFirst().orElse(null);
        //查询有效检测单
        List<MedicalPromiseDTO> medicalPromiseDTOS = medicalPromiseApplication.queryMedicalPromiseList(
                MedicalPromiseListRequest.builder()
                        .promiseId(jdhPromise.getPromiseId())
                        .reportStatus(CommonConstant.ONE)
                        .invalid(Boolean.FALSE)
                        .promisePatientIdList(Lists.newArrayList(jdhPromisePatient.getPromisePatientId()))
                        .build()
        );
        log.info("queryReportShare->medicalPromiseDTOS={}",JsonUtil.toJSONString(medicalPromiseDTOS));

        List<Long> medicalPromiseIdList = medicalPromiseDTOS.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList());
        List<MedicalReport> reportList = medicalReportRepository.getByMedicalPromiseIdList(medicalPromiseIdList);
        Integer haveAbnormal = CommonConstant.ZERO;
        Integer abnormalNum = CommonConstant.ZERO;
        List<StructQuickReportResultIndicatorDTO> abnormalList = Lists.newArrayList();
        for (MedicalReport medicalReport : reportList){
            String structReportJson = "";
            String structReportKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STRUCT_REPORT_CACHE_KEY,medicalReport.getStructReportOss());
            structReportJson = jimClient.get(structReportKey);
            if (StringUtils.isBlank(structReportJson)){
                structReportJson = getStructReportStr(medicalReport.getStructReportOss());
                jimClient.setEx(structReportKey,structReportJson,5L,TimeUnit.MINUTES);
            }

            StructQuickReportContentDTO reportContentDTO = JSON.parseObject(structReportJson, StructQuickReportContentDTO.class);
            List<StructQuickReportResultDTO> reportResult = reportContentDTO.getReportResult();

            for (StructQuickReportResultDTO quickReportResultDTO : reportResult){
                List<StructQuickReportResultIndicatorDTO> abnormal = quickReportResultDTO.getIndicators().stream().filter(p -> !StringUtils.equals(CommonConstant.ZERO_STR, p.getAbnormalType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(abnormal)){
                    abnormalList.addAll(abnormal);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(abnormalList)){
            haveAbnormal = CommonConstant.ONE;
            abnormalNum = abnormalList.size() > 3 ? CommonConstant.THREE : abnormalList.size();
        }


        ReportShareDTO reportShareDTO = null;

        //判断检测单规则
        //sku规则
        Set<String> serviceIdSet = medicalPromiseDTOS.stream().map(p->String.valueOf(p.getServiceId())).collect(Collectors.toSet());
        Map<String, String> serviceReportShareType = duccConfig.getServiceReportShareType();
        Set<String> configKeySet = serviceReportShareType.keySet();
        if (configKeySet.containsAll(serviceIdSet)){

            Set<String> intersection = new HashSet<>(configKeySet); // 创建一个set1的副本
            intersection.retainAll(serviceIdSet);
            Set<String> shareType = Sets.newHashSet();
            intersection.forEach(p->{
                shareType.add(serviceReportShareType.get(p));
            });
            if (shareType.size()>1){
                reportShareDTO = getReportShareDTO(abnormalNum, "default");
            }else {
                reportShareDTO =  getReportShareDTO(abnormalNum, shareType.stream().findFirst().orElse(null));
            }

        }


        if (Objects.isNull(reportShareDTO)){
            //项目规则
            Set<String> serviceItemIdSet = medicalPromiseDTOS.stream().map(MedicalPromiseDTO::getServiceItemId).collect(Collectors.toSet());
            Map<String, String> itemReportShareType = duccConfig.getItemReportShareType();
            Set<String> itemKeySet = itemReportShareType.keySet();
            if (itemKeySet.containsAll(serviceItemIdSet)){

                Set<String> intersection = new HashSet<>(itemKeySet); // 创建一个set1的副本
                intersection.retainAll(serviceItemIdSet);
                Set<String> shareType = Sets.newHashSet();
                intersection.forEach(p->{
                    shareType.add(itemReportShareType.get(p));
                });
                if (shareType.size()>1){
                    reportShareDTO = getReportShareDTO(abnormalNum, "default");
                }else {
                    reportShareDTO =  getReportShareDTO(abnormalNum, shareType.stream().findFirst().orElse(null));
                }

            }

        }

        if (Objects.isNull(reportShareDTO)){
            reportShareDTO =  getReportShareDTO(haveAbnormal,"default");
        }
        jimClient.setEx(redisKey,JsonUtil.toJSONString(reportShareDTO),CommonConstant.TWENTY,TimeUnit.HOURS);
        return reportShareDTO;
    }

    /**
     * 新增或保存体检报告
     *
     * @param medicalReportSaveCmd
     * @return
     */
    @Override
    public SaveMedicalReportResultDTO savePhyReport(MedicalReportSaveCmd medicalReportSaveCmd) {
        AssertUtils.nonNull(medicalReportSaveCmd.getFileId(),"fileId不能为空");
        AssertUtils.nonNull(medicalReportSaveCmd.getPromiseId(),"promiseId不能为空");

        JdhFile jdhFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(medicalReportSaveCmd.getFileId()).build());
        SaveMedicalReportResultDTO saveMedicalReportResultDTO = new SaveMedicalReportResultDTO();

        if (Objects.isNull(jdhFile)){
            saveMedicalReportResultDTO.setRes(Boolean.FALSE);
            return saveMedicalReportResultDTO;
        }
        JdhPromise jdhPromise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(medicalReportSaveCmd.getPromiseId()).build());

        //目前一个promise只有一个报告，所以如果库里有报告的话，则属于更新
        List<MedicalReport> medicalReports = medicalReportRepository.selectByCondition(MedicalReportQueryBO.builder().promiseId(medicalReportSaveCmd.getPromiseId()).build());
        if (CollectionUtils.isNotEmpty(medicalReports)){
            MedicalReport medicalReportExist = medicalReports.get(0);
            MedicalReport medicalReport = new MedicalReport();
            medicalReport.setId(medicalReportExist.getId());
            medicalReport.setReportOss(jdhFile.getFilePath());
            boolean update = medicalReportRepository.update(medicalReport);
            if (update) {
                medicalReportRepository.insertMedicalReportChangeLog(MedicalReportConverter.INS.reportBoToChangeLogBo(medicalReportExist));
            }
            saveMedicalReportResultDTO.setRes(update);
            saveMedicalReportResultDTO.setReportId(medicalReport.getId());
            Event event = EventFactory.newDefaultEvent(jdhPromise, PromiseEventTypeEnum.PROMISE_REPORT_GENERATE,null);
            Event event2 = EventFactory.newDefaultEvent(jdhPromise, PromiseEventTypeEnum.PROMISE_REPORT_GENERATE_TO_BUY,null);
            eventCoordinator.publish(event);
            eventCoordinator.publish(event2);
            return saveMedicalReportResultDTO;
        }

        PromiseDto promiseByPromiseId = promiseApplication.findPromiseByPromiseId(PromiseIdRequest.builder().promiseId(medicalReportSaveCmd.getPromiseId()).build());
        if (Objects.isNull(promiseByPromiseId)){
            throw new BusinessException(PromiseErrorCode.PROMISE_CHANNEL_NO_ILLEGAL);
        }


        MedicalReport medicalReport = new MedicalReport();
        medicalReport.setPromiseId(medicalReportSaveCmd.getPromiseId());
        medicalReport.setReportOss(jdhFile.getFilePath());
        medicalReport.setReportTime(new Date());
        medicalReport.setUserPin(promiseByPromiseId.getUserPin());
        medicalReport.setParentId(promiseByPromiseId.getPatients().get(0).getPatientId());
        //TODO
        medicalReport.setExaminationTime("2024-01-01");
        medicalReport.setReportSource(CommonConstant.ONE);
        medicalReport.setChannelNo(promiseByPromiseId.getChannelNo());
        medicalReport.setCreateTime(new Date());
        medicalReport.setUpdateTime(new Date());
        Long insert = medicalReportRepository.insert(medicalReport);
        if (Objects.nonNull(insert)){
            saveMedicalReportResultDTO.setRes(Boolean.TRUE);
            saveMedicalReportResultDTO.setReportId(insert);
        }

        //发送报告回传事件
        Event event = EventFactory.newDefaultEvent(jdhPromise, PromiseEventTypeEnum.PROMISE_REPORT_GENERATE,null);
        Event event2 = EventFactory.newDefaultEvent(jdhPromise, PromiseEventTypeEnum.PROMISE_REPORT_GENERATE_TO_BUY,null);
        eventCoordinator.publish(event);
        eventCoordinator.publish(event2);


        return saveMedicalReportResultDTO;
    }

    /**
     * 按报告id，查询报告url
     *
     * @param request
     * @return
     */
    @Override
    public MedicalReportUrlDTO queryUrlWithAuth(MedicalReportNoPinRequest request) {
        //TODO  鉴权方式增多后改为策略模式
        //鉴权
        MedicalReportAuthDTO auth = checkReportAuth(request);
        if (Objects.equals(CommonConstant.ZERO,auth.getAuth())){
            MedicalReportUrlDTO medicalReportUrlDTO = new MedicalReportUrlDTO();
            medicalReportUrlDTO.setReportId(request.getReportId());
            medicalReportUrlDTO.setExtendInfo(auth.getExtendInfo());
            return medicalReportUrlDTO;
//            throw new BusinessException(ReportErrorCode.HAVE_NO_AUTH);
        }

        //2.查询报告
        List<MedicalReport> medicalReports = medicalReportRepository.selectByCondition(MedicalReportQueryBO.builder().id(request.getReportId()).promiseId(request.getPromiseId()).build());
        // 过滤主子报告
        MedicalReport mainMedicalReport = medicalReports.stream().filter(p -> Objects.equals(CommonConstant.ONE, p.getReportType())).findFirst().orElse(null);
        List<MedicalReport> subMedicalReport = medicalReports.stream().filter(p -> Objects.equals(CommonConstant.TWO, p.getReportType())).collect(Collectors.toList());

        //有效期 5分钟
        Date expire = DateUtil.offsetMinute(new Date(),5);
        MedicalReportUrlDTO medicalReportUrlDTO = convert(mainMedicalReport,expire);
        if (CollectionUtils.isNotEmpty(subMedicalReport)){
            List<MedicalReportUrlDTO> subDTO = Lists.newArrayList();
            for (MedicalReport medicalReport : subMedicalReport){
                subDTO.add(convert(medicalReport,expire));
            }
            medicalReportUrlDTO.setSubMedicalReports(subDTO);
        }
        return medicalReportUrlDTO;
    }

    /**
     * 鉴权
     * @param request
     */
    @Override
    public MedicalReportAuthDTO checkReportAuth(MedicalReportNoPinRequest request){
        MedicalReportAuthDTO medicalReportAuthDTO = new MedicalReportAuthDTO();
        medicalReportAuthDTO.setAuth(CommonConstant.ONE);
        if (Objects.equals(CommonConstant.ONE,request.getAuthentication())){
            //设备号+短信验证码校验
            Boolean auth = Boolean.FALSE;
            //1.查看此设备号是否有权限
            String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.REPORT_UUID_AUTH_CACHE, request.getReportId(),request.getUserPin());
            //有权限
            if (StringUtils.isNotBlank(jimClient.get(redisKey))){
                auth = Boolean.TRUE;
            }else {
                if (StringUtils.isNotBlank(request.getVerificationCode())){
                    String messageKey = ReachConstant.SMS_CODE_CACHE_PRE + request.getReportId().toString() + "_" + request.getUserPin();
//                    String messageKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.REPORT_UUID_AUTH_MESSAGE_CACHE,request.getReportId(), request.getDeviceNumber(),request.getVerificationCode());
                    if (StringUtils.isNotBlank(jimClient.get(messageKey)) && StringUtils.equals(jimClient.get(messageKey),request.getVerificationCode())){
                        //有权限
                        auth = Boolean.TRUE;
                        jimClient.setEx(redisKey,String.valueOf(request.getReportId()),duccConfig.getPopReportAuthTime(),TimeUnit.MINUTES);
                    }else {
                        throw new BusinessException(PromiseErrorCode.SMS_CODE_ILLEGAL);
                    }
                }

            }

            if (!auth){
                log.info("MedicalReportApplicationImpl->queryUrlWithAuth,无权限,promiseId = {},uuid={}",String.valueOf(request.getPromiseId()),request.getDeviceNumber());
                MedicalReport byId = medicalReportRepository.getById(request.getReportId());
                if (Objects.isNull(byId)){
                    throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
                }
                JdhPromise jdhPromise = promiseRepository.find(JdhPromiseIdentifier.builder().promiseId(byId.getPromiseId()).build());
                String phone = jdhPromise.getPatients().get(0).getPhoneNumber().mask();
                String tip = "将向体检人手机号码"+phone+"发送验证码，请输入接收到的验证码";
                Map<String,String> extendInfo = Maps.newHashMap();
                extendInfo.put("popReportSendMsg",tip);
                medicalReportAuthDTO.setAuth(CommonConstant.ZERO);
                medicalReportAuthDTO.setExtendInfo(extendInfo);
            }
        }else {
            throw new BusinessException(BaseDomainErrorCode.UNKNOWN_ERROR);
        }
        return medicalReportAuthDTO;
    }

    /**
     * 根据其中一个检测单，查询履约单+patientId下对应的所有检测单
     *
     * @param request 根据其中一个检测单，查询履约单+patientId下对应的所有检测单
     * @return 医疗报告摘要DTO列表
     */
    @Override
    @LogAndAlarm
    public MedicalReportSummaryDTO queryListByMedPromiseId(OuterOrderRequest request) {

        MedicalReportSummaryDTO resultDTO  = new MedicalReportSummaryDTO();

        List<MedicalPromiseDTO> medicalPromiseDTOList = null;
        MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest.builder().medicalPromiseId(request.getMedicalPromiseId()).build());
        if (Objects.isNull(medicalPromiseDTO)){
            return null;
        }
        //根据查询有哪些检测项，哪些检测项目已出
        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseIdList(Lists.newArrayList(medicalPromiseDTO.getPromiseId()));
        medicalPromiseListRequest.setPromisePatientIdList(Lists.newArrayList(medicalPromiseDTO.getPromisePatientId()));
        medicalPromiseListRequest.setInvalid(false);
        medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);


        if (CollectionUtils.isEmpty(medicalPromiseDTOList)) {
            log.error("MedicalReportApplicationImpl.queryListByMedPromiseId orderList isEmpty request={}", JSON.toJSONString(request));
            return resultDTO;
        }
        //过滤已出的报告列表
        medicalPromiseDTOList = medicalPromiseDTOList.stream().filter(o -> MedicalPromiseStatusEnum.isReportOut(o.getStatus())).collect(Collectors.toList());

        List<PromiseMedicalReportDTO> reportDtoList = Lists.newArrayList();
        resultDTO.setReportDtoList(reportDtoList);

        List<Long> medicalPromiseIdList = medicalPromiseDTOList.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(medicalPromiseIdList)) {
            return resultDTO;
        }

        for (MedicalPromiseDTO promiseDTO : medicalPromiseDTOList) {
            PromiseMedicalReportDTO promiseMedicalReportDTO = new PromiseMedicalReportDTO();
            promiseMedicalReportDTO.setMedicalPromiseId(promiseDTO.getMedicalPromiseId());
            promiseMedicalReportDTO.setStatus(promiseDTO.getStatus());
            promiseMedicalReportDTO.setServiceItemName(promiseDTO.getServiceItemName());
            promiseMedicalReportDTO.setServiceId(String.valueOf(promiseDTO.getServiceId()));
            reportDtoList.add(promiseMedicalReportDTO);
        }



        MedPromiseHistoryRequest medPromise = new MedPromiseHistoryRequest();
        medPromise.setMedicalPromiseIds(Sets.newHashSet(medicalPromiseIdList));
        medPromise.setAfterStatus(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus());
        List<MedPromiseHistoryDTO> medPromiseHistoryDTOList = medPromiseHistoryApplication.queryMedPromiseHistoryList(medPromise);
        log.info("MedicalReportApplicationImpl.queryListByMedPromiseId medicalPromiseIdList={} medPromiseHistoryDTOList={}", JSON.toJSONString(medicalPromiseIdList), JSON.toJSONString(medPromiseHistoryDTOList));
        Map<Long, List<MedPromiseHistoryDTO>> historyMap = medPromiseHistoryDTOList.stream().collect(Collectors.groupingBy(MedPromiseHistoryDTO :: getMedicalPromiseId));

        List<MedicalReport> reportList = medicalReportRepository.getByMedicalPromiseIdList(medicalPromiseIdList);
        if (request.getPatientId() != null) {
            reportList = reportList.stream().filter(o -> o.getPatientId() == null || o.getPatientId().equals(request.getPatientId())).collect(Collectors.toList());
        }
        log.info("MedicalReportApplicationImpl.queryListByMedPromiseId medicalPromiseIdList={} reportList={}", JSON.toJSONString(medicalPromiseIdList), JSON.toJSONString(reportList));
        if (CollectionUtils.isEmpty(reportList)) {
            return resultDTO;
        }
        // key:medicalPromiseId  value:reportId
        Map<Long, Long> medicalPromiseIdMap = reportList.stream().collect(Collectors.toMap(MedicalReport::getMedicalPromiseId, MedicalReport::getId, (key1, key2) -> key2));
        Map<Long, MedicalReport> mpIdToReport = reportList.stream().collect(Collectors.toMap(MedicalReport::getMedicalPromiseId, p -> p));

        //维护检测单和报告的关系
        for (PromiseMedicalReportDTO promiseDTO : reportDtoList) {
            if(CollectionUtils.isNotEmpty(historyMap.get(promiseDTO.getMedicalPromiseId()))){
                promiseDTO.setCheckTime(historyMap.get(promiseDTO.getMedicalPromiseId()).get(0).getCreateTime());
            }
            if (medicalPromiseIdMap.get(promiseDTO.getMedicalPromiseId()) == null) {
                continue;
            }

            promiseDTO.setReportId(medicalPromiseIdMap.get(promiseDTO.getMedicalPromiseId()).toString());
        }



        if (Boolean.TRUE.equals(request.getReportDetail())){
            log.info("病假单-判断是否开具病假单");
            MedReportIndicatorQueryBO medReportIndicatorQueryBO = new MedReportIndicatorQueryBO();
            Set<String> mpIds = reportDtoList.stream().map(PromiseMedicalReportDTO::getReportId).collect(Collectors.toSet());
            medReportIndicatorQueryBO.setReportIds(mpIds);
            medReportIndicatorQueryBO.setAbnormalOnly(Boolean.TRUE);
            List<MedicalReportIndicator> medicalReportIndicators = medicalReportIndicatorRepository.listMedicalReportIndicators(medReportIndicatorQueryBO);
            if(CollectionUtils.isEmpty(medicalReportIndicators)){
                return resultDTO;
            }
            resultDTO.setAllAbnormalIndicatorDTOList(MedicalReportIndicatorConvert.INSTANCE.convertResultIndicator(medicalReportIndicators));
            log.info("病假单-判断是否开具病假单 medicalReportIndicators={}",JSON.toJSONString(medicalReportIndicators));
            //判断是否开具病假单
            MedicalReport medicalReport = reportList.stream().min(Comparator.comparing(MedicalReport::getCreateTime)).orElse(null);
            Date dayOfTomorrow = TimeUtils.getDateEnding(TimeUtils.addDays(medicalReport.getCreateTime(),duccConfig.getSickCertExpiredDayNum()));
            //查出符合条件的报告
            List<Long> reportIds = reportList.stream().filter(t->t.getCreateTime().getTime()>=medicalReport.getCreateTime().getTime()&&t.getCreateTime().getTime()<dayOfTomorrow.getTime()&&dayOfTomorrow.getTime()>System.currentTimeMillis()).map(MedicalReport::getId).collect(Collectors.toList());
            long abnormalNum = medicalReportIndicators.stream().filter(t->reportIds.contains(Long.parseLong(t.getReportId()))).count();
            if(abnormalNum>0){
                resultDTO.setOpenSickCert(true);
                resultDTO.setSickCertEndDay(dayOfTomorrow);
            }
        }

        return resultDTO;

    }

    /**
     * 根据外部订单ID查询医疗报告订单
     *
     * @param request 外部订单请求对象
     * @return 医疗报告订单DTO
     */
    @Override
    public MedicalReportOrderDTO queryPromiseByMedPromiseId(OuterOrderRequest request) {
        MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest.builder().medicalPromiseId(request.getMedicalPromiseId()).build());
        if (Objects.isNull(medicalPromiseDTO)){
            return null;
        }
        MedicalReportOrderDTO medicalReportOrderDTO = new MedicalReportOrderDTO();
        medicalReportOrderDTO.setPromiseId(medicalPromiseDTO.getPromiseId());
        JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(medicalPromiseDTO.getPromiseId()).userPin(request.getUserPin()).build());
        JdhPromisePatient jdhPromisePatient = promise.getPatients().stream().filter(p -> Objects.equals(p.getPromisePatientId(), medicalPromiseDTO.getPromisePatientId())).findFirst().orElse(null);
        if(Objects.nonNull(jdhPromisePatient)){
            medicalReportOrderDTO.setPatientId(jdhPromisePatient.getPatientId());
        }
        return medicalReportOrderDTO;
    }

    @Override
    public Integer queryAbnormalCount(List<Long> medicalPromiseIds) {
        if (CollectionUtils.isEmpty(medicalPromiseIds)){
            return null;
        }

        LambdaQueryWrapper<MedicalReportPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MedicalReportPo::getMedicalPromiseId, medicalPromiseIds);
        queryWrapper.eq(MedicalReportPo::getYn, YnStatusEnum.YES.getCode());
        List<MedicalReportPo> reports = medicalReportMapper.selectList(queryWrapper);

        List<String> reportIds = reports.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.toList());

        LambdaQueryWrapper<MedicalReportIndicatorPo> indicatorWrapper = Wrappers.lambdaQuery();
        indicatorWrapper.in(MedicalReportIndicatorPo::getReportId, reportIds)
                .eq(MedicalReportIndicatorPo::getYn, YnStatusEnum.YES.getCode())
                .ne(MedicalReportIndicatorPo::getAbnormalMarkType, 0);
        return medicalReportIndicatorMapper.selectCount(indicatorWrapper);
    }

    /**
     * 根据快速检查药品请求生成跳转链接。
     *
     * @param request 快速检查药品请求对象，包含查询所需的参数。
     * @return 生成的跳转链接。
     */
    @Override
    public String queryQuickCheckJumpLink(QuickCheckMedicineRequest request) {
        QuickCheckMedicineBo medicineBo = MedicalReportIndicatorConvert.INSTANCE.convert(request);
        //唯一标识
        String uniqueSign = request.getPerformanceId()+"_"+request.getPatientId().toString();
        medicineBo.setUniqueSign(uniqueSign);
        medicineBo.setDetectionImgList(request.getDetectionImgList());
        QuickCheckResultBo quickCheckResultBo = quickCheckMedicineRpc.quickCheckJumpLink(medicineBo);
        if (Objects.nonNull(quickCheckResultBo)){
            return quickCheckResultBo.getJumpLink();
        }
        return null;
    }

    /**
     * 添加处方药到购物车
     *
     * @param rxNewPrescriptAddCartCmd 处方药添加到购物车的命令对象
     * @return 添加结果，true表示成功，false表示失败
     */
    @Override
    public AddCartForPrescriptDTO addCartForPrescriptV2(RxNewPrescriptAddCartCmd rxNewPrescriptAddCartCmd) {
        RxNewPrescriptAddCartBo bo = MedicalReportIndicatorConvert.INSTANCE.convert(rxNewPrescriptAddCartCmd);
        AddCartResultBo addCartResultBo = quickCheckMedicineRpc.addCartForPrescriptV2(bo);
        AddCartForPrescriptDTO cartForPrescriptDTO = new AddCartForPrescriptDTO();
        cartForPrescriptDTO.setRes(Boolean.FALSE);
        if (Objects.nonNull(addCartResultBo) && addCartResultBo.getSuccess()){
            cartForPrescriptDTO.setRes(addCartResultBo.getSuccess());
            UserMarketRequest userMarketRequest = new UserMarketRequest();
            userMarketRequest.setMarketType(UserMarketTypeEnum.NET_FORMULARY.getType());
            userMarketRequest.setUserPin(rxNewPrescriptAddCartCmd.getPin());
            userMarketRequest.setScene(UserMarketSceneEnum.QUICK_CHECK_REPORT_DRUG.getScene());
            userMarketRequest.setMarketDetail(rxNewPrescriptAddCartCmd.getPrescriptId());
            List<UserMarketDTO> userMarketDTOS = userMarketApplication.queryUserMarketList(userMarketRequest);
            if (CollectionUtils.isNotEmpty(userMarketDTOS)) {
                NethpRxSkuExtendBO nethpRxSkuExtendBO = JsonUtil.parseObject(userMarketDTOS.get(0).getExtendInfo(), NethpRxSkuExtendBO.class);
                cartForPrescriptDTO.setStoreId(nethpRxSkuExtendBO.getStoreId());
                cartForPrescriptDTO.setSkuInfoList(MedicalReportIndicatorConvert.INSTANCE.convertSkuBo(nethpRxSkuExtendBO.getNethpRxSkuBOList()));
            }


        }
        return  cartForPrescriptDTO;
    }

    /**
     * 查询已出报告图片链接
     *
     * @param medicalReportImgRequest 包含医疗报告图片相关信息的请求对象。
     * @return 报告摘要图片的URL。
     */
    @Override
    public String queryReportSummaryImg(MedicalReportImgRequest medicalReportImgRequest) {
        MedicalReportQueryBO medicalReportQueryBO = MedicalReportQueryBO.builder().promiseId(medicalReportImgRequest.getPromiseId()).patientId(medicalReportImgRequest.getPatientId()).userPin(medicalReportImgRequest.getUserPin()).build();
        List<MedicalReport> medicalReports = medicalReportRepository.selectByCondition(medicalReportQueryBO);
        if (CollectionUtils.isEmpty(medicalReports)){
            return null;
        }
        MedicalReport medicalReport = medicalReports.stream().filter(p -> StringUtils.isNotBlank(p.getReportJpgOss())).findFirst().orElse(null);
        if (Objects.nonNull(medicalReport)){
            return fileManageService.getPublicUrl(medicalReport.getReportJpgOss(),Boolean.TRUE,DateUtil.offsetDay(new Date(),1));
        }
        return null;
    }

    /**
     * 查询报告历史变更记录，记录的是前一个最新状态，最新的在medicalReport表
     *
     * @return list
     */
    @Override
    public List<MedicalReportChangeLog> queryMedicalReportChangeLogList(MedicalReportChangeLog medicalReportChangeLog) {
        return medicalReportRepository.queryMedicalReportChangeLogList(medicalReportChangeLog);
    }

    /**
     * 查询检测报告列表
     *
     * @param medicalReportRequest 医疗报告请求对象，包含查询条件
     * @return 医疗报告DTO列表
     */
    @Override
    public List<MedicalReportDTO> queryMedicalReportList(MedicalReportRequest medicalReportRequest) {
        MedicalReportQueryBO medicalReportQueryBO = MedicalReportQueryBO.builder().promiseId(medicalReportRequest.getPromiseId()).build();
        List<MedicalReport> medicalReports = medicalReportRepository.selectByCondition(medicalReportQueryBO);
        return  convert2MedicalReportDTOs(medicalReports);
    }

    /**
     * 按报告id，查询报告url（跳转档案页面）
     *
     * @param request
     * @return
     */
    @Override
    public MedicalReportUrlDTO queryUrlForCenter(MedicalReportRequest request) {
        MedicalReportUrlDTO medicalReportUrlDTO = queryUrl(request);
        if (Objects.nonNull(medicalReportUrlDTO) && StringUtils.isNotBlank(medicalReportUrlDTO.getReportUrl())){
            try {
                String encode = URLEncoder.encode(medicalReportUrlDTO.getReportUrl(), "UTF-8");
                medicalReportUrlDTO.setReportUrl(MessageFormat.format(duccConfig.getCenterUrlPre(), encode));
            }catch (Exception e){
                log.info("url encode error,request={}",JsonUtil.toJSONString(request),e);
            }
        }
        return medicalReportUrlDTO;
    }


    /**
     * convert
     * @param medicalReport
     * @param expire
     * @return
     */
    private MedicalReportUrlDTO convert(MedicalReport medicalReport,Date expire){
        MedicalReportUrlDTO medicalReportUrlDTO = new MedicalReportUrlDTO();
        medicalReportUrlDTO.setReportId(medicalReport.getId());
        String publicUrl = fileManageService.getPublicUrl(medicalReport.getReportOss(), Boolean.TRUE, expire);
        medicalReportUrlDTO.setReportUrl(publicUrl);
        return medicalReportUrlDTO;
    }

    private List<MedicalReportDTO> convert2MedicalReportDTOs(List<MedicalReport> medicalReports){
        if (CollectionUtils.isEmpty(medicalReports)){
            return null;
        }
        List<MedicalReportDTO> medicalReportDTOS = new ArrayList<>();
        for (MedicalReport medicalReport : medicalReports) {
            medicalReportDTOS.add(convert2MedicalReportDTO(medicalReport));
        }
        return medicalReportDTOS;
    }

    private MedicalReportDTO convert2MedicalReportDTO(MedicalReport medicalReport) {
        if (medicalReport == null) {
            return null;
        }
        MedicalReportDTO medicalReportDTO = new MedicalReportDTO();
        medicalReportDTO.setId(medicalReport.getId());
        medicalReportDTO.setPromiseId(medicalReport.getPromiseId());
        medicalReportDTO.setMedicalPromiseId(medicalReport.getMedicalPromiseId());
        medicalReportDTO.setUserPin(medicalReport.getUserPin());
        medicalReportDTO.setPatientId(medicalReport.getPatientId());
        medicalReportDTO.setPatientName(medicalReport.getPatientName());
        medicalReportDTO.setMedicalType(medicalReport.getMedicalType());
        medicalReportDTO.setRelativesType(medicalReport.getRelativesType());
        medicalReportDTO.setParentId(medicalReport.getParentId());
        medicalReportDTO.setReportType(medicalReport.getReportType());
        medicalReportDTO.setExaminationTime(medicalReport.getExaminationTime());
        medicalReportDTO.setFileMd5(medicalReport.getFileMd5());
        medicalReportDTO.setReportOss(medicalReport.getReportOss());
        medicalReportDTO.setSourceOss(medicalReport.getSourceOss());
        medicalReportDTO.setStructReportOss(medicalReport.getStructReportOss());
        medicalReportDTO.setReportSource(medicalReport.getReportSource());
        medicalReportDTO.setChannelNo(medicalReport.getChannelNo());
        medicalReportDTO.setChannelReportNo(medicalReport.getChannelReportNo());
        medicalReportDTO.setReportTime(medicalReport.getReportTime());
        medicalReportDTO.setReportStatus(medicalReport.getReportStatus());
        medicalReportDTO.setReportCenterId(medicalReport.getReportCenterId());
        medicalReportDTO.setReportJpgOss(medicalReport.getReportJpgOss());
        medicalReportDTO.setCreateTime(medicalReport.getCreateTime());
        medicalReportDTO.setUpdateTime(medicalReport.getUpdateTime());
        return medicalReportDTO;
    }

    private Long getReportIdByMedicalPromiseId(Long medicalPromiseId) {
        //添加缓存，防止主从不同步，影响查询准确性
        String reportId = jimClient.get(CacheConstant.MEDICAL_PROMISE_ID_REPORT_ID_PREFIX + medicalPromiseId);
        if (StringUtils.isNotBlank(reportId)) {
            return new Long(reportId);
        }
        //按检测单去查询报告是否存在
        MedicalReport oldReport = medicalReportRepository.getByMedicalPromiseId(medicalPromiseId);
        if (oldReport == null) {
            return null;
        }
        return oldReport.getId();
    }

    private List<PromiseMedicalReportDTO> convert2PromiseMedicalReportDTOList(List<MedicalPromiseDTO> medicalPromiseDTOList, List<MedicalReport> reportList,Boolean combine,Set<Long> promisePatientId) {
        List<PromiseMedicalReportDTO> resultList = Lists.newArrayList();
        Map<Long, MedicalReport> medicalReportMap = reportList.stream().collect(Collectors.toMap(MedicalReport::getMedicalPromiseId, Function.identity(), (key1, key2) -> key2));
        for (MedicalPromiseDTO dto : medicalPromiseDTOList) {
            PromiseMedicalReportDTO reportDTO = new PromiseMedicalReportDTO();
//            resultList.add(reportDTO);
            reportDTO.setMedicalPromiseId(dto.getMedicalPromiseId());
            reportDTO.setStatus(dto.getStatus());
            reportDTO.setServiceItemName(dto.getServiceItemName());
            reportDTO.setCheckTime(dto.getCheckTime());
            reportDTO.setServiceItemId(dto.getServiceItemId());
            if (!MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(dto.getStatus())) {
                //如果指标是非完成状态
                if (combine){
                    resultList.add(reportDTO);
                }else if (CollectionUtils.isNotEmpty(promisePatientId) && promisePatientId.contains(dto.getPromisePatientId())){
                    resultList.add(reportDTO);
                }
                continue;
            }
            MedicalReport report = medicalReportMap.get(dto.getMedicalPromiseId());
            if (report == null) {
                if (combine){
                    resultList.add(reportDTO);
                }else if (CollectionUtils.isNotEmpty(promisePatientId) && promisePatientId.contains(dto.getPromisePatientId())){
                    resultList.add(reportDTO);
                }
                log.warn("MedicalReportApplicationImpl.convert2PromiseMedicalReportDTOList report is null MedicalPromiseId={}", dto.getMedicalPromiseId());
                continue;
            }
            resultList.add(reportDTO);
            reportDTO.setReportId(report.getId().toString());
            reportDTO.setReportCreateTime(report.getCreateTime());
            String structReportJson = getStructReportStr(report.getStructReportOss());
            if (StringUtils.isBlank(structReportJson)) {
                log.warn("MedicalReportApplicationImpl.convert2PromiseMedicalReportDTOList structReportJson is null MedicalPromiseId={}, ReportId={}", dto.getMedicalPromiseId(), report.getId());
                continue;
            }
            StructQuickReportContentDTO reportContentDTO = JSON.parseObject(structReportJson, StructQuickReportContentDTO.class);
            if (reportContentDTO == null) {
                log.warn("MedicalReportApplicationImpl.convert2PromiseMedicalReportDTOList reportContentDTO is null MedicalPromiseId={}, ReportId={}", dto.getMedicalPromiseId(), report.getId());
                continue;
            }
            if (reportContentDTO.getReportBasicInfo() != null) {
                reportDTO.setSampleCharacteristics(reportContentDTO.getReportBasicInfo().getSampleCharacteristics());//样本性状
                reportDTO.setTestingMethod(reportContentDTO.getReportBasicInfo().getTestingMethod());
            }
            List<ReportIndicatorDTO> reportIndicatorDTOList = convert2ReportIndicatorDTOList(reportContentDTO.getReportResult());
            int anomalyNum = 0;
            for (ReportIndicatorDTO reportIndicatorDTO : reportIndicatorDTOList) {
                if (StringUtils.isBlank(reportIndicatorDTO.getAbnormalType())) {
                    continue;
                }
                if (!reportIndicatorDTO.getAbnormalType().equals("0")) {
                    anomalyNum++;
                }
            }
            reportDTO.setIndicatorNum(reportIndicatorDTOList.size());
            reportDTO.setAnomalyNum(anomalyNum);
            reportIndicatorDTOList.sort(Comparator.comparing(ReportIndicatorDTO::getAbnormalType).reversed());
            reportDTO.setReportIndicatorDTOList(reportIndicatorDTOList);
        }
        return resultList;
    }

    private List<ReportIndicatorDTO> convert2ReportIndicatorDTOList(List<StructQuickReportResultDTO> reportResult) {
        List<ReportIndicatorDTO> resultList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(reportResult)) {
            return resultList;
        }
        Set<String> oldKnightSkuIndicatorValue = duccConfig.getOldKnightSkuIndicatorValue();
        Map<String, String> indicatorAbnoramlDesc = duccConfig.getIndicatorAbnoramlDesc();
        for (StructQuickReportResultDTO resultDTO : reportResult) {
            if (CollectionUtils.isEmpty(resultDTO.getIndicators())) {
                continue;
            }
            for (StructQuickReportResultIndicatorDTO dto : resultDTO.getIndicators()) {
                ReportIndicatorDTO reportIndicatorDTO = new ReportIndicatorDTO();
                reportIndicatorDTO.setIndicatorName(dto.getIndicatorName());
                reportIndicatorDTO.setIndicatorNo(dto.getIndicatorNo());
                if (StringUtils.isNumeric(dto.getIndicatorNo())) {
                    reportIndicatorDTO.setIndicatorId(new Long(dto.getIndicatorNo()));
                }
                if (oldKnightSkuIndicatorValue.contains(dto.getIndicatorName())){
                    String s = indicatorAbnoramlDesc.get(dto.getAbnormalType());
                    reportIndicatorDTO.setValue(StringUtils.isNotBlank(s) ? s : dto.getValue());
                    reportIndicatorDTO.setUnit(null);
                    reportIndicatorDTO.setNormalRangeValue(StringUtils.isNotBlank(s) ? "阴性" : dto.getNormalRangeValue());
                }else {
                    reportIndicatorDTO.setValue(dto.getValue());
                    reportIndicatorDTO.setNormalRangeValue(dto.getNormalRangeValue());
                    reportIndicatorDTO.setUnit(dto.getUnit());
                }
                reportIndicatorDTO.setTags(dto.getTags());
                reportIndicatorDTO.setAbnormalType(dto.getAbnormalType());
                String valueDescription = Objects.equals(CommonConstant.ZERO_STR,dto.getAbnormalType()) ? "正常" : "异常";
                reportIndicatorDTO.setValueDescription(valueDescription);
                if (StringUtils.isNotBlank(dto.getTags())){
                    List<IndicatorTagDetailDTO> indicatorTagDetailDTOS = Lists.newArrayList();
                    String[] tagSplit = dto.getTags().split(",");
                    for (int i = 0; i < tagSplit.length; i++) {
                        //条件致病只有在致病异常时候才可展示
                        if (StringUtils.equals(CommonConstant.ZERO_STR,dto.getAbnormalType()) && Objects.equals(CommonConstant.ONE,Integer.valueOf(tagSplit[i]))){
                            continue;
                        }
                        IndicatorTagDetailDTO indicatorTagDetailDTO = new IndicatorTagDetailDTO();
                        indicatorTagDetailDTO.setTag(Integer.valueOf(tagSplit[i]));
                        indicatorTagDetailDTO.setTagName(IndicatorTagEnum.getNameByType(Integer.valueOf(tagSplit[i])));
                        indicatorTagDetailDTOS.add(indicatorTagDetailDTO);
                    }
                    reportIndicatorDTO.setTagDetailDTOS(indicatorTagDetailDTOS);
                }
                resultList.add(reportIndicatorDTO);
            }
        }
        return resultList;
    }

    /**
     * 下载结构化报告字符串
     *
     * @param jssUrl
     * @return
     */
    private String getStructReportStr(String jssUrl) {
        if (StringUtils.isBlank(jssUrl)) {
            return null;
        }
        log.info("MedicalReportApplicationImpl.getStructReportStr -> jssUrl={}", jssUrl);
        InputStream inputStream = fileManageService.get(jssUrl);
        String result = new BufferedReader(new InputStreamReader(inputStream))
                .lines().collect(Collectors.joining("\n"));
        log.info("MedicalReportApplicationImpl.getStructReportStr -> result={}", result);
        String structReportKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STRUCT_REPORT_CACHE_KEY,jssUrl);
        jimClient.setEx(structReportKey,result,5L,TimeUnit.MINUTES);
        return result;
    }

    /**
     * 补全患者信息
     *
     * @param medicalReport
     * @param patientId
     * @param userPin
     */
    private void fillPatient(MedicalReport medicalReport, Long patientId, String userPin) {
        Patient patient = patientRepository.findById(patientId);
        if (patient == null) {
            log.error("[MedicalReportApplicationImpl->fillPatient], result={}", patientId);
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR, "患者id错误");
        }
        if (!userPin.equals(patient.getUserPin())) {
            log.error("[MedicalReportApplicationImpl->fillPatient], userPin={} patient.getUserPin={}", userPin, patient.getUserPin());
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR, "患者id越界");
        }
        medicalReport.setPatientName(patient.getName());
        medicalReport.setRelativesType(patient.getRelativesType());
    }

    private void checkUpdateParam(MedicalReportSaveCmd medicalReportSaveCmd) {
        //用户pin不能为空
        AssertUtils.hasText(medicalReportSaveCmd.getUserPin(), BusinessErrorCode.ILLEGAL_ARG_ERROR);
    }


    private String getOssHeepUrl(String url) {

        LocalDateTime expireTime = LocalDateTime.now().plus(1, ChronoUnit.DAYS);
        Date expire = TimeUtils.localDateTimeToDate(expireTime);
        String urlResult = fileManageService.getPublicUrl(url, true, expire);
        log.info("MedicalReportApplicationImpl.getOssHeepUrl urlResult= {} url={} ", urlResult, url);
        return urlResult;
    }

    /**
     * 数据完整性校验-insert
     *
     * @param medicalReportSaveCmd
     */
    private void checkInsertParam(MedicalReportSaveCmd medicalReportSaveCmd) {
        //用户pin不能为空
        AssertUtils.hasText(medicalReportSaveCmd.getUserPin(), BusinessErrorCode.ILLEGAL_ARG_ERROR);
        //检测单id不能为空
        AssertUtils.nonNull(medicalReportSaveCmd.getMedicalPromiseId(), BusinessErrorCode.ILLEGAL_ARG_ERROR);
        //患者id不能为空，体检的可为空，待优化
//        AssertUtils.nonNull(medicalReportSaveCmd.getParentId(), BusinessErrorCode.ILLEGAL_ARG_ERROR);

    }

    private MedicalReport convert2MedicalReport(MedicalReportSaveCmd medicalReportSaveCmd) {
        if (medicalReportSaveCmd == null) {
            return null;
        }
        MedicalReport medicalReport = new MedicalReport();
        medicalReport.setPromiseId(medicalReportSaveCmd.getPromiseId());
        medicalReport.setMedicalPromiseId(medicalReportSaveCmd.getMedicalPromiseId());
        medicalReport.setUserPin(medicalReportSaveCmd.getUserPin());
        medicalReport.setPatientId(medicalReportSaveCmd.getPatientId());
        medicalReport.setPatientName(medicalReportSaveCmd.getPatientName());
        medicalReport.setRelativesType(medicalReportSaveCmd.getRelativesType());
        medicalReport.setMedicalType(medicalReportSaveCmd.getMedicalType());
        medicalReport.setParentId(medicalReportSaveCmd.getParentId());
        medicalReport.setReportType(medicalReportSaveCmd.getReportType());
        medicalReport.setExaminationTime(medicalReportSaveCmd.getExaminationTime());
        medicalReport.setFileMd5(medicalReportSaveCmd.getFileMd5());
        medicalReport.setReportOss(medicalReportSaveCmd.getReportOss());
        medicalReport.setSourceOss(medicalReportSaveCmd.getSourceOss());
        medicalReport.setStructReportOss(medicalReportSaveCmd.getStructReportOss());
        medicalReport.setReportSource(medicalReportSaveCmd.getReportSource());
        medicalReport.setChannelNo(medicalReportSaveCmd.getChannelNo());
        medicalReport.setChannelReportNo(medicalReportSaveCmd.getChannelReportNo());
        medicalReport.setReportTime(medicalReportSaveCmd.getReportTime());
        medicalReport.setReportStatus(medicalReportSaveCmd.getReportStatus());
        medicalReport.setManufacturerNumber(medicalReportSaveCmd.getManufacturerNumber());
        medicalReport.setSnCode(medicalReportSaveCmd.getSnCode());
        medicalReport.setReportCenterId(medicalReportSaveCmd.getReportCenterId());
        medicalReport.setReportJpgOss(medicalReportSaveCmd.getReportJpgOss());
        return medicalReport;
    }

    /**
     * 根据分享类型获取分享配置
     * @param abnormalNum
     * @param shareType
     * @return
     */
    private ReportShareDTO getReportShareDTO(Integer abnormalNum, String shareType) {
        log.info("");
        String configStr = duccConfig.getShareTypeConfig().get(shareType);
        ReportShareConfigBO reportShareConfigBO = JsonUtil.parseObject(configStr, ReportShareConfigBO.class);
        ReportShareDTO reportShareDTO = new ReportShareDTO();
        reportShareDTO.setImg(reportShareConfigBO.getImgLink());
        reportShareDTO.setContent(reportShareConfigBO.getSubtitle());
        reportShareDTO.setTitle(reportShareConfigBO.getMainTitle());
        reportShareDTO.setUrl(reportShareConfigBO.getUrl());
        reportShareDTO.setJdhImg(reportShareConfigBO.getJdhImgLink());
        Map<String, String> resultLinkMap = reportShareConfigBO.getResultLinkMap();
        if (MapUtils.isEmpty(resultLinkMap)){
            return reportShareDTO;
        }
        String posterLink = resultLinkMap.getOrDefault(abnormalNum.toString(),"");
        if (StringUtils.isBlank(posterLink)){
            posterLink =  resultLinkMap.get("default");
        }
        PosterDTO posterDTO = new PosterDTO();
        posterDTO.setQr_direct(posterLink);
        reportShareDTO.setQrparam(posterDTO);

        return reportShareDTO;
    }

    /**
     * 将 StructQuickReportResultIndicatorDTO 列表转换为 ReportIndicatorDTO 列表。
     * @param indicatorDTOS StructQuickReportResultIndicatorDTO 列表
     * @param indicatorMap 指标ID对应的百科ID映射表
     * @return 转换后的 ReportIndicatorDTO 列表
     */
    private List<ReportIndicatorDTO> convert(List<StructQuickReportResultIndicatorDTO> indicatorDTOS,Map<String,ServiceIndicatorDto> indicatorMap,DuccConfig duccConfig){
        if (CollectionUtils.isEmpty(indicatorDTOS)) {
            return null;
        }
        List<ReportIndicatorDTO> resultList = Lists.newArrayList();
        for (StructQuickReportResultIndicatorDTO dto : indicatorDTOS) {
            ReportIndicatorDTO reportIndicatorDTO = new ReportIndicatorDTO();
            reportIndicatorDTO.setIndicatorName(dto.getIndicatorName());
            reportIndicatorDTO.setIndicatorNo(dto.getIndicatorNo());
            if (StringUtils.isNumeric(dto.getIndicatorNo())) {
                reportIndicatorDTO.setIndicatorId(new Long(dto.getIndicatorNo()));
            }
            reportIndicatorDTO.setCtValue(dto.getCtValue());
            reportIndicatorDTO.setTemplateType(dto.getTemplateType());
            reportIndicatorDTO.setCtValue(dto.getCtValue());
            reportIndicatorDTO.setRangeValueDTOS(dto.getRangeValueDTOS());
            reportIndicatorDTO.setReferenceRangeValue(dto.getReferenceRangeValue());
            //CT模版要用科学计数法
            if (StringUtils.equals(IndicatorTemplateEnum.CT.getTemplateType(),dto.getTemplateType())){
                if (StringUtils.isNotBlank(dto.getIndicatorConcentration())){
                    reportIndicatorDTO.setIndicatorConcentration(NumberUtils.toScientificNotation(dto.getIndicatorConcentration()));
                }
                if (CollectionUtils.isNotEmpty(dto.getRangeValueDTOS())){
                    for (RangeValueDTO rangeValueDTO : dto.getRangeValueDTOS()) {
                        rangeValueDTO.setRangeMax(NumberUtils.toScientificNotation(rangeValueDTO.getRangeMax()));
                        rangeValueDTO.setRangeMin(NumberUtils.toScientificNotation(rangeValueDTO.getRangeMin()));
                        rangeValueDTO.setValue(NumberUtils.toScientificNotation(rangeValueDTO.getValue()));
                    }
                }
            }

            reportIndicatorDTO.setIndicatorConcentrationBigDecimal(dto.getIndicatorConcentrationBigDecimal());

            Set<String> oldKnightSkuIndicatorValue = duccConfig.getOldKnightSkuIndicatorValue();
            Map<String, String> indicatorAbnoramlDesc = duccConfig.getIndicatorAbnoramlDesc();
            if (oldKnightSkuIndicatorValue.contains(dto.getIndicatorName())){
                String s = indicatorAbnoramlDesc.get(dto.getAbnormalType());
                reportIndicatorDTO.setValue(StringUtils.isNotBlank(s) ? s : dto.getValue());
                reportIndicatorDTO.setUnit(null);
                reportIndicatorDTO.setNormalRangeValue(StringUtils.isNotBlank(s) ? "阴性" : dto.getNormalRangeValue());
            }else {
                reportIndicatorDTO.setValue(dto.getValue());
                reportIndicatorDTO.setNormalRangeValue(dto.getNormalRangeValue());
                reportIndicatorDTO.setUnit(dto.getUnit());
            }

            reportIndicatorDTO.setTags(dto.getTags());
            reportIndicatorDTO.setAbnormalType(dto.getAbnormalType());
            //百科ID
            if (StringUtils.isNotBlank(dto.getIndicatorNo()) && indicatorMap.containsKey(dto.getIndicatorNo())){
                reportIndicatorDTO.setVirusEncyclopediaId(indicatorMap.get(dto.getIndicatorNo()).getJdhWikiId());
            }
            reportIndicatorDTO.setValueDescription(StringUtils.equals(CommonConstant.ZERO_STR, dto.getAbnormalType()) ? "正常" : "异常");
            reportIndicatorDTO.setPercentage(dto.getPercentage());
            if (StringUtils.isNotBlank(dto.getTags())) {
                List<IndicatorTagDetailDTO> indicatorTagDetailDTOS = Lists.newArrayList();
                String[] tagSplit = dto.getTags().split(",");
                for (int i = 0; i < tagSplit.length; i++) {
                    //条件致病只有在致病异常时候才可展示
                    if (StringUtils.equals(CommonConstant.ZERO_STR, dto.getAbnormalType()) && Objects.equals(CommonConstant.ONE, Integer.valueOf(tagSplit[i]))) {
                        continue;
                    }
                    IndicatorTagDetailDTO indicatorTagDetailDTO = new IndicatorTagDetailDTO();
                    indicatorTagDetailDTO.setTag(Integer.valueOf(tagSplit[i]));
                    indicatorTagDetailDTO.setTagName(IndicatorTagEnum.getNameByType(Integer.valueOf(tagSplit[i])));
                    indicatorTagDetailDTO.setTagTip(duccConfig.getTagTip().get(String.valueOf(indicatorTagDetailDTO.getTag())));
                    indicatorTagDetailDTOS.add(indicatorTagDetailDTO);
                }
                reportIndicatorDTO.setTagDetailDTOS(indicatorTagDetailDTOS);
            }
            resultList.add(reportIndicatorDTO);
        }
        return resultList;
    }


}
