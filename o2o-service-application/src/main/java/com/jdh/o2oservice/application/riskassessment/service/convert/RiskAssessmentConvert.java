package com.jdh.o2oservice.application.riskassessment.service.convert;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.common.enums.ReturnVisitStatusEnum;
import com.jdh.o2oservice.common.enums.RiskAssessmentStatusEnum;
import com.jdh.o2oservice.common.enums.RiskQuestionStatusEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessment;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessmentDetail;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentPageQuery;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.export.angelpromise.dto.AngelServiceRecordGroupQuestionDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelServiceRecordQuestionDto;
import com.jdh.o2oservice.export.product.dto.QuestionDTO;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.dto.PromiseStationDto;
import com.jdh.o2oservice.export.riskassessment.cmd.RiskAssDetailResultCmd;
import com.jdh.o2oservice.export.riskassessment.cmd.RiskAssReturnVisitCmd;
import com.jdh.o2oservice.export.riskassessment.cmd.RiskAssessmentCmd;
import com.jdh.o2oservice.export.riskassessment.dto.*;
import com.jdh.o2oservice.export.riskassessment.request.RiskAssessmentPageRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/26
 */
@Mapper
public interface RiskAssessmentConvert {

    /**
     * 获取 RiskAssessmentConvert 接口的单例实例。
     */
    RiskAssessmentConvert INSTANCE = Mappers.getMapper(RiskAssessmentConvert.class);
    
    
    /**
     * 将RiskAssDetailResultCmd列表转换为RiskAssessmentDetail列表。
     * @param riskAssDetailResultCmds 需要转换的RiskAssDetailResultCmd对象列表。
     * @return 转换后的RiskAssessmentDetail对象列表。
     */
    List<RiskAssessmentDetail> convert(List<RiskAssDetailResultCmd> riskAssDetailResultCmds);

    /**
     * 将 RiskAssessmentPage 转换为 PageDto<RiskAssessmentPageDTO>
     * @param riskAssessmentPage 需要转换的 RiskAssessmentPage 对象
     * @return 转换后的 PageDto<RiskAssessmentPageDTO> 对象
     */
    @Mapping(source = "records" ,target = "list")
    @Mapping(source = "total" ,target = "totalCount")
    @Mapping(source = "current" ,target = "pageNum")
    @Mapping(source = "size" ,target = "pageSize")
    PageDto<RiskAssessmentPageDTO> convert(Page<RiskAssessment> riskAssessmentPage);

    default RiskAssessmentPageDTO convertPageDTO(RiskAssessment riskAssessment){
        RiskAssessmentPageDTO pageDTO = new RiskAssessmentPageDTO();
        pageDTO.setId(riskAssessment.getId());
        pageDTO.setServiceType(riskAssessment.getServiceType());
        pageDTO.setOrderId(riskAssessment.getOrderId());
        pageDTO.setUserPin(riskAssessment.getUserPin());
        pageDTO.setRiskAssessmentId(riskAssessment.getRiskAssessmentId());
        pageDTO.setRiskAssessmentStatus(riskAssessment.getRiskAssessmentStatus());
        pageDTO.setServiceId(riskAssessment.getServiceId());
        pageDTO.setServiceItemName(riskAssessment.getServiceItemName());
        pageDTO.setOrderTime(riskAssessment.getOrderTime());
        pageDTO.setAppointmentTime(riskAssessment.getAppointmentTime());
        pageDTO.setUpdateTime(riskAssessment.getUpdateTime());
        pageDTO.setUpdateUser(riskAssessment.getUpdateUser());
        pageDTO.setServiceFinishTime(riskAssessment.getServiceFinishTime());
        pageDTO.setAssessmentUser(riskAssessment.getAssessmentUser());
        pageDTO.setAssessmentUserType(riskAssessment.getAssessmentUserType());
        pageDTO.setAssessmentTime(riskAssessment.getAssessmentTime());
        pageDTO.setReturnVisitUser(riskAssessment.getReturnVisitUser());
        pageDTO.setReturnVisitTime(riskAssessment.getReturnVisitTime());
        pageDTO.setReturnVisitStatus(riskAssessment.getReturnVisitStatus());
        pageDTO.setPartnerSource(riskAssessment.getPartnerSource());
        pageDTO.setSkuServiceType(riskAssessment.getSkuServiceType());
        //如果已超时，则状态就是超时
        if (Objects.equals(CommonConstant.ONE,riskAssessment.getAssessmentTimeoutStatus())){
            pageDTO.setAssessmentTimeOutStatus(riskAssessment.getAssessmentTimeoutStatus());
        }else {
            //如果未评估，判断当前时间与截止时间,如果当前时间大于截止时间，则超时
            if (DateUtil.compare(new Date(),riskAssessment.getAssessmentDeadlineTime()) > 0){
                pageDTO.setAssessmentTimeOutStatus(CommonConstant.ONE);
            }else {
                pageDTO.setAssessmentTimeOutStatus(CommonConstant.ZERO);
                pageDTO.setAssessmentBufferTime(DateUtil.between(riskAssessment.getAssessmentDeadlineTime(),new Date(), DateUnit.MS));
            }
        }

        return pageDTO;
    }
    /**
     * 将RiskAssessmentCmd命令转换为RiskAssessment实体。
     * @param riskAssessmentCmd 风险评估命令对象
     * @return 转换后的RiskAssessment实体
     */
    RiskAssessment cmdToEntity(RiskAssessmentCmd riskAssessmentCmd);



    /**
     * 将RiskAssessmentPageRequest对象转换为RiskAssessmentPageQuery对象。
     * @param riskAssessmentPageRequest 风险评估分页请求对象
     * @return 转换后的风险评估分页查询对象
     */
    RiskAssessmentPageQuery convert(RiskAssessmentPageRequest riskAssessmentPageRequest);

    /**
     * 将RiskAssReturnVisitCmd对象转换为RiskAssessment对象。
     * @param riskAssessmentCmd 需要转换的RiskAssReturnVisitCmd对象。
     * @return 转换后的RiskAssessment对象。
     */
    RiskAssessment convert(RiskAssReturnVisitCmd riskAssessmentCmd);


    /**
     * 根据提供的参数生成一个风险评估单对象。
     * @param generateIdFactory 用于生成唯一ID的工厂对象。
     * @param medicalPromises 医疗承诺列表，用于获取服务项目名称。
     * @param promiseDto 承诺DTO对象，包含与风险评估相关的信息。
     * @param questionCmd 问题DTO列表，用于生成风险评估单问题。
     * @return 生成的风险评估单对象。
     */
    default RiskAssessment getRiskAssessment(GenerateIdFactory generateIdFactory,List<MedicalPromise> medicalPromises, PromiseDto promiseDto, List<QuestionDTO> questionCmd){
        String serviceItemNames = medicalPromises.stream().map(MedicalPromise::getServiceItemName).distinct().collect(Collectors.joining("、"));


        //组装风险评估单
        RiskAssessment riskAssessment = new RiskAssessment();
        riskAssessment.setVerticalCode(promiseDto.getVerticalCode());
        riskAssessment.setServiceType(promiseDto.getServiceType());
        riskAssessment.setPromiseId(promiseDto.getPromiseId());
        riskAssessment.setUserPin(promiseDto.getUserPin());
        riskAssessment.setRiskAssessmentId(generateIdFactory.getId());
        riskAssessment.setRiskAssessmentStatus(RiskAssessmentStatusEnum.WAITING_ASS.getStatus());
        riskAssessment.setReturnVisitStatus(ReturnVisitStatusEnum.INIT.getStatus());
        riskAssessment.setServiceItemName(serviceItemNames);
        riskAssessment.setAppointmentTime(promiseDto.getAppointmentTime().getAppointmentStartTime());

        PromiseStationDto store = promiseDto.getStore();
        riskAssessment.setPatientProvince(store.getProvinceCode());
        riskAssessment.setPatientCity(store.getCityCode());
        riskAssessment.setPatientCounty(store.getDistrictCode());
//        riskAssessment.setPatientCounty(store.getc);
        riskAssessment.setPatientTown(store.getTownCode());
        riskAssessment.setServiceId(medicalPromises.get(0).getServiceId());
        riskAssessment.setAssessmentTimeoutStatus(CommonConstant.ZERO);
        riskAssessment.setAssessmentDeadlineTime(DateUtil.offsetHour(promiseDto.getAppointmentTime().getAppointmentStartTime(),-1));
        List<RiskAssessmentDetail> riskAssessmentDetailList = Lists.newArrayList();
        //组装风险评估单问题
        List<PromisePatientDto> patients = promiseDto.getPatients();

        for (PromisePatientDto promisePatientDto : patients) {
            for (QuestionDTO questionDTO : questionCmd) {
                RiskAssessmentDetail riskAssessmentDetail = new RiskAssessmentDetail();
                riskAssessmentDetail.setRiskAssessmentId(riskAssessment.getRiskAssessmentId());
                riskAssessmentDetail.setVerticalCode(promiseDto.getVerticalCode());
                riskAssessmentDetail.setUserPin(promiseDto.getUserPin());
                riskAssessmentDetail.setPromisePatientId(promisePatientDto.getPromisePatientId());
                riskAssessmentDetail.setRiskQuestionId(generateIdFactory.getId());
                riskAssessmentDetail.setRiskQuestionStatus(RiskQuestionStatusEnum.WAITING_ANSWER.getStatus());
                riskAssessmentDetail.setRiskAssessmentStatus(RiskAssessmentStatusEnum.WAITING_ASS.getStatus());
                riskAssessmentDetail.setServiceType(riskAssessment.getServiceType());
                riskAssessmentDetail.setQuestionId(questionDTO.getQuesId());
                riskAssessmentDetail.setPromiseId(promiseDto.getPromiseId());


                JdhQuestion jdhQuestion = new JdhQuestion();
                jdhQuestion.setId(questionDTO.getId());
                jdhQuestion.setName(questionDTO.getName());
                jdhQuestion.setQuesDesc(questionDTO.getQuesDesc());
                jdhQuestion.setType(questionDTO.getType());
                jdhQuestion.setRequired(questionDTO.getRequired());
                jdhQuestion.setQuesId(questionDTO.getQuesId());
                jdhQuestion.setHighRisk(questionDTO.getHighRisk());
                jdhQuestion.setUnit(questionDTO.getUnit());
                jdhQuestion.setExtJson(questionDTO.getExtJson());
                jdhQuestion.setValue(questionDTO.getValue());
                jdhQuestion.setSort(questionDTO.getSort());
                jdhQuestion.setTag(questionDTO.getTag());
                jdhQuestion.setQuesCode(questionDTO.getQuesCode());
                riskAssessmentDetail.setQuestionExt(JsonUtil.toJSONString(jdhQuestion));

                riskAssessmentDetailList.add(riskAssessmentDetail);

            }
        }
        riskAssessment.setRiskAssessmentDetailList(riskAssessmentDetailList);
        return riskAssessment;
    }



    RiskAssessmentDetailManDTO convert(RiskAssessment riskAssessment);


    List<RiskAssDetailBaseDTO> convertList(List<RiskAssessmentDetail> list);



    default   List<RiskAssessmentDetail> getRiskAssessmentDetails(AngelServiceRecordGroupQuestionDto queryRecordGroupQuestion, RiskAssessment riskAssessmentOld, PromiseDto promiseDto,GenerateIdFactory generateIdFactory) {
        List<RiskAssessmentDetail> riskAssessmentDetailList = Lists.newArrayList();

        for (AngelServiceRecordQuestionDto questionDto : queryRecordGroupQuestion.getAngelServiceRecordQuestionDtoList()) {
            RiskAssessmentDetail riskAssessmentDetail = new RiskAssessmentDetail();
            riskAssessmentDetail.setRiskAssessmentId(riskAssessmentOld.getRiskAssessmentId());
            riskAssessmentDetail.setVerticalCode(promiseDto.getVerticalCode());
            riskAssessmentDetail.setUserPin(promiseDto.getUserPin());
            riskAssessmentDetail.setPromisePatientId(Long.valueOf(queryRecordGroupQuestion.getPromisePatientId()));
            riskAssessmentDetail.setRiskQuestionId(generateIdFactory.getId());
            if (StringUtil.isNotBlank(questionDto.getAnswerValue())){
                riskAssessmentDetail.setRiskQuestionStatus(RiskQuestionStatusEnum.ANSWER.getStatus());
            }else {
                riskAssessmentDetail.setRiskQuestionStatus(RiskQuestionStatusEnum.WAITING_ANSWER.getStatus());
            }
            if (Objects.equals(-1,queryRecordGroupQuestion.getGroupStatus())){
                riskAssessmentDetail.setRiskAssessmentStatus(RiskAssessmentStatusEnum.REFUSE.getStatus());
            }else {
                riskAssessmentDetail.setRiskAssessmentStatus(RiskAssessmentStatusEnum.PASS.getStatus());
            }
            riskAssessmentDetail.setServiceType(riskAssessmentOld.getServiceType());
            riskAssessmentDetail.setQuestionId(questionDto.getQuesId());
            riskAssessmentDetail.setRiskQuestionRes(questionDto.getAnswerValue());
            riskAssessmentDetail.setPromiseId(promiseDto.getPromiseId());

            JdhQuestion jdhQuestion = new JdhQuestion();
            jdhQuestion.setId(questionDto.getId());
            jdhQuestion.setName(questionDto.getName());
            jdhQuestion.setQuesDesc(questionDto.getQuesDesc());
            jdhQuestion.setType(questionDto.getType());
            jdhQuestion.setRequired(questionDto.getRequired());
            jdhQuestion.setQuesId(questionDto.getQuesId());
            jdhQuestion.setHighRisk(questionDto.getHighRisk());
            jdhQuestion.setUnit(questionDto.getUnit());
            jdhQuestion.setExtJson(questionDto.getExtJson());
            jdhQuestion.setValue(questionDto.getValue());
            jdhQuestion.setSort(questionDto.getSort());
            jdhQuestion.setTag(questionDto.getTag());
            riskAssessmentDetail.setQuestionExt(JsonUtil.toJSONString(jdhQuestion));

            riskAssessmentDetailList.add(riskAssessmentDetail);
        }
        return riskAssessmentDetailList;
    }

    default   RiskAssessment getRiskAssessment(PromiseDto promiseDto, JdhVerticalBusiness jdhVerticalBusiness, List<MedicalPromise> medicalPromises, String serviceItemNames, JdOrder jdOrder, AngelServiceRecordGroupQuestionDto queryRecordGroupQuestion,GenerateIdFactory generateIdFactory) {
        RiskAssessment riskAssessment = new RiskAssessment();
        riskAssessment.setRiskAssessmentId(generateIdFactory.getId());
        riskAssessment.setVerticalCode(promiseDto.getVerticalCode());
        riskAssessment.setServiceType(promiseDto.getServiceType());
        riskAssessment.setBusinessMode(jdhVerticalBusiness.getBusinessModeCode());
        riskAssessment.setOrderId(Long.valueOf(promiseDto.getSourceVoucherId()));
        riskAssessment.setPromiseId(promiseDto.getPromiseId());
        riskAssessment.setUserPin(promiseDto.getUserPin());
        //TODO
        if (Objects.equals(-1,queryRecordGroupQuestion.getGroupStatus())){
            riskAssessment.setRiskAssessmentStatus(RiskAssessmentStatusEnum.REFUSE.getStatus());
        }else {
            riskAssessment.setRiskAssessmentStatus(RiskAssessmentStatusEnum.PASS.getStatus());
        }
        riskAssessment.setServiceId(medicalPromises.get(0).getServiceId());
        riskAssessment.setServiceItemName(serviceItemNames);
        //TODO
//        riskAssessment.setAssessmentUser();
        riskAssessment.setAssessmentTime(new Date());
        riskAssessment.setReturnVisitStatus(ReturnVisitStatusEnum.INIT.getStatus());
        riskAssessment.setAppointmentTime(promiseDto.getAppointmentTime().getAppointmentStartTime());
        PromiseStationDto store = promiseDto.getStore();
        riskAssessment.setPatientProvince(store.getProvinceCode());
        riskAssessment.setPatientCity(store.getCityCode());
        riskAssessment.setPatientCounty(store.getDistrictCode());
        riskAssessment.setPatientTown(store.getTownCode());
        riskAssessment.setAssessmentDeadlineTime(CommonConstant.EXPIRE_DATE);
        riskAssessment.setAssessmentTimeoutStatus(CommonConstant.ZERO);
        riskAssessment.setRiskLevel(CommonConstant.TWO);
        if (Objects.nonNull(jdOrder)){
            riskAssessment.setOrderTime(jdOrder.getCreateTime());
            riskAssessment.setPartnerSource(jdOrder.getPartnerSource());
        }

        List<AngelServiceRecordQuestionDto> angelServiceRecordQuestionDtoList = queryRecordGroupQuestion.getAngelServiceRecordQuestionDtoList();

        List<RiskAssessmentDetail> riskAssessmentDetailList = Lists.newArrayList();
        riskAssessment.setRiskAssessmentDetailList(riskAssessmentDetailList);

        for (AngelServiceRecordQuestionDto questionDto : angelServiceRecordQuestionDtoList) {
            RiskAssessmentDetail riskAssessmentDetail = new RiskAssessmentDetail();
            riskAssessmentDetail.setRiskAssessmentId(riskAssessment.getRiskAssessmentId());
            riskAssessmentDetail.setVerticalCode(promiseDto.getVerticalCode());
            riskAssessmentDetail.setUserPin(promiseDto.getUserPin());
            riskAssessmentDetail.setPromisePatientId(Long.valueOf(queryRecordGroupQuestion.getPromisePatientId()));
            riskAssessmentDetail.setRiskQuestionId(generateIdFactory.getId());
            if (StringUtil.isNotBlank(questionDto.getAnswerValue())){
                riskAssessmentDetail.setRiskQuestionStatus(RiskQuestionStatusEnum.ANSWER.getStatus());
            }else {
                riskAssessmentDetail.setRiskQuestionStatus(RiskQuestionStatusEnum.WAITING_ANSWER.getStatus());

            }
            riskAssessmentDetail.setRiskAssessmentStatus(riskAssessment.getRiskAssessmentStatus());
            riskAssessmentDetail.setServiceType(riskAssessment.getServiceType());
            riskAssessmentDetail.setQuestionId(questionDto.getQuesId());
            riskAssessmentDetail.setPromiseId(promiseDto.getPromiseId());
            riskAssessmentDetail.setRiskQuestionRes(questionDto.getAnswerValue());

            JdhQuestion jdhQuestion = new JdhQuestion();
            jdhQuestion.setId(questionDto.getId());
            jdhQuestion.setName(questionDto.getName());
            jdhQuestion.setQuesDesc(questionDto.getQuesDesc());
            jdhQuestion.setType(questionDto.getType());
            jdhQuestion.setRequired(questionDto.getRequired());
            jdhQuestion.setQuesId(questionDto.getQuesId());
            jdhQuestion.setHighRisk(questionDto.getHighRisk());
            jdhQuestion.setUnit(questionDto.getUnit());
            jdhQuestion.setExtJson(questionDto.getExtJson());
            jdhQuestion.setValue(questionDto.getValue());
            jdhQuestion.setSort(questionDto.getSort());
            jdhQuestion.setTag(questionDto.getTag());
            riskAssessmentDetail.setQuestionExt(JsonUtil.toJSONString(jdhQuestion));

            riskAssessmentDetailList.add(riskAssessmentDetail);
        }
        return riskAssessment;
    }

    default RiskAssOrderDetailDTO getRiskAssOrderDetailDTO(RiskAssessment riskAssessment, PromiseDto promiseDto) {
        RiskAssOrderDetailDTO riskAssOrderDetailDTO = new RiskAssOrderDetailDTO();

        riskAssOrderDetailDTO.setServiceType(riskAssessment.getServiceType());
        riskAssOrderDetailDTO.setOrderId(riskAssessment.getOrderId());
        riskAssOrderDetailDTO.setUserPin(riskAssessment.getUserPin());
        riskAssOrderDetailDTO.setServiceId(riskAssessment.getServiceId());
        riskAssOrderDetailDTO.setServiceItemName(riskAssessment.getServiceItemName());
        riskAssOrderDetailDTO.setAssessmentUser(riskAssessment.getAssessmentUser());
        riskAssOrderDetailDTO.setAssessmentUserType(riskAssessment.getAssessmentUserType());
        riskAssOrderDetailDTO.setAssessmentTime(riskAssessment.getAssessmentTime());
        riskAssOrderDetailDTO.setReturnVisitUser(riskAssessment.getReturnVisitUser());
        riskAssOrderDetailDTO.setReturnVisitTime(riskAssessment.getReturnVisitTime());
        riskAssOrderDetailDTO.setReturnVisitRecord(riskAssessment.getReturnVisitRecord());
        riskAssOrderDetailDTO.setAppointmentTime(riskAssessment.getAppointmentTime());
        riskAssOrderDetailDTO.setOrderTime(riskAssessment.getOrderTime());
        riskAssOrderDetailDTO.setOrderPhone(promiseDto.getAppointmentPhone());
        riskAssOrderDetailDTO.setAppointmentAddr(promiseDto.getStore().getStoreAddr());
        riskAssOrderDetailDTO.setServiceType(promiseDto.getServiceType());
        riskAssOrderDetailDTO.setPartnerSource(riskAssessment.getPartnerSource());
        riskAssOrderDetailDTO.setReturnVisitStatus(riskAssessment.getReturnVisitStatus());
        riskAssOrderDetailDTO.setSkuServiceType(riskAssessment.getSkuServiceType());

        //如果已超时，则状态就是超时
        if (Objects.equals(CommonConstant.ONE, riskAssessment.getAssessmentTimeoutStatus())){
            riskAssOrderDetailDTO.setAssessmentTimeOutStatus(riskAssessment.getAssessmentTimeoutStatus());
        }else {
            //如果未评估，判断当前时间与截止时间,如果当前时间大于截止时间，则超时
            if (DateUtil.compare(new Date(), riskAssessment.getAssessmentDeadlineTime()) > 0){
                riskAssOrderDetailDTO.setAssessmentTimeOutStatus(CommonConstant.ONE);
            }else {
                riskAssOrderDetailDTO.setAssessmentTimeOutStatus(CommonConstant.ZERO);
                riskAssOrderDetailDTO.setAssessmentBufferTime(DateUtil.between(riskAssessment.getAssessmentDeadlineTime(),new Date(), DateUnit.MS));
            }
        }
        return riskAssOrderDetailDTO;
    }

    default RiskAssUserDetailDTO getRiskAssUserDetailDTO(Long ppid, List<RiskAssessmentDetail> details, PromisePatientDto promisePatientDto, PromiseDto promiseDto, Map<Long, List<MedicalPromise>> finalMedicalPromiseMap, List<MedicalPromise> medicalPromiseList) {
        RiskAssUserDetailDTO riskAssUserDetailDTO = new RiskAssUserDetailDTO();


        riskAssUserDetailDTO.setUserPhone(promisePatientDto.getPhoneNumber().getPhone());
        riskAssUserDetailDTO.setPromisePatientId(ppid);
        riskAssUserDetailDTO.setUserAge(promisePatientDto.getBirthday().getAge());
        riskAssUserDetailDTO.setUserGender(promisePatientDto.getGender());
        riskAssUserDetailDTO.setUserGenderDesc(GenderEnum.getReportTextOfType(promisePatientDto.getGender()));
        RiskAssessmentDetail finish = details.stream().filter(p -> RiskAssessmentStatusEnum.getFinishStatus().contains(p.getRiskAssessmentStatus())).findFirst().orElse(null);
        Integer status = Objects.nonNull(finish) ? finish.getRiskAssessmentStatus() : details.get(0).getRiskAssessmentStatus();
        riskAssUserDetailDTO.setRiskAssessmentStatus(status);
        riskAssUserDetailDTO.setUserName(new UserName(promisePatientDto.getUserName().getName()).mask());
        riskAssUserDetailDTO.setAppointmentStartTime(promiseDto.getAppointmentTime().getAppointmentStartTime());
        riskAssUserDetailDTO.setAppointmentEndTime(promiseDto.getAppointmentTime().getAppointmentEndTime());
        riskAssUserDetailDTO.setIsImmediately(promiseDto.getAppointmentTime().getIsImmediately());
        riskAssUserDetailDTO.setAppointmentAddr(promiseDto.getStore().getStoreAddr());
        List<MedicalPromise> medicalPromises = finalMedicalPromiseMap.get(ppid);
        if (CollectionUtils.isNotEmpty(medicalPromises)){
            riskAssUserDetailDTO.setServiceItemNameList(medicalPromiseList.stream().map(MedicalPromise::getServiceItemName).distinct().collect(Collectors.toList()));
        }
        return riskAssUserDetailDTO;
    }

    /**
     * convertToRiskAssDto
     *
     * @param riskAssessment
     * @return
     */
    RiskAssDto convertToRiskAssDto(RiskAssessment riskAssessment);

    /**
     * convertToRiskAssessmentDetailDto
     * @param riskAssessmentDetail
     * @return
     */
    RiskAssessmentDetailDto convertToRiskAssessmentDetailDto(RiskAssessmentDetail riskAssessmentDetail);

    /**
     * convertToRiskAssessmentDetailDtoList
     * @param riskAssessmentDetailList
     * @return
     */
    List<RiskAssessmentDetailDto> convertToRiskAssessmentDetailDtoList(List<RiskAssessmentDetail> riskAssessmentDetailList);
}
