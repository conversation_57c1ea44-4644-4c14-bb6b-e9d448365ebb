package com.jdh.o2oservice.application.dispatch.service;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.export.dispatch.cmd.*;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchTeamDTO;
import com.jdh.o2oservice.export.dispatch.dto.SaveDispatchTeamDTO;
import com.jdh.o2oservice.export.dispatch.query.DispatchTeamListRequest;
import com.jdh.o2oservice.export.dispatch.query.DispatchTeamRequest;

import java.util.List;

/**
 * @ClassName DispatchTeamApplication
 * @Description
 * <AUTHOR>
 * @Date 2025/7/17 13:05
 **/
public interface DispatchTeamApplication {

    /**
     * 查询派单小队列表
     *
     * @param request 查询
     * @return {@link JdhDispatchTeamDTO}
     */
    PageDto<JdhDispatchTeamDTO> queryDispatchTeamPage(DispatchTeamListRequest request);

    /**
     * 查询派单小队明细
     * @param request
     * @return
     */
    JdhDispatchTeamDTO queryDispatchTeamDetail(DispatchTeamRequest request);

    /**
     * 保存派单小队
     *
     * @param saveDispatchTeamCmd
     * @return
     */
    Boolean saveDispatchTeam(SaveDispatchTeamCmd saveDispatchTeamCmd);

    /**
     * 保存派单小队技能
     *
     * @param saveDispatchTeamSkillCmd
     * @return
     */
    SaveDispatchTeamDTO saveDispatchTeamSkill(SaveDispatchTeamSkillCmd saveDispatchTeamSkillCmd);

    /**
     * 保存派单小队服务者
     *
     * @param saveDispatchTeamAngelCmd
     * @return
     */
    SaveDispatchTeamDTO saveDispatchTeamAngel(SaveDispatchTeamAngelCmd saveDispatchTeamAngelCmd);

    /**
     * 删除派单小队技能
     *
     * @param deleteDispatchTeamSkillCmd
     * @return
     */
    Boolean deleteDispatchTeamSkill(DeleteDispatchTeamSkillCmd deleteDispatchTeamSkillCmd);

    /**
     * 删除派单小队服务者
     *
     * @param deleteDispatchTeamAngelCmd
     * @return
     */
    Boolean deleteDispatchTeamAngel(DeleteDispatchTeamAngelCmd deleteDispatchTeamAngelCmd);
}