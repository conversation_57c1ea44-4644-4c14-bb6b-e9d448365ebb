package com.jdh.o2oservice.application.dispatch.convert;

import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchTeam;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchTeamSkillRel;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchTeamRepQuery;
import com.jdh.o2oservice.export.dispatch.cmd.SaveDispatchTeamCmd;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchTeamDTO;
import com.jdh.o2oservice.export.dispatch.query.DispatchTeamListRequest;
import com.jdh.o2oservice.export.dispatch.query.DispatchTeamRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName DispatchTeamApplicationConverter
 * @Description
 * <AUTHOR>
 * @Date 2025/7/17 13:24
 **/
@Mapper
public interface DispatchTeamApplicationConverter {

    /**
     * log
     */
    Logger log = LoggerFactory.getLogger(DispatchTeamApplicationConverter.class);

    /**
     *
     */
    DispatchTeamApplicationConverter  INSTANCE = Mappers.getMapper(DispatchTeamApplicationConverter.class);

    /**
     *
     * @param request
     * @return
     */
    DispatchTeamRepQuery request2DispatchTeamRepQuery(DispatchTeamListRequest request);

    /**
     *
     * @param request
     * @return
     */
    DispatchTeamRepQuery request2DispatchTeamRepQuery(DispatchTeamRequest request);

    /**
     *
     * @param dispatchTeam
     * @return
     */
    @Mapping(target = "teamSkillDesc", source = "teamSkillRelList", qualifiedByName = "convertTeamSkillDesc")
    @Mapping(target = "dispatchTeamStatusDesc", expression = "java(com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchTeamStatusEnum.getDescByType(dispatchTeam.getDispatchTeamStatus()))")
    JdhDispatchTeamDTO dispatchTeam2Dto(JdhDispatchTeam dispatchTeam);

    /**
     *
     * @param list
     * @return
     */
    List<JdhDispatchTeamDTO> dispatchTeam2Dto(List<JdhDispatchTeam> list);

    /**
     *
     * @param cmd
     * @return
     */
    JdhDispatchTeam saveCmd2DispatchTeam(SaveDispatchTeamCmd cmd);

    /**
     * 转换小队技能描述
     * @param teamSkillRelList
     * @return
     */
    @Named("convertTeamSkillDesc")
    default String convertTeamSkillDesc(List<JdhDispatchTeamSkillRel> teamSkillRelList){
        if (CollectionUtils.isEmpty(teamSkillRelList)) {
            return "";
        }
        return teamSkillRelList.stream().map(JdhDispatchTeamSkillRel::getAngelSkillName).reduce((a, b) -> a + "," + b).orElse("");
    }
}