package com.jdh.o2oservice.application.via.handler;

import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.enums.ViaPageEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 上门护理 楼层数据填充
 *
 * <AUTHOR>
 * @date 2024/01/02
 */
@Slf4j
@Service
public class XfylHomeCareOrderDetailHandler extends AbstractViaDataFillHandler implements MapAutowiredKey{

    @Resource
    private XfylHomeTestOrderDetailHandler xfylHomeTestOrderDetailHandler;

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return ViaPageEnum.HOME_ORDER_DETAIL.getScene() + "_" + BusinessModeEnum.ANGEL_CARE.getCode() + "_" + ServiceTypeEnum.CARE.getServiceType();
    }


    /**
     * 填充数据
     *
     * @param ctx ctx
     */
    @Override
    public void handle(FillViaConfigDataContext ctx) {
        xfylHomeTestOrderDetailHandler.handle(ctx);
    }
}
