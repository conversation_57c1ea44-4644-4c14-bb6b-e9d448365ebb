package com.jdh.o2oservice.application.via.handler;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.promise.model.PromiseAppointmentTime;
import com.jdh.o2oservice.core.domain.promise.model.PromiseStation;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.PromiseGoRpcService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.PromisegoRequestAddress;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.PromisegoRequestAppointmentTime;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.UserPromisegoBo;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.UserPromisegoRequestBo;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.enums.PromiseAggregateStatusEnum;
import com.jdh.o2oservice.export.report.dto.MedicalReportOrderDTO;
import com.jdh.o2oservice.export.report.query.MedicalReportRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AbstractViaDataFillHandler
 *
 * <AUTHOR>
 * @date 2024/01/02
 */
@Slf4j
public abstract class AbstractViaDataFillHandler {


    @Resource
    private MedicalReportApplication medicalReportApplication;

    @Resource
    private PromiseGoRpcService promiseGoRpcService;

    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * 需要预测的阶段
     */
    public static final Set<String> AGGREGATE_STATUS_ETA = Sets.newHashSet(PromiseAggregateStatusEnum.DISPATCH.getCode(),
            PromiseAggregateStatusEnum.TO_HOME.getCode(), PromiseAggregateStatusEnum.SERVICING.getCode(),
            PromiseAggregateStatusEnum.TO_LAB.getCode(), PromiseAggregateStatusEnum.TESTING.getCode(),
            PromiseAggregateStatusEnum.RISK_ASSESSMENT.getCode()
    );

    /**
     * 填充数据
     *
     * @param ctx ctx
     */
//    public abstract void preHandle(FillViaConfigDataContext ctx);
    /**
     * 填充数据
     *
     * @param ctx ctx
     */
    public abstract void handle(FillViaConfigDataContext ctx);



    /**
     * 获取待接单时间
     *
     * @return
     */
    public String getWaitPickUpTime(List<JdhPromiseHistory> promiseHistories, JdhPromise jdhPromise, Integer dynamicCursorMinutes){
        DuccConfig duccConfig = SpringUtil.getBean(DuccConfig.class);
        Map<String, Map<String, JSONObject>> dispatchDetailDurationMap = duccConfig.getDispatchDetailDurationMap();
        BusinessContext businessContext = new BusinessContext();
        businessContext.setServiceType(jdhPromise.getServiceType());
        businessContext.setVerticalCode(jdhPromise.getVerticalCode());
        businessContext.initVertical();

        List<JdhPromiseHistory> promiseHistoryList = promiseHistories.stream().filter(ele -> JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus()
                .equals(ele.getAfterStatus())).sorted(Comparator.comparing(JdhPromiseHistory::getCreateTime).reversed()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(promiseHistoryList)) {
            log.info("[AbstractCustomOrderHandler -> getAngelWorkReceiveTime],没有预约中的履约单记录。");
            return null;
        }

        JdhPromiseHistory jdhPromiseHistory = promiseHistoryList.get(0);
        if(businessContext.getVerticalBusiness().getBusinessModeCode().equals(BusinessModeEnum.SELF_TEST.getCode())) {
            Date now = new Date();
            Date tomorrow = DateUtil.tomorrow().toJdkDate();
            Date submitDispatchTime = jdhPromise.getUpdateTime();
            if (Objects.nonNull(jdhPromiseHistory)) {
                submitDispatchTime = jdhPromiseHistory.getCreateTime();
            }

            DateTime receiveTime = DateUtil.offset(submitDispatchTime, DateField.MINUTE, dynamicCursorMinutes);
            if (DateUtil.isSameDay(now, receiveTime.toJdkDate())) {
                return "[今天]" + TimeUtils.dateTimeToStr(receiveTime, TimeFormat.DATE_PATTERN_HM_SIMPLE);
            } else if (DateUtil.isSameDay(tomorrow, receiveTime.toJdkDate())) {
                return "[明天]" + TimeUtils.dateTimeToStr(receiveTime, TimeFormat.DATE_PATTERN_HM_SIMPLE);
            } else {
                return TimeUtils.dateTimeToStr(receiveTime, TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE_NO_SSS);
            }
        }

        Map<String, JSONObject> durationMap = dispatchDetailDurationMap.get(businessContext.getVerticalBusiness().getBusinessModeCode());
        if(MapUtils.isEmpty(durationMap)) {
            durationMap = dispatchDetailDurationMap.get("angelCare");
        }
        JSONObject jsonObject = BooleanUtils.isTrue(jdhPromise.getAppointmentTime().getIsImmediately()) ? durationMap.get("1") : durationMap.get("2");

        LocalDateTime promiseDateTime = TimeUtils.dateToLocalDateTime(jdhPromiseHistory.getCreateTime());
        LocalTime promiseTime = LocalTime.of(promiseDateTime.getHour(), promiseDateTime.getMinute());

        LocalTime startTime = LocalTime.parse(jsonObject.getString("startTime"), TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter);
        LocalTime endTime = LocalTime.parse(jsonObject.getString("endTime"), TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter);

        log.info("[AbstractCustomOrderHandler -> getAngelWorkReceiveTime],promiseTime={}, startTime={}, endTime={}",
                JSON.toJSONString(promiseTime), JSON.toJSONString(startTime), JSON.toJSONString(endTime));

        if (Objects.nonNull(jsonObject.getInteger("durationMinute"))){
            dynamicCursorMinutes = jsonObject.getInteger("durationMinute");
        }

        String timeStr;
        String msgTemp;
        if(promiseTime.isAfter(startTime.minusMinutes(dynamicCursorMinutes)) && promiseTime.isBefore(endTime)){
            promiseDateTime = promiseDateTime.plusMinutes(dynamicCursorMinutes);
            timeStr =  promiseDateTime.format(TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter);
            msgTemp = "预计 <span id='appointTitleId'>%s</span> 前接单";
        }else if(promiseTime.isAfter(endTime)) {
            promiseDateTime = promiseDateTime.plusDays(CommonConstant.ONE);
            timeStr = startTime.format(TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter);
            msgTemp = "夜间护士接单可能延迟，最晚 <span id='appointTitleId'>%s</span> 完成派单";
        }else {
            timeStr = startTime.format(TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter);
            msgTemp = "夜间护士接单可能延迟，最晚 <span id='appointTitleId'>%s</span> 完成派单";
        }

        LocalDate today = LocalDate.now();
        if(promiseDateTime.isAfter(LocalDateTime.of(today, LocalTime.MIN)) && promiseDateTime.isBefore(LocalDateTime.of(today, LocalTime.MAX))){
            return String.format(msgTemp, "[今天]" + timeStr);
        }else if(promiseDateTime.isAfter(LocalDateTime.of(today, LocalTime.MIN).plusDays(CommonConstant.ONE)) && promiseDateTime.isBefore(LocalDateTime.of(today, LocalTime.MAX).plusDays(CommonConstant.ONE))){
            return String.format(msgTemp, "[明天]" + timeStr);
        }else{
            return String.format(msgTemp, TimeUtils.localDateTimeToStr(promiseDateTime,TimeFormat.SHORT_PATTERN_LINE) + " " +timeStr);
        }
    }

    /**
     * promise eta
     * @param jdhPromise
     * @param aggregateStatus
     * @return
     */
    public UserPromisegoBo queryUserPromiseGoInfo(JdhPromise jdhPromise, String aggregateStatus){
        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(jdhPromise.getVerticalCode());
        PromiseStation store = jdhPromise.getStore();
        UserPromisegoBo userPromisegoBo = promiseGoRpcService.queryUserPromisego(
                UserPromisegoRequestBo.builder()
                        .aggregateStatus(aggregateStatus)
                        .promiseId(jdhPromise.getPromiseId())
                        .businessMode(jdhVerticalBusiness.getBusinessModeCode())
                        .appointmentTime(PromisegoRequestAppointmentTime.builder()
                                .immediately(EntityUtil.getFiledDefaultNull(jdhPromise.getAppointmentTime(), PromiseAppointmentTime::getIsImmediately))
                                .appointmentStartTime(EntityUtil.getFiledDefaultNull(jdhPromise.getAppointmentTime(), PromiseAppointmentTime::getAppointmentStartTime))
                                .appointmentEndTime(EntityUtil.getFiledDefaultNull(jdhPromise.getAppointmentTime(), PromiseAppointmentTime::getAppointmentEndTime))
                                .build())
                        .appointmentAddress(PromisegoRequestAddress.builder()
                                .provinceId(store.getProvinceCode())
                                .cityId(store.getCityCode())
                                .countyId(store.getDistrictCode())
                                .townId(store.getTownCode())
                                .provinceName(store.getProvinceName())
                                .cityName(store.getCityName())
                                .countyName(store.getDistrictName())
                                .townName(store.getTownName())
                                .fullAddress(store.getStoreAddr())
                                .build())
                        .queryTermScript(Boolean.TRUE)
                        .build());
        return userPromisegoBo;
    }


}
