package com.jdh.o2oservice.application.angel.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.angel.AngelExtApplication;
import com.jdh.o2oservice.application.angel.convert.AngelApplicationConverter;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelLocationApplication;
import com.jdh.o2oservice.application.angel.service.AngelScheduleApplication;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.content.service.ContentApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.CpsSkuConfig;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.RandomNumberGenerator;
import com.jdh.o2oservice.base.util.TrimUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.bo.JdUnionGoodsListBo;
import com.jdh.o2oservice.core.domain.angel.context.*;
import com.jdh.o2oservice.core.domain.angel.enums.*;
import com.jdh.o2oservice.core.domain.angel.event.AngelAuditEventBody;
import com.jdh.o2oservice.core.domain.angel.model.*;
import com.jdh.o2oservice.core.domain.angel.repository.db.*;
import com.jdh.o2oservice.core.domain.angel.repository.query.*;
import com.jdh.o2oservice.core.domain.angel.repository.rpc.JdUnionRpc;
import com.jdh.o2oservice.core.domain.angel.rpc.NethpAngelRpc;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NethpBaseDoctorInfoClientBo;
import com.jdh.o2oservice.core.domain.angel.rpc.param.NethpBaseDoctorInfoClientParam;
import com.jdh.o2oservice.core.domain.angel.service.AngelDomainService;
import com.jdh.o2oservice.core.domain.content.enums.ContentPositionEnum;
import com.jdh.o2oservice.core.domain.content.enums.ContentsStatusEnum;
import com.jdh.o2oservice.core.domain.dispatch.rpc.DispatchFlowDependRpc;
import com.jdh.o2oservice.export.angel.cmd.*;
import com.jdh.o2oservice.export.angel.dto.*;
import com.jdh.o2oservice.export.angel.query.*;
import com.jdh.o2oservice.export.content.query.ContentQuery;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 服务者 应用服务
 * <AUTHOR>
 * @Date 2024/4/18
 * @Version V1.0
 **/
@Service
@Slf4j
public class AngelApplicationImpl implements AngelApplication, AngelExtApplication {

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private AngelDomainService angelDomainService;

    @Resource
    private AngelRepository angelRepository;

    @Resource
    private StationApplication stationApplication;

    @Resource
    private AngelSkillRelRepository angelSkillRelRepository;

    @Resource
    private AngelSkillDictRepository angelSkillDictRepository;

    @Resource
    private ScheduleAngelRepository scheduleAngelRepository;

    @Resource
    private ProductApplication productApplication;

    @Resource
    private NethpAngelRpc nethpAngelRpc;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * angelLocationApplication
     */
    @Resource
    private AngelLocationApplication angelLocationApplication;

    @Autowired
    private JdhDepartmentRepository jdhDepartmentRepository;

    @Resource
    private JdUnionRpc jdUnionRpc;

    @Resource
    private ContentApplication contentApplication;

    /**
     * angelScheduleApplication
     */
    @Resource
    private AngelScheduleApplication angelScheduleApplication;

    /**
     * dispatchFlowDependRpc
     */
    @Resource
    private DispatchFlowDependRpc dispatchFlowDependRpc;



    @Override
    public JdhAngelDto queryByAngelInfo(AngelRequest angelRequest) {
        log.info("AngelApplicationImpl -> queryByAngelId angelRequest:{}", JSON.toJSONString(angelRequest));
        if (Objects.isNull(angelRequest)) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        JdhAngelRepQuery jdhAngelRepQuery = new JdhAngelRepQuery();
        jdhAngelRepQuery.setAngelId(angelRequest.getAngelId());
        jdhAngelRepQuery.setNethpDocId(angelRequest.getNethpDocId());
        jdhAngelRepQuery.setAngelPin(angelRequest.getAngelPin());
        JdhAngel jdhAngel = angelRepository.queryByUniqueId(jdhAngelRepQuery);
        log.info("AngelApplicationImpl -> queryByAngelId jdhAngel:{}", JSON.toJSONString(jdhAngel));
        return AngelApplicationConverter.INS.convert2JdhAngelDto(jdhAngel);
    }

    @Override
    public List<JdhAngelDto> queryByStationId(AngelStationRequest angelStationRequest) {
        if (Objects.isNull(angelStationRequest) ||
                (Objects.isNull(angelStationRequest.getStationId()) && CollectionUtils.isEmpty(angelStationRequest.getStationIdList()))) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        JdhAngelRepQuery jdhAngelRepQuery = new JdhAngelRepQuery();
        jdhAngelRepQuery.setStationId(angelStationRequest.getStationId());
        jdhAngelRepQuery.setStationIdList(angelStationRequest.getStationIdList());
        jdhAngelRepQuery.setAuditProcessStatus(angelStationRequest.getAuditProcessStatus());
        jdhAngelRepQuery.setJobNature(angelStationRequest.getJobNature());
        jdhAngelRepQuery.setIntendedAngelIds(angelStationRequest.getIntendedAngelIds());

        List<JdhAngel> jdhAngels = angelRepository.queryByStationId(jdhAngelRepQuery);
        log.info("AngelApplicationImpl -> queryByStationId jdhAngel:{}", JSON.toJSONString(jdhAngels));
        if (CollectionUtils.isEmpty(jdhAngels)) {
            return new ArrayList<>();
        }

        List<JdhAngelDto> list = Lists.newArrayListWithExpectedSize(jdhAngels.size());
        for (JdhAngel jdhAngel : jdhAngels) {
            JdhAngelDto jdhAngelDto = AngelApplicationConverter.INS.convert2JdhAngelDto(jdhAngel);
            list.add(jdhAngelDto);
        }
        return list;
    }

    @Override
    public Boolean batchBindStationMaster(AngelBindStationMasterCmd angelBindStationMasterCmd) {
        if (Objects.isNull(angelBindStationMasterCmd) || StringUtils.isEmpty(angelBindStationMasterCmd.getStationMaster()) || CollectionUtils.isEmpty(angelBindStationMasterCmd.getAngelIdList())) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        log.info("AngelApplicationImpl -> queryStationInfoByLoginInfo angelBindCmd:{}", JSON.toJSONString(angelBindStationMasterCmd));
        AngelBindStationMasterContext angelBindStationMasterContext = AngelApplicationConverter.INS.cmd2AngelBindStationMasterContext(angelBindStationMasterCmd);
        int bindSum = angelRepository.batchBindStationMaster(angelBindStationMasterContext.getJdhAngelBindStationMasterEntity());
        log.info("AngelApplicationImpl -> queryStationInfoByLoginInfo bindSum:{}", JSON.toJSONString(bindSum));
        return Boolean.TRUE;
    }

    @Override
    public Boolean batchUnBindStationMaster(AngelBindStationMasterCmd angelBindStationMasterCmd) {
        if (Objects.isNull(angelBindStationMasterCmd) || CollectionUtils.isEmpty(angelBindStationMasterCmd.getAngelIdList())) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        log.info("AngelApplicationImpl -> batchUnBindStationMaster angelBindCmd:{}", JSON.toJSONString(angelBindStationMasterCmd));
        AngelBindStationMasterContext angelBindStationMasterContext = AngelApplicationConverter.INS.cmd2AngelBindStationMasterContext(angelBindStationMasterCmd);
        int unbindSum = angelRepository.batchUnbindStationMaster(angelBindStationMasterContext.getJdhAngelBindStationMasterEntity());
        log.info("AngelApplicationImpl -> batchUnBindStationMaster unbindSum:{}", JSON.toJSONString(unbindSum));
        return Boolean.TRUE;
    }

    @Override
    public Boolean batchBindStation(AngelBindStationCmd angelBindStationCmd) {
        if (Objects.isNull(angelBindStationCmd) || CollectionUtils.isEmpty(angelBindStationCmd.getAngelIdList()) || Objects.isNull(angelBindStationCmd.getStationId())) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelBindStationContext angelBindStationContext = AngelApplicationConverter.INS.cmd2AngelBindStationContext(angelBindStationCmd);
        angelRepository.batchBindStation(angelBindStationContext.getJdhAngelBindStationEntity());

        List<Long> angelIdList = angelBindStationContext.getJdhAngelBindStationEntity().getAngelIdList();
        for (Long angelId : angelIdList) {
            JdhAngel jdhAngel = new JdhAngel();
            jdhAngel.setAngelId(angelId);
            //发送修改护士信息事件
            eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngel, AngelEventTypeEnum.ANGEL_MAIN_DATA_MODIFY, null));
        }


        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUnBindStation(AngelBindStationCmd angelBindStationCmd) {
        if (Objects.isNull(angelBindStationCmd) || CollectionUtils.isEmpty(angelBindStationCmd.getAngelIdList())) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelBindStationContext angelBindStationContext = AngelApplicationConverter.INS.cmd2AngelBindStationContext(angelBindStationCmd);
        angelRepository.batchUnBindStation(angelBindStationContext.getJdhAngelBindStationEntity());
        // angel 解绑 station 时 删除对应的排班
        scheduleAngelRepository.unbindAngelAndStaton(angelBindStationContext.getJdhAngelBindStationEntity());
        List<Long> angelIdList = angelBindStationContext.getJdhAngelBindStationEntity().getAngelIdList();
        for (Long angelId : angelIdList) {
            JdhAngel jdhAngel = new JdhAngel();
            jdhAngel.setAngelId(angelId);
            //发送修改护士信息事件
            eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngel, AngelEventTypeEnum.ANGEL_MAIN_DATA_MODIFY, null));
        }

        return Boolean.TRUE;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAngelJobNature(SubmitAngelCmd submitAngelCmd) {
        if (Objects.isNull(submitAngelCmd) || Objects.isNull(submitAngelCmd.getAngelId()) || Objects.isNull(submitAngelCmd.getJobNature())) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        JdhAngelRepQuery query = new JdhAngelRepQuery();
        query.setAngelId(submitAngelCmd.getAngelId());
        JdhAngel jdhAngelDetail = angelRepository.queryAngelDetail(query);
        if(Objects.isNull(jdhAngelDetail)) {
            log.error("AngelApplicationImpl -> updateAngelJobNature, 服务者不存在");
            return Boolean.FALSE;
        }

        log.info("AngelApplicationImpl -> updateAngelJobNature jdhAngelCmd:{}", JSON.toJSONString(submitAngelCmd));
        AngelSubmitContext angelSubmitContext = AngelApplicationConverter.INS.cmd2SubmitContext(submitAngelCmd);
        JdhAngel jdhAngel = angelSubmitContext.getJdhAngel();
        angelRepository.updateAngelJobNature(jdhAngel);
        // 变更职业标签时 删除旧的排班
        scheduleAngelRepository.logicDeleteByAngelPinAndDate(jdhAngelDetail.getAngelPin(), null, jdhAngel.getOperator(), jdhAngel.getJobNature());

        //修改服务者标签为全职时,默认打开接单开关
        if (Objects.nonNull(jdhAngel.getJobNature()) && JobNatureEnum.FULL_TIME.getValue() == jdhAngel.getJobNature()) {
            jdhAngel.setTakeOrderStatus(AngelTakeOrderStatusEnum.OPEN.getCode());
            angelRepository.updateAngelTakeOrderStatus(jdhAngel);
            //发送修改护士信息事件
            eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngel, AngelEventTypeEnum.ANGEL_WORK_STATUS_MODIFY, null));
        }

        //发送修改护士信息事件
        eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngel, AngelEventTypeEnum.ANGEL_MAIN_DATA_MODIFY, null));
        return Boolean.TRUE;
    }

    @Override
    @LogAndAlarm
    public Boolean batchBindAngelSkill(AngelBindSkillCmd angelBindSkillCmd) {
        if (Objects.isNull(angelBindSkillCmd) || Objects.isNull(angelBindSkillCmd.getAngelId())
                || CollectionUtils.isEmpty(angelBindSkillCmd.getAngelSkillCodeList())) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        List<String> angelSkillCodeList = angelBindSkillCmd.getAngelSkillCodeList();
        AngelSkillDictRepQuery query = new AngelSkillDictRepQuery();
        query.setAngelSkillCodeList(angelSkillCodeList);
        //根据技能code查询技能信息
        List<JdhAngelSkillDict> skillDictList = angelSkillDictRepository.findList(query);

        //查询已开启技能
        AngelSkillRelRepPageQuery angelSkillRelRepPageQuery = new AngelSkillRelRepPageQuery();
        angelSkillRelRepPageQuery.setAngelId(angelBindSkillCmd.getAngelId());
        List<JdhAngelSkillRel> angelSkillRelList = angelSkillRelRepository.getAngelSkillRelList(angelSkillRelRepPageQuery);
        Set<String> angelSkillCodeSet = angelSkillRelList.stream().map(JdhAngelSkillRel::getAngelSkillCode).collect(Collectors.toSet());

        List<JdhAngelSkillRel> jdhAngelSkillRelList = new ArrayList<>();
        for (JdhAngelSkillDict jdhAngelSkillDict : skillDictList) {
            //已开启技能过滤,无需重复绑定
            if (angelSkillCodeSet.contains(jdhAngelSkillDict.getAngelSkillCode())) {
                continue;
            }
            JdhAngelSkillRel jdhAngelSkillRel = new JdhAngelSkillRel();
            jdhAngelSkillRel.setAngelId(angelBindSkillCmd.getAngelId());
            jdhAngelSkillRel.setAngelSkillCode(jdhAngelSkillDict.getAngelSkillCode());
            jdhAngelSkillRel.setAngelSkillName(jdhAngelSkillDict.getAngelSkillName());
            jdhAngelSkillRel.setServiceGroupId(jdhAngelSkillDict.getServiceGroupId());
            jdhAngelSkillRel.setItemType(jdhAngelSkillDict.getItemType());
            jdhAngelSkillRel.setCreateUser(angelBindSkillCmd.getOperator());
            jdhAngelSkillRelList.add(jdhAngelSkillRel);
        }

        angelSkillRelRepository.batchBindAngelSkill(jdhAngelSkillRelList);

        JdhAngel jdhAngel = new JdhAngel();
        jdhAngel.setAngelId(angelBindSkillCmd.getAngelId());
        //发送修改护士信息事件
        eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngel, AngelEventTypeEnum.ANGEL_SKILL_MODIFY, null));
        return Boolean.TRUE;
    }

    @Override
    @LogAndAlarm
    public Boolean batchUnBindAngelSkill(AngelBindSkillCmd angelBindSkillCmd) {
        if (Objects.isNull(angelBindSkillCmd) || Objects.isNull(angelBindSkillCmd.getAngelId())
                || CollectionUtils.isEmpty(angelBindSkillCmd.getAngelSkillCodeList())) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        List<JdhAngelSkillRel> jdhAngelSkillRelList = new ArrayList<>();
        List<String> angelSkillCodeList = angelBindSkillCmd.getAngelSkillCodeList();
        for (String angelSkillCode : angelSkillCodeList) {
            JdhAngelSkillRel jdhAngelSkillRel = new JdhAngelSkillRel();
            jdhAngelSkillRel.setAngelSkillCode(angelSkillCode);
            jdhAngelSkillRel.setAngelId(angelBindSkillCmd.getAngelId());
            jdhAngelSkillRel.setUpdateUser(angelBindSkillCmd.getOperator());
            jdhAngelSkillRelList.add(jdhAngelSkillRel);
        }
        angelSkillRelRepository.batchUnBindAngelSkill(jdhAngelSkillRelList);

        JdhAngel jdhAngel = new JdhAngel();
        jdhAngel.setAngelId(angelBindSkillCmd.getAngelId());
        //发送修改护士信息事件
        eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngel, AngelEventTypeEnum.ANGEL_SKILL_MODIFY, null));

        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTakeOrderStatus(AngelUpdateTakeOrderStatusCmd angelUpdateTakeOrderStatusCmd) {
        if (Objects.isNull(angelUpdateTakeOrderStatusCmd) || Objects.isNull(angelUpdateTakeOrderStatusCmd.getAngelId())
                || Objects.isNull(angelUpdateTakeOrderStatusCmd.getTakeOrderStatus())) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        JdhAngel jdhAngel = new JdhAngel();
        jdhAngel.setAngelId(angelUpdateTakeOrderStatusCmd.getAngelId());
        jdhAngel.setTakeOrderStatus(angelUpdateTakeOrderStatusCmd.getTakeOrderStatus());

        angelRepository.updateAngelTakeOrderStatus(jdhAngel);

        //发送修改护士信息事件
        eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngel, AngelEventTypeEnum.ANGEL_WORK_STATUS_MODIFY, null));
        return Boolean.TRUE;
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.angel.service.impl.AngelApplicationImpl.syncAngelAuditInfoFromNethp")
    public void syncAngelAuditInfoFromNethp(AngelSyncCmd angelSyncCmd) {
        //益世审核驳回发送触达，给护士发短信
        if (NethpAngelEventAuditTypeEnum.YSSFZSH.getCode().equals(angelSyncCmd.getAuditType())) {
            JdhAngel jdhAngel = angelRepository.queryByUniqueId(JdhAngelRepQuery.builder().angelPin(angelSyncCmd.getDoctorPin()).build());
            if (Objects.isNull(jdhAngel)) {
                log.warn("NethpAngelAuditListener -> 益世审核驳回通过pin反查护士信息为空，不处理");
                throw new BusinessException(AngelErrorCode.ANGEL_NOT_EXIST);
            }
            //发送服务者审核事件
            publishAngelAuditEvent(null, jdhAngel, angelSyncCmd);
            return;
        }

        //互医医生id(可能为空)
        Long platformId = angelSyncCmd.getPlatformId();
        //医生pin
        String doctorPin = angelSyncCmd.getDoctorPin();

        //根据医生id或pin查询互医医生主数据
        NethpBaseDoctorInfoClientParam nethpBaseDoctorInfoClientParam = new NethpBaseDoctorInfoClientParam();
        nethpBaseDoctorInfoClientParam.setPin(doctorPin);
        nethpBaseDoctorInfoClientParam.setPlatformId(platformId);
        NethpBaseDoctorInfoClientBo baseDoctorInfo = nethpAngelRpc.getBaseDoctorInfo(nethpBaseDoctorInfoClientParam);
        //将互医医生数据转换为消医护士主数据entity
        JdhAngel jdhAngelAdd = AngelApplicationConverter.INS.cmd2JdhAngel(baseDoctorInfo);
        //审核驳回原因
        jdhAngelAdd.setAuditProcessRemark(angelSyncCmd.getAuditReason());
        //查询现有服务者信息
        JdhAngel jdhAngelOld = queryAngelByPin(doctorPin);
        if (Objects.nonNull(jdhAngelOld)) {
            //入驻消息中，如果身份证号相同，沿用老护士ID，在老数据基础上更新
            if (Objects.equals(jdhAngelOld.getIdCard(), jdhAngelAdd.getIdCard())) {
                jdhAngelAdd.setAngelId(jdhAngelOld.getAngelId());
                jdhAngelAdd.setVersion(jdhAngelOld.getVersion());
                log.info("AngelApplicationImpl -> syncAngelFromNethp jdhAngelOld:{}", JSON.toJSONString(jdhAngelOld));
                //更新邀请护士信息
                JdhAngelExtend angelOldExtend = jdhAngelOld.findExtend(AngelExtendKeyEnum.INVITE_ANGEL_ID.getFiledKey());
                //如果库中已有邀请护士信息
                if (Objects.nonNull(angelOldExtend)) {
                    //如果从互医查出来有邀请护士信息-进行更新
                    if (Objects.nonNull(jdhAngelAdd.findExtend(AngelExtendKeyEnum.INVITE_ANGEL_ID.getFiledKey()))) {
                        jdhAngelAdd.findExtend(AngelExtendKeyEnum.INVITE_ANGEL_ID.getFiledKey()).setId(angelOldExtend.getId());
                    }
                }
            } else {
                //入驻消息中，如果身份证号不同，删除现有护士数据，新增护士数据
                //保存服务者信息
                angelRepository.remove(jdhAngelOld);
            }
        }

        log.info("AngelApplicationImpl -> syncAngelFromNethp jdhAngelAdd:{}", JSON.toJSONString(jdhAngelAdd));
        //保存服务者信息
        angelRepository.saveAngel(jdhAngelAdd);

        //发送服务者审核事件
        publishAngelAuditEvent(jdhAngelAdd, jdhAngelOld, angelSyncCmd);
    }

    @Override
    @LogAndAlarm
    public void syncAngelChangeFromNethp(AngelNethpChangeCmd angelNethpChangeCmd) {
        log.info("AngelApplicationImpl -> syncAngelChangeFromNethp angelNethpChangeCmd:{}", JSON.toJSONString(angelNethpChangeCmd));
        List<String> remark = angelNethpChangeCmd.getRemark();
        boolean needChangeFlag = false;
        for (String field : remark) {
            //如果更改字段需要处理
            if (NethpAngelChangeFiledEnum.containsChangeField(field)) {
                needChangeFlag = true;
                break;
            }
        }

        if (BooleanUtils.isNotTrue(needChangeFlag)) {
            log.info("AngelApplicationImpl -> syncAngelChangeFromNethp 本次信息变更字段不在处理范围内 angelNethpChangeCmd:{}", JSON.toJSONString(angelNethpChangeCmd));
            return;
        }

        //根据医生id或pin查询互医医生主数据
        NethpBaseDoctorInfoClientParam nethpBaseDoctorInfoClientParam = new NethpBaseDoctorInfoClientParam();
        nethpBaseDoctorInfoClientParam.setPlatformId(angelNethpChangeCmd.getDoctorId());
        NethpBaseDoctorInfoClientBo baseDoctorInfo = nethpAngelRpc.getBaseDoctorInfo(nethpBaseDoctorInfoClientParam);

        //将互医医生数据转换为消医护士主数据entity
        JdhAngel jdhAngelModify = AngelApplicationConverter.INS.cmd2JdhAngel(baseDoctorInfo);
        //查询现有服务者信息
        JdhAngel jdhAngelOld = queryAngelByDoctorId(angelNethpChangeCmd.getDoctorId());

        if (Objects.isNull(jdhAngelOld) || Objects.isNull(jdhAngelOld.getAngelId())) {
            log.error("AngelApplicationImpl -> syncAngelChangeFromNethp 互医护士主数据变更MQ处理失败,消医侧不存在用户信息 angelNethpChangeCmd:{}", JSON.toJSONString(angelNethpChangeCmd));
            return;
        }
        //更新邀请护士信息
        JdhAngelExtend angelOldExtend = jdhAngelOld.findExtend(AngelExtendKeyEnum.INVITE_ANGEL_ID.getFiledKey());
        JdhAngelExtend angelModifyExtend = jdhAngelModify.findExtend(AngelExtendKeyEnum.INVITE_ANGEL_ID.getFiledKey());
        //如果库中已有邀请护士信息
        if (Objects.nonNull(angelOldExtend)) {
            //如果从互医查出来有邀请护士信息-进行更新
            if (Objects.nonNull(angelModifyExtend)) {
                angelModifyExtend.setId(angelOldExtend.getId());
            } else {
                //如果从互医查出来没有邀请护士信息-删除库中已有数据
                angelOldExtend.setValue("");
                jdhAngelModify.setAngelExtends(Lists.newArrayList(angelOldExtend));
            }
        }

        jdhAngelModify.setAngelId(jdhAngelOld.getAngelId());
        jdhAngelModify.setVersion(jdhAngelOld.getVersion());

        log.info("AngelApplicationImpl -> syncAngelChangeFromNethp jdhAngelAdd:{}", JSON.toJSONString(jdhAngelModify));
        //保存服务者信息
        angelRepository.saveAngel(jdhAngelModify);

        //发送消医侧服务者基本信息变更事件
        publishAngelBaseUpdateEvent(remark, jdhAngelModify);

    }

    /**
     * 获取护士职称列表
     * @return
     */
    @Override
    public List<JdhAngelProfessionTitleDictDto> getAngelProfessionTitleDictList() {
        return Arrays.stream(AngelProfessionTitleCodeEnum.values()).map(angelProfessionTitleCodeEnum -> JdhAngelProfessionTitleDictDto.builder().professionTitleCode(angelProfessionTitleCodeEnum.getCode().toString()).professionTitleName(angelProfessionTitleCodeEnum.getDesc()).build()).collect(Collectors.toList());
    }

    /**
     * 获取护士实时位置
     *
     * @param angelId
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelLocationDto getExtLocation(Long angelId) {
        AssertUtils.nonNull(angelId, BusinessErrorCode.ILLEGAL_ARG_ERROR);
        return angelLocationApplication.getLocation(angelId);
    }

    /**
     * @param remark
     * @param jdhAngelModify
     * @Description: 发送消医侧服务者基本信息变更事件
     * @Return: void
     * @Author: zhangxiaojie17
     * @Date: 2024/5/14
     **/
    private void publishAngelBaseUpdateEvent(List<String> remark, JdhAngel jdhAngelModify) {
        for (String field : remark) {
            if (NethpAngelChangeFiledEnum.containsNeedPublishAngelEventField(field)) {
                //发送服务者基本信息变更事件
                eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngelModify, AngelEventTypeEnum.ANGEL_BASE_MODIFY, null));
                break;
            }
        }
    }

    /**
     * @param jdhAngelAdd
     * @param jdhAngelOld
     * @param angelSyncCmd
     * @Description: 发送服务者审核事件
     * @Return: void
     * @Author: zhangxiaojie17
     * @Date: 2024/5/10
     **/
    private void publishAngelAuditEvent(JdhAngel jdhAngelAdd, JdhAngel jdhAngelOld, AngelSyncCmd angelSyncCmd) {
        Integer auditType = angelSyncCmd.getAuditType();

        //审核类型-资质
        if (NethpAngelEventAuditTypeEnum.ZZ.getCode().equals(auditType)) {
            //消医侧不存在服务者信息,互医第一次同步医生主数据
            if (Objects.isNull(jdhAngelOld)) {
                //本次审核状态为通过
                if (AngelAuditProcessStatusEnum.AUDIT_PASS.getCode().equals(jdhAngelAdd.getAuditProcessStatus())) {
                    //发送审核通过事件
                    eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngelAdd, AngelEventTypeEnum.ANGEL_SETTLE_AUDIT_PASS, null));
                } else if (AngelAuditProcessStatusEnum.AUDIT_REJECT.getCode().equals(jdhAngelAdd.getAuditProcessStatus())) {
                    AngelAuditEventBody angelAuditEventBody = new AngelAuditEventBody();
                    angelAuditEventBody.setAuditReason(angelSyncCmd.getAuditReason());
                    //发送审核驳回事件
                    eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngelAdd, AngelEventTypeEnum.ANGEL_SETTLE_AUDIT_REJECT, angelAuditEventBody));
                } else if (AngelAuditProcessStatusEnum.AUDIT_WAIT.getCode().equals(jdhAngelAdd.getAuditProcessStatus())) {
                    AngelAuditEventBody angelAuditEventBody = new AngelAuditEventBody();
                    angelAuditEventBody.setAuditReason(angelSyncCmd.getAuditReason());
                    //发送待审核事件
                    eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngelAdd, AngelEventTypeEnum.ANGEL_REGISTER_AUDIT_WAIT, angelAuditEventBody));
                }
            } else {
                //历史审核状态
                Integer auditProcessStatus = jdhAngelOld.getAuditProcessStatus();
                //当服务者当前审核状态为待审核
                if (AngelAuditProcessStatusEnum.AUDIT_WAIT.getCode().equals(jdhAngelAdd.getAuditProcessStatus())) {
                    //发送审核通过事件
                    eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngelAdd, AngelEventTypeEnum.ANGEL_REGISTER_AUDIT_WAIT, null));
                }
                //当服务者历史审核状态为待审核、审核驳回
                if (AngelAuditProcessStatusEnum.AUDIT_WAIT.getCode().equals(auditProcessStatus)
                        || AngelAuditProcessStatusEnum.AUDIT_REJECT.getCode().equals(auditProcessStatus)) {
                    //本次审核状态为通过
                    if (AngelAuditProcessStatusEnum.AUDIT_PASS.getCode().equals(jdhAngelAdd.getAuditProcessStatus())) {
                        //发送审核通过事件
                        eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngelOld, AngelEventTypeEnum.ANGEL_SETTLE_AUDIT_PASS, null));
                    }
                }
                //当服务者历史审核状态为待审核
                if (AngelAuditProcessStatusEnum.AUDIT_WAIT.getCode().equals(auditProcessStatus)) {
                    //本次审核状态为驳回
                    if (AngelAuditProcessStatusEnum.AUDIT_REJECT.getCode().equals(jdhAngelAdd.getAuditProcessStatus())) {
                        AngelAuditEventBody angelAuditEventBody = new AngelAuditEventBody();
                        angelAuditEventBody.setAuditReason(angelSyncCmd.getAuditReason());
                        //发送审核驳回事件
                        eventCoordinator.publish(EventFactory.newDefaultEvent(jdhAngelOld, AngelEventTypeEnum.ANGEL_SETTLE_AUDIT_REJECT, angelAuditEventBody));
                    }
                }
            }
        } else if (NethpAngelEventAuditTypeEnum.XXBG.getCode().equals(auditType)) {
            //审核类型-信息变更(目前只有身份证变更会触发该消息)
            if (NethpAngelEventAuditStatusEnum.AUDIT_PASS.getCode().equals(angelSyncCmd.getAuditStatus())) {
                eventCoordinator.publish(EventFactory.newDefaultEvent(Optional.ofNullable(jdhAngelOld).orElse(jdhAngelAdd), AngelEventTypeEnum.ANGEL_UPDATE_AUDIT_PASS, null));
            } else if (NethpAngelEventAuditStatusEnum.AUDIT_REJECT.getCode().equals(angelSyncCmd.getAuditStatus())) {
                AngelAuditEventBody angelAuditEventBody = new AngelAuditEventBody();
                angelAuditEventBody.setAuditReason(angelSyncCmd.getAuditReason());
                angelAuditEventBody.setAuditType(angelSyncCmd.getAuditType());
                eventCoordinator.publish(EventFactory.newDefaultEvent(Optional.ofNullable(jdhAngelOld).orElse(jdhAngelAdd), AngelEventTypeEnum.ANGEL_UPDATE_AUDIT_REJECT, angelAuditEventBody));
            }
        } else if (NethpAngelEventAuditTypeEnum.YSSFZSH.getCode().equals(auditType)) {
            //审核类型-益世审核
            if (NethpAngelEventAuditStatusEnum.AUDIT_PASS.getCode().equals(angelSyncCmd.getAuditStatus())) {
                //审核成功不处理
            } else if (NethpAngelEventAuditStatusEnum.AUDIT_REJECT.getCode().equals(angelSyncCmd.getAuditStatus())) {
                //审核失败
                AngelAuditEventBody angelAuditEventBody = new AngelAuditEventBody();
                angelAuditEventBody.setAuditReason(angelSyncCmd.getAuditReason());
                angelAuditEventBody.setAuditType(angelSyncCmd.getAuditType());
                eventCoordinator.publish(EventFactory.newDefaultEvent(Optional.ofNullable(jdhAngelOld).orElse(jdhAngelAdd), AngelEventTypeEnum.ANGEL_REAL_NAME_AUDIT_REJECT, angelAuditEventBody));
            }
        }
    }

    /**
     * 查询护士详情
     * @param angelDetailRequest
     * @return
     */
    @Override
    public JdhAngelDetailDto queryAngelDetail(AngelDetailRequest angelDetailRequest) {
        if (Objects.isNull(angelDetailRequest) || Objects.isNull(angelDetailRequest.getAngelId())) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        log.info("AngelApplicationImpl -> queryAngelDetail angelDetailRequest:{}", JSON.toJSONString(angelDetailRequest));
        JdhAngelRepQuery jdhAngelRepQuery = new JdhAngelRepQuery();
        jdhAngelRepQuery.setAngelId(angelDetailRequest.getAngelId());
        jdhAngelRepQuery.setIsQueryNotOpenSkill(angelDetailRequest.getIsQueryNotOpenSkill());

        JdhAngel jdhAngel = angelRepository.queryAngelDetail(jdhAngelRepQuery);
        log.info("AngelApplicationImpl -> queryAngelDetail jdhAngel:{}", JSON.toJSONString(jdhAngel));

        return AngelApplicationConverter.INS.convert2JdhAngelDetailDto(jdhAngel);
    }

    @Override
    public PageDto<JdhAngelDto> queryAngelByPage(AngelPageRequest request) {
        if (Objects.isNull(request)) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        //统一去前后空格
        TrimUtil.trimStringFields(request);

        JdhAngelRepPageQuery query = new JdhAngelRepPageQuery();
        query.setPageNum(request.getPageNum());
        query.setPageSize(request.getPageSize());
        query.setAngelId(request.getAngelId());
        query.setAngelName(request.getAngelName());
        query.setStationId(request.getStationId());
        //身份证号明文
        query.setIdCardIndex(request.getIdCard());
        //电话明文
        query.setPhoneIndex(request.getPhone());
        //服务者审核状态
        query.setAuditProcessStatus(request.getAuditProcessStatus());
        //服务者id集合
        query.setAngelIdList(request.getAngelIdList());
        query.setRelationStationMasterFlag(request.getRelationStationMasterFlag());
        query.setRelationStationFlag(request.getRelationStationFlag());
        query.setJobNature(request.getJobNature());
        query.setStationMaster(request.getStationMaster());
        query.setNethpDocIdList(request.getNethpDocIdList());
        query.setTakeOrderStatus(request.getTakeOrderStatus());
        query.setJdhProviderId(request.getJdhProviderId());
        query.setJdhProviderIds(request.getJdhProviderIds());
        query.setQueryAllJdhProvider(request.getQueryAllJdhProvider());

        query.setProvinceCode(request.getProvinceCode());
        query.setCityCode(request.getCityCode());
        query.setCountyCode(request.getCountyCode());
        query.setOneDepartmentCode(request.getOneDepartmentCode());
        query.setTwoDepartmentCode(request.getTwoDepartmentCode());
        query.setWorkIdentity(request.getWorkIdentity());

        log.info("AngelApplicationImpl -> queryAngelByPage query:{}", JSON.toJSONString(query));
        Page<JdhAngel> page = angelRepository.page(query);
        log.info("AngelApplicationImpl -> queryAngelByPage page:{}", JSON.toJSONString(page));

        if (page == null) {
            return PageDto.getEmptyPage();
        }

        PageDto<JdhAngelDto> pageDto = new PageDto<>();
        //服务者站点信息
        Map<Long, AngelStationDto> jdhStationDtoMap = buildAngelStationMap(page);
        List<JdhAngelDto> list = AngelApplicationConverter.INS.convert2AngelDtoForPage(request.getNameMask(), page.getRecords(), jdhStationDtoMap);

        pageDto.setList(list);
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalCount(page.getTotal());
        return pageDto;
    }

    @Override
    public Boolean submitAngel(SubmitAngelCmd submitAngelCmd) {
        log.info("AngelApplicationImpl -> submit cmd:{}", JSON.toJSONString(submitAngelCmd));

        JdhAngel jdhAngel = AngelApplicationConverter.INS.cmd2JdhAngel(submitAngelCmd);

        angelRepository.saveAngel(jdhAngel);
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeAllAngel(Long angelId) {
        angelRepository.removeAllAngel(angelId);
        return Boolean.TRUE;
    }

    /**
     * 导入科室数据
     * @param importDepartmentCmd
     */
    @Override
    public void importDepartmentData(ImportDepartmentCmd importDepartmentCmd) {
        angelDomainService.importDepartmentData(importDepartmentCmd.getFilePath());
    }

    /**
     * @param page
     * @Description: 构建服务者站点信息map key:stationId value:JdhStationDto
     * @Return: void
     * @Author: zhangxiaojie17
     * @Date: 2024/4/23
     **/
    private Map<Long, AngelStationDto> buildAngelStationMap(Page<JdhAngel> page) {
        List<JdhAngel> records = page.getRecords();
        List<Long> stationIdList = records.stream().map(JdhAngel::getStationId).collect(Collectors.toList());
        StationQuery stationQuery = new StationQuery();
        stationQuery.setStationIds(Sets.newHashSet(stationIdList));
        List<AngelStationDto> jdhStationDtos = stationApplication.queryJdhStation(stationQuery);
        return jdhStationDtos.stream().collect(Collectors.toMap(AngelStationDto::getAngelStationId, each -> each, (value1, value2) -> value1));
    }

    /**
     * @param doctorPin
     * @Description: 根据doctorPin查询护士信息
     * @Return: void
     * @Author: zhangxiaojie17
     * @Date: 2024/5/9
     **/
    private JdhAngel queryAngelByPin(String doctorPin) {
        if (StringUtils.isBlank(doctorPin)) {
            return null;
        }
        JdhAngelRepQuery jdhAngelRepQuery = new JdhAngelRepQuery();
        jdhAngelRepQuery.setAngelPin(doctorPin);
        //根据互医医生id、pin查询服务者信息
        //return angelDomainService.queryByUniqueId(jdhAngelRepQuery);
        return angelRepository.queryByUniqueId(jdhAngelRepQuery);
    }

    /**
     * @param doctorId
     * @Description: 根据doctorPin查询护士信息
     * @Return: void
     * @Author: zhangxiaojie17
     * @Date: 2024/5/9
     **/
    private JdhAngel queryAngelByDoctorId(Long doctorId) {
        if (Objects.isNull(doctorId)) {
            return null;
        }
        JdhAngelRepQuery jdhAngelRepQuery = new JdhAngelRepQuery();
        jdhAngelRepQuery.setNethpDocId(doctorId);
        //根据互医医生id服务者信息
        return angelRepository.queryByUniqueId(jdhAngelRepQuery);
    }

    /**
     * @param angelDetailRequest
     * @return
     */
    @Override
    public JdhAngelDetailDto queryAngelInfo(AngelDetailRequest angelDetailRequest) {
        return queryAngelDetail(angelDetailRequest);
    }

    /**
     * 查询科室
     * @param getDepartmentRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public List<JdhDepartmentDto> getDepartments(GetDepartmentRequest getDepartmentRequest){
        List<JdhDepartment> jdhDepartments = jdhDepartmentRepository.findByParentCode(StringUtils.defaultString(getDepartmentRequest.getParentCode(),"0"));
        List<JdhDepartmentDto> jdhDepartmentDtos = AngelApplicationConverter.INS.toJdhDepartmentDto(jdhDepartments);
        return jdhDepartmentDtos;
    }

    @Override
    @LogAndAlarm
    public Boolean saveAngelInviteCode(Long angelId) {
        AssertUtils.nonNull(angelId, "AngelApplicationImpl -> saveAngelInviteCode angelId is null");
        JdhAngelExtendQuery query = JdhAngelExtendQuery.builder().angelId(angelId).extendKeyList(Arrays.asList(AngelExtendKeyEnum.CPS_INVITE_CODE.getFiledKey())).build();
        List<JdhAngelExtend> angelExtendList = angelRepository.findAngelExtendList(query);
        if(CollectionUtils.isEmpty(angelExtendList)){
            JdhAngelExtend angelExtend = JdhAngelExtend.builder().angelId(angelId).attribute(AngelExtendKeyEnum.CPS_INVITE_CODE.getFiledKey()).value(String.valueOf(getAvaiableAngelInviteCode())).build();
            angelRepository.insertAngelExtend(angelExtend);
            return true;
        }
        return false;
    }

    @Override
    @LogAndAlarm
    public Boolean saveAngelUnionId(AngelUnionIdCmd cmd) {
        AssertUtils.nonNull(cmd, "AngelApplicationImpl -> saveAngelUnionId cmd is null");
        checkUnionId(cmd.getUnionId());

        JdhAngelRepQuery angelRepQuery = JdhAngelRepQuery.builder().angelPin(cmd.getUserPin()).build();
        JdhAngel angel = angelRepository.queryByUniqueId(angelRepQuery);
        if(Objects.nonNull(angel)){
            JdhAngelExtend angelExtend = JdhAngelExtend.builder().angelId(angel.getAngelId()).attribute(AngelExtendKeyEnum.CPS_JF_UNION_ID.getFiledKey()).value(String.valueOf(cmd.getUnionId())).build();
            return angelRepository.saveAngelExtend(angelExtend) > 0 ;
        }
        return false;
    }

    private void checkUnionId(Long unionId){
        if(Objects.isNull(unionId)){
            throw new BusinessException(AngelErrorCode.ANGEL_CPS_UNIONID_NOT_NULL);
        }

        JdUnionGoodsListContext jdUnionGoodsListContext = JdUnionGoodsListContext.builder().key("手机").build();
        List<JdUnionGoodsListBo> jdUnionGoodsListBoList = jdUnionRpc.queryGoodsListByKey(jdUnionGoodsListContext);

        // 当检索不到商品的时候兜底检查结果为通过，防止查不到商品时候卡流程
        if(jdUnionGoodsListBoList.isEmpty()){
            return ;
        }

        GenerateClickUrlContext context = GenerateClickUrlContext.builder().unionId(unionId).skuId(jdUnionGoodsListBoList.get(0).getSkuId()).build();

        try {
            String clickUrl = jdUnionRpc.generateClickUrl(context);
            if(Objects.isNull(clickUrl)){
                throw new BusinessException(AngelErrorCode.ANGEL_CPS_UNIONID_NO_AVAIABLE);
            }
        } catch (Exception e){
            throw new BusinessException(AngelErrorCode.ANGEL_CPS_UNIONID_NO_AVAIABLE);
        }
    }

    @LogAndAlarm
    @Override
    public JdUnionPageDto queryGoodsListByCpsPage(JdUnionGoodsListQuery query) {
        JdUnionPageDto result = new JdUnionPageDto();
        // 1、补充轮播图
        ContentQuery contentQuery = new ContentQuery();
        contentQuery.setStatus(ContentsStatusEnum.SHOW.getStatus());
        contentQuery.setPosition(ContentPositionEnum.ANGEL_CPS.getKey());
        result.setContentDtoList(contentApplication.pageList(contentQuery).getList());

        // 2、查询联盟商品
        JdUnionGoodsListContext context = AngelApplicationConverter.INS.convert2JdUnionGoodsListContext(query);
        List<CpsSkuConfig> duccCpsConfig = JSON.parseArray(JSON.toJSONString(duccConfig.getCpsRecommendProductList()), CpsSkuConfig.class);
        log.info("AngelApplicationImpl -> queryGoodsListByCpsPage duccCpsConfig:{}", JSON.toJSONString(duccCpsConfig));
        if(CollectionUtils.isEmpty(duccCpsConfig)){
            return result;
        }
        CpsSkuConfig cpsSkuConfig = null;
        if(Objects.nonNull(query.getTabName())){
            Optional<CpsSkuConfig> optional = duccCpsConfig.stream().filter(s -> query.getTabName().equalsIgnoreCase(s.getName())).findFirst();
            if (Objects.nonNull(optional) && optional.isPresent() && Objects.nonNull(optional.get())){
                cpsSkuConfig = optional.get();
            }
        } else {
            cpsSkuConfig = duccCpsConfig.get(0);
        }
        if(Objects.nonNull(cpsSkuConfig)){
            context.setSkuIds(cpsSkuConfig.getSkuIds());
            cpsSkuConfig.setSelected(true);
            List<JdUnionGoodsListBo> goodsList = jdUnionRpc.queryGoodsList(context);
            List<JdUnionGoodsDto> jdUnionGoodsDtoList = AngelApplicationConverter.INS.convert2JdUnionGoodsDtoList(goodsList);
            handleSkuType(cpsSkuConfig.getSkuIds(), jdUnionGoodsDtoList);
            result.setJdUnionGoodsDtoList(jdUnionGoodsDtoList);
        }

        // 3、补充tab
        List<CpsSkuConfigDto> cpsSkuConfigDtoList =  AngelApplicationConverter.INS.convert2CpsSkuConfigDtoList(duccCpsConfig);
        result.setCpsSkuConfigDtoList(cpsSkuConfigDtoList);

        // 4、护士脱敏姓名
        JdhAngelRepQuery jdhAngelRepQuery = JdhAngelRepQuery.builder()
                .angelPin(query.getUserPin())
                .build();
        JdhAngel jdhAngel = angelRepository.queryByUniqueId(jdhAngelRepQuery);
        if(Objects.nonNull(jdhAngel)){
            result.setAngelName(jdhAngel.getAngelName());
        }
        return result;
    }

    /**
     * 补充skuType
     */
    private void handleSkuType(List<Long> skuIds, List<JdUnionGoodsDto> jdUnionGoodsDtoList){
        if(CollectionUtils.isEmpty(jdUnionGoodsDtoList)){
            return ;
        }
        JdhSkuListRequest request = JdhSkuListRequest.builder().skuIdList(skuIds.stream().collect(Collectors.toSet())).build();
        Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(request);
        for(JdUnionGoodsDto jdUnionGoodsDto : jdUnionGoodsDtoList){
            JdhSkuDto skuDto = jdhSkuDtoMap.get(jdUnionGoodsDto.getSkuId());
            if(Objects.isNull(skuDto)){
                continue;
            }
            jdUnionGoodsDto.setSkuType(skuDto.getSkuType());
        }
    }

    @LogAndAlarm
    @Override
    public String generateClickUrl(GenerateClickUrlQuery query) {
        // 服务者基本信息
        JdhAngelRepQuery jdhAngelRepQuery = JdhAngelRepQuery.builder()
                .angelPin(query.getUserPin())
                .build();
        JdhAngel jdhAngel = angelRepository.queryByUniqueId(jdhAngelRepQuery);
        if(Objects.isNull(jdhAngel)){
            throw new BusinessException(AngelErrorCode.ANGEL_NOT_EXIST);
        }
        Long angelUnionId = getAngelUnionId(jdhAngel);
        if(Objects.isNull(angelUnionId)){
            throw new BusinessException(AngelErrorCode.ANGEL_UNIONID_NOT_EXIST);
        }
        GenerateClickUrlContext context = AngelApplicationConverter.INS.convert2GenerateClickUrlContext(query);
        context.setUnionId(angelUnionId);
        return jdUnionRpc.generateClickUrl(context);
    }

    private Long getAngelUnionId(JdhAngel jdhAngel){
        JdhAngelExtendQuery query = JdhAngelExtendQuery.builder().angelId(jdhAngel.getAngelId()).extendKeyList(Arrays.asList(AngelExtendKeyEnum.CPS_JF_UNION_ID.getFiledKey())).build();
        List<JdhAngelExtend> angelExtendList = angelRepository.findAngelExtendList(query);
        if(CollectionUtils.isNotEmpty(angelExtendList) && Objects.nonNull(angelExtendList.get(0))){
            String inviteCodeStr = angelExtendList.get(0).getValue();
            if (Objects.nonNull(inviteCodeStr) && inviteCodeStr.matches("\\d+")){
                return Long.parseLong(inviteCodeStr);
            }
        }
        return null;
    }


    private Integer getAvaiableAngelInviteCode(){
        Integer angelInviteCode = null;
        List<JdhAngelExtend> angelExtendList = Collections.emptyList();
        do {
            angelInviteCode = RandomNumberGenerator.generateUniqueNumberNone4();
            log.info("AngelApplicationImpl -> getAvaiableAngelInviteCode 计划使用angelInviteCode:{}", String.valueOf(angelInviteCode));
            JdhAngelExtendQuery query = JdhAngelExtendQuery.builder().extendKeyList(Arrays.asList(AngelExtendKeyEnum.CPS_INVITE_CODE.getFiledKey())).extendValue(String.valueOf(angelInviteCode)).build();
            angelExtendList = angelRepository.findAngelExtendList(query);
        } while(CollectionUtils.isNotEmpty(angelExtendList));
        log.info("AngelApplicationImpl -> getAvaiableAngelInviteCode 实际使用angelInviteCode:{}", String.valueOf(angelInviteCode));
        return angelInviteCode;
    }

    /**
     * 根据服务站id查询服务者列表信息
     * @param angelStationRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public List<JdhAngelDto> queryAngelByStationId(AngelStationRequest angelStationRequest) {
        return queryByStationId(angelStationRequest);
    }

    /**
     * 查询护士技能集合
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public List<JdhAngelSkillRelDto> queryAngelSkillListByAngelInfo(AngelSkillDictPageRequest request) {
        if (Objects.isNull(request) || (Objects.isNull(request.getAngelId()) && CollectionUtils.isEmpty(request.getAngelIdList()))) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelSkillRelRepPageQuery angelSkillRelRepPageQuery = new AngelSkillRelRepPageQuery();
        angelSkillRelRepPageQuery.setAngelId(request.getAngelId());
        angelSkillRelRepPageQuery.setAngelIdList(request.getAngelIdList());
        List<JdhAngelSkillRel> angelSkillRelList = angelSkillRelRepository.getAngelSkillRelList(angelSkillRelRepPageQuery);
        return AngelApplicationConverter.INS.convertAngelSkillRelDto(angelSkillRelList);
    }

    @Override
    public List<JdhAngelDto> listByAngelIds(Integer auditProcessStatus, List<Long> angelIdList) {
        if (Objects.isNull(auditProcessStatus) || CollectionUtils.isEmpty(angelIdList)){
            return Collections.emptyList();
        }
        JdhAngelRepQuery jdhAngelRepQuery = new JdhAngelRepQuery();
        jdhAngelRepQuery.setAuditProcessStatus(auditProcessStatus);
        jdhAngelRepQuery.setIntendedAngelIds(angelIdList);

        List<JdhAngel> jdhAngels = angelRepository.queryByStationId(jdhAngelRepQuery);
        log.info("AngelApplicationImpl -> queryByStationId jdhAngel:{}", JSON.toJSONString(jdhAngels));
        if (CollectionUtils.isEmpty(jdhAngels)) {
            return new ArrayList<>();
        }

        List<JdhAngelDto> list = Lists.newArrayListWithExpectedSize(jdhAngels.size());
        for (JdhAngel jdhAngel : jdhAngels) {
            JdhAngelDto jdhAngelDto = AngelApplicationConverter.INS.convert2JdhAngelDto(jdhAngel);
            list.add(jdhAngelDto);
        }
        return list;
    }

    /**
     * 查询护士排期
     * @param angelScheduleCalendarRequest
     * @return
     */
    @Override
    public List<AngelScheduleDto> queryAngelScheduleCalendar(AngelScheduleCalendarRequest angelScheduleCalendarRequest) {
        return angelScheduleApplication.queryAngelScheduleCalendar(angelScheduleCalendarRequest);
    }

    /**
     * 更新工作身份
     *
     * @param cmd cmd
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateWorkIdentity(UpdateAngelWorkIdentityCmd cmd) {
        if (Objects.isNull(cmd)
                || Objects.isNull(cmd.getAngelId())
                || Objects.isNull(cmd.getWorkIdentity())
                || Objects.isNull(WorkIdentityEnum.getByCode(cmd.getWorkIdentity()))) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        log.info("AngelApplicationImpl -> updateWorkIdentity cmd:{}", JSON.toJSONString(cmd));

        JdhAngel angel = angelRepository.find(JdhAngelIdentifier.builder().angelId(cmd.getAngelId()).build());
        if(Objects.isNull(angel)){
            throw new BusinessException(BusinessErrorCode.ANGEL_NOT_EXIST);
        }

        // 更新工作身份
        angel.updateWorkIdentity(UpdateAngelWorkIdentityContext.builder()
                                                                .angelId(cmd.getAngelId())
                                                                .workIdentity(cmd.getWorkIdentity())
                                                                .build());

        //save
        angelRepository.saveAngel(angel);

        return Boolean.TRUE;
    }
}
