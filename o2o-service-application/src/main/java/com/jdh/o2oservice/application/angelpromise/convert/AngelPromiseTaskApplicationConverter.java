package com.jdh.o2oservice.application.angelpromise.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.enums.GenderEnum;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelTaskExtVo;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.export.angelpromise.dto.AngelTaskDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkListNodeDto;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务单 work应用层转换
 * @author: yangxiyu
 * @date: 2024/5/08 4:21 下午
 * @version: 1.0
 */
@Mapper
public interface AngelPromiseTaskApplicationConverter {

    /** 图片过期时间 */
    Integer EXPIRE_TIME = 30;

    /** */
    AngelPromiseTaskApplicationConverter INS = Mappers.getMapper(AngelPromiseTaskApplicationConverter.class);

    /**
     *
     * @param task
     * @return
     */
    List<AngelTaskDto> convert2TaskDto(List<AngelTask> task);

        /**
         * 转换task实体
         * @param task
         * @return
         */
    default AngelTaskDto convert2TaskDto(AngelTask task){
        if ( task == null ) {
            return null;
        }

        AngelTaskDto angelTaskDto = new AngelTaskDto();

        JdhAngelTaskExtVo jdhAngelTaskExtVo = task.getJdhAngelTaskExtVo();
        if (Objects.nonNull(jdhAngelTaskExtVo)) {
            String patientName = jdhAngelTaskExtVo.getPatientName();
            angelTaskDto.setPatientName(patientName);

            if (Objects.nonNull(jdhAngelTaskExtVo.getPatientGender())){
                angelTaskDto.setPatientGender(GenderEnum.getDescOfType(jdhAngelTaskExtVo.getPatientGender()));
            }

            if (Objects.nonNull(jdhAngelTaskExtVo.getPatientAge())){
                angelTaskDto.setPatientAge(String.valueOf(jdhAngelTaskExtVo.getPatientAge()));
            }
            FileManageApplication application = SpringUtil.getBean(FileManageApplication.class);


            LocalDateTime time = LocalDateTime.now().plus(EXPIRE_TIME, ChronoUnit.MINUTES);
            Date expireTime = TimeUtils.localDateTimeToDate(time);
            // 就诊记录图片
            if (CollectionUtils.isNotEmpty(jdhAngelTaskExtVo.getVisitRecordImg())){
                GenerateGetUrlCommand command = new GenerateGetUrlCommand();
                command.setFileIds(Sets.newHashSet(jdhAngelTaskExtVo.getVisitRecordImg()));
                command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
                command.setIsPublic(Boolean.TRUE);
                command.setExpireTime(expireTime);
                List<FilePreSignedUrlDto> urlDtos = application.generateGetUrl(command);
                List<String> medicalCertificateUrls = urlDtos.stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
                angelTaskDto.setMedicalCertificateUrls(medicalCertificateUrls);
            }
            // 知情同意书
            if (Objects.nonNull(jdhAngelTaskExtVo.getLetterOfConsentFileId())){
                GenerateGetUrlCommand command = new GenerateGetUrlCommand();
                command.setFileIds(Sets.newHashSet(jdhAngelTaskExtVo.getLetterOfConsentFileId()));
                command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
                command.setIsPublic(Boolean.TRUE);
                command.setExpireTime(expireTime);
                FilePreSignedUrlDto urlDto = application.generateGetUrl(command).get(0);
                angelTaskDto.setSignatureLetterOfConsentUrl(urlDto.getUrl());
            }
        }

        angelTaskDto.setTaskId( task.getTaskId() );
        angelTaskDto.setWorkId( task.getWorkId() );
        angelTaskDto.setStatus( task.getTaskStatus() );
        angelTaskDto.setBizExtStatus( task.getBizExtStatus() );
        angelTaskDto.setPatientId( task.getPatientId() );
        angelTaskDto.setPatientFullAddress( task.getPatientFullAddress() );
        angelTaskDto.setPatientAddressLat( task.getPatientAddressLat() );
        angelTaskDto.setPatientAddressLng( task.getPatientAddressLng() );
        angelTaskDto.setServiceStartTime( task.getTaskStartTime() );
        angelTaskDto.setServiceEndTime( task.getTaskEndTime() );
        angelTaskDto.setStopStatus(task.getStopStatus());

        return angelTaskDto;
    }
}
