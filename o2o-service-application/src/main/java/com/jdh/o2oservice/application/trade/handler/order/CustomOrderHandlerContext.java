package com.jdh.o2oservice.application.trade.handler.order;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.trade.model.AppointmentInfoDetail;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description CustomOrderHandlerContext
 * @Date 2024/9/2 下午3:06
 * <AUTHOR>
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomOrderHandlerContext implements Serializable {

    /**
     * 订单
     */
    private JdOrder order;

    /**
     * 订单中台状态
     * -1 Unknown 未知，无效状态
     * 1 WaitForPayment 待支付
     * 2 PaymentSuccessful 支付完成
     * 3 WaitForDelivery 待收货
     * 4 DeliverySuccessful 收货完成
     * 5 Accomplish 订单已完成
     * 6 Canceled 订单已取消
     */
    private Integer orderStatusId;

    /**
     * orderId与OrderItem映射关系
     */
    private Map<Long, List<JdOrderItem>> orderIdMappingOrderItemMap;

    /**
     * orderId与sourceVoucherId映射关系 或 orderItemId与sourceVoucherId映射关系
     */
    private Map<Long, String> orderIdMappingSourceVoucherIdMap;

    /**
     *  sourceVoucherId与履约单映射关系
     */
    private Map<String, List<JdhPromise>> sourceVoucherIdMappingPromiseMap;

    /**
     * promiseId与检测单映射关系
     */
    private Map<Long, List<MedicalPromiseDTO>> promiseIdMappingMedicalPromiseMap;

    /**
     * promiseId与服务者工单映射关系
     */
    private Map<Long, List<AngelWork>> promiseIdMappingAngelWorkMap;

    /**
     * promiseId与履约单历史映射关系
     */
    private Map<Long, List<JdhPromiseHistory>> promiseIdMappingPromiseHistoryMap;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * orderId与体检预约信息映射关系
     */
    private Map<Long, List<AppointmentInfoDetail>> orderIdMappingAppointmentInfoDetailMap;
}
