package com.jdh.o2oservice.application.medicalpromise.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.event.*;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.model.ImgExifModel;
import com.jdh.o2oservice.base.util.GeoDistanceUtil;
import com.jdh.o2oservice.base.util.O2oHttpClient;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.result.response.BaseResponse;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelWorkEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkHistoryRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkHistoryDbQuery;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelWorkExtVo;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.ShortUrlRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseStatusCmd;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.support.query.GetFileUrlRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.jboss.logging.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description: 服务者事件
 * @Author: wangpengfei144
 * @Date: 2024/5/31
 */
@Slf4j
@Component
public class AngelWorkEventSubscriber {
    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    /**
     * 履约检测单应用层
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     *
     */
    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     *
     */
    @Resource
    private AngelShipRepository angelShipRepository;

    /**
     *
     */
    @Resource
    private AngelWorkRepository angelWorkRepository;

    /**
     *
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * commonDuccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * dongDongRobotRpc
     */
    @Resource
    private DongDongRobotRpc dongDongRobotRpc;

    /**
     * 履约检测单应用层
     */
    @Resource
    private PromiseApplication promiseApplication;

    /**
     * 文件管理
     */
    @Resource
    private FileManageApplication fileManageApplication;
    /**
     * 基础地址
     */
    @Value("${promiseGoModel.domain}")
    private String domain;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    @Resource
    private AddressRpc addressRpc;

    /**
     * shortUrlRpc
     */
    @Resource
    private ShortUrlRpc shortUrlRpc;

    /**
     * 历史记录
     */
    @Resource
    private AngelWorkHistoryRepository angelWorkHistoryRepository;

    /**
     * env
     */
    @Value("${spring.profiles.active}")
    private String env;

    /**
     * 注册事件使用者
     */
    @PostConstruct
    public void registerEventConsumer() {

        //=======>>>>>> 服务者工单完成 监听消息 -> 触发检测单拉完成
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_FINISH_SERVED, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "medicalPromiseAngel", this::angelMedicalPromiseFinish, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));

        //=======>>>>>> 服务者工单完成 监听消息 -> 触发检测单拉完成
        eventConsumerRegister.register(AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_FINISH, WrapperEventConsumer.newInstance(DomainEnum.MED_PROMISE,
                "shipFinishMonitor", this::shipFinishMonitor, Boolean.TRUE, Boolean.TRUE));

        //=======>>>>>>派单任务 超时未接单提醒
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE_DELAY_ALARM, WrapperEventConsumer.newDelayInstance(DomainEnum.MED_PROMISE,
                "medPromiseStationReceiveDelayAlarm", this::medPromiseStationReceiveDelayAlarm));

        //=======>>>>>>派单任务 提交服务信息
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_SUBMIT_SERVICE_RECORDS, WrapperEventConsumer.newDelayInstance(DomainEnum.MED_PROMISE,
                "submitServiceRecords", this::submitServiceRecords));

        //=======>>>>>>派单任务 提交服务信息照片额外信息
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_SERVICE_RECORDS_IMG_EXIF_CREATE, WrapperEventConsumer.newDelayInstance(DomainEnum.MED_PROMISE,
                "serviceRecordsImgExifCreate", this::serviceRecordsImgExifCreate));
    }


    /**
     * 服务者工单完成->检测单服务拉完成
     * @param event
     */
    private void angelMedicalPromiseFinish(Event event){
        log.info("AngelWorkEventSubscriber->angelMedicalPromiseFinish,event={}",JsonUtil.toJSONString(event));
        AngelWorkEventBody angelWorkEventBody = JsonUtil.parseObject(event.getBody(), AngelWorkEventBody.class);
        Long promiseId = angelWorkEventBody.getPromiseId();
        MedicalPromiseStatusCmd medicalPromiseStatusCmd = new MedicalPromiseStatusCmd();
        medicalPromiseStatusCmd.setPromiseId(promiseId);
        medicalPromiseApplication.angelMedicalPromiseFinish(medicalPromiseStatusCmd);
    }

    /**
     * 处理运单完成
     * 1、运单完成推动工单完成
     *
     * @param event
     */
    public void shipFinishMonitor(Event event) {
        log.info("AngelWorkEventSubscriber -> shipFinishMonitor 运单配送完成!, event={}", JSON.toJSONString(event));
        AngelShip angelShip = getAngelShip(event);
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(angelShip.getWorkId()).build());
        if(Objects.isNull(angelWork)){
            log.info("AngelWorkEventSubscriber -> shipFinishMonitor 未查到服务者工单信息, angelWork={}", JSON.toJSONString(angelWork));
            return;
        }
        List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(angelWork.getPromiseId()).build());
        List<MedicalPromise> medicalPromises = medicalPromiseList.stream().filter(medicalPromise -> Objects.equals(medicalPromise.getStationId(), angelShip.getReceiverId())).collect(Collectors.toList());

        //超时未收样校验、报警
        String logId= Objects.toString(MDC.get("PFTID"), null);
        medicalPromises.forEach(medicalPromise -> {
            CompletableFuture.runAsync(() -> {
                try {
                    MDC.put("PFTID", logId);
                    if (ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getVerticalCode().equalsIgnoreCase(medicalPromise.getVerticalCode())) {
                        Long delayTime = duccConfig.getHomeSelfTestAlarm().getLong("transportTestReceiveLaboratoryTimeInterval") * 60L;
                        log.info("AngelWorkEventSubscriber -> shipFinishMonitor 非快检发送延迟消息，超时未收样校验、报警 logId={}, medicalPromise={}, delayTime={}", logId, JSON.toJSONString(medicalPromise), delayTime);
                        eventCoordinator.publishDelay(EventFactory.newDelayEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE_DELAY_ALARM, MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).shipFinishTime(new Date()).build(), delayTime));

                        Long delayTimeTest = duccConfig.getHomeSelfTestAlarm().getLong("transportTestReportTimeInterval") * 60L;
                        log.info("MedicalPromiseEventSubscriber -> medPromiseStationReceiveMonitor 非快检发送延迟消息，到期未出报告报警 logId={}, medicalPromiseId={}, delayTime={}", logId, medicalPromise.getMedicalPromiseId(), delayTimeTest);
                        eventCoordinator.publishDelay(EventFactory.newDelayEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE_ALARM_DELAY, MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).stationReceiveTime(new Date()).build(), delayTimeTest));
                    } else {
                        Long delayTime = duccConfig.getHomeSelfTestAlarm().getLong("receiveLaboratoryTimeInterval") * 60L;
                        log.info("AngelWorkEventSubscriber -> shipFinishMonitor 发送延迟消息，超时未收样校验、报警 logId={}, medicalPromise={}, delayTime={}", logId, JSON.toJSONString(medicalPromise), delayTime);
                        eventCoordinator.publishDelay(EventFactory.newDelayEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE_DELAY_ALARM, MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).shipFinishTime(new Date()).build(), delayTime));

                        Long delayTimeTest = duccConfig.getDelayTime();
                        log.info("MedicalPromiseEventSubscriber -> medPromiseStationReceiveMonitor 发送延迟消息，到期未出报告报警 logId={}, medicalPromiseId={}, delayTime={}", logId, medicalPromise.getMedicalPromiseId(), delayTimeTest);
                        eventCoordinator.publishDelay(EventFactory.newDelayEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE_ALARM_DELAY, MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).stationReceiveTime(new Date()).build(), delayTimeTest));
                    }

                }finally {
                    MDC.remove("PFTID");
                }
            });
        });
    }

    /**
     * 实验室收样超时报警
     * @param event
     */
    public void medPromiseStationReceiveDelayAlarm(Event event) {
        log.info("AngelWorkEventSubscriber -> medPromiseStationReceiveDelayAlarm 实验室收样超时报警, event={}", JSON.toJSONString(event));
        MedicalPromiseEventBody eventBody = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        //如果是骑手上门场景，调用服务商同步采样信息
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(eventBody.getMedicalPromiseId()).build());
        if (Objects.isNull(medicalPromise)) {
            log.info("MedicalPromiseEventSubscriber -> medPromiseStationReceiveDelayAlarm medicalPromise null");
            return;
        }
        log.info("MedicalPromiseEventSubscriber -> medPromiseStationReceiveDelayAlarm medicalPromise={}", JSON.toJSONString(medicalPromise));
        ArrayList<Integer> statusList = Lists.newArrayList(MedicalPromiseStatusEnum.WAIT_COLLECTED.getStatus(), MedicalPromiseStatusEnum.COLLECTED.getStatus(), MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus());
        if (!statusList.contains(medicalPromise.getStatus())) {
            log.info("MedicalPromiseEventSubscriber -> medPromiseStationReceiveDelayAlarm 实验室已收样，不报警");
            return;
        }

        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
        JSONObject jsonObject = robotAlarmMap.get("超时未收样");
        if (Objects.isNull(jsonObject) || StringUtil.isBlank(jsonObject.getString("groupId"))) {
            log.info("MedicalPromiseEventSubscriber -> alarm disable robotAlarmMap={}", JsonUtil.toJSONString(robotAlarmMap));
            return;
        }
        String shipFinishTime = "";
        if (Objects.nonNull(eventBody.getShipFinishTime())) {
            shipFinishTime = DateUtil.formatDateTime(eventBody.getShipFinishTime());
        }

        if (ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getVerticalCode().equalsIgnoreCase(medicalPromise.getVerticalCode())) {
            JSONObject transportJsonObject = robotAlarmMap.get("非快检超时未收样");
            if (!Objects.isNull(transportJsonObject) && StringUtil.isNotBlank(jsonObject.getString("groupId"))) {
                jsonObject = transportJsonObject;
            }
            List<MedicalPromise> list = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(medicalPromise.getPromiseId()).build());
            dongDongRobotRpc.sendDongDongRobotMessage(String.format("实验室超时未收样，请及时关注！实验室ID：%s，实验室名称：%s，样本编码：%s；",
                            medicalPromise.getStationId(),
                            medicalPromise.getStationName(),
                            CollUtil.isEmpty(list) ? medicalPromise.getSpecimenCode() : Joiner.on("、").join(list.stream().map(MedicalPromise::getSpecimenCode).filter(StringUtils::isNotBlank).collect(Collectors.toList()))),
                    jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
        } else {
            dongDongRobotRpc.sendDongDongRobotMessage(String.format("样本已于%s前送到实验室，但仍未接收样本，请尽快排查解决（实验室名称：%s，已送达时间：%s，样本编码：%s，服务单号：%s，检测项目名称：%s）",
                duccConfig.getHomeSelfTestAlarm().getString("receiveLaboratoryTimeIntervalDesc"),
                medicalPromise.getStationName(), shipFinishTime,
                medicalPromise.getSpecimenCode(), medicalPromise.getMedicalPromiseId(), medicalPromise.getServiceItemName()),
                jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
        }
    }

    /**
     * 获取运单
     *
     * @param event
     * @return
     */
    public AngelShip getAngelShip(Event event){
        String shipId = event.getAggregateId();
        if(StringUtils.isBlank(shipId)){
            log.error("[AngelShipEventConsumer.handleShipFinish],运单完成状态无聚合根id信息!");
            throw new BusinessException(AngelPromiseBizErrorCode.EVENT_INFO_ERROR);
        }
        AngelShip angelShip = angelShipRepository.find(AngelShipIdentifier.builder().shipId(Long.valueOf(shipId)).build());
        if(Objects.isNull(angelShip)){
            log.error("[AngelShipEventConsumer.handleShipFinish],运单信息不存在!");
            throw new BusinessException(AngelPromiseBizErrorCode.SHIP_TASK_NOT_EXIST);
        }

        return angelShip;
    }


    /**
     * 提交服务记录事件处理
     */
    private void submitServiceRecords(Event event) {
        log.info("AngelWorkEventSubscriber->submitServiceRecords,event={}",JsonUtil.toJSONString(event));

        String serviceQualityControlRules = duccConfig.getServiceQualityControlRules();
        if (StringUtils.isBlank(serviceQualityControlRules)) {
            log.info("AngelWorkEventSubscriber -> submitServiceRecords ducc未配置");
            return;
        }
        JSONObject controlRules = JSON.parseObject(serviceQualityControlRules);
        if (controlRules == null || controlRules.isEmpty()) {
            log.info("AngelWorkEventSubscriber -> submitServiceRecords ducc未配置");
            return;
        }
        boolean switchFlag = false;
        try {
            switchFlag = Boolean.parseBoolean(controlRules.getString("submitServiceRecordsConsumerSwitch"));
        } catch (Exception e) {
            log.error("submitServiceRecordsConsumerSwitch 开关获取失败");
        }
        if (!Boolean.TRUE.equals(switchFlag)) {
            log.info("AngelWorkEventSubscriber -> submitServiceRecordsConsumerSwitch 开关关闭");
            return;
        }

        AngelWorkEventBody angelWorkEventBody = JsonUtil.parseObject(event.getBody(), AngelWorkEventBody.class);
        Long workId = angelWorkEventBody.getWorkId();
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(workId).build());
        if(Objects.isNull(angelWork)){
            log.info("AngelWorkEventSubscriber -> submitServiceRecords 未查到服务者工单信息, angelWork is null");
            return;
        }
        if(angelWork.getJdhAngelWorkExtVo() == null){
            log.info("AngelWorkEventSubscriber -> submitServiceRecords 未查到服务者工单扩展信息, angelWork={}", JSON.toJSONString(angelWork));
            return;
        }
        List<CompletableFuture<Map<String, ImgExifModel>>> futures = new ArrayList<>();
        Map<String, ImgExifModel> serviceRecordMaps = new HashMap<>();
        if (CollUtil.isNotEmpty(angelWork.getJdhAngelWorkExtVo().getServiceRecordFileIds())) {
            for (Long id: angelWork.getJdhAngelWorkExtVo().getServiceRecordFileIds()) {
               futures.add(CompletableFuture.supplyAsync(() -> {
                   Map<String, ImgExifModel> m = getFileAndExIf(id);
                   if (CollUtil.isNotEmpty(m)) {
                       serviceRecordMaps.putAll(m);
                   }
                   return m;
               }, executorPoolFactory.get(ThreadPoolConfigEnum.GET_IMG_EXIF)));
           }
        }
        Map<String, ImgExifModel> clothingRecordMaps = new HashMap<>();
        if (CollUtil.isNotEmpty(angelWork.getJdhAngelWorkExtVo().getClothingFileIds())) {
            for (Long id: angelWork.getJdhAngelWorkExtVo().getClothingFileIds()) {
                futures.add(CompletableFuture.supplyAsync(() -> {
                    Map<String, ImgExifModel> m = getFileAndExIf(id);
                    if (CollUtil.isNotEmpty(m)) {
                        clothingRecordMaps.putAll(m);
                    }
                    return m;
                }, executorPoolFactory.get(ThreadPoolConfigEnum.GET_IMG_EXIF)));
            }
        }
        Map<String, ImgExifModel> wasteRecordMaps = new HashMap<>();
        if (CollUtil.isNotEmpty(angelWork.getJdhAngelWorkExtVo().getWasteDestroyFileIds())) {
            for (Long id: angelWork.getJdhAngelWorkExtVo().getWasteDestroyFileIds()) {
                futures.add(CompletableFuture.supplyAsync(() -> {
                    Map<String, ImgExifModel> m = getFileAndExIf(id);
                    if (CollUtil.isNotEmpty(m)) {
                        wasteRecordMaps.putAll(m);
                    }
                    return m;
                }, executorPoolFactory.get(ThreadPoolConfigEnum.GET_IMG_EXIF)));
            }
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        AngelWork angelWorkSnapshot = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(workId).build());
        JdhAngelWorkExtVo jdhAngelWorkExtVo = angelWorkSnapshot.getJdhAngelWorkExtVo();
        if (jdhAngelWorkExtVo == null) {
            jdhAngelWorkExtVo = new JdhAngelWorkExtVo();
        }
        jdhAngelWorkExtVo.setServiceRecordImgExif(serviceRecordMaps);
        jdhAngelWorkExtVo.setClothingImgExif(clothingRecordMaps);
        jdhAngelWorkExtVo.setWasteDestroyImgExif(wasteRecordMaps);
        angelWorkSnapshot.setJdhAngelWorkExtVo(jdhAngelWorkExtVo);
        angelWorkSnapshot.setOldVersion(angelWorkSnapshot.getVersion());
        int count = angelWorkRepository.save(angelWorkSnapshot);
        if (count < 1) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR, "更新失败");
        }
        eventCoordinator.publish(EventFactory.newDefaultEvent(angelWorkSnapshot, AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_SERVICE_RECORDS_IMG_EXIF_CREATE, angelWorkEventBody));
    }

    /**
     * 查询文件
     * @param fileId
     * @return
     */
    private Map<String, ImgExifModel> getFileAndExIf(Long fileId) {
        GetFileUrlRequest getFileUrlRequest = new GetFileUrlRequest();
        getFileUrlRequest.setFileId(fileId);
        getFileUrlRequest.setIsPublic(false);
        String res = fileManageApplication.getFileUrl(getFileUrlRequest);
        if(StringUtils.isBlank(res)){
            log.error("[AngelWorkEventSubscriber.getFileAndExIf],返回结果为空!");
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        ImgExifModel imgExifModel = getImageExIfFromPromiseGoModel(res);
        Map<String, ImgExifModel> fileAndExIfMap = new HashMap<>();
        fileAndExIfMap.put(String.valueOf(fileId), imgExifModel);
        return fileAndExIfMap;
    }


    /**
     * 调用照片exif接口
     * @return str
     */
    private ImgExifModel getImageExIfFromPromiseGoModel(String url) {
        String requestUrl = domain.concat("/imagehash");
        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("content-type", "application/json");
        headMap.put("accept", "application/json");
        log.info("[AngelWorkEventSubscriber.getImageExIf],headMap={}", JSON.toJSONString(headMap));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("img_url", url);
        paramMap.put("hash_size", 8);
        log.info("[AngelWorkEventSubscriber.getImageExIf],paramMap={}", JSON.toJSONString(paramMap));
        String httpResponse = O2oHttpClient.postJson(requestUrl, headMap, JSON.toJSONString(paramMap));
        log.info("[AngelWorkEventSubscriber.getImageExIf],httpResponse={}", httpResponse);
        if (StringUtils.isBlank(httpResponse)) {
            log.info("[AngelWorkEventSubscriber.getImageExIf],返回结果为空!");
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        // 根据status判断是否执行成功
        Response<String> parseObject = JSON.parseObject(httpResponse, new TypeReference<Response<String>>() {
        });
        if (Objects.nonNull(parseObject) && Objects.equals(BaseResponse.SUCCESS, parseObject.getCode())) {
            ImgExifModel imgExifModel = new ImgExifModel();
            try {
                JSONObject jsonObject = JSON.parseObject(parseObject.getData());
                if (jsonObject == null) {
                    return null;
                }
                if (jsonObject.containsKey("exif_transform")) {
                    String exif_transform = jsonObject.getString("exif_transform");
                    JSONObject jsonObj = JSON.parseObject(exif_transform);
                    if (jsonObj != null && jsonObj.containsKey("GPSLongitude") && jsonObj.containsKey("GPSLatitude")) {
                        String gpsLongitude = jsonObj.getString("GPSLongitude");
                        String gpsLatitude = jsonObj.getString("GPSLatitude");
                        imgExifModel.setGpsLongitude(gpsLongitude);
                        imgExifModel.setGpsLatitude(gpsLatitude);
                    }
                }
                if (jsonObject.containsKey("exif")) {
                    String exif = jsonObject.getString("exif");
                    JSONObject jsonObj = JSON.parseObject(exif);
                    String dateTime = jsonObj.getString("DateTime");
                    if (StringUtils.isNotBlank(dateTime)) {
                        Date imgDate = DateUtil.parse(dateTime, "yyyy:MM:dd HH:mm:ss");
                        imgExifModel.setDateTime(imgDate);
                    }
                }
            } catch (Exception e) {
                log.error("[AngelWorkEventSubscriber.getImageExIf],获取照片信息异常!", e);
            }
            return imgExifModel;
        } else {
            log.error("[AngelWorkEventSubscriber.getImageExIf],获取照片信息异常!");
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
    }


    /**
     * 提交服务记录事件处理
     */
    private void serviceRecordsImgExifCreate(Event event) {
        log.info("AngelWorkEventSubscriber->serviceRecordsImgExifCreate,event={}", JsonUtil.toJSONString(event));

        String serviceQualityControlRules = duccConfig.getServiceQualityControlRules();
        if (StringUtils.isBlank(serviceQualityControlRules)) {
            log.info("AngelWorkEventSubscriber -> serviceRecordsImgExifCreate ducc未配置");
            return;
        }
        JSONObject controlRules = JSON.parseObject(serviceQualityControlRules);
        if (controlRules == null || controlRules.isEmpty()) {
            log.info("AngelWorkEventSubscriber -> serviceRecordsImgExifCreate ducc未配置");
            return;
        }
        boolean switchFlag = false;
        try {
            switchFlag = Boolean.parseBoolean(controlRules.getString("serviceRecordsImgExifConsumerSwitch"));
        } catch (Exception e) {
            log.error("serviceRecordsImgExifConsumerSwitch 开关获取失败");
        }
        if (!Boolean.TRUE.equals(switchFlag)) {
            log.info("AngelWorkEventSubscriber -> serviceRecordsImgExifConsumerSwitch 开关关闭");
            return;
        }

        AngelWorkEventBody angelWorkEventBody = JsonUtil.parseObject(event.getBody(), AngelWorkEventBody.class);
        Long workId = angelWorkEventBody.getWorkId();
        AngelWork angelWork = angelWorkRepository.find(AngelWorkIdentifier.builder().workId(workId).build());
        if(Objects.isNull(angelWork)){
            log.info("AngelWorkEventSubscriber -> serviceRecordsImgExifCreate 未查到服务者工单信息, angelWork is null");
            return;
        }
        if(angelWork.getJdhAngelWorkExtVo() == null){
            log.info("AngelWorkEventSubscriber -> serviceRecordsImgExifCreate 未查到服务者工单扩展信息, angelWork={}", JSON.toJSONString(angelWork));
            return;
        }
        GisPointBo senderPos = null;

        //查询工单历史
        AngelWorkHistoryDbQuery workHistoryDbQuery = AngelWorkHistoryDbQuery.builder().workId(workId).afterStatus(AngelWorkStatusEnum.SERVICED.getType()).build();
        List<AngelWorkHistory> workHistoryList = angelWorkHistoryRepository.findList(workHistoryDbQuery);
        Date workEndTime = null;
        if (CollUtil.isNotEmpty(workHistoryList)) {
            workEndTime = workHistoryList.get(0).getCreateTime();
        }

        PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(angelWork.getPromiseId()).build());
        if(promiseDto != null && promiseDto.getStore() != null && StringUtils.isNotBlank(promiseDto.getStore().getStoreAddr())){
            senderPos = addressRpc.getLngLatByAddress(promiseDto.getStore().getStoreAddr());
        }
        List<String> errorMsg = new ArrayList<>();
        if (CollUtil.isNotEmpty(angelWork.getJdhAngelWorkExtVo().getServiceRecordImgExif())) {
            List<String> imgError = new ArrayList<>();
            for (Map.Entry<String, ImgExifModel> entry : angelWork.getJdhAngelWorkExtVo().getServiceRecordImgExif().entrySet()) {
                Map<String, String> stringMap = checkImgIsValid(entry.getValue(), senderPos, workEndTime, serviceQualityControlRules);
                if (CollUtil.isNotEmpty(stringMap)) {
                    imgError.add(getFileInfoAndErrorMsg(Long.parseLong(entry.getKey()),stringMap));
                }
            }
            if (CollUtil.isNotEmpty(imgError)) {
                String serviceErrorMsg = "【服务记录】" + Joiner.on("。").join(imgError);
                errorMsg.add(serviceErrorMsg);
            }
        }
        if (CollUtil.isNotEmpty(angelWork.getJdhAngelWorkExtVo().getClothingImgExif())) {
            List<String> imgError = new ArrayList<>();
            for (Map.Entry<String, ImgExifModel> entry : angelWork.getJdhAngelWorkExtVo().getClothingImgExif().entrySet()) {
                Map<String, String> stringMap = checkImgIsValid(entry.getValue(), senderPos, workEndTime, serviceQualityControlRules);
                if (CollUtil.isNotEmpty(stringMap)) {
                    imgError.add(getFileInfoAndErrorMsg(Long.parseLong(entry.getKey()),stringMap));
                }
            }
            if (CollUtil.isNotEmpty(imgError)) {
                String serviceErrorMsg = "【着装照片】" + Joiner.on("。").join(imgError);
                errorMsg.add(serviceErrorMsg);
            }
        }
        if (CollUtil.isNotEmpty(angelWork.getJdhAngelWorkExtVo().getWasteDestroyImgExif())) {
            List<String> imgError = new ArrayList<>();
            for (Map.Entry<String, ImgExifModel> entry : angelWork.getJdhAngelWorkExtVo().getWasteDestroyImgExif().entrySet()) {
                Map<String, String> stringMap = checkImgIsValid(entry.getValue(), senderPos, workEndTime, serviceQualityControlRules);
                if (CollUtil.isNotEmpty(stringMap)) {
                    imgError.add(getFileInfoAndErrorMsg(Long.parseLong(entry.getKey()),stringMap));
                }
            }
            if (CollUtil.isNotEmpty(imgError)) {
                String serviceErrorMsg = "【耗材处理】" + Joiner.on("。").join(imgError);
                errorMsg.add(serviceErrorMsg);
            }
        }
        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
        JSONObject jsonObject = robotAlarmMap.get("服务质控预警");
        log.info("AngelWorkEventSubscriber -> serviceRecordsImgExifCreate errorMsg={}", JSON.toJSONString(errorMsg));
        if (jsonObject != null && CollUtil.isNotEmpty(errorMsg)) {
            String envName = "预发环境";
            if ("production".equalsIgnoreCase(env)) {
                envName = "生产环境";
            }
            dongDongRobotRpc.sendDongDongRobotMessage(String.format("【%s】订单%s，履约单%s，工单%s，上传服务照片疑似违规，照片信息：%s",envName,promiseDto != null ? promiseDto.getSourceVoucherId() : "", angelWork.getPromiseId(), angelWork.getWorkId(), Joiner.on("；").join(errorMsg)),
                    jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
        }
    }

    /**
     * 获取文件信息
     * @param fileId
     * @param stringMap
     * @return
     */
    private String getFileInfoAndErrorMsg(Long fileId, Map<String, String> stringMap) {
        GetFileUrlRequest getFileUrlRequest = new GetFileUrlRequest();
        getFileUrlRequest.setFileId(fileId);
        getFileUrlRequest.setIsPublic(true);
        LocalDateTime expireTime = LocalDateTime.now().plusHours(24);
        Date expire = TimeUtils.localDateTimeToDate(expireTime);
        getFileUrlRequest.setExpiration(expire);
        String res = fileManageApplication.getFileUrl(getFileUrlRequest);
        //生成短链
        String shortUrl = shortUrlRpc.generateShortUrl(res);
        return String.format("照片id：%s，照片地址（24小时失效）：%s，%s", fileId, shortUrl, Joiner.on("、").join(stringMap.values()));
    }

    /**
     *
     */
    private Map<String, String> checkImgIsValid(ImgExifModel imgExifModel, GisPointBo senderPos, Date workEndTime, String serviceQualityControlRules) {
        log.info("AngelWorkEventSubscriber -> checkImgIsValid imgExifModel={} senderPos={} workEndTime={} serviceQualityControlRules={}", JSON.toJSONString(imgExifModel),JSON.toJSONString(senderPos),workEndTime, serviceQualityControlRules);
        if (imgExifModel == null) {
            return null;
        }
        JSONObject controlRules = JSON.parseObject(serviceQualityControlRules);
        if (controlRules == null || controlRules.isEmpty()) {
            return null;
        }
        Integer serviceImgDistance = controlRules.getInteger("serviceImgDistance");
        Integer serviceImgTimeRange = controlRules.getInteger("serviceImgTimeRange");
        String serviceImgDistanceDesc = controlRules.getString("serviceImgDistanceDesc");
        String serviceImgTimeRangeDesc = controlRules.getString("serviceImgTimeRangeDesc");
        String serviceImgSubmitTimeRangeDesc = controlRules.getString("serviceImgSubmitTimeRangeDesc");
        String serviceImgTimeAfterDesc = controlRules.getString("serviceImgTimeAfterDesc");

        Map<String, String> map = Maps.newHashMap();
        if (StringUtils.isNotBlank(imgExifModel.getGpsLongitude()) && StringUtils.isNotBlank(imgExifModel.getGpsLatitude()) && senderPos != null) {
            double distance = GeoDistanceUtil.calculateDistance(Double.parseDouble(imgExifModel.getGpsLatitude()),Double.parseDouble(imgExifModel.getGpsLongitude()),senderPos.getLatitude().doubleValue(),senderPos.getLongitude().doubleValue());
            if (distance > serviceImgDistance) {
                map.put("distance",  String.format("%s(照片GPS经纬度：[%s, %s]，服务位置经纬度：[%s, %s]，误差距离%s米)", serviceImgDistanceDesc, imgExifModel.getGpsLongitude(), imgExifModel.getGpsLatitude(), senderPos.getLongitude(), senderPos.getLatitude(), distance));
            }
        }
        if (imgExifModel.getDateTime() != null) {
            if (workEndTime != null) {
                if (imgExifModel.getDateTime().after(workEndTime)) {
                    map.put("times", String.format("%s(照片拍摄时间：%s，服务完成时间：%s)", serviceImgTimeAfterDesc, TimeUtils.dateTimeToStr(imgExifModel.getDateTime()), TimeUtils.dateTimeToStr(workEndTime)));
                } else {
                    long times = DateUtil.between(imgExifModel.getDateTime(), workEndTime, DateUnit.SECOND);
                    if (times > serviceImgTimeRange) {
                        map.put("times", String.format("%s(照片拍摄时间：%s，服务完成时间：%s)", serviceImgTimeRangeDesc, TimeUtils.dateTimeToStr(imgExifModel.getDateTime()), TimeUtils.dateTimeToStr(workEndTime)));
                    }
                }
            } else {
                Date now = new Date();
                long times = DateUtil.between(imgExifModel.getDateTime(), now, DateUnit.SECOND);
                if (times > serviceImgTimeRange) {
                    map.put("times",  String.format("%s(照片拍摄时间：%s，上传服务记录时间：%s)", serviceImgSubmitTimeRangeDesc,TimeUtils.dateTimeToStr(imgExifModel.getDateTime()), TimeUtils.dateTimeToStr(now)));
                }
            }
        }
        return map;
    }
}
