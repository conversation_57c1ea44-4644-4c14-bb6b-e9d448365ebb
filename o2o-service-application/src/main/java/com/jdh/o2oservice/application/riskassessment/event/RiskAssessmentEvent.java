package com.jdh.o2oservice.application.riskassessment.event;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.product.service.ProductServiceIndicatorApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.riskassessment.context.RiskAssEventBody;
import com.jdh.o2oservice.application.riskassessment.service.RiskAssessmentApplication;
import com.jdh.o2oservice.application.riskassessment.service.RiskInterceptApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.event.*;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelServiceRecordEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.RiskAssEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelWorkEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessment;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.RiskAssessmentRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentQuery;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.event.PromiseSubmitEventBody;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.support.riskintercept.enums.RiskInterceptCodeEnum;
import com.jdh.o2oservice.core.domain.support.riskintercept.enums.RiskPassStatusEnum;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.riskassessment.cmd.InvalidPatientRiskCmd;
import com.jdh.o2oservice.export.riskassessment.cmd.ModifyRiskInterceptCmd;
import com.jdh.o2oservice.export.riskassessment.cmd.RiskInterceptCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/27
 */
@Component
@Slf4j
public class RiskAssessmentEvent {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    /**
     * 注入的Ducc配置类，用于获取相关配置信息。
     */
    @Autowired
    private DuccConfig duccConfig;

    /**
     * 用于执行风险评估并派发 PromiseDispatchCmd 的 PromiseApplication 实例。
     */
    @Autowired
    private PromiseApplication promiseApplication;

    /**
     * 检测单仓储层
     */
    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;


    /**
     * 风险评估单应用层
     */
    @Autowired
    private RiskAssessmentApplication riskAssessmentApplication;

    @Autowired
    private RiskAssessmentRepository riskAssessmentRepository;

    /**
     * 自动注入的产品服务指标应用程序，用于获取和更新产品服务指标信息。
     */
    @Autowired
    private ProductServiceIndicatorApplication productServiceIndicatorApplication;

    /**
     * 自动注入的产品服务指标应用程序，用于获取和更新产品服务指标信息。
     */
    @Autowired
    private RiskInterceptApplication riskInterceptApplication;

  /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    @Autowired
    private PromiseRepository promiseRepository;

    @Autowired
    private VerticalBusinessRepository businessRepository;



    /**
     * 注册consumer
     */
    @PostConstruct
    public void registerEventConsumer() {

        /** 拦截履约单创建评估单 */
        eventConsumerRegister.register(Arrays.asList(PromiseEventTypeEnum.INTERCEPT_PROMISE_SUBMIT, PromiseEventTypeEnum.INTERCEPT_PROMISE_AUTO_SUBMIT),
                WrapperEventConsumer.newInstance(DomainEnum.PROMISE, "riskAssessment", this::riskIntercept, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.fixRetryInstance(3,60000)));

        /** 人维度退款，做废掉评估单 */
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_PATIENT_INVALID,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "riskAssessmentInvalid", this::riskAssessmentInvalidPatient, Boolean.TRUE, Boolean.FALSE, EventConsumerRetryTemplate.fixRetryInstance(3,5000)));

        /** 人维度风险评估单通过 */
        eventConsumerRegister.register(RiskAssEventTypeEnum.RISK_ASS_PATIENT_PASS,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "riskAssPatientPassDeal", this::riskAssPassDeal, Boolean.TRUE, Boolean.FALSE));

        /** 人维度风险评估单驳回 */
        eventConsumerRegister.register(RiskAssEventTypeEnum.RISK_ASS_PATIENT_REFUSE,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "riskAssPatientRefuseDeal", this::riskAssRefuseDeal, Boolean.TRUE, Boolean.FALSE));

        //=======>>>>>> 服务者工单完成 监听消息 -> 触发检测单拉完成
        eventConsumerRegister.register(AngelServiceRecordEventTypeEnum.MIDDLE_DANGER_LEVEL_PRERECEIVEASSESSMENT_CREATED, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "preReceiveAssessment", this::serviceFinishMidRisk, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));

        //=======>>>>>> 服务者工单完成 监听消息 -> 触发检测单拉完成
        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_FINISH_SERVED, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "serviceFinishReturnVisit", this::serviceVisitReturn, Boolean.TRUE, Boolean.TRUE, EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));
    }

    /**
     * 人维度风险评估单不通过
     *
     * @param event
     */
    private void riskAssRefuseDeal(Event event) {
        log.info("RiskAssessmentEvent -> riskAssRefuseDeal, 接收到评估单评估不通过.event={}", JSON.toJSONString(event));
        RiskAssEventBody riskAssEventBody = JSON.parseObject(event.getBody(), RiskAssEventBody.class);

        //更新风控记录单状态
        ModifyRiskInterceptCmd modifyRiskInterceptCmd = new ModifyRiskInterceptCmd();
        modifyRiskInterceptCmd.setRiskPassStatus(RiskPassStatusEnum.NO_PASS.getStatus());
        modifyRiskInterceptCmd.setPromiseId(riskAssEventBody.getPromiseId());
        modifyRiskInterceptCmd.setPromisePatientIds(Lists.newArrayList(riskAssEventBody.getPromisePatientId()));
        modifyRiskInterceptCmd.setRiskCode(RiskInterceptCodeEnum.RISK_HIGH_ITEM_INTERCEPT.getRiskCode());

        riskInterceptApplication.modifyRiskInterceptStatus(modifyRiskInterceptCmd);
    }

    /**
     * 人维度退款作废评估单
     *
     * @param event
     */
    private void riskAssessmentInvalidPatient(Event event) {
        log.info("RiskAssessmentEvent -> riskAssessmentInvalidPatient, 收到事件.event={}", JSON.toJSONString(event));
        MedicalPromiseEventBody medicalPromiseEventBody = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        AssertUtils.nonNull(medicalPromiseEventBody.getPromisePatientId(), "患者id不能为空");
        Long promiseId = medicalPromiseEventBody.getPromiseId();
        if(Objects.isNull(promiseId)) {
            log.error("RiskAssessmentEvent -> riskAssessmentInvalid,消息内缺失履约单标识!medicalPromiseEventBody={}", JSON.toJSONString(medicalPromiseEventBody));
            return;
        }

        InvalidPatientRiskCmd invalidPatientRiskCmd = new InvalidPatientRiskCmd();
        invalidPatientRiskCmd.setAggregateCode(event.getAggregateCode());
        invalidPatientRiskCmd.setDomainCode(event.getDomainCode());
        invalidPatientRiskCmd.setPromiseId(medicalPromiseEventBody.getPromiseId());
        invalidPatientRiskCmd.setPromisePatientIds(Lists.newArrayList(medicalPromiseEventBody.getPromisePatientId()));
        riskInterceptApplication.invalidPatientRiskAssessment(invalidPatientRiskCmd);
    }

    /**
     * 人维度风险评估单不通过
     *
     * @param event
     */
    private void riskAssPassDeal(Event event) {
        log.info("RiskAssessmentEvent -> riskAssPassDeal, 接收到评估单评估通过.event={}", JSON.toJSONString(event));
        RiskAssEventBody riskAssEventBody = JSON.parseObject(event.getBody(), RiskAssEventBody.class);

        //更新风控记录单状态
        ModifyRiskInterceptCmd modifyRiskInterceptCmd = new ModifyRiskInterceptCmd();
        modifyRiskInterceptCmd.setRiskPassStatus(RiskPassStatusEnum.PASS.getStatus());
        modifyRiskInterceptCmd.setPromiseId(riskAssEventBody.getPromiseId());
        modifyRiskInterceptCmd.setPromisePatientIds(Lists.newArrayList(riskAssEventBody.getPromisePatientId()));
        modifyRiskInterceptCmd.setRiskCode(RiskInterceptCodeEnum.RISK_HIGH_ITEM_INTERCEPT.getRiskCode());

        riskInterceptApplication.modifyRiskInterceptStatus(modifyRiskInterceptCmd);
    }

    /**
     * 风险链接
     *
     * @param event
     */
    private void riskIntercept(Event event) {
        if(duccConfig.getGrayEnvSwitch()) {
            log.info("RiskAssessmentEvent -> riskIntercept, 打开降级开关，不拦截高风险,直接通过");
            if(PromiseAggregateEnum.PROMISE.getDomainCode().getCode().equals(event.getDomainCode())
                    && PromiseAggregateEnum.PROMISE.getCode().equals(event.getAggregateCode())){
                JdhPromise jdhPromise = promiseRepository.find(new JdhPromiseIdentifier(Long.valueOf(event.getAggregateId())));
                String realEventCode = event.getEventCode().replaceAll("intercept_", "");
                PromiseEventTypeEnum eventTypeEnum = PromiseEventTypeEnum.getByCode(realEventCode);
                PromiseSubmitEventBody submitBody = JSON.parseObject(event.getBody(), PromiseSubmitEventBody.class);
                log.info("RiskAssessmentEvent -> riskIntercept, submitBody={}, eventTypeEnum={}", JSON.toJSONString(submitBody), eventTypeEnum);
                eventCoordinator.publish(EventFactory.newDefaultEvent(jdhPromise, eventTypeEnum, submitBody));
            }else {
                log.error("RiskAssessmentEvent -> riskIntercept,未知的拦截事件,重点关注.record={}", JSON.toJSONString(event));
            }
            return;
        }

        log.info("RiskAssessmentEvent -> riskIntercept, event={}", JSON.toJSONString(event));
        //查询履约单信息
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(Long.valueOf(event.getAggregateId()));
        PromiseDto promiseDto = promiseApplication.findPromiseByPromiseId(promiseIdRequest);
        if(Objects.isNull(promiseDto)) {
            log.error("RiskAssessmentEvent -> riskIntercept, 没有查询到履约单信息");
            return;
        }
        List<PromisePatientDto> patients = promiseDto.getPatients();
        if(CollectionUtils.isEmpty(patients)) {
            log.error("RiskAssessmentEvent -> riskIntercept, 没有患者信息");
            return;
        }
        List<Long> promisePatientIds = patients.stream().map(PromisePatientDto::getPromisePatientId).collect(Collectors.toList());

        RiskInterceptCmd riskInterceptCmd = new RiskInterceptCmd();
        riskInterceptCmd.setDomainCode(event.getDomainCode());
        riskInterceptCmd.setAggregateCode(event.getAggregateCode());
        riskInterceptCmd.setOrderId(promiseDto.getSourceVoucherId());
        riskInterceptCmd.setPromiseId(Long.valueOf(event.getAggregateId()));
        riskInterceptCmd.setPromisePatientIds(promisePatientIds);
        riskInterceptCmd.setInterceptEventCode(event.getEventCode().replaceAll("intercept_", ""));
        riskInterceptCmd.setRiskCode(RiskInterceptCodeEnum.RISK_HIGH_ITEM_INTERCEPT.getRiskCode());
        riskInterceptCmd.setEventBody(event.getBody());
        riskInterceptApplication.doRiskIntercept(riskInterceptCmd);
    }



    /**
     * 服务完成后，判断是否是中风险，如果是中风险，需要补偿中风险问题到评估单
     */
    @LogAndAlarm
    private void serviceFinishMidRisk(Event event) {
        log.info("serviceFinishMidRisk->event={}", JSON.toJSONString(event));
        riskAssessmentApplication.createMidRiskAssByRecord(Long.valueOf(event.getAggregateId()));
    }



    private void  serviceVisitReturn(Event event) {

        AngelWorkEventBody angelWorkEventBody = JSON.parseObject(event.getBody(), AngelWorkEventBody.class);
        if (Objects.isNull(angelWorkEventBody) || Objects.isNull(angelWorkEventBody.getPromiseId())) {
            log.info("RiskAssessmentEvent->serviceFinishMidRisk, angelWorkEventBody is null,{}", JsonUtil.toJSONString(angelWorkEventBody));
            return;
        }

        RiskAssessment riskAssessment = riskAssessmentRepository.queryRiskAssessment(RiskAssessmentQuery.builder().promiseId(angelWorkEventBody.getPromiseId()).build());
        if(Objects.isNull(riskAssessment)) {
            log.info("RiskAssessmentEvent -> serviceFinishMidRisk, 没有查询到风险评估单信息");
             return;
        }

        if (Objects.equals(CommonConstant.THREE,riskAssessment.getRiskLevel())
                && Objects.equals(CommonConstant.ZERO,riskAssessment.getReturnVisitStatus())
        ){
            RiskAssessment riskAssessmentCmd = new RiskAssessment();
            riskAssessmentCmd.setPromiseId(riskAssessment.getPromiseId());
            riskAssessmentCmd.setId(riskAssessment.getId());
            riskAssessmentCmd.setRiskAssessmentId(riskAssessment.getRiskAssessmentId());
            riskAssessmentCmd.setReturnVisitStatus(CommonConstant.ONE);
            riskAssessmentCmd.setServiceFinishTime(new Date());
            riskAssessmentRepository.save(riskAssessmentCmd);
        }


    }



}
