package com.jdh.o2oservice.application.dispatch.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.dispatch.convert.DispatchTeamApplicationConverter;
import com.jdh.o2oservice.application.dispatch.service.DispatchTeamApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.cache.RedisUtil;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.enums.AngelAuditProcessStatusEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelSkillDict;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelSkillDictRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.AngelSkillDictRepQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchTeam;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchTeamAngelRel;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchTeamIdentifier;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchTeamSkillRel;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchTeamRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchTeamRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.export.dispatch.cmd.*;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchTeamAngelRelDTO;
import com.jdh.o2oservice.export.dispatch.dto.JdhDispatchTeamDTO;
import com.jdh.o2oservice.export.dispatch.dto.SaveDispatchTeamDTO;
import com.jdh.o2oservice.export.dispatch.query.DispatchTeamListRequest;
import com.jdh.o2oservice.export.dispatch.query.DispatchTeamRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchTeamApplicationImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/7/17 13:05
 **/
@Service
@Slf4j
public class DispatchTeamApplicationImpl implements DispatchTeamApplication {

    /**
     * dispatchTeamRepository
     */
    @Resource
    private DispatchTeamRepository dispatchTeamRepository;

    /**
     *
     */
    @Resource
    private AngelRepository angelRepository;

    /**
     *
     */
    @Resource
    private AngelSkillDictRepository angelSkillDictRepository;

    /**
     *
     */
    @Resource
    private RedisUtil redisUtil;

    /**
     * 查询派单小队列表
     * @param request 查询
     * @return
     */
    @Override
    @LogAndAlarm
    public PageDto<JdhDispatchTeamDTO> queryDispatchTeamPage(DispatchTeamListRequest request) {
        DispatchTeamRepQuery teamRepQuery = DispatchTeamApplicationConverter.INSTANCE.request2DispatchTeamRepQuery(request);
        //需要查询小队技能
        teamRepQuery.setQueryTeamSkill(true);
        Page<JdhDispatchTeam> dispatchTeamPage = dispatchTeamRepository.findDispatchTeamPage(teamRepQuery);
        List<JdhDispatchTeamDTO> list = DispatchTeamApplicationConverter.INSTANCE.dispatchTeam2Dto(dispatchTeamPage.getRecords());

        PageDto<JdhDispatchTeamDTO> dto = new PageDto<>();
        dto.setTotalPage(dispatchTeamPage.getPages());
        dto.setPageNum(dispatchTeamPage.getCurrent());
        dto.setPageSize(dispatchTeamPage.getSize());
        dto.setTotalCount(dispatchTeamPage.getTotal());
        dto.setList(list);
        return dto;
    }

    /**
     * 查询派单小队明细
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public JdhDispatchTeamDTO queryDispatchTeamDetail(DispatchTeamRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getDispatchTeamId())) {
            return null;
        }
        JdhDispatchTeam dispatchTeam = dispatchTeamRepository.find(JdhDispatchTeamIdentifier.builder().dispatchTeamId(request.getDispatchTeamId()).build());
        JdhDispatchTeamDTO jdhDispatchTeamDTO = DispatchTeamApplicationConverter.INSTANCE.dispatchTeam2Dto(dispatchTeam);
        //有关联的护士，填充护士姓名
        if (CollectionUtils.isNotEmpty(jdhDispatchTeamDTO.getTeamAngelRelList())) {
            Map<Long, JdhDispatchTeamAngelRelDTO> collect = jdhDispatchTeamDTO.getTeamAngelRelList().stream().collect(Collectors.toMap(JdhDispatchTeamAngelRelDTO::getAngelId, teamAngelRel -> teamAngelRel, (t, t2) -> t2));
            List<JdhAngel> list = angelRepository.findList(JdhAngelRepQuery.builder().angelIdList(Lists.newArrayList(collect.keySet())).build());
            list.forEach(angel -> {
                JdhDispatchTeamAngelRelDTO jdhDispatchTeamAngelRelDTO = collect.get(angel.getAngelId());
                if (Objects.nonNull(jdhDispatchTeamAngelRelDTO)) {
                    jdhDispatchTeamAngelRelDTO.setAngelName(new UserName(angel.getAngelName()).mask());
                }
            });
        }
        return jdhDispatchTeamDTO;
    }

    /**
     * 保存派单小队
     *
     * @param saveDispatchTeamCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean saveDispatchTeam(SaveDispatchTeamCmd saveDispatchTeamCmd) {
        JdhDispatchTeam dispatchTeam = DispatchTeamApplicationConverter.INSTANCE.saveCmd2DispatchTeam(saveDispatchTeamCmd);

        Long dispatchTeamId = dispatchTeam.getDispatchTeamId();
        boolean created = Objects.isNull(dispatchTeamId);
        //新增
        if (created) {
            dispatchTeam.setDispatchTeamId(SpringUtil.getBean(GenerateIdFactory.class).getId());
            dispatchTeam.setCreateUser(saveDispatchTeamCmd.getOperator());
            return dispatchTeamRepository.save(dispatchTeam) > 0;
        }
        //更新
        //分布式锁
        String lockRedisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.DISPATCH_TEAM_UPDATE_LOCK_KEY, dispatchTeamId);
        try {
            boolean lock = redisUtil.tryLock(lockRedisKey, RedisKeyEnum.DISPATCH_TEAM_UPDATE_LOCK_KEY.getExpireTime(), RedisKeyEnum.DISPATCH_TEAM_UPDATE_LOCK_KEY.getExpireTimeUnit());
            if (!lock) {
                throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
            }
            JdhDispatchTeam jdhDispatchTeam = dispatchTeamRepository.find(JdhDispatchTeamIdentifier.builder().dispatchTeamId(dispatchTeamId).build());
            if (Objects.isNull(jdhDispatchTeam)) {
                dispatchTeam.setCreateUser(saveDispatchTeamCmd.getOperator());
                return dispatchTeamRepository.save(dispatchTeam) > 0;
            }
            dispatchTeam.setId(jdhDispatchTeam.getId());
            dispatchTeam.setTeamAngelRelList(jdhDispatchTeam.getTeamAngelRelList());
            dispatchTeam.setTeamSkillRelList(jdhDispatchTeam.getTeamSkillRelList());
            dispatchTeam.setUpdateUser(saveDispatchTeamCmd.getOperator());
            return dispatchTeamRepository.save(dispatchTeam) > 0;
        } catch (Exception e) {
            log.error("DispatchTeamApplicationImpl -> saveDispatchTeam, saveDispatchTeamCmd={}", JsonUtil.toJSONString(saveDispatchTeamCmd), e);
        } finally {
            redisUtil.unLock(lockRedisKey);
        }
        return false;
    }

    /**
     * 保存派单小队技能
     *
     * @param saveDispatchTeamSkillCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public SaveDispatchTeamDTO saveDispatchTeamSkill(SaveDispatchTeamSkillCmd saveDispatchTeamSkillCmd) {
        Long dispatchTeamId = saveDispatchTeamSkillCmd.getDispatchTeamId();
        //派单小队新增技能列表
        List<String> teamSkillList = Arrays.stream(saveDispatchTeamSkillCmd.getTeamSkillStr().split("\n")).map(String::trim).collect(Collectors.toList());
        //更新
        //分布式锁
        String lockRedisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.DISPATCH_TEAM_UPDATE_LOCK_KEY, dispatchTeamId);
        try {
            boolean lock = redisUtil.tryLock(lockRedisKey, RedisKeyEnum.DISPATCH_TEAM_UPDATE_LOCK_KEY.getExpireTime(), RedisKeyEnum.DISPATCH_TEAM_UPDATE_LOCK_KEY.getExpireTimeUnit());
            if (!lock) {
                throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
            }
            JdhDispatchTeam jdhDispatchTeam = dispatchTeamRepository.find(JdhDispatchTeamIdentifier.builder().dispatchTeamId(dispatchTeamId).build());
            log.info("DispatchTeamApplicationImpl -> saveDispatchTeamSkill, jdhDispatchTeam={}", JsonUtil.toJSONString(jdhDispatchTeam));
            if (Objects.isNull(jdhDispatchTeam)) {
                return SaveDispatchTeamDTO.builder().successNum(0).failNum(teamSkillList.size()).build();
            }
            //查询技能基础信息
            List<JdhAngelSkillDict> list = angelSkillDictRepository.findList(AngelSkillDictRepQuery.builder().angelSkillCodeList(teamSkillList).build());

            List<JdhDispatchTeamSkillRel> newSkills = getNewSkills(jdhDispatchTeam, list, saveDispatchTeamSkillCmd.getOperator());
            log.info("DispatchTeamApplicationImpl -> saveDispatchTeamSkill, newSkills={}", JsonUtil.toJSONString(newSkills));
            List<JdhDispatchTeamSkillRel> teamSkillRelList = jdhDispatchTeam.getTeamSkillRelList();

            if (!newSkills.isEmpty()) {
                teamSkillRelList.addAll(newSkills);
                jdhDispatchTeam.setTeamSkillRelList(teamSkillRelList);
                dispatchTeamRepository.save(jdhDispatchTeam);
            }
            return SaveDispatchTeamDTO.builder().successNum(newSkills.size()).failNum(0).build();
        } catch (Exception e) {
            log.error("DispatchTeamApplicationImpl -> saveDispatchTeamSkill, saveDispatchTeamSkillCmd={}", JsonUtil.toJSONString(saveDispatchTeamSkillCmd), e);
        } finally {
            redisUtil.unLock(lockRedisKey);
        }
        return SaveDispatchTeamDTO.builder().successNum(0).failNum(teamSkillList.size()).build();
    }

    /**
     *
     * @param jdhDispatchTeam
     * @param skillDictList
     * @param operator
     * @return
     */
    private List<JdhDispatchTeamSkillRel> getNewSkills(JdhDispatchTeam jdhDispatchTeam,
                                                       List<JdhAngelSkillDict> skillDictList,
                                                       String operator) {
        Set<String> existingSkillCodes = CollectionUtils.isNotEmpty(jdhDispatchTeam.getTeamSkillRelList()) ? jdhDispatchTeam.getTeamSkillRelList().stream()
                .map(JdhDispatchTeamSkillRel::getAngelSkillCode)
                .collect(Collectors.toSet()) : new HashSet<>();

        return skillDictList.stream()
                .filter(skill -> !existingSkillCodes.contains(skill.getAngelSkillCode()))
                .map(skill -> createSkillRel(jdhDispatchTeam.getDispatchTeamId(), skill, operator))
                .collect(Collectors.toList());
    }

    /**
     *
     * @param dispatchTeamId
     * @param skillDict
     * @param operator
     * @return
     */
    private JdhDispatchTeamSkillRel createSkillRel(Long dispatchTeamId, JdhAngelSkillDict skillDict, String operator) {
        return JdhDispatchTeamSkillRel.builder()
                .dispatchTeamId(dispatchTeamId)
                .angelSkillCode(skillDict.getAngelSkillCode())
                .angelSkillName(skillDict.getAngelSkillCode())
                .itemType(skillDict.getItemType())
                .serviceGroupId(skillDict.getServiceGroupId())
                .createUser(operator)
                .updateUser(operator)
                .build();
    }

    /**
     * 保存派单小队服务者
     *
     * @param saveDispatchTeamAngelCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public SaveDispatchTeamDTO saveDispatchTeamAngel(SaveDispatchTeamAngelCmd saveDispatchTeamAngelCmd) {
        Long dispatchTeamId = saveDispatchTeamAngelCmd.getDispatchTeamId();
        //派单小队新增技能列表
        List<String> teamAngelList = Arrays.stream(saveDispatchTeamAngelCmd.getTeamAngelStr().split("\n")).map(String::trim).collect(Collectors.toList());
        //更新
        //分布式锁
        String lockRedisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.DISPATCH_TEAM_UPDATE_LOCK_KEY, dispatchTeamId);
        try {
            boolean lock = redisUtil.tryLock(lockRedisKey, RedisKeyEnum.DISPATCH_TEAM_UPDATE_LOCK_KEY.getExpireTime(), RedisKeyEnum.DISPATCH_TEAM_UPDATE_LOCK_KEY.getExpireTimeUnit());
            if (!lock) {
                throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
            }
            JdhDispatchTeam jdhDispatchTeam = dispatchTeamRepository.find(JdhDispatchTeamIdentifier.builder().dispatchTeamId(dispatchTeamId).build());
            log.info("DispatchTeamApplicationImpl -> saveDispatchTeamAngel, jdhDispatchTeam={}", JsonUtil.toJSONString(jdhDispatchTeam));
            if (Objects.isNull(jdhDispatchTeam)) {
                return SaveDispatchTeamDTO.builder().successNum(0).failNum(teamAngelList.size()).build();
            }
            //查询护士基础信息
            List<Long> angelIdList = teamAngelList.stream().map(Long::valueOf).collect(Collectors.toList());

            List<JdhAngel> list = angelRepository.findList(JdhAngelRepQuery.builder().angelIdList(angelIdList).build());
            if (CollectionUtils.isEmpty(list)) {
                return SaveDispatchTeamDTO.builder().successNum(0).failNum(teamAngelList.size()).build();
            }

            List<JdhDispatchTeamAngelRel> newAngels = getNewAngels(jdhDispatchTeam, list, saveDispatchTeamAngelCmd.getOperator());
            log.info("DispatchTeamApplicationImpl -> saveDispatchTeamSkill, newAngels={}", JsonUtil.toJSONString(newAngels));
            List<JdhDispatchTeamAngelRel> teamAngelRelList = jdhDispatchTeam.getTeamAngelRelList();

            if (!newAngels.isEmpty()) {
                teamAngelRelList.addAll(newAngels);
                jdhDispatchTeam.setTeamAngelRelList(teamAngelRelList);
                dispatchTeamRepository.save(jdhDispatchTeam);
            }
            return SaveDispatchTeamDTO.builder().successNum(newAngels.size()).failNum(teamAngelList.size() - newAngels.size()).build();
        } catch (Exception e) {
            log.error("DispatchTeamApplicationImpl -> saveDispatchTeamAngel, saveDispatchTeamAngelCmd={}", JsonUtil.toJSONString(saveDispatchTeamAngelCmd), e);
        } finally {
            redisUtil.unLock(lockRedisKey);
        }
        return SaveDispatchTeamDTO.builder().successNum(0).failNum(teamAngelList.size()).build();
    }

    /**
     * 删除派单小队技能
     *
     * @param deleteDispatchTeamSkillCmd
     * @return
     */
    @Override
    public Boolean deleteDispatchTeamSkill(DeleteDispatchTeamSkillCmd deleteDispatchTeamSkillCmd) {
        if (Objects.isNull(deleteDispatchTeamSkillCmd) || Objects.isNull(deleteDispatchTeamSkillCmd.getDispatchTeamId()) || StringUtils.isBlank(deleteDispatchTeamSkillCmd.getAngelSkillCode())) {
            return false;
        }
        int count = dispatchTeamRepository.deleteDispatchTeamSkillRel(JdhDispatchTeamSkillRel.builder().dispatchTeamId(deleteDispatchTeamSkillCmd.getDispatchTeamId()).angelSkillCode(deleteDispatchTeamSkillCmd.getAngelSkillCode()).updateUser(deleteDispatchTeamSkillCmd.getOperator()).build());
        return count > 0;
    }

    /**
     * 删除派单小队服务者
     *
     * @param deleteDispatchTeamAngelCmd
     * @return
     */
    @Override
    public Boolean deleteDispatchTeamAngel(DeleteDispatchTeamAngelCmd deleteDispatchTeamAngelCmd) {
        if (Objects.isNull(deleteDispatchTeamAngelCmd) || Objects.isNull(deleteDispatchTeamAngelCmd.getDispatchTeamId()) || Objects.isNull(deleteDispatchTeamAngelCmd.getAngelId())) {
            return false;
        }
        int count = dispatchTeamRepository.deleteDispatchTeamAngelRel(JdhDispatchTeamAngelRel.builder().dispatchTeamId(deleteDispatchTeamAngelCmd.getDispatchTeamId()).angelId(deleteDispatchTeamAngelCmd.getAngelId()).updateUser(deleteDispatchTeamAngelCmd.getOperator()).build());
        return count > 0;
    }

    /**
     *
     * @param jdhDispatchTeam
     * @param jdhAngelList
     * @param operator
     * @return
     */
    private List<JdhDispatchTeamAngelRel> getNewAngels(JdhDispatchTeam jdhDispatchTeam,
                                                       List<JdhAngel> jdhAngelList,
                                                       String operator) {
        Set<Long> existingAngelIds = CollectionUtils.isNotEmpty(jdhDispatchTeam.getTeamAngelRelList()) ? jdhDispatchTeam.getTeamAngelRelList().stream()
                .map(JdhDispatchTeamAngelRel::getAngelId)
                .collect(Collectors.toSet()) : new HashSet<>();

        return jdhAngelList.stream()
                .filter(angel -> !existingAngelIds.contains(angel.getAngelId()) && Objects.equals(AngelAuditProcessStatusEnum.AUDIT_PASS.getCode(), angel.getAuditProcessStatus()))
                .map(angel -> JdhDispatchTeamAngelRel.builder().build())
                .collect(Collectors.toList());
    }
}