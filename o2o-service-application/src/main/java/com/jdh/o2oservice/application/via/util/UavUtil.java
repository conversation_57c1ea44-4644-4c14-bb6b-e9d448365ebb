package com.jdh.o2oservice.application.via.util;

import com.google.common.collect.Lists;
import com.jdh.o2oservice.common.enums.DeliveryStepTypeEnum;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.common.enums.PromiseGoAggregateStatusEnum;
import com.jdh.o2oservice.common.enums.UavErrorResultEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.UserPromisegoBo;
import com.jdh.o2oservice.core.domain.support.via.enums.PromiseAggregateStatusEnum;
import com.jdh.o2oservice.core.domain.support.via.model.ViaStatusMapping;
import com.jdh.o2oservice.export.angelpromise.dto.AngelShipDto;
import com.jdh.o2oservice.export.medicalpromise.dto.MedPromiseDeliveryStepDTO;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class UavUtil {


    /**
     * 无人机组装查询promisego 参数
     * @return
     */
    public static String orderDetailQueryPromiseGoStatus(String aggregateStatus, AngelShipDto angelShipDto, List<MedicalPromiseDTO> medicalPromiseList) {
        String queryPromiseGoStatus = aggregateStatus;

        // 无人机节点特殊处理
        if (Objects.equals(PromiseAggregateStatusEnum.TO_LAB.getCode(), aggregateStatus) && isUavFLow(medicalPromiseList)){
            if (angelShipDto != null && Objects.equals(angelShipDto.getType(), DeliveryTypeEnum.UAV.getType())){
                if (AngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus().equals(angelShipDto.getShipStatus())) {
                    queryPromiseGoStatus = PromiseGoAggregateStatusEnum.DELIVERY_WAITING.getType();
                } else if (AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus().equals(angelShipDto.getShipStatus())) {
                    queryPromiseGoStatus = PromiseGoAggregateStatusEnum.DELIVERING.getType();
                } else {
                    queryPromiseGoStatus = PromiseGoAggregateStatusEnum.DELIVERING.getType();
                }
                // 无人机模式，骑手配送阶段
            }else {
                queryPromiseGoStatus = PromiseGoAggregateStatusEnum.DELIVERING.getType();
            }
        }
        return queryPromiseGoStatus;
    }

    /**
     * 无人机异常情况
     * @param angelShipDto
     * @return
     */
    public static String orderDetailNavMainTitle(AngelShipDto angelShipDto, UserPromisegoBo userPromisegoBo, String queryPromiseGoStatus) {
        String mainTitle = null;
        if(userPromisegoBo != null && StringUtils.isNotBlank(queryPromiseGoStatus)) {
            if (angelShipDto != null && DeliveryTypeEnum.UAV.getType().equals(angelShipDto.getType()) && (userPromisegoBo.getCurrScript() == null || userPromisegoBo.getCurrScript().getEndTime().before(new Date())) && Lists.newArrayList(PromiseGoAggregateStatusEnum.DELIVERY_WAITING.getType(), PromiseGoAggregateStatusEnum.DELIVERING.getType()).contains(queryPromiseGoStatus)) {
                mainTitle = "正在加急送往实验室，请您耐心等待";
            }
        }
        if (mainTitle == null) {
            if (angelShipDto != null && DeliveryTypeEnum.UAV.getType().equals(angelShipDto.getType()) && angelShipDto.getUavResultDto() != null && StringUtils.isNotBlank(angelShipDto.getUavResultDto().getErrorCode()) && Lists.newArrayList(AngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus(), AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus()).contains(angelShipDto.getShipStatus())) {
                if (UavErrorResultEnum.limitScene().contains(angelShipDto.getUavResultDto().getErrorCode())) {
                    mainTitle = "航线限飞，已安排专人为您加急送样";
                } else if (UavErrorResultEnum.machineFaultScene().contains(angelShipDto.getUavResultDto().getErrorCode())) {
                    mainTitle = "为保障时效，已安排专人为您加急送样";
                } else if (UavErrorResultEnum.timeOutScene().contains(angelShipDto.getUavResultDto().getErrorCode())) {
                    mainTitle = "正在加急送往实验室，请您耐心等待";
                }
            }
        }
        return mainTitle;
    }


    public static Boolean isUavFLow(List<MedicalPromiseDTO> medicalPromiseList){
        if (CollectionUtils.isEmpty(medicalPromiseList)){
            return false;
        }

        List<MedPromiseDeliveryStepDTO> deliveryStepFlow = medicalPromiseList.get(0).getDeliveryStepFlow();
        if (CollectionUtils.isEmpty(deliveryStepFlow)){
            return false;
        }

        deliveryStepFlow.sort(Comparator.comparing(MedPromiseDeliveryStepDTO::getSort));
        if (deliveryStepFlow.size() == 2){
            Integer typeOne = deliveryStepFlow.get(0).getDeliveryStepType();
            Integer typeTwo = deliveryStepFlow.get(1).getDeliveryStepType();
            return Objects.equals(typeOne, DeliveryStepTypeEnum.RIDER.getType()) && Objects.equals(typeTwo, DeliveryStepTypeEnum.UAV.getType());
        }
        return false;
    }
}
