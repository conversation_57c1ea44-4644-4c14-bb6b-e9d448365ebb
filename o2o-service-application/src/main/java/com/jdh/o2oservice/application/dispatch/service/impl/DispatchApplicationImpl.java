package com.jdh.o2oservice.application.dispatch.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelLocationApplication;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.angelpromise.AngelWorkStatusApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelTaskApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.dispatch.convert.DispatchApplicationConverter;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.settlement.service.JdServiceSettleApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.base.annotation.AlarmPolicy;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.UserOperationLimit;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.cache.RedisUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.AlarmPolicyTypeEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.ErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.common.ext.O2oBusinessIdentifier;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.enums.JobNatureEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelSkillDict;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelSkillDictRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.AngelSkillDictRepQuery;
import com.jdh.o2oservice.core.domain.angelpromise.enums.*;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkHistory;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelTaskStatusCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelWorkStatusDbCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkHistoryRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.ability.WorkCreateAbility;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelWorkHistoryExtVo;
import com.jdh.o2oservice.core.domain.dispatch.context.*;
import com.jdh.o2oservice.core.domain.dispatch.context.bo.DispatchPlanTime;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchEventTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchExecuteRouteEnum;
import com.jdh.o2oservice.core.domain.dispatch.enums.FlowExecuteTypeEnum;
import com.jdh.o2oservice.core.domain.dispatch.event.*;
import com.jdh.o2oservice.core.domain.dispatch.flow.DispatchFlowDelegate;
import com.jdh.o2oservice.core.domain.dispatch.model.*;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchFlowTaskRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchHistoryRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.*;
import com.jdh.o2oservice.core.domain.dispatch.rpc.DispatchFlowDependRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.NewNethpDispatchRpc;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelSkillRelBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.bo.DispatchAngelWorkGroupCountBO;
import com.jdh.o2oservice.core.domain.dispatch.rpc.param.*;
import com.jdh.o2oservice.core.domain.dispatch.service.JdhDispatchDomainService;
import com.jdh.o2oservice.core.domain.product.context.JdhItemAngelSkillRelQueryContext;
import com.jdh.o2oservice.core.domain.product.model.ServiceItemAngelSkillRel;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemRepository;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderStoreRepository;
import com.jdh.o2oservice.core.domain.settlement.bo.ServerSettleAmountBo;
import com.jdh.o2oservice.core.domain.support.basic.enums.DispatchFlowTaskStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchDetailStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhDispatchStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.model.DomainAppointmentTime;
import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DirectionServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DirectionResultBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.DirectionRequestParam;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.enums.JdOrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderExt;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentInfoValueObject;
import com.jdh.o2oservice.export.angel.cmd.AngelUpdateTakeOrderStatusCmd;
import com.jdh.o2oservice.export.angel.dto.AngelLocationDto;
import com.jdh.o2oservice.export.angel.dto.AngelStationDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelGeoQuery;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.angel.query.StationQuery;
import com.jdh.o2oservice.export.angelpromise.cmd.*;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.dispatch.cmd.*;
import com.jdh.o2oservice.export.dispatch.dto.*;
import com.jdh.o2oservice.export.dispatch.query.*;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseDispatchCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.product.query.ServiceItemExtQuery;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.trade.query.JdServiceSettleParam;
import com.jdh.o2oservice.ext.ship.reponse.AngelWorkPlanTime;
import com.jdh.o2oservice.ext.work.param.WorkBizParamCheckParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.MDC;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName DispatchApplicationImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/4/17 18:47
 **/
@Service
@Slf4j
public class DispatchApplicationImpl implements DispatchApplication {

    /**
     * 重试配置
     */
    private final RetryTemplate RETRY_TEMPLATE = RetryTemplate.builder().maxAttempts(2).fixedBackoff(1000).build();

    /**
     * jdhDispatchDomainService
     */
    @Resource
    private JdhDispatchDomainService jdhDispatchDomainService;

    /**
     * dispatchRepository
     */
    @Resource
    private DispatchRepository dispatchRepository;

    /**
     * addressRpc
     */
    @Resource
    private AddressRpc addressRpc;

    /**
     * dispatchHistoryRepository
     */
    @Resource
    private DispatchHistoryRepository dispatchHistoryRepository;

    /**
     * jdhServiceItemRepository
     */
    @Resource
    private JdhServiceItemRepository jdhServiceItemRepository;

    /**
     * angelSkillDictRepository
     */
    @Resource
    private AngelSkillDictRepository angelSkillDictRepository;

    /**
     *
     */
    @Resource
    private VerticalBusinessRepository businessRepository;

    /**
     * 服务者
     */
    @Resource
    private AngelApplication angelApplication;

    /**
     * 服务者实时位置
     */
    @Resource
    private AngelLocationApplication angelLocationApplication;

    /**
     * 商品
     */
    @Resource
    private ProductApplication productApplication;

    /**
     * 标准项目
     */
    @Resource
    private ProductServiceItemApplication productServiceItemApplication;

    /**
     * 服务者履约工单
     */
    @Resource
    private AngelWorkApplication angelWorkApplication;

    /**
     * 服务者履约状态
     */
    @Resource
    private AngelWorkStatusApplication angelWorkStatusApplication;

    /**
     * 服务者履约单
     */
    @Resource
    private AngelPromiseApplication angelPromiseApplication;

    /**
     * 服务站
     */
    @Resource
    private StationApplication stationApplication;

    /**
     * 查询服务者结算价
     */
    @Resource
    private JdServiceSettleApplication jdServiceSettleApplication;

    /**
     * jdOrderApplication
     */
    @Resource
    private JdOrderApplication jdOrderApplication;

    /**
     * 实验室履约域服务
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * 实验室服务
     */
    @Resource
    private ProviderStoreRepository providerStoreRepository;

    /**
     * newNethpDispatchRpc
     */
    @Resource
    private NewNethpDispatchRpc newNethpDispatchRpc;

    /**
     * 距离路径规划
     */
    @Resource
    private DirectionServiceRpc directionServiceRpc;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * redisUtil
     */
    @Resource
    private RedisUtil redisUtil;

    /**
     * dispatchFlowDelegate
     */
    @Resource
    private DispatchFlowDelegate dispatchFlowDelegate;

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    @Autowired
    private DispatchFlowDependRpc dispatchFlowDependRpc;

    @Autowired
    private AngelWorkRepository angelWorkRepository;

    /**
     * 任务
     */
    @Resource
    AngelTaskApplication angelTaskApplication;

    /**
     * promiseApplication
     */
    @Resource
    private PromiseApplication promiseApplication;

    /**
     * 京me消息
     */
    @Resource
    DongDongRobotRpc dongDongRobotRpc;

    /**
     * workCreateAbility
     */
    @Resource
    private WorkCreateAbility workCreateAbility;
    /** */
    @Resource
    private DispatchFlowTaskRepository dispatchFlowTaskRepository;

    @Resource
    private AngelTaskRepository angelTaskRepository;

    @Resource
    private AngelWorkHistoryRepository angelWorkHistoryRepository;

    /**
     * 提交派单
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.submitDispatch")
    public Boolean submitDispatch(SubmitDispatchCmd cmd) {
        DispatchRepQuery dispatchRepQuery = new DispatchRepQuery();
        dispatchRepQuery.setPromiseId(cmd.getPromiseId());
        JdhDispatch jdhDispatch = dispatchRepository.findValidDispatch(dispatchRepQuery);
        log.info("DispatchApplicationImpl submitDispatch jdhDispatch:{}", JSON.toJSONString(jdhDispatch));
        //查询商品信息
        Set<Long> serviceIdList = cmd.getPatients().stream()
                .flatMap(patient -> patient.getServiceItems().stream())
                .map(DispatchServiceItem::getServiceId).collect(Collectors.toSet());
        Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder().skuIdList(serviceIdList).querySkuCoreData(false).queryServiceItem(true).queryItemMaterial(true).queryItemSkill(true).build());

        //查询项目所需技能
        Set<Long> itemIdList = cmd.getPatients().stream()
                .flatMap(patient -> patient.getServiceItems().stream())
                .map(DispatchServiceItem::getItemId).collect(Collectors.toSet());
        List<ServiceItemAngelSkillRel> serviceItemAngelSkillRels = jdhServiceItemRepository.queryServiceItemAngelSkillRel(JdhItemAngelSkillRelQueryContext.builder().serviceItemIds(itemIdList).build());
        SubmitDispatchContext context = DispatchApplicationConverter.INS.cmd2SubmitContext(cmd);
        context.initVertical();
        context.setSnapshot(jdhDispatch);
        context.setJdhSkuDtoMap(DispatchApplicationConverter.INS.jdhSku2DispatchSku(jdhSkuDtoMap));
        context.setItemSkillRelList(DispatchApplicationConverter.INS.skillRel2DispatchItemSkillRel(serviceItemAngelSkillRels));
        log.info("DispatchApplicationImpl -> submitDispatch context:{}",JSON.toJSONString(context));
        Boolean submitDispatch = jdhDispatchDomainService.submitDispatch(context);
        if (!submitDispatch) {
            return false;
        }
        //发送派单任务创建事件
        eventCoordinator.publish(EventFactory.newDefaultEvent(context.getDispatch(), DispatchEventTypeEnum.DISPATCH_CREATE, new DispatchWaitEventBody(context.getDispatch())));
        return Boolean.TRUE;
    }

    /**
     * 修改预约时间派单
     *
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.modifyServiceDateDispatch")
    public Boolean modifyServiceDateDispatch(SubmitDispatchCmd cmd) {
        Long promiseId = cmd.getPromiseId();
        String lockRedisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.DISPATCH_MODIFY_SERVICE_DATE_LOCK_KEY, promiseId);
        // 是否成功
        boolean ret = false;
        String errorMsg = null;
        try {
            boolean lock = redisUtil.tryLock(lockRedisKey, RedisKeyEnum.DISPATCH_RECEIVING_ORDER_LOCK_KEY.getExpireTime(), RedisKeyEnum.DISPATCH_RECEIVING_ORDER_LOCK_KEY.getExpireTimeUnit());
            if (!lock) {
                throw new BusinessException(DispatchErrorCode.DISPATCH_MODIFY_DATE_EXIST);
            }
            DispatchRepQuery dispatchRepQuery = new DispatchRepQuery();
            dispatchRepQuery.setPromiseId(cmd.getPromiseId());
            JdhDispatch jdhDispatchSnapshot = dispatchRepository.findValidDispatch(dispatchRepQuery);
            log.info("DispatchApplicationImpl modifyServiceDateDispatch jdhDispatchSnapshot={}", JSON.toJSONString(jdhDispatchSnapshot));
            // 不存在有效派单任务，直接发起派单
            if (jdhDispatchSnapshot == null) {
                log.info("DispatchApplicationImpl modifyServiceDateDispatch jdhDispatchSnapshot is null 直接发起派单");
                ret = submitDispatch(cmd);
                return ret;
            }
            AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
            angelWorkDBQuery.setPromiseId(promiseId);
            angelWorkDBQuery.setStatusList(AngelWorkStatusEnum.getValidStatus());
            List<AngelWork> angelWorkSnapShotList = angelWorkRepository.findList(angelWorkDBQuery);

            Map<Long, AngelWork> angelWorkMap = angelWorkSnapShotList.stream().collect(Collectors.toMap(AngelWork::getWorkId, Function.identity(), (existingValue, newValue) -> newValue));

            // 修改派单信息中预约时间，增加原预约时间
            appendDispatchBeforeDate(jdhDispatchSnapshot, cmd);
            angelWorkMap.forEach((angelWorkId, angelWorkSnapShot) -> {
                log.info("DispatchApplicationImpl modifyServiceDateDispatch angelWorkSnapShot={}", JSON.toJSONString(angelWorkSnapShot));
                // 1、取消运单, 工单不需要取消
                cancelShip(angelWorkSnapShot, cmd);
                // 2、修改派单状态
                modifyDateUpdateDispatchStatus(jdhDispatchSnapshot, angelWorkSnapShot);
                // 3、修改预约时间(工单、任务单)
                modifyDate(jdhDispatchSnapshot, angelWorkSnapShot, cmd);
                // 4、修预约时间成功，发布事件，触达事件需要
                modifyDateSuccessEvent(angelWorkSnapShot, cmd);
                // 5、重新派发实验室，避免历史预约实验室关店或者无库存  非快检业务不走派实验室
                if(!ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getVerticalCode().equals(jdhDispatchSnapshot.getVerticalCode())){
                    dispatchStore(promiseId);
                }
                // 6、重新派发服务者
                modifyDateReDispatch(jdhDispatchSnapshot, angelWorkSnapShot, cmd);
            });
            // 查询最新数据发送事件
            DispatchRepQuery dispatchReq = new DispatchRepQuery();
            dispatchReq.setPromiseId(cmd.getPromiseId());
            JdhDispatch dispatch = dispatchRepository.findValidDispatch(dispatchReq);
            // 派单成功，发送已派单时间，promise修改为修改预约成功
            eventCoordinator.publish(EventFactory.newDefaultEvent(dispatch, DispatchEventTypeEnum.MODIFY_DATE_DISPATCH_SUCCESS, new DispatchCallbackEventBody(dispatch, dispatch, null, cmd.getOperatorRoleType(), cmd.getReason(), cmd.getServiceBriefInfo())));
            ret = true;
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                errorMsg = ((BusinessException) e).getErrorCode().getCode() + ":" + ((BusinessException) e).getErrorCode().getDescription();
            } else {
                errorMsg = StringUtils.isNotBlank(e.getMessage()) ? e.getMessage().substring(0,Math.min(200, e.getMessage().length())) : "";
            }
            // 查询最新数据发送事件
            DispatchRepQuery dispatchReq = new DispatchRepQuery();
            dispatchReq.setPromiseId(cmd.getPromiseId());
            JdhDispatch dispatch = dispatchRepository.findValidDispatch(dispatchReq);
            // 派单成功，发送已派单时间，promise修改为修改预约成功
            eventCoordinator.publish(EventFactory.newDefaultEvent(dispatch, DispatchEventTypeEnum.MODIFY_DATE_DISPATCH_FAIL, new DispatchCallbackEventBody(dispatch, dispatch, null, cmd.getOperatorRoleType(), cmd.getReason(), cmd.getServiceBriefInfo())));
            log.error("DispatchApplicationImpl -> modifyServiceDateDispatch 修改预约时间失败", e);
            throw e;
        } finally {
            redisUtil.unLock(lockRedisKey);
            if (ret) {
                successSendMsg(cmd.getOperateErp(), promiseId, cmd.getAppointmentTime().getAppointmentStartTime());
            } else {
                failSendMsg(cmd.getOperateErp(), promiseId, errorMsg);
            }
        }
        return ret;
    }

    /**
     * 发起服务者派单
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.angelDispatch")
    public Boolean angelDispatch(AngelDispatchCmd cmd) {
        return angelDispatchLogic(cmd);
    }

    /**
     * 冻结派单任务
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.freezeDispatch")
    public DispatchFreezeStatusDto freezeDispatch(FreezeDispatchCmd cmd){
        FreezeDispatchContext context = DispatchApplicationConverter.INS.cmd2FreezeDispatchContext(cmd);
        jdhDispatchDomainService.freezeDispatch(context);
        //冻结服务者工单
        if (context.getFreezeAngelWork()) {
            AngelWorkExecuteCmd workExecuteCmd = DispatchApplicationConverter.INS.cmd2AngelWorkExecuteCmd(cmd);
            workExecuteCmd.setSourceId(context.getJdhDispatch().getDispatchId());
            AngelWorkStatusDto angelWorkStatusDto = angelWorkStatusApplication.executeAngelWork(workExecuteCmd);
            //返回值组装
            DispatchFreezeStatusDto freezeStatusDto = DispatchApplicationConverter.INS.angelWorkStatus2DispatchFreezeStatus(angelWorkStatusDto);
            freezeStatusDto.setDispatchId(context.getJdhDispatch().getDispatchId());
            freezeStatusDto.setDispatchStatus(context.getJdhDispatch().getDispatchStatus());
            freezeStatusDto.setHasAvailableService(context.getHasAvailableService());
            return freezeStatusDto;
        }
        return DispatchFreezeStatusDto.builder()
                .dispatchStatus(context.getJdhDispatch().getDispatchStatus())
                .dispatchId(context.getJdhDispatch().getDispatchId())
                .hasAvailableService(context.getHasAvailableService())
                .build();
    }

    /**
     * 取消派单
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.cancelDispatch")
    public DispatchInvalidStatusDto cancelDispatch(CancelDispatchCmd cmd) {
        JdhDispatch snapshot = dispatchRepository.findValidDispatchWithDetail(DispatchRepQuery.builder().promiseId(cmd.getPromiseId()).dispatchId(cmd.getDispatchId()).build());
        if (Objects.isNull(snapshot)) {
            return DispatchInvalidStatusDto.builder()
                    //.dispatchStatus(context.getJdhDispatch().getDispatchStatus())
                    //.dispatchId(context.getJdhDispatch().getDispatchId())
                    .hasAvailableService(false)
                    .build();
        }
        InvalidDispatchContext context = DispatchApplicationConverter.INS.cmd2InvalidDispatchContext(cmd);
        jdhDispatchDomainService.invalidDispatch(context);
        if (context.getCancelAngelWork()) {
            AngelWorkExecuteCmd workCmd = new AngelWorkExecuteCmd();
            workCmd.setPromiseId(context.getJdhDispatch().getPromiseId());
            workCmd.setUserPin(context.getJdhDispatch().getUserPin());
            workCmd.setEventCode(AngelWorkStatusEventEnum.ANGEL_WORK_C_USER_CANCEL_SERVED.getEventCode());
            angelWorkStatusApplication.executeAngelWork(workCmd);
        }
        eventCoordinator.publish(EventFactory.newDefaultEvent(
                context.getJdhDispatch(),
                DispatchEventTypeEnum.DISPATCH_INVALID,
                new InvalidDispatchEventBody(context.getSnapshot(), context.getJdhDispatch(), context.getReason())
        ));
        return DispatchInvalidStatusDto.builder()
                .dispatchStatus(context.getJdhDispatch().getDispatchStatus())
                .dispatchId(context.getJdhDispatch().getDispatchId())
                .hasAvailableService(context.getHasAvailableService())
                .build();
    }

    /**
     * 取消派单
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.invalidDispatch")
    public DispatchInvalidStatusDto invalidDispatch(CancelDispatchCmd cmd) {
        JdhDispatch snapshot = dispatchRepository.findValidDispatchWithDetail(DispatchRepQuery.builder().promiseId(cmd.getPromiseId()).dispatchId(cmd.getDispatchId()).build());
        if (Objects.isNull(snapshot)) {
            return DispatchInvalidStatusDto.builder()
                    //.dispatchStatus(context.getJdhDispatch().getDispatchStatus())
                    //.dispatchId(context.getJdhDispatch().getDispatchId())
                    .hasAvailableService(false)
                    .build();
        }
        InvalidDispatchContext context = DispatchApplicationConverter.INS.cmd2InvalidDispatchContext(cmd);
        jdhDispatchDomainService.invalidDispatch(context);
        //冻结服务者工单（2024-05-16 只有整单退款时履约域才会调派单作废，服务者履约域自行处理）
        /*if (context.getCancelAngelWork()) {
            AngelWorkExecuteCmd workExecuteCmd = DispatchApplicationConverter.INS.cmd2AngelWorkExecuteCmd(cmd);
            workExecuteCmd.setSourceId(context.getJdhDispatch().getDispatchId());
            AngelWorkStatusDto angelWorkStatusDto = angelWorkStatusApplication.executeAngelWork(workExecuteCmd);
            //返回值组装
            DispatchInvalidStatusDto freezeStatusDto = DispatchApplicationConverter.INS.angelWorkStatus2DispatchInvalidStatus(angelWorkStatusDto);
            freezeStatusDto.setDispatchId(context.getJdhDispatch().getDispatchId());
            freezeStatusDto.setDispatchStatus(context.getJdhDispatch().getDispatchStatus());
            freezeStatusDto.setHasAvailableService(context.getHasAvailableService());
            return freezeStatusDto;
        }*/
        eventCoordinator.publish(EventFactory.newDefaultEvent(
                context.getJdhDispatch(),
                DispatchEventTypeEnum.DISPATCH_INVALID,
                new InvalidDispatchEventBody(context.getSnapshot(), context.getJdhDispatch(), context.getReason())
        ));
        return DispatchInvalidStatusDto.builder()
                .dispatchStatus(context.getJdhDispatch().getDispatchStatus())
                .dispatchId(context.getJdhDispatch().getDispatchId())
                .hasAvailableService(context.getHasAvailableService())
                .build();
    }

    /**
     * 重新派单
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.reDispatch")
    public Boolean reDispatch(ReDispatchCmd cmd) {
        String errMsg = ValidateParamUtil.paramValidation(cmd);
        if(StringUtils.isNotBlank(errMsg)){
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR, errMsg);
        }
        JdhDispatch snapshot = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(cmd.getDispatchId()).promiseId(cmd.getPromiseId()).build());
        //重新派单按钮：仅在护士接单后、护士上门之前才可以重新派单
        if (Objects.isNull(snapshot) || !Objects.equals(snapshot.getDispatchStatus(), JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus())) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION);
        }
        //服务者工单状态：1待接单，2、已接单，3、待服务 可重新派单
        ArrayList<Integer> angelStatusList = Lists.newArrayList(1, 2, 3);
        //查询服务者工单
        AngelWorkDetailDto angelWorkDto = angelPromiseApplication.queryAngelWork(AngelWorkQuery.builder().promiseId(snapshot.getPromiseId()).build());
        //如果不是从服务者履约域发起重派的，必须有服务者工单
        log.info("DispatchApplicationImpl -> reDispatch, angelWorkDto={}",JSON.toJSONString(angelWorkDto));
        if (!Objects.equals(cmd.getRoleType(), 5) && (Objects.isNull(angelWorkDto) || !angelStatusList.contains(angelWorkDto.getStatus()))) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION);
        }
        ReDispatchContext context = DispatchApplicationConverter.INS.cmd2ReDispatchContext(cmd);
        Boolean result = jdhDispatchDomainService.reDispatch(context);
        //取消服务者工单
        if (context.getCancelAngelWork()) {
            AngelWorkExecuteCmd workExecuteCmd = DispatchApplicationConverter.INS.dispatch2AngelWorkExecuteCmd(context.getJdhDispatch(), AngelWorkStatusEventEnum.ANGEL_WORK_CANCEL_SERVE);
            workExecuteCmd.setSourceId(context.getJdhDispatch().getDispatchId());
            if (Objects.equals(cmd.getRoleType(), 4)) {
                workExecuteCmd.setEventCode(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_B_CANCEL.getCode());
            }
            angelWorkStatusApplication.executeAngelWork(workExecuteCmd);
        }
        JdhDispatchDetail cancelDetail = null;
        Optional<JdhDispatchDetail> optional = context.getJdhDispatch().getAngelDetailList().stream().filter(dispatchDetail -> Objects.equals(dispatchDetail.getDispatchDetailStatus(), JdhDispatchDetailStatusEnum.DISPATCH_CANCEL.getStatus())).findFirst();
        if (optional.isPresent()) {
            cancelDetail = optional.get();
        }
        eventCoordinator.publish(EventFactory.newDefaultEvent(
                context.getJdhDispatch(),
                DispatchEventTypeEnum.DISPATCH_REDISPATCH,
                new ReDispatchEventBody(context.getSnapshot(), context.getJdhDispatch(), cancelDetail, context)
        ));
        return result;
    }

    /**
     * 指定派单
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.targetDispatch", alarmPolicy = {
            @AlarmPolicy(type = AlarmPolicyTypeEnum.UMP),
            @AlarmPolicy(methodName = "运营端指定派单", keyword = "promiseId", type = AlarmPolicyTypeEnum.DONGDONG_ROBOT)
    })
    public DispatchTargetStatusDto targetDispatch(TargetDispatchCmd cmd) {
        //try{
        log.info("DispatchApplicationImpl -> targetDispatch, cmd={}",JSON.toJSONString(cmd));
        String errMsg = ValidateParamUtil.paramValidation(cmd);
        if(StringUtils.isNotBlank(errMsg)){
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR, errMsg);
        }
        JdhDispatch snapshot = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(cmd.getDispatchId()).promiseId(cmd.getPromiseId()).build());
        if (Objects.isNull(snapshot)) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION);
        }
        if (Objects.equals(snapshot.getDispatchStatus(), JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus())) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION);
        }
        //指定派单：服务者接单之前的所有状态都可以操作
        TargetDispatchContext context = DispatchApplicationConverter.INS.cmd2TargetDispatchCmd(cmd);
        //查询护士信息
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelId(cmd.getTargetAngelId());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if(Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        //护士信息放入上下文
        context.setAngelId(jdhAngelDto.getAngelId());
        context.setAngelPin(jdhAngelDto.getAngelPin());
        context.setOutAngelId(String.valueOf(jdhAngelDto.getNethpDocId()));
        context.setAngelName(jdhAngelDto.getAngelName());
        //查询护士结算价
        DispatchAngelPlanCharge dispatchAngelPlanCharge = getDispatchAngelPlanCharge(cmd.getTargetAngelId(),jdhAngelDto.getJobNature(),snapshot);
        if(Objects.isNull(dispatchAngelPlanCharge)) {
            //未获取到结算价，抛出异常
            throw new BusinessException(DispatchErrorCode.DISPATCH_SETTLE_PRICE_ERROR);
        }
        //成本价放入上下文
        context.setAngelPlanCharge(dispatchAngelPlanCharge);

        //护士技能集合
        List<DispatchAngelSkillRelBO> targetAngelSkillRelBOS = dispatchFlowDependRpc.queryAngelSkillListByAngelInfo(DispatchAngelSkillDictParam.builder().angelId(cmd.getTargetAngelId()).build());
        if(CollUtil.isEmpty(targetAngelSkillRelBOS)) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_ANGEL_SKILL_EMPTY);
        }
        context.setTargetAngelSkillRelList(targetAngelSkillRelBOS);

        //serviceInfo
        JdhDispatchServiceInfo serviceInfo = snapshot.getServiceInfo();
        context.setServiceInfo(serviceInfo);

        Boolean result = jdhDispatchDomainService.targetDispatch(context);

        //创建服务者工单
        JdhAngelWorkSaveCmd workSaveCmd = DispatchApplicationConverter.INS.dispatch2AngelWorkSaveCmd(context.getJdhDispatch());
        //设置护士全兼职和服务站省市区
        workSaveCmd.setJobNature(jdhAngelDto.getJobNature());
        if (Objects.nonNull(jdhAngelDto.getStationId()) && Objects.equals(jdhAngelDto.getJobNature(), JobNatureEnum.FULL_TIME.getValue())) {
            StationQuery stationQuery = new StationQuery();
            stationQuery.setStationIds(Sets.newHashSet(jdhAngelDto.getStationId()));
            List<AngelStationDto> angelStationDtos = stationApplication.queryJdhStation(stationQuery);
            if (CollectionUtils.isNotEmpty(angelStationDtos)) {
                AngelStationDto angelStationDto = angelStationDtos.get(0);
                workSaveCmd.setProvinceCode(StringUtils.isNotBlank(angelStationDto.getProvinceCode()) ? Integer.valueOf(angelStationDto.getProvinceCode()) : null);
                workSaveCmd.setCityCode(StringUtils.isNotBlank(angelStationDto.getCityCode()) ? Integer.valueOf(angelStationDto.getCityCode()) : null);
                workSaveCmd.setDistrictCode(StringUtils.isNotBlank(angelStationDto.getDistrictCode()) ? Integer.valueOf(angelStationDto.getDistrictCode()) : null);
                workSaveCmd.setTownCode(StringUtils.isNotBlank(angelStationDto.getCountyCode()) ? Integer.valueOf(angelStationDto.getCountyCode()) : null);
            }
        }
        angelWorkApplication.createAngelWork(workSaveCmd);

        eventCoordinator.publish(EventFactory.newDefaultEvent(
                context.getJdhDispatch(),
                DispatchEventTypeEnum.DISPATCH_TARGET_DISPATCH,
                new TargetDispatchEventBody(context.getSnapshot(), context.getJdhDispatch(), context.getReason(), context.getTargetAngelId(), context.getTargetDispatchDetailId(), context.getIsFirstReceived())
        ));
        return DispatchTargetStatusDto.builder().targetResult(result).dispatchId(cmd.getDispatchId()).isFirstReceived(context.getIsFirstReceived()).build();
//        } catch (BusinessException e){
//            log.info("DispatchApplicationImpl -> targetDispatch business exception", e);
//            ErrorCode errorCode = e.getErrorCode();
//            //如果业务异常不是由于请求状态已过期或着获取锁失败，将异常抛出
//            if (!Objects.equals(errorCode, DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION) && !Objects.equals(errorCode, DispatchErrorCode.DISPATCH_CALLBACK_BUSY)) {
//                throw e;
//            }
//            return DispatchTargetStatusDto.builder().targetResult(false).dispatchId(cmd.getDispatchId()).errorCode(errorCode.getCode()).description(errorCode.getDescription()).build();
//        }
    }

    /**
     * 接单护士转单
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(alarmPolicy = {@AlarmPolicy(type = AlarmPolicyTypeEnum.UMP), @AlarmPolicy(methodName = "接单护士转单", keyword = "promiseId", type = AlarmPolicyTypeEnum.DONGDONG_ROBOT)})
    public DispatchTargetStatusDto receiveTransferDispatch(TargetDispatchCmd cmd) {
        try{
            log.info("DispatchApplicationImpl -> receiveTransferDispatch, cmd={}",JSON.toJSONString(cmd));
            String errMsg = ValidateParamUtil.paramValidation(cmd);
            if(StringUtils.isNotBlank(errMsg)){
                throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR, errMsg);
            }
            JdhDispatch snapshot = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(cmd.getDispatchId()).promiseId(cmd.getPromiseId()).build());
            if (Objects.isNull(snapshot)) {
                throw new BusinessException(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION);
            }
            //查询护士工单状态, 未出门之前可以转
            ArrayList<Integer> canTransferWorkStatusList = Lists.newArrayList(AngelWorkStatusEnum.INIT.getType(),
                    AngelWorkStatusEnum.WAIT_RECEIVE.getType(), AngelWorkStatusEnum.RECEIVED.getType());
            AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
            //派单id
            angelWorkDBQuery.setSourceId(snapshot.getDispatchId());
            angelWorkDBQuery.setPromiseId(snapshot.getPromiseId());
            angelWorkDBQuery.setStatusList(AngelWorkStatusEnum.getValidStatus());
            AngelWork angelWork = angelWorkRepository.findAngelWork(angelWorkDBQuery);
            if (Objects.isNull(angelWork) || !canTransferWorkStatusList.contains(angelWork.getWorkStatus())) {
                throw new BusinessException(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION);
            }

            //取消原护士工单
            AngelWorkExecuteCmd workExecuteCmd = DispatchApplicationConverter.INS.dispatch2AngelWorkExecuteCmd(snapshot, AngelWorkStatusEventEnum.ANGEL_WORK_CANCEL_SERVE);
            workExecuteCmd.setSourceId(snapshot.getDispatchId());
            if (Objects.equals(cmd.getRoleType(), 4)) {
                workExecuteCmd.setEventCode(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_B_CANCEL.getCode());
            }
            angelWorkStatusApplication.executeAngelWork(workExecuteCmd);

            //处理护士转单逻辑
            TargetDispatchContext context = DispatchApplicationConverter.INS.cmd2TargetDispatchCmd(cmd);
            //查询护士信息
            AngelRequest angelRequest = new AngelRequest();
            angelRequest.setAngelId(cmd.getTargetAngelId());
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
            if(Objects.isNull(jdhAngelDto)) {
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
            }
            //护士信息放入上下文
            context.setAngelId(jdhAngelDto.getAngelId());
            context.setOutAngelId(String.valueOf(jdhAngelDto.getNethpDocId()));
            context.setAngelPin(jdhAngelDto.getAngelPin());
            context.setAngelName(jdhAngelDto.getAngelName());
            //查询护士结算价
            DispatchAngelPlanCharge dispatchAngelPlanCharge = getDispatchAngelPlanCharge(cmd.getTargetAngelId(), jdhAngelDto.getJobNature(),snapshot);
            if(Objects.isNull(dispatchAngelPlanCharge)) {
                //未获取到结算价，抛出异常
                throw new BusinessException(DispatchErrorCode.DISPATCH_SETTLE_PRICE_ERROR);
            }
            //成本价放入上下文
            context.setAngelPlanCharge(dispatchAngelPlanCharge);


            //护士技能集合
            List<DispatchAngelSkillRelBO> targetAngelSkillRelBOS = dispatchFlowDependRpc.queryAngelSkillListByAngelInfo(DispatchAngelSkillDictParam.builder().angelId(cmd.getTargetAngelId()).build());
            if(CollUtil.isEmpty(targetAngelSkillRelBOS)) {
                throw new BusinessException(DispatchErrorCode.DISPATCH_ANGEL_SKILL_EMPTY);
            }
            context.setTargetAngelSkillRelList(targetAngelSkillRelBOS);

            //serviceInfo
            JdhDispatchServiceInfo serviceInfo = snapshot.getServiceInfo();
            context.setServiceInfo(serviceInfo);

            Boolean result = jdhDispatchDomainService.receiveTransferDispatch(context);

            //创建新服务者工单
            JdhAngelWorkSaveCmd workSaveCmd = DispatchApplicationConverter.INS.dispatch2AngelWorkSaveCmd(context.getJdhDispatch());
            //设置护士全兼职和服务站省市区
            workSaveCmd.setJobNature(jdhAngelDto.getJobNature());
            if (Objects.nonNull(jdhAngelDto.getStationId()) && Objects.equals(jdhAngelDto.getJobNature(), JobNatureEnum.FULL_TIME.getValue())) {
                StationQuery stationQuery = new StationQuery();
                stationQuery.setStationIds(Sets.newHashSet(jdhAngelDto.getStationId()));
                List<AngelStationDto> angelStationDtos = stationApplication.queryJdhStation(stationQuery);
                if (CollectionUtils.isNotEmpty(angelStationDtos)) {
                    AngelStationDto angelStationDto = angelStationDtos.get(0);
                    workSaveCmd.setProvinceCode(StringUtils.isNotBlank(angelStationDto.getProvinceCode()) ? Integer.valueOf(angelStationDto.getProvinceCode()) : null);
                    workSaveCmd.setCityCode(StringUtils.isNotBlank(angelStationDto.getCityCode()) ? Integer.valueOf(angelStationDto.getCityCode()) : null);
                    workSaveCmd.setDistrictCode(StringUtils.isNotBlank(angelStationDto.getDistrictCode()) ? Integer.valueOf(angelStationDto.getDistrictCode()) : null);
                    workSaveCmd.setTownCode(StringUtils.isNotBlank(angelStationDto.getCountyCode()) ? Integer.valueOf(angelStationDto.getCountyCode()) : null);
                }
            }
            angelWorkApplication.createAngelWork(workSaveCmd);

            eventCoordinator.publish(EventFactory.newDefaultEvent(
                    context.getJdhDispatch(),
                    DispatchEventTypeEnum.DISPATCH_TARGET_DISPATCH,
                    new TargetDispatchEventBody(context.getSnapshot(), context.getJdhDispatch(), context.getReason(), context.getTargetAngelId(), context.getTargetDispatchDetailId(), context.getIsFirstReceived())
            ));
            return DispatchTargetStatusDto.builder().targetResult(result).dispatchId(cmd.getDispatchId()).build();
        } catch (BusinessException e){
            log.info("DispatchApplicationImpl -> receiveTransferDispatch business exception", e);
            ErrorCode errorCode = e.getErrorCode();
            //如果业务异常不是由于请求状态已过期或着获取锁失败，将异常抛出
            if (!Objects.equals(errorCode, DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION) && !Objects.equals(errorCode, DispatchErrorCode.DISPATCH_CALLBACK_BUSY)) {
                throw e;
            }
            return DispatchTargetStatusDto.builder().targetResult(false).dispatchId(cmd.getDispatchId()).errorCode(errorCode.getCode()).description(errorCode.getDescription()).build();
        }
    }

    /**
     * 派单信息回调
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.callBack")
    public DispatchCallbackDto callBack(DispatchCallbackCmd cmd) {
        return disposeCallBack(cmd, true);
    }

    /**
     * 互医派单信息回调
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.nethpTriageDispatchCallBack")
    public DispatchCallbackDto nethpTriageDispatchCallBack(DispatchCallbackCmd cmd) {
        try {
            Boolean result = dispatchFlowDelegate.disposeCallBack(DispatchApplicationConverter.INS.cmd2DispatchCallbackContext(cmd));
            if (!result) {
                return DispatchCallbackDto.builder().dispatchId(cmd.getDispatchId()).build();
            }
            return disposeCallBack(cmd, true);
        } catch (SystemException e) {
            if (Objects.equals(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION, e.getErrorCode())) {
                log.warn("DispatchApplicationImpl -> callBack 当前单据状态不允许此操作", e);
                return DispatchCallbackDto.builder().dispatchId(cmd.getDispatchId()).build();
            }else {
                throw e;
            }
        }
    }

    /**
     * 服务者接单
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.angelReceive")
    public DispatchCallbackDto angelReceive(DispatchCallbackCmd cmd) {
        return disposeCallBack(cmd, false);
    }

    /**
     * 派单回收
     * @param cmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.recoverDispatch")
    public Boolean recoverDispatch(RecoverDispatchCmd cmd) {
        JdhDispatch snapshot = dispatchRepository.findDispatchWithDetail(DispatchRepQuery.builder().dispatchId(cmd.getDispatchId()).build());
        if (Objects.isNull(snapshot)) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION);
        }
        if (CollectionUtils.isEmpty(snapshot.getAngelDetailList())) {
            log.warn("DispatchApplicationImpl -> recoverDispatch 派单任务已无有效派单结果明细数据，无需回收, cmd={}", JSON.toJSONString(cmd));
        }
        RecoverDispatchContext context = new RecoverDispatchContext();
        context.init(snapshot);
        Boolean result = dispatchFlowDelegate.recoverDispatch(context);
        if (!result) {
            return Boolean.FALSE;
        }
        //发送派单任务回收事件
        eventCoordinator.publish(EventFactory.newDefaultEvent(context.getJdhDispatch(), DispatchEventTypeEnum.DISPATCH_RECOVER, new DispatchCallbackEventBody(context.getSnapshot(), context.getJdhDispatch())));
        return Boolean.TRUE;
    }

    /**
     * 查询待接单总览
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.queryDispatchWaitingReceiveOverview")
    public JdhDispatchWaitingReceiveOverview queryDispatchWaitingReceiveOverview(DispatchWaitingReceiveOverviewRequest request) {
        JdhDispatchWaitingReceiveOverview result = JdhDispatchWaitingReceiveOverview.builder()
                .angelId(request.getAngelId())
                .currentTotalNum(0)
                .currentGrabNum(0)
                .currentAppointNum(0)
                .topTabShow(duccConfig.getTopTabShow()).build();
        //用登录pin查服务者域换ID继续查询
        if (StringUtil.isNotBlank(request.getUserPin()) && Objects.isNull(request.getAngelId())) {
            AngelRequest angelRequest = new AngelRequest();
            angelRequest.setAngelPin(request.getUserPin());
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);

            if(Objects.isNull(jdhAngelDto)) {
                return result;
            }
            request.setAngelId(Objects.nonNull(jdhAngelDto) ? jdhAngelDto.getAngelId() : null);
        }
        Date now = new Date();

        //查询派单明细总览（待接单数量）
        List<DispatchDetailGroupCount> dispatchDetailGroupCount = dispatchRepository.findDispatchDetailGroupCount(DispatchDetailGroupCountQuery.builder().column("dispatch_detail_type").angelId(request.getAngelId()).dispatchDetailStatus(JdhDispatchDetailStatusEnum.DISPATCH_COMPLETED.getStatus()).expireDate(now).build());
        if (CollectionUtils.isNotEmpty(dispatchDetailGroupCount)) {
            dispatchDetailGroupCount.forEach(groupCount -> {
                if (Objects.equals(groupCount.getGroupKeyValue(), DispatchDetailTypeEnum.ASSIGN.getType())) {
                    result.setCurrentAppointNum(groupCount.getCount().intValue());
                }
                else if (Objects.equals(groupCount.getGroupKeyValue(), DispatchDetailTypeEnum.GRAB.getType())) {
                    result.setCurrentGrabNum(groupCount.getCount().intValue());
                }
            });
            result.setCurrentTotalNum(Stream.of(result.getCurrentAppointNum(),result.getCurrentGrabNum()).filter(Objects::nonNull).reduce(0, Integer::sum));
        }
        //如果没有待接单数据，直接返回结果
        if (result.getCurrentTotalNum() < 1) {
            return result;
        }

        //查询派单明细列表
        DispatchDetailPageRepQuery query = DispatchDetailPageRepQuery.builder()
                .angelId(request.getAngelId())
                .dispatchDetailType(request.getDispatchDetailType())
                .dispatchDetailStatus(JdhDispatchDetailStatusEnum.DISPATCH_COMPLETED.getStatus())
                .expireDate(now)
                .build();
        query.setPageNum(request.getPageNum());
        query.setPageSize(request.getPageSize());

        Page<JdhDispatchDetail> detailPage = dispatchRepository.findDispatchDetailPage(query);
        if (Objects.isNull(detailPage)) {
            return result;
        }
        //查询服务者实时位置，如果有实时位置计算上门地址之间距离和时长；如果没有实时位置则不展示
        AngelLocationDto location = angelLocationApplication.getLocation(query.getAngelId());
        List<JdhDispatchDetailDto> list = attachDispatchAndLocation(detailPage.getRecords(), location);
        log.info("DispatchApplicationImpl -> queryDispatchWaitingReceiveOverview,list={}", JSON.toJSONString(list));
        try {
            //第一排序维度：订单预约时间距离当前时刻时间，由小到大（含负值，即时单的情况）
            //第二排序维度：订单金额（护士可赚到的钱）由大到小
            list = list.stream().sorted(Comparator.comparing(JdhDispatchDetailDto::getAppointmentTime).thenComparing(JdhDispatchDetailDto::getAngelCharge).reversed()).collect(Collectors.toList());
            //临期单/即时单打标
            list.forEach(t -> {
                //是否临期单
                Boolean isNearExpiry = TimeUtils.add(TimeUtils.timeStrToDate(t.getAppointmentTime().getAppointmentStartTime(), TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE_NO_SSS), Calendar.MINUTE, duccConfig.getTimeToVisitMinute()).before(new Date());
                if (isNearExpiry || DispatchTypeEnum.IMMEDIATELY.getType().equals(t.getDispatchType())) {
                    t.setLabel(DispatchLabelTypeEnum.URGENT.getType());
                    t.setLabelIcon(DispatchLabelTypeEnum.URGENT.getIcon());
                } else {
                    t.setLabel(DispatchLabelTypeEnum.NOTURGENT.getType());
                    t.setLabelIcon(DispatchLabelTypeEnum.NOTURGENT.getIcon());
                }
            });
        } catch (Exception ex) {
            log.error("DispatchApplicationImpl -> queryDispatchWaitingReceiveOverview,临期单处理异常", ex);
        }
        PageDto<JdhDispatchDetailDto> pageDto = new PageDto<>();
        pageDto.setList(list);
        pageDto.setPageSize(detailPage.getPages());
        pageDto.setPageNum(detailPage.getCurrent());
        pageDto.setTotalPage(detailPage.getPages());
        pageDto.setTotalCount(detailPage.getTotal());
        result.setPageDto(pageDto);
        return result;
    }

    /**
     * 查询派单明细列表
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.queryDispatchWaitingReceiveList")
    public List<JdhDispatchDetailDto> queryDispatchWaitingReceiveList(DispatchWaitingReceiveListRequest request) {
        //用登录pin查服务者域换ID继续查询
        if (StringUtil.isNotBlank(request.getUserPin()) && Objects.isNull(request.getAngelId())) {
            AngelRequest angelRequest = new AngelRequest();
            angelRequest.setAngelPin(request.getUserPin());
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
            request.setAngelId(Objects.nonNull(jdhAngelDto) ? jdhAngelDto.getAngelId() : null);
        }
        //查询派单明细列表
        DispatchDetailListRepQuery query = DispatchApplicationConverter.INS.request2DispatchDetailListRepQuery(request);
        query.setDispatchDetailStatus(JdhDispatchDetailStatusEnum.DISPATCH_COMPLETED.getStatus());
        query.setExpireDate(new Date());
        List<JdhDispatchDetail> detailList = dispatchRepository.findDispatchDetailList(query);
        if (CollectionUtils.isEmpty(detailList)) {
            return Collections.emptyList();
        }
        //查询服务者实时位置，如果有实时位置计算上门地址之间距离和时长；如果没有实时位置则不展示
        AngelLocationDto location = angelLocationApplication.getLocation(query.getAngelId());
        return attachDispatchAndLocation(detailList, location);
    }

    /**
     * 查询派单明细
     * @param request
     * @return
     */
    @Override
    public AngelWorkDto queryDispatchWaitingReceiveDetail(DispatchWaitingReceiveDetailRequest request) {
        //根据userPin查询护士ID
        if (StringUtil.isNotBlank(request.getUserPin()) && Objects.isNull(request.getAngelId())) {
            AngelRequest angelRequest = new AngelRequest();
            angelRequest.setAngelPin(request.getUserPin());
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
            request.setAngelId(Objects.nonNull(jdhAngelDto) ? jdhAngelDto.getAngelId() : null);
        }
        //查询派单明细
        JdhDispatchDetail dispatchDetail = dispatchRepository.findDispatchDetail(DispatchDetailRepQuery.builder().angelId(request.getAngelId()).dispatchDetailId(request.getDispatchDetailId()).build());
        if (Objects.isNull(dispatchDetail)) {
            return null;
        }
        //查询服务者实时位置，如果有实时位置计算上门地址之间距离和时长；如果没有实时位置则不展示
        AngelLocationDto location = angelLocationApplication.getLocation(dispatchDetail.getAngelId());
        return convertAngelWorkDto(dispatchDetail, location);
    }

    /**
     * 派单信息处理
     * @param cmd
     * @return
     */
    public DispatchCallbackDto disposeCallBack(DispatchCallbackCmd cmd, boolean retry) {
        StopWatch stopWatch = new StopWatch();
        //派单失败, 尝试一次指定派单给全职护士
        if (Objects.equals(cmd.getDispatchStatus(), JdhDispatchStatusEnum.DISPATCH_FAIL.getStatus())) {
            stopWatch.start("派单失败兜底");
            boolean success = executeTargetDispatch(cmd);
            stopWatch.stop();
            if (success) {
                log.info("DispatchApplicationImpl -> disposeCallBack, stopWatch:{}", stopWatch.prettyPrint());
                return DispatchCallbackDto.builder().dispatchId(cmd.getDispatchId()).build();
            }
        }

        DispatchCallbackContext context = DispatchApplicationConverter.INS.cmd2DispatchCallbackContext(cmd);
        JdhDispatch snapshot = dispatchRepository.find(JdhDispatchIdentifier.builder().dispatchId(cmd.getDispatchId()).build());
        if(Objects.isNull(snapshot)){
            log.error("[DispatchApplicationImpl -> callBack],派单任务不存在!");
            throw new BusinessException(DispatchErrorCode.DISPATCH_NOT_EXIST);
        }
        context.setVerticalCode(snapshot.getVerticalCode());
        context.setServiceType(snapshot.getServiceType());
        context.initVertical();
        //如果是派单成功且是派单类型，设置状态机事件
        if (Objects.equals(cmd.getAssignType(), DispatchDetailTypeEnum.ASSIGN.getType()) && Objects.equals(cmd.getDispatchStatus(), JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus())) {
            context.setEventTypeEnum(DispatchEventTypeEnum.DISPATCH_ASSIGN_SUCCESS_RECEIVED);
        }

        //已派单处理，查询已派单服务者的结算价
        //上门业务
        boolean goHomeFlag = BusinessModeEnum.checkTestAndCare(context.getVerticalBusiness().getBusinessModeCode());
        //已派单
        boolean completeInSpace = Objects.equals(cmd.getDispatchStatus(), JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus());
        if (completeInSpace && goHomeFlag) {
            //优化获取结算价逻辑，并发获取
            stopWatch.start("查询护士结算价");
            List<DispatchAngelPlanCharge> angelPlanChargeDtos = getDispatchAngelPlanCharge(cmd.getAngelDetailList(), snapshot);
            //成本价放入上下文
            context.setAngelPlanCharges(angelPlanChargeDtos);
            stopWatch.stop();
        }
        stopWatch.start("组装派单成功护士数据保存");
        if (retry) {
            RETRY_TEMPLATE.execute(retryContext -> jdhDispatchDomainService.callBack(context));
        } else {
            jdhDispatchDomainService.callBack(context);
        }
        stopWatch.stop();

        //创建服务者工单
        if (context.getCreateAngelWork()) {
            JdhAngelWorkSaveCmd workSaveCmd = DispatchApplicationConverter.INS.dispatch2AngelWorkSaveCmd(context.getJdhDispatch());
            //设置护士全兼职和服务站省市区
            if (Objects.nonNull(workSaveCmd.getAngelId())) {
                JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(AngelRequest.builder().angelId(workSaveCmd.getAngelId()).build());
                if (Objects.nonNull(jdhAngelDto)) {
                    workSaveCmd.setJobNature(jdhAngelDto.getJobNature());
                    if (Objects.nonNull(jdhAngelDto.getStationId()) && Objects.equals(jdhAngelDto.getJobNature(), JobNatureEnum.FULL_TIME.getValue())) {
                        StationQuery stationQuery = new StationQuery();
                        stationQuery.setStationIds(Sets.newHashSet(jdhAngelDto.getStationId()));
                        List<AngelStationDto> angelStationDtos = stationApplication.queryJdhStation(stationQuery);
                        if (CollectionUtils.isNotEmpty(angelStationDtos)) {
                            AngelStationDto angelStationDto = angelStationDtos.get(0);
                            workSaveCmd.setProvinceCode(StringUtils.isNotBlank(angelStationDto.getProvinceCode()) ? Integer.valueOf(angelStationDto.getProvinceCode()) : null);
                            workSaveCmd.setCityCode(StringUtils.isNotBlank(angelStationDto.getCityCode()) ? Integer.valueOf(angelStationDto.getCityCode()) : null);
                            workSaveCmd.setDistrictCode(StringUtils.isNotBlank(angelStationDto.getDistrictCode()) ? Integer.valueOf(angelStationDto.getDistrictCode()) : null);
                            workSaveCmd.setTownCode(StringUtils.isNotBlank(angelStationDto.getCountyCode()) ? Integer.valueOf(angelStationDto.getCountyCode()) : null);
                        }
                    }
                }
            }
            AngelWorkCreateResultDto angelWork = angelWorkApplication.createAngelWork(workSaveCmd);
            context.setWorkId(Objects.nonNull(angelWork) ? angelWork.getWorkId() : null);
        }
        //发送事件
        Event event = context.getDispatchEvent();
        if (Objects.nonNull(event)) {
            eventCoordinator.publish(event);
        }
        log.info("DispatchApplicationImpl -> disposeCallBack, stopWatch:{}", stopWatch.prettyPrint());
        return DispatchCallbackDto.builder().workId(context.getWorkId()).dispatchId(context.getDispatchId()).build();
    }

    /**
     * 获取护士结算价
     * @param angelId
     * @param snapshot
     * @return
     */
    private DispatchAngelPlanCharge getDispatchAngelPlanCharge(Long angelId,Integer jobNature,JdhDispatch snapshot) {
        //查询护士结算价
        JdServiceSettleParam serviceSettleParam = new JdServiceSettleParam();
        try {
            serviceSettleParam.setAngelId(angelId);
            //精准营养 不传orderId
            if(!VerticalEnum.isNonOrderVertical(snapshot.getVerticalCode())){
                serviceSettleParam.setOrderId(Long.valueOf(snapshot.getServiceInfo().getSourceVoucherId()));
            }
            serviceSettleParam.setPromiseId(snapshot.getPromiseId());
            serviceSettleParam.setShowSettleType(1);
            log.info("DispatchApplicationImpl -> getDispatchAngelPlanCharge getOrderSettleAmount start serviceSettleParam:{}", JSON.toJSONString(serviceSettleParam));
            ServerSettleAmountBo serverSettleAmountBo = jdServiceSettleApplication.getOrderSettleAmount(serviceSettleParam);
            BigDecimal orderSettleAmount = serverSettleAmountBo.getSettleTotalAmount();
            //成本价放入上下文
            if (Objects.nonNull(orderSettleAmount)) {
                return DispatchAngelPlanCharge.builder().angelId(String.valueOf(angelId)).orderSettleAmount(orderSettleAmount).jobNature(jobNature).dispatchMarkupPrice(BigDecimal.ZERO).build();
            }
        } catch (Throwable e){
            log.info("DispatchApplicationImpl -> getDispatchAngelPlanCharge getDispatchAngelPlanCharge error, serviceSettleParam:{}", JSON.toJSONString(serviceSettleParam), e);
        }
        return null;
    }

    /**
     * 获取护士结算价
     * @param angelDetailList
     * @param snapshot
     * @return
     */
    private List<DispatchAngelPlanCharge> getDispatchAngelPlanCharge(List<DispatchAngelDetail> angelDetailList, JdhDispatch snapshot) {
        if (CollectionUtils.isEmpty(angelDetailList)) {
            return new ArrayList<>();
        }
        //查询护士结算价
        JdServiceSettleParam serviceSettleParam = new JdServiceSettleParam();
        try {
            //精准营养 不传orderId
            if(!VerticalEnum.isNonOrderVertical(snapshot.getVerticalCode())){
                serviceSettleParam.setOrderId(Long.valueOf(snapshot.getServiceInfo().getSourceVoucherId()));
            }
            serviceSettleParam.setPromiseId(snapshot.getPromiseId());
            serviceSettleParam.setShowSettleType(1);
            serviceSettleParam.setJobNatureList(Lists.newArrayList(JobNatureEnum.FULL_TIME.getValue(), JobNatureEnum.PART_TIME.getValue()));
            serviceSettleParam.setProfessionTitleCodeList(Lists.newArrayList(angelDetailList.stream().map(dispatchAngelDetail -> CollectionUtils.isNotEmpty(dispatchAngelDetail.getProfessionTitleCodeList()) ? dispatchAngelDetail.getProfessionTitleCodeList().get(0) : null).filter(Objects::nonNull).collect(Collectors.toSet())));
            log.info("DispatchApplicationImpl -> getDispatchAngelPlanCharge getOrderSettleAmountDiagram start serviceSettleParam:{}", JSON.toJSONString(serviceSettleParam));
            Map<String, ServerSettleAmountBo> serverSettleAmountMap = jdServiceSettleApplication.getOrderSettleAmountDiagram(serviceSettleParam);

            return angelDetailList.stream().map(dispatchAngelDetail -> {
                ServerSettleAmountBo serverSettleAmountBo = serverSettleAmountMap.get(dispatchAngelDetail.getProfessionTitleCodeList().get(0) + "-" + dispatchAngelDetail.getJobNature());
                //成本价放入上下文
                if (Objects.nonNull(serverSettleAmountBo) && Objects.nonNull(serverSettleAmountBo.getSettleTotalAmount())) {
                    return DispatchAngelPlanCharge.builder()
                            .angelId(String.valueOf(dispatchAngelDetail.getAngelId()))
                            .orderSettleAmount(serverSettleAmountBo.getSettleTotalAmount())
                            .dispatchMarkupPrice(BigDecimal.ZERO)
                            .jobNature(dispatchAngelDetail.getJobNature()).build();
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
        } catch (Throwable e){
            log.info("DispatchApplicationImpl -> getDispatchAngelPlanCharge getOrderSettleAmountDiagram error, serviceSettleParam:{}", JSON.toJSONString(serviceSettleParam), e);
        }
        return null;
    }

    /**
     * convertAngelWorkDto
     * @param dispatchDetail
     * @param location
     * @return
     */
    private AngelWorkDto convertAngelWorkDto(JdhDispatchDetail dispatchDetail, AngelLocationDto location) {
        //查询派单任务信息
        JdhDispatch dispatch = dispatchRepository.findDispatch(DispatchRepQuery.builder().dispatchId(dispatchDetail.getDispatchId()).build());
        //组装返回值
        if (Objects.isNull(dispatch)) {
            return null;
        }
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(dispatch.getPromiseId());
        PromiseDto promiseDto = promiseApplication.findByPromiseId(promiseIdRequest);
        if (Objects.isNull(promiseDto)) {
            return null;
        }
        //查询项目及耗材信息
        Set<Long> itemIds = dispatch.getServiceInfo().getPatients().stream().flatMap(dispatchAppointmentPatientDto -> dispatchAppointmentPatientDto.getServiceItems().stream()).map(ServiceItem::getItemId).collect(Collectors.toSet());
        List<ServiceItemDto> serviceItemDtos = productServiceItemApplication.queryServiceItemList(ServiceItemQuery.builder().itemIds(itemIds).build());
        Map<Long, ServiceItemDto> itemDtoMap = serviceItemDtos.stream().collect(Collectors.toMap(ServiceItemDto::getItemId, Function.identity(), (o1, o2) -> o2));
        //护士上门检测类型，需要查询对应实验室履约单，拿到服务项目及分派的实验室。在页面展示服务要求、样本存放要求等
        Map<Long, JdhStationServiceItemRel> serviceItem2StationItemRelDto = new HashMap<>();
        JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(dispatch.getVerticalCode());
        ArrayList<String> businessModeCodeList = Lists.newArrayList(BusinessModeEnum.ANGEL_TEST.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode());
        if (Objects.nonNull(jdhVerticalBusiness) && businessModeCodeList.contains(jdhVerticalBusiness.getBusinessModeCode())) {
            log.info("DispatchApplicationImpl -> convertAngelWorkDto 护士上门检测查询实验室履约单，拿到服务项目及分派的实验室。在页面展示服务要求、样本存放要求 dispatchId={}, promiseId={}", dispatch.getDispatchId(), dispatch.getPromiseId());
            //查询实验室履约单
            MedicalPromiseListRequest promiseListRequest = new MedicalPromiseListRequest();
            promiseListRequest.setPromiseIdList(Lists.newArrayList(dispatch.getPromiseId()));
            promiseListRequest.setInvalid(false);
            List<MedicalPromiseDTO> medicalPromises = medicalPromiseApplication.queryMedicalPromiseList(promiseListRequest);
            log.info("DispatchApplicationImpl -> convertAngelWorkDto medicalPromises={}",JSON.toJSONString(medicalPromises));
            //如果分派了实验室，查询实验室对应的项目配置信息
            List<JdhStationServiceItemRel> params = medicalPromises.stream().filter(medicalPromiseDTO -> StringUtils.isNotBlank(medicalPromiseDTO.getServiceItemId()) && StringUtils.isNotBlank(medicalPromiseDTO.getStationId())).map(medicalPromiseDTO -> JdhStationServiceItemRel.builder().serviceItemId(Long.valueOf(medicalPromiseDTO.getServiceItemId())).stationId(medicalPromiseDTO.getStationId()).build()).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(params)) {
                //查询实验室对应服务项目信息
                List<JdhStationServiceItemRel> jdhStationServiceItemRels = providerStoreRepository.queryStationServiceItemList(params);
                //实验室ID：服务项目id：数据
                HashBasedTable<String, Long, JdhStationServiceItemRel> objectObjectObjectHashBasedTable = HashBasedTable.create();
                jdhStationServiceItemRels.forEach(jdhStationServiceItemRel -> {
                    objectObjectObjectHashBasedTable.put(jdhStationServiceItemRel.getStationId(), jdhStationServiceItemRel.getServiceItemId(), jdhStationServiceItemRel);
                });
                //遍历分派了实验室的实验室履约单，获取服务项目对应的实验室配置：服务项目ID：配置信息
                medicalPromises.stream().filter(medicalPromiseDTO -> StringUtils.isNotBlank(medicalPromiseDTO.getServiceItemId()) && StringUtils.isNotBlank(medicalPromiseDTO.getStationId())).forEach(medicalPromiseDTO -> {
                    JdhStationServiceItemRel serviceItemRel = objectObjectObjectHashBasedTable.get(medicalPromiseDTO.getStationId(), Long.valueOf(medicalPromiseDTO.getServiceItemId()));
                    if (Objects.nonNull(serviceItemRel)) {
                        serviceItem2StationItemRelDto.put(serviceItemRel.getServiceItemId(), serviceItemRel);
                    }
                });
            }
        }
        log.info("DispatchApplicationImpl -> convertAngelWorkDto, serviceItem2StationItemRelDto={}", JSON.toJSONString(serviceItem2StationItemRelDto));
        JdhDispatchDetailDto detailDto = DispatchApplicationConverter.INS.dispatchDetail2Dto(dispatchDetail, dispatch, itemDtoMap, serviceItem2StationItemRelDto, promiseDto);
        detailDto.setDispatchDetailUrl(MessageFormat.format(duccConfig.getDispatchDetailUrl(), detailDto.getDispatchDetailId()));
        //查询订单信息
        if (StringUtils.isNotBlank(dispatch.getServiceInfo().getSourceVoucherId())) {
            JdOrder jdOrder = jdOrderApplication.queryJdOrderAndItemExt(Long.valueOf(dispatch.getServiceInfo().getSourceVoucherId()));
            log.info("DispatchApplicationImpl -> convertAngelWorkDto, jdOrder={}",JSON.toJSONString(jdOrder));
            JdOrderExt jdOrderExtDetail = null;
            if (Objects.nonNull(jdOrder) && CollectionUtils.isNotEmpty(jdOrder.getJdOrderExtList())) {
                jdOrderExtDetail = jdOrder.getJdOrderExtList().stream().filter(ext -> Objects.equals(JdOrderExtTypeEnum.APPOINTMENT_INFO.getExtType(), ext.getExtType())).findFirst().get();
            }
            //JdOrderDTO orderDetail = tradeApplication.getOrderDetail(OrderDetailParam.builder().orderId().build());
            if(Objects.nonNull(jdOrderExtDetail) && StringUtils.isNotBlank(jdOrderExtDetail.getExtContext())) {
                OrderAppointmentInfoValueObject orderAppointmentInfo = JsonUtil.parseObject(jdOrderExtDetail.getExtContext(),OrderAppointmentInfoValueObject.class);
                detailDto.setOrderUserName(Objects.nonNull(orderAppointmentInfo) && Objects.nonNull(orderAppointmentInfo.getAddressInfo()) ? new UserName(orderAppointmentInfo.getAddressInfo().getName()).mask() : null);
                detailDto.setOrderUserPhone(Objects.nonNull(orderAppointmentInfo) && Objects.nonNull(orderAppointmentInfo.getAddressInfo()) ? new PhoneNumber(orderAppointmentInfo.getAddressInfo().getMobile()).mask() : null);
                detailDto.setSubmitOrderTime(jdOrder.getCreateTime());
            }
        }
        log.info("DispatchApplicationImpl -> convertAngelWorkDto, itemDtoMap={}", JSON.toJSONString(itemDtoMap));
        if (Objects.nonNull(location)) {
            DirectionResultBO directionResult = directionServiceRpc.getDirectionResult(DirectionRequestParam.builder()
                    .fromLocation(String.format("%s,%s", location.getLatitude(), location.getLongitude()))
                    .toLocation(String.format("%s,%s", dispatch.getServiceInfo().getGisPoint().getLatitude(), dispatch.getServiceInfo().getGisPoint().getLongitude()))
                    .travelMode(DirectionServiceRpc.TravelMode.BICYCLING).build());
            if (Objects.nonNull(directionResult)) {
                detailDto.getServiceLocation().setDuration(directionResult.getDuration());
                detailDto.getServiceLocation().setDistance(directionResult.getDistance());
            }
        }
        log.info("DispatchApplicationImpl -> convertAngelWorkDto, detailDto={}", JSON.toJSONString(detailDto));
        return DispatchApplicationConverter.INS.dispatch2AngelWorkDto(detailDto, dispatch);
    }

    /**
     *
     * @param detailList
     * @param location
     * @return
     */
    private List<JdhDispatchDetailDto> attachDispatchAndLocation(List<JdhDispatchDetail> detailList, AngelLocationDto location) {
        //查询派单任务信息
        List<Long> dispatchIdList = detailList.stream().map(JdhDispatchDetail::getDispatchId).collect(Collectors.toList());
        List<JdhDispatch> dispatchList = dispatchRepository.findDispatchList(DispatchRepQuery.builder().dispatchIdList(dispatchIdList).dispatchStatusNotInList(Lists.newArrayList(JdhDispatchStatusEnum.DISPATCH_INVALID.getStatus())).build());
        Map<Long, JdhDispatch> id2Dispatch = dispatchList.stream().collect(Collectors.toMap(JdhDispatch::getDispatchId, jdhDispatch -> jdhDispatch, (t, t2) -> t2));
        //查询项目及耗材信息
        Set<Long> itemIds = dispatchList.stream().flatMap(jdhDispatch -> jdhDispatch.getServiceInfo().getPatients().stream()).flatMap(dispatchAppointmentPatientDto -> dispatchAppointmentPatientDto.getServiceItems().stream()).map(ServiceItem::getItemId).collect(Collectors.toSet());
        List<ServiceItemDto> serviceItemDtos = productServiceItemApplication.queryServiceItemList(ServiceItemQuery.builder().itemIds(itemIds).build());
        Map<Long, ServiceItemDto> itemDtoMap = serviceItemDtos.stream().collect(Collectors.toMap(ServiceItemDto::getItemId, Function.identity(), (o1, o2) -> o2));
        //组装返回值
        List<JdhDispatchDetailDto> list = Lists.newArrayListWithCapacity(detailList.size());
        CompletableFuture[] futures = new CompletableFuture[detailList.size()];
        int futureIndex = 0;
        for (JdhDispatchDetail dispatchDetail : detailList) {
            JdhDispatch jdhDispatch = id2Dispatch.get(dispatchDetail.getDispatchId());
            if (Objects.isNull(jdhDispatch)) {
                continue;
            }
            PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
            promiseIdRequest.setPromiseId(jdhDispatch.getPromiseId());
            PromiseDto promiseDto = promiseApplication.findByPromiseId(promiseIdRequest);
            if(Objects.isNull(promiseDto)) {
                log.info("DispatchApplicationImpl -> attachDispatchAndLocation, 预约单不存在,promiseIdRequest={}", JSON.toJSONString(promiseIdRequest));
                continue;
            }
            JdhDispatchDetailDto detailDto = DispatchApplicationConverter.INS.dispatchDetail2Dto(dispatchDetail, jdhDispatch, itemDtoMap, promiseDto);
            detailDto.setDispatchDetailUrl(MessageFormat.format(duccConfig.getDispatchDetailUrl(), detailDto.getDispatchDetailId().toString()));
            list.add(detailDto);
            if (Objects.nonNull(location)) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    DirectionResultBO directionResult = directionServiceRpc.getDirectionResult(DirectionRequestParam.builder()
                            .fromLocation(String.format("%s,%s", location.getLatitude(), location.getLongitude()))
                            .toLocation(String.format("%s,%s", jdhDispatch.getServiceInfo().getGisPoint().getLatitude(), jdhDispatch.getServiceInfo().getGisPoint().getLongitude()))
                            .travelMode(DirectionServiceRpc.TravelMode.BICYCLING).build());
                    if (Objects.nonNull(directionResult)) {
                        detailDto.getServiceLocation().setDuration(directionResult.getDuration());
                        //距离除以1000，转换为km
                        detailDto.getServiceLocation().setDistance(Objects.nonNull(directionResult.getDistance()) ? BigDecimal.valueOf(directionResult.getDistance()).divide(new BigDecimal(1000), 1, RoundingMode.HALF_UP).doubleValue() : null);

                    }
                }, executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
                futures[futureIndex++] = future;
            }
        }
        try {
            CompletableFuture.allOf(futures).get();
        } catch (Exception e) {
            log.error("DispatchApplicationImpl -> queryDispatchWaitingReceiveList CompletableFuture.allOf fail", e);
        }
        return list;
    }

    /**
     * 服务者派单操作
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(alarmPolicy = {
        @AlarmPolicy(type = AlarmPolicyTypeEnum.UMP),
        @AlarmPolicy(methodName = "服务者接单操作", keyword = "dispatchDetailId", type = AlarmPolicyTypeEnum.DONGDONG_ROBOT)
    })
    public AngelDispatchOperationDto angelDispatchOperate(AngelDispatchOperationCmd cmd) {
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(cmd.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            AngelDispatchOperationDto operationDto = new AngelDispatchOperationDto();
            operationDto.setErrorCode(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION.getCode());
            operationDto.setDescription(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION.getDescription());
            return operationDto;
        }
        JdhDispatchDetail dispatchDetail = dispatchRepository.findDispatchDetail(DispatchDetailRepQuery.builder().angelId(jdhAngelDto.getAngelId()).dispatchDetailId(cmd.getDispatchDetailId()).build());
        if (Objects.isNull(dispatchDetail)) {
            AngelDispatchOperationDto operationDto = new AngelDispatchOperationDto();
            operationDto.setErrorCode(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION.getCode());
            operationDto.setDescription(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION.getDescription());
            return operationDto;
        }
        if (DateUtil.date().after(dispatchDetail.getExpireDate()) || Objects.equals(dispatchDetail.getDispatchDetailStatus(), JdhDispatchDetailStatusEnum.DISPATCH_EXPIRED.getStatus())) {
            DispatchErrorCode errorCode = Objects.equals(DispatchDetailTypeEnum.ASSIGN.getType(), dispatchDetail.getDispatchDetailType()) ?
                    DispatchErrorCode.DISPATCH_RECEIVE_ASSIGN_ORDER_ERROR : DispatchErrorCode.DISPATCH_RECEIVE_GRAB_ORDER_ERROR;
            log.error("DispatchApplicationImpl -> angelDispatchOperate 接单任务已过期, dispatchDetail={}, errorCode={}", JSON.toJSONString(dispatchDetail), JSON.toJSONString(errorCode));
            AngelDispatchOperationDto operationDto = new AngelDispatchOperationDto();
            operationDto.setErrorCode(errorCode.getCode());
            operationDto.setDescription(errorCode.getDescription());
            return operationDto;
        }
        //确认接单
        Long workId = null;
        AngelDispatchOperationDto operationDto = new AngelDispatchOperationDto();
        if (Objects.equals(cmd.getOperation(), 1)) {
            JdhDispatch dispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(dispatchDetail.getDispatchId()).build());
            if (Objects.isNull(dispatch)) {
                operationDto.setErrorCode(DispatchErrorCode.DISPATCH_RECEIVE_GRAB_ORDER_ERROR.getCode());
                operationDto.setDescription(DispatchErrorCode.DISPATCH_RECEIVE_GRAB_ORDER_ERROR.getDescription());
                return operationDto;
            }
            //非精准营养 校验订单状态
            if(!VerticalEnum.isNonOrderVertical(dispatch.getVerticalCode())){
                Boolean processingOrder = jdOrderApplication.isProcessingOrder(Long.valueOf(dispatch.getServiceInfo().getSourceVoucherId()));
                if (!processingOrder) {
                    log.error("DispatchApplicationImpl -> angelDispatchOperate 订单非进行中, processingOrder={}", processingOrder);
                    AngelDispatchOperationDto noProcessDto = new AngelDispatchOperationDto();
                    noProcessDto.setErrorCode(DispatchErrorCode.DISPATCH_RECEIVE_ORDER_NOT_PROCESS_ERROR.getCode());
                    noProcessDto.setDescription(DispatchErrorCode.DISPATCH_RECEIVE_ORDER_NOT_PROCESS_ERROR.getDescription());
                    return noProcessDto;
                }
            }
            //校验护士技能是否匹配
            ///查询sku所需技能的详细信息
            JdhDispatch jdhDispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(dispatchDetail.getDispatchId()).build());
            Set<String> skuSkillCodeList = jdhDispatch.getServiceInfo().getPatients().stream()
                    .flatMap(appointmentPatient -> appointmentPatient.getServiceItems().stream())
                    .flatMap(serviceItem -> Optional.ofNullable(serviceItem.getItemSkillList()).orElseGet(Collections::emptyList).stream())
                    .map(JdhDispatchServiceItemSkillRel::getAngelSkillCode)
                    .collect(Collectors.toSet());
            log.info("skuSkillCodeList = {}",JSON.toJSONString(skuSkillCodeList));
            ///查询护士技能
            List<DispatchAngelSkillRelBO> dispatchAngelSkillRelBOList = dispatchFlowDependRpc.queryAngelSkillListByAngelInfo(DispatchAngelSkillDictParam.builder().angelIdList(Arrays.asList(jdhAngelDto.getAngelId())).build());
            Set<String> angelSkills = dispatchAngelSkillRelBOList.stream().map(DispatchAngelSkillRelBO::getAngelSkillCode).collect(Collectors.toSet());
            log.info("angelSkills = {}",JSON.toJSONString(angelSkills));

            ///护士不匹配的技能
            if(!angelSkills.containsAll(skuSkillCodeList)){
                operationDto.setErrorCode(DispatchErrorCode.DISPATCH_SKILL_NOT_ALLOW_OPERATION.getCode());
                skuSkillCodeList.removeAll(angelSkills);

                AngelSkillDictRepQuery query = new AngelSkillDictRepQuery();
                query.setAngelSkillCodeList(new ArrayList<>(skuSkillCodeList));
                //根据技能code查询技能信息
                List<JdhAngelSkillDict> skillDictList = angelSkillDictRepository.findList(query);
                operationDto.setDescription(DispatchErrorCode.DISPATCH_SKILL_NOT_ALLOW_OPERATION.formatDescription(Joiner.on("、").join(skillDictList.stream().map(t->"\""+t.getAngelSkillName()+"\"").collect(Collectors.toList()))).getDescription());
                return operationDto;
            }
            //生成工单
            workId = angelReceive(operationDto, cmd, dispatch, dispatchDetail);
        }
        //拒绝接单
        else if (Objects.equals(cmd.getOperation(), 2)) {
            jdhDispatchDomainService.angelRefuse(DispatchApplicationConverter.INS.cmd2AngelRefuseDispatchContext(cmd));
        }
        operationDto.setDispatchId(dispatchDetail.getDispatchId());
        operationDto.setDispatchDetailId(dispatchDetail.getDispatchDetailId());
        operationDto.setWorkId(workId);
        if(Objects.nonNull(workId)){
            operationDto.setWorkIdDetailUrl(String.format(duccConfig.getWorkIdDetailUrl(),workId));
        }
        return operationDto;
    }

    /**
     * 护士接单前提醒
     *
     * @param cmd
     * @return
     */
    @Override
    public AngelDispatchOperationDto angelDispatchTips(AngelDispatchOperationCmd cmd) {
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(cmd.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            AngelDispatchOperationDto operationDto = new AngelDispatchOperationDto();
            operationDto.setErrorCode(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION.getCode());
            operationDto.setDescription(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION.getDescription());
            return operationDto;
        }
        JdhDispatchDetail dispatchDetail = dispatchRepository.findDispatchDetail(DispatchDetailRepQuery.builder().angelId(jdhAngelDto.getAngelId()).dispatchDetailId(cmd.getDispatchDetailId()).build());
        if (Objects.isNull(dispatchDetail)) {
            AngelDispatchOperationDto operationDto = new AngelDispatchOperationDto();
            operationDto.setErrorCode(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION.getCode());
            operationDto.setDescription(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION.getDescription());
            return operationDto;
        }
        if (DateUtil.date().after(dispatchDetail.getExpireDate()) || Objects.equals(dispatchDetail.getDispatchDetailStatus(), JdhDispatchDetailStatusEnum.DISPATCH_EXPIRED.getStatus())) {
            DispatchErrorCode errorCode = Objects.equals(DispatchDetailTypeEnum.ASSIGN.getType(), dispatchDetail.getDispatchDetailType()) ?
                    DispatchErrorCode.DISPATCH_RECEIVE_ASSIGN_ORDER_ERROR : DispatchErrorCode.DISPATCH_RECEIVE_GRAB_ORDER_ERROR;
            log.error("DispatchApplicationImpl -> angelDispatchOperate 接单任务已过期, dispatchDetail={}, errorCode={}", JSON.toJSONString(dispatchDetail), JSON.toJSONString(errorCode));
            AngelDispatchOperationDto operationDto = new AngelDispatchOperationDto();
            operationDto.setErrorCode(errorCode.getCode());
            operationDto.setDescription(errorCode.getDescription());
            return operationDto;
        }

        //抢单后->临期单/相邻单-提醒 临期单提醒>相邻单提醒
        ///临期单提醒
        AngelDispatchOperationDto operationDto = new AngelDispatchOperationDto();
        JdhDispatch dispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(dispatchDetail.getDispatchId()).build());
        if (Objects.isNull(dispatch)) {
            operationDto.setErrorCode(DispatchErrorCode.DISPATCH_RECEIVE_GRAB_ORDER_ERROR.getCode());
            operationDto.setDescription(DispatchErrorCode.DISPATCH_RECEIVE_GRAB_ORDER_ERROR.getDescription());
            return operationDto;
        }
        Date appointmentStartTime = Date.from(dispatch.getServiceInfo().getAppointmentTime().getAppointmentStartTime().atZone(ZoneId.systemDefault()).toInstant());
        if(TimeUtils.add(appointmentStartTime, Calendar.MINUTE,duccConfig.getTimeToVisitMinute()).before(new Date())){
            ////预约上门时间距离当前时间 < 60 分钟的单子
            ////护士当前位置到预约地址的公共交通时长超过 60 分钟，则提醒：
            AngelLocationDto location = angelLocationApplication.getLocation(jdhAngelDto.getAngelId());
            if(location!=null&&location.getLatitude()!=null){
                DirectionResultBO directionResult = directionServiceRpc.getDirectionResult(DirectionRequestParam.builder()
                        .fromLocation(String.format("%s,%s", location.getLatitude(), location.getLongitude()))
                        .toLocation(String.format("%s,%s", dispatch.getServiceInfo().getGisPoint().getLatitude(), dispatch.getServiceInfo().getGisPoint().getLongitude()))
                        .travelMode(DirectionServiceRpc.TravelMode.BICYCLING).build());
                if(directionResult==null){
                    /////directionResult未查询到结果,走兜底逻辑!!!
                    log.info("directionResult为空,未查询到预估上门所需时间,走弹框兜底逻辑!!!");
                    operationDto.setBusinessCode(DispatchErrorCode.DISPATCH_TIME_TO_VISIT_REMIND.getCode());
                    operationDto.setBusinessMsg(DispatchErrorCode.DISPATCH_TIME_TO_VISIT_REMIND.getDescription());
                }else{
                    if(directionResult.getDuration()>Math.abs(duccConfig.getTimeToVisitMinute())){
                        operationDto.setBusinessCode(DispatchErrorCode.DISPATCH_TIME_TO_VISIT_REMIND.getCode());
                        operationDto.setBusinessMsg(DispatchErrorCode.DISPATCH_TIME_TO_VISIT_REMIND.getDescription());
                    }
                }

            }else{
                /////兜底逻辑
                log.info("location为空,未查询到护士位置信息,走弹框兜底逻辑!!!");
                operationDto.setBusinessCode(DispatchErrorCode.DISPATCH_TIME_TO_VISIT_REMIND.getCode());
                operationDto.setBusinessMsg(DispatchErrorCode.DISPATCH_TIME_TO_VISIT_REMIND.getDescription());
            }
        }
        if(StringUtils.isEmpty(operationDto.getBusinessCode())){
            ///相邻单提醒
            AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
            angelWorkDBQuery.setStatusList(AngelWorkStatusEnum.getWaitServiceStatus());
            angelWorkDBQuery.setAngelPin(cmd.getUserPin());
            angelWorkDBQuery.setServiceStartTimeBegin(DateUtils.addMinutes(appointmentStartTime,-1*duccConfig.getWorkStartTimeOffsetMinute()));
            angelWorkDBQuery.setServiceStartTimeEnd(DateUtils.addMinutes(appointmentStartTime,duccConfig.getWorkStartTimeOffsetMinute()));
            List<AngelWork> angelWorks = angelWorkRepository.findList(angelWorkDBQuery);
            if(angelWorks.size()>1){
                operationDto.setBusinessCode(DispatchErrorCode.DISPATCH_TIME_TO_VISIT_CONFLICT_REMIND.getCode());
                operationDto.setBusinessMsg(DispatchErrorCode.DISPATCH_TIME_TO_VISIT_CONFLICT_REMIND.getDescription());
            }
        }
        operationDto.setDispatchId(dispatchDetail.getDispatchId());
        operationDto.setDispatchDetailId(dispatchDetail.getDispatchDetailId());
        return operationDto;
    }

    /**
     * 服务者接单
     * @param cmd
     * @param dispatch
     * @param dispatchDetail
     * @return
     */
    private Long angelReceive(AngelDispatchOperationDto operationDto, AngelDispatchOperationCmd cmd, JdhDispatch dispatch, JdhDispatchDetail dispatchDetail) {
        Long workId = null;
        //分布式锁
        String lockRedisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.DISPATCH_RECEIVING_ORDER_LOCK_KEY, dispatch.getDispatchId());
        try {
            boolean lock = redisUtil.tryLock(lockRedisKey, RedisKeyEnum.DISPATCH_RECEIVING_ORDER_LOCK_KEY.getExpireTime(), RedisKeyEnum.DISPATCH_RECEIVING_ORDER_LOCK_KEY.getExpireTimeUnit());
            if (!lock) {
                throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
            }
            //冻结状态不允许接单
            if(Objects.equals(dispatch.getFreeze(), 1)) {
                throw new BusinessException(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION);
            }

            DispatchCallbackCmd callbackCmd = new DispatchCallbackCmd();
            callbackCmd.setUserPin(cmd.getUserPin());
            callbackCmd.setDispatchId(dispatch.getDispatchId());
            callbackCmd.setPromiseId(dispatch.getPromiseId());
            callbackCmd.setDispatchStatus(JdhDispatchStatusEnum.DISPATCH_RECEIVED.getStatus());
            callbackCmd.setDispatchType(DispatchTypeEnum.valuesOf(dispatch.getDispatchType()));
            callbackCmd.setAngelDetailList(Lists.newArrayList(DispatchAngelDetail.builder().angelId(dispatchDetail.getAngelId()).build()));
            DispatchApplicationImpl dispatchApplicationProx = Convert.convert(DispatchApplicationImpl.class, AopContext.currentProxy());
            DispatchCallbackDto dispatchCallbackDto = dispatchApplicationProx.angelReceive(callbackCmd);
            workId = dispatchCallbackDto.getWorkId();
        } catch (BusinessException e){
            log.info("DispatchApplicationImpl -> angelReceive business exception", e);
            operationDto.setErrorCode(e.getErrorCode().getCode());
            operationDto.setDescription(e.getErrorCode().getDescription());
            return workId;
        } finally {
            redisUtil.unLock(lockRedisKey);
        }
        return workId;
    }

    /**
     * 服务者操作接单按钮
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.angelDispatchSwitchOperate")
    public AngelDispatchSwitchDto angelDispatchSwitchOperate(AngelDispatchSwitchCmd cmd) {
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(cmd.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_STATUS_NOT_ALLOW_OPERATION);
        }
        //1.服务者开关诊
        //2.调用互医开关诊
        AngelUpdateTakeOrderStatusCmd takeOrderStatusCmd = new AngelUpdateTakeOrderStatusCmd();
        takeOrderStatusCmd.setAngelId(jdhAngelDto.getAngelId());
        takeOrderStatusCmd.setTakeOrderStatus(Objects.equals(cmd.getOperation(), 2) ? 0 : 1);
        angelApplication.updateTakeOrderStatus(takeOrderStatusCmd);

        //3.返回成功
        return AngelDispatchSwitchDto.builder()
                .angelId(jdhAngelDto.getAngelId())
                .operation(cmd.getOperation()).build();
    }

    /**
     * 查询围栏命中的护士ID列表
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.dispatch.service.impl.DispatchApplicationImpl.getSelectedByLocation")
    public LocationSelectedDTO getSelectedByLocation(LocationSelectedRequest request) {
        if (Objects.isNull(request) || (Objects.isNull(request.getDiagId()) && Objects.isNull(request.getBusinessId()))) {
            return LocationSelectedDTO.builder().doctorList(Lists.newArrayList()).build();
        }
        JdhDispatch dispatch = null;
        if (Objects.nonNull(request.getBusinessId())) {
            dispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(Long.valueOf(request.getBusinessId())).build());
        } else {
            dispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().outDispatchId(request.getDiagId()).build());
        }
        if (Objects.isNull(dispatch)) {
            return LocationSelectedDTO.builder().doctorList(Lists.newArrayList()).build();
        }
        //上门地址
        String locationDetail = dispatch.getServiceLocation().getServiceLocationDetail();
        //sku列表
        Set<Long> skuNoList = dispatch.getServiceInfo().getPatients().stream().flatMap(patient -> patient.getServiceItems().stream()).map(ServiceItem::getServiceId).collect(Collectors.toSet());
        log.info("DispatchApplicationImpl -> getSelectedByLocation start queryAngelListByGeo, locationDetail={}, skuNoList={}", locationDetail, JSON.toJSONString(skuNoList));
        List<JdhAngelDto> list = stationApplication.queryAngelListByGeo(AngelGeoQuery.builder().address(locationDetail).skuNos(skuNoList).build());
        //返回主数据ID列表
        List<Long> doctorList = list.stream().map(JdhAngelDto::getNethpDocId).filter(Objects::nonNull).collect(Collectors.toList());
        return LocationSelectedDTO.builder().doctorList(doctorList).build();
    }

    /**
     * 查询派单历史记录列表
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public List<JdhDispatchHistoryDto> queryDispatchHistoryListByPromiseId(DispatchHistoryListRequest request) {
        List<JdhDispatch> dispatchList = dispatchRepository.findDispatchList(DispatchRepQuery.builder().dispatchId(request.getDispatchId()).promiseId(request.getPromiseId()).build());
        if (CollectionUtils.isEmpty(dispatchList)) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_NOT_EXIST);
        }
        List<Long> dispatchIdList = dispatchList.stream().map(JdhDispatch::getDispatchId).collect(Collectors.toList());
        List<JdhDispatchHistory> list = dispatchHistoryRepository.findList(DispatchHistoryRepQuery.builder().dispatchIdList(dispatchIdList).build());
        return DispatchApplicationConverter.INS.jdhDispatchHistory2Dto(list);
    }

    /**
     * 查询派单任务详情
     * @param request
     * @return
     */
    @Override
    public JdhDispatchDto queryDispatchInfo(DispatchQueryRequest request) {
        JdhDispatch jdhDispatch = dispatchRepository.findValidDispatchWithDetail(DispatchRepQuery.builder().dispatchId(request.getDispatchId()).promiseId(request.getPromiseId()).build());
        if (Objects.isNull(jdhDispatch)) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_NOT_EXIST);
        }
        return DispatchApplicationConverter.INS.JdhDispatch2Dto(jdhDispatch);
    }

    /**
     * 派单拉完成
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean dispatchComplete(DispatchCompleteCmd cmd) {
        //查询派单任务信息
        JdhDispatch dispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().promiseId(cmd.getPromiseId()).build());
        //组装返回值
        if (Objects.isNull(dispatch)) {
            return false;
        }

        DispatchCompleteContext context = new DispatchCompleteContext();
        context.setPromiseId(cmd.getPromiseId());
        context.setDispatch(dispatch);
        context.setVerticalCode(dispatch.getVerticalCode());
        context.setServiceType(dispatch.getServiceType());
        context.initVertical();
        return jdhDispatchDomainService.dispatchComplete(context);
    }

    /**
     * 执行派单流程
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm
    @Transactional(rollbackFor = Exception.class)
    public Boolean dispatchFlowExecuteHandle(AngelDispatchCmd cmd) {
        log.info("DispatchApplicationImpl -> dispatchFlowExecuteHandle cmd:{}", JSON.toJSONString(cmd));
        // 派单前置操作
        dispatchFlowExecuteHandlePre(cmd);

        DispatchRepQuery dispatchRepQuery = new DispatchRepQuery();
        dispatchRepQuery.setDispatchId(cmd.getDispatchId());
        JdhDispatch dispatch = dispatchRepository.findValidDispatch(dispatchRepQuery);

        AngelDispatchContext context = DispatchApplicationConverter.INS.cmd2AngelDispatchContext(cmd);
        context.init(dispatch);
        //查询所需技能的详细信息
        List<String> skillCodeList = dispatch.getServiceInfo().getPatients().stream()
                .flatMap(appointmentPatient -> appointmentPatient.getServiceItems().stream())
                .flatMap(serviceItem -> Optional.ofNullable(serviceItem.getItemSkillList()).orElseGet(Collections::emptyList).stream())
                .map(JdhDispatchServiceItemSkillRel::getAngelSkillCode)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(skillCodeList)) {
            //查询技能信息
            List<JdhAngelSkillDict> skillList = angelSkillDictRepository.findList(AngelSkillDictRepQuery.builder().angelSkillCodeList(skillCodeList).build());
            if (CollectionUtils.isNotEmpty(skillList)) {
                List<String> serviceGroupIdList = skillList.stream().map(JdhAngelSkillDict::getServiceGroupId).distinct().collect(Collectors.toList());
                context.setServiceGroupIdList(serviceGroupIdList);
                List<String> angelSkillCodeList = skillList.stream().map(JdhAngelSkillDict::getAngelSkillCode).distinct().collect(Collectors.toList());
                context.setAngelSkillCodeList(angelSkillCodeList);
            }
        }
        DispatchFlowResultEventBody resultEventBody = jdhDispatchDomainService.executeDispatch(context);
        Event event = null;
        if (resultEventBody.isDispatchResult()) {
            event = EventFactory.newDefaultEvent(context.getJdhDispatch(), DispatchEventTypeEnum.DISPATCH_FLOW_SUCCESS, resultEventBody);
        } else {
            event = EventFactory.newDefaultEvent(context.getJdhDispatch(), DispatchEventTypeEnum.DISPATCH_FLOW_FAIL, resultEventBody);
        }
        eventCoordinator.publish(event);
        return true;
    }


    /**
     * 派单流程执行失败处理
     * @param dispatchCmd
     */
    public void dispatchFlowExecuteHandlePre(AngelDispatchCmd dispatchCmd) {

        if (StringUtils.equals(dispatchCmd.getFlowExecuteType(), FlowExecuteTypeEnum.MULTIPLE_ROUNDS.getType())
                || StringUtils.equals(dispatchCmd.getFlowExecuteType(), FlowExecuteTypeEnum.INTENDED_BACK.getType())
                || StringUtils.equals(dispatchCmd.getFlowExecuteType(), FlowExecuteTypeEnum.STANDARD_ASSIGN_ROUNDS_BACK.getType())){
            RecoverDispatchCmd cmd = new RecoverDispatchCmd();
            cmd.setDispatchId(dispatchCmd.getDispatchId());
            // 回收派单任务，将dispatchDetail过期
            recoverDispatch(cmd);
            // 重置flow状态
            JdhDispatchFlowTaskIdentifier id = JdhDispatchFlowTaskIdentifier.builder().flowTaskId(dispatchCmd.getFlowId()).build();
            JdhDispatchFlowTask task = dispatchFlowTaskRepository.find(id);
            task.setFlowStatus(DispatchFlowTaskStatusEnum.DISPATCH_FLOW_EXECUTING.getStatus());
            dispatchFlowTaskRepository.save(task);
        }
    }
    /**
     * 处理互医侧派单
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean handleNewNethpDispatch(DispatchNewNethpHandleCmd cmd) {
        switch (cmd.getType()){
            case 1:
                newNethpDispatchRpc.submitOrder(JSON.parseObject(cmd.getCommand(), NewNethpDiagOrderParam.class));
                break;
            case 2:
                newNethpDispatchRpc.doctorReply(JSON.parseObject(cmd.getCommand(), NewNethpDiagDoctorReplyParam.class));
                break;
            case 3:
                dispatchFlowDelegate.transferDispatch(JSON.parseObject(cmd.getCommand(), NewNethpOrderTransferByDoctorParam.class));
                break;
            case 4:
                //{"diagId": 808192848532431,"operator":"admin","reason":"接单前重新派单","reasonType":6,"roleType":2,"targetType":3,"tenantType":"JD8888"}
                newNethpDispatchRpc.reappointDiagBeforeReplyDefaultFromInitial(JSON.parseObject(cmd.getCommand(), NewNethpOrderTransferByDoctorParam.class));
                break;
            case 5:
                newNethpDispatchRpc.reappointDiagBeforeReplyToDoctor(JSON.parseObject(cmd.getCommand(), NewNethpOrderTransferByDoctorParam.class));
                break;
            case 6:
                newNethpDispatchRpc.suspendDiagAssign(JSON.parseObject(cmd.getCommand(), NewNethpDiagAssignSuspendParam.class));
                break;
            case 7:
                dispatchFlowDelegate.cancelDispatch(JSON.parseObject(cmd.getCommand(), NewNethpTradeCancelDiagParam.class));
                break;
            case 8:
                dispatchFlowDelegate.dispatchComplete(JSON.parseObject(cmd.getCommand(), NewNethpTradeEndDiagParam.class));
                break;
            default:
                break;
        }

        return Boolean.TRUE;
    }

    /**
     * 查询派单明细列表
     *
     * @param request
     * @return
     */
    @Override
    public PageDto<JdhDispatchForManDTO> queryDispatchListForMan(DispatchDetailForManRequest request) {
        AssertUtils.nonNull(request.getDispatchId(),"派单任务ID为空");
        DispatchDetailPageRepQuery convert = DispatchApplicationConverter.INS.convert(request);
        convert.setPageNum(request.getPageNum());
        convert.setPageSize(request.getPageSize());
        Page<JdhDispatchDetail> dispatchDetailPage = dispatchRepository.findDispatchDetailPage(convert);
        log.info("queryDispatchListForMan->dispatchDetailPage={}",JsonUtil.toJSONString(dispatchDetailPage));
        PageDto<JdhDispatchForManDTO> res = new PageDto<>();
        res.setPageNum(request.getPageNum());
        res.setPageSize(request.getPageSize());
        if(Objects.nonNull(dispatchDetailPage)){
            res.setTotalCount(dispatchDetailPage.getTotal());
            res.setTotalPage(dispatchDetailPage.getPages());
            if (CollectionUtils.isNotEmpty(dispatchDetailPage.getRecords())){
                List<JdhDispatchForManDTO> list = DispatchApplicationConverter.INS.convert(dispatchDetailPage.getRecords());
                res.setList(list);
            }
        }
        return res;
    }

    /**
     *
     * @param cmd
     * @return
     */
    private boolean executeTargetDispatch(DispatchCallbackCmd cmd) {
        try {
            JdhDispatch jdhDispatch = dispatchRepository.findValidDispatch(DispatchRepQuery.builder().dispatchId(cmd.getDispatchId()).promiseId(cmd.getPromiseId()).build());
            if (Objects.isNull(jdhDispatch)) {
                return false;
            }
            if (!Objects.equals(jdhDispatch.getServiceInfo().getDispatchExecuteRoute(), DispatchExecuteRouteEnum.FLOW.getRouteCode())) {
                log.info("DispatchApplicationImpl -> executeTargetDispatch, 非本地派单逻辑任务不执行派单失败指定派单, jdhDispatch={}",JsonUtil.toJSONString(jdhDispatch));
                return false;
            }
            //只有护士上门两种模式（检测、护理）才尝试派单失败后指定派单一次，其他模式的单据默认不处理
            JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(jdhDispatch.getVerticalCode());
            List<String> executeCode = Lists.newArrayList(BusinessModeEnum.ANGEL_TEST.getCode(), BusinessModeEnum.ANGEL_TEST_NO_LABORATORY.getCode(), BusinessModeEnum.ANGEL_CARE.getCode());
            if (Objects.nonNull(jdhVerticalBusiness) && !executeCode.contains(jdhVerticalBusiness.getBusinessModeCode())) {
                log.info("DispatchApplicationImpl -> executeTargetDispatch, 非护士派单任务不执行派单失败指定派单, jdhDispatch={}",JsonUtil.toJSONString(jdhDispatch));
                return false;
            }

            //用户预约上门时间
            Date from = Date.from(jdhDispatch.getServiceInfo().getAppointmentTime().getAppointmentStartTime().atZone(ZoneId.systemDefault()).toInstant());

            //获取当天开始时间和结束时间
            DateTime serviceStartTimeBegin = DateUtil.beginOfDay(from);
            DateTime serviceStartTimeEnd = DateUtil.endOfDay(from);

            //根据用户上门地址获取京标地址
            if (Objects.isNull(jdhDispatch.getServiceLocation().getServiceLocationProvinceId()) || Objects.isNull(jdhDispatch.getServiceLocation().getServiceLocationCityId())) {
                //上门地址省市区未存，调用地址服务根据详细地址查询省市区信息
                BaseAddressBo baseAddressBo = addressRpc.getJDAddressFromAddress(jdhDispatch.getServiceLocation().getServiceLocationDetail());
                jdhDispatch.getServiceLocation().setServiceLocationProvinceId(Objects.nonNull(baseAddressBo) ? baseAddressBo.getProvinceCode() : null);
                jdhDispatch.getServiceLocation().setServiceLocationCityId(Objects.nonNull(baseAddressBo) ? baseAddressBo.getCityCode() : null);
                jdhDispatch.getServiceLocation().setServiceLocationDistrictId(Objects.nonNull(baseAddressBo) ? baseAddressBo.getDistrictCode() : null);
                jdhDispatch.getServiceLocation().setServiceLocationTownId(Objects.nonNull(baseAddressBo) ? baseAddressBo.getTownCode() : null);
            }
            //未拿到城市，默认圈选护士失败
            if (Objects.isNull(jdhDispatch.getServiceLocation().getServiceLocationProvinceId()) || Objects.isNull(jdhDispatch.getServiceLocation().getServiceLocationCityId())) {
                log.info("DispatchApplicationImpl -> executeTargetDispatch, 未拿到城市，指定派单流程终止 jdhDispatch={}", JSON.toJSONString(jdhDispatch));
                return false;
            }
            //查询该城市所有的全职护士
            //2025-03-03 兜底逻辑改造，多区域共用兜底站长，ducc配置multi_region_master_config
            Map<String, List<Long>> multiRegionMasterMap = duccConfig.getMultiRegionMasterMap();
            log.info("DispatchApplicationImpl -> executeTargetDispatch, multiRegionMasterMap={}", JSON.toJSONString(multiRegionMasterMap));
            List<Long> angelIdList = multiRegionMasterMap.get(String.valueOf(jdhDispatch.getServiceLocation().getServiceLocationCityId()));
            if (CollectionUtils.isEmpty(angelIdList)) {
                angelIdList = multiRegionMasterMap.get(String.valueOf(jdhDispatch.getServiceLocation().getServiceLocationProvinceId()));
            }
            if (CollectionUtils.isEmpty(angelIdList)) {
                log.warn("DispatchApplicationImpl -> executeTargetDispatch, 未查到与用户上门地址相同城市的兜底站长配置，指定派单流程终止 jdhDispatch={}", JSON.toJSONString(jdhDispatch));
                return false;
            }
            List<String> angelIds = angelIdList.stream().map(String::valueOf).collect(Collectors.toList());

            List<DispatchAngelWorkGroupCountBO> groupCountBOList = dispatchFlowDependRpc.queryAngelWorkGroupCountDto(DispatchAngelWorkCountQuery.builder()
                    .angelIds(angelIds)
                    .serviceStartTimeBegin(serviceStartTimeBegin)
                    .serviceStartTimeEnd(serviceStartTimeEnd)
                    .statusList(Lists.newArrayList(2, 3, 4, 5, 6, 7, 8))
                    .column("angel_id")
                    .build());
            log.info("DispatchApplicationImpl -> executeTargetDispatch, 查询护士当天订单数量 groupCountBOList={}", JSON.toJSONString(groupCountBOList));
            //要指定派单的护士ID
            Long targetAngelId = null;

            //判断是否有当天没有订单的护士，如果有把这些护士找出来
            List<String> hasTaskAngelIdList = groupCountBOList.stream().map(groupCountBO -> Convert.convert(String.class, groupCountBO.getGroupKeyValue())).collect(Collectors.toList());
            angelIds.removeAll(hasTaskAngelIdList);
            if (CollectionUtils.isNotEmpty(angelIds)) {
                Collections.shuffle(angelIds);
                targetAngelId = Convert.convert(Long.class, angelIds.get(0));
            } else {
                //如果每个全职护士当天都有订单，找订单数量最少的
                TreeMap<Long, List<DispatchAngelWorkGroupCountBO>> countMap = groupCountBOList.stream().collect(Collectors.groupingBy(DispatchAngelWorkGroupCountBO::getCount, TreeMap::new, Collectors.toList()));
                //取key最少的list
                List<DispatchAngelWorkGroupCountBO> angelCountList = countMap.get(countMap.firstKey());
                //随机取出一位护士进行指定派单
                Collections.shuffle(angelCountList);
                DispatchAngelWorkGroupCountBO groupCountBO = angelCountList.get(0);
                targetAngelId = Convert.convert(Long.class, groupCountBO.getGroupKeyValue());
            }

            //[{"operator":"ext.zhangpingli3","promiseId":162484151910775,"roleType":4,"targetAngelId":160422808256551}]
            TargetDispatchCmd targetDispatchCmd = new TargetDispatchCmd();
            targetDispatchCmd.setOperator(CommonConstant.SYSTEM);
            targetDispatchCmd.setPromiseId(jdhDispatch.getPromiseId());
            targetDispatchCmd.setRoleType(NumConstant.NUM_4);
            targetDispatchCmd.setTargetAngelId(targetAngelId);
            DispatchTargetStatusDto targetStatusDto = targetDispatch(targetDispatchCmd);
            boolean result = Objects.nonNull(targetStatusDto) && Objects.equals(targetStatusDto.getTargetResult(), true);
            if (result) {
                //指定派单成功，发送专门给运营触发消息的事件
                eventCoordinator.publish(EventFactory.newDefaultEvent(jdhDispatch, DispatchEventTypeEnum.DISPATCH_FAIL_TARGET_DISPATCH, new TargetDispatchEventBody(jdhDispatch, jdhDispatch, "派单失败尝试指定派单成功", targetAngelId, null, targetStatusDto.getIsFirstReceived())));
            }
            return result;
        } catch (Exception e){
            log.info("DispatchApplicationImpl -> executeTargetDispatch error, cmd:{}", JSON.toJSONString(cmd), e);
            return false;
        }
    }

    /**
     * 已派单成功，再次修改预约时间
     *
     * @param jdhDispatchSnapshot
     * @param angelWorkSnapShot
     * @param cmd
     */
    private void modifyDate(JdhDispatch jdhDispatchSnapshot, AngelWork angelWorkSnapShot, SubmitDispatchCmd cmd) {
        log.info("DispatchApplicationImpl modifyDate start");
        // 更新工单
        modifyWorkDate(jdhDispatchSnapshot, angelWorkSnapShot, cmd);
        // 更新任务单
        modifyTaskDate(angelWorkSnapShot, cmd);
    }

    /**
     * 已派单成功，再次修改预约时间
     *
     * @param jdhDispatchSnapshot
     */
    private void appendDispatchBeforeDate(JdhDispatch jdhDispatchSnapshot, SubmitDispatchCmd cmd) {
        log.info("DispatchApplicationImpl -> appendDispatchBeforeDate start, dispatchId:{}", jdhDispatchSnapshot.getDispatchId());
        DomainAppointmentTime beforeTime = null;
        if (jdhDispatchSnapshot.getServiceInfo() != null && jdhDispatchSnapshot.getServiceInfo().getAppointmentTime() != null) {
            beforeTime = new DomainAppointmentTime();
            beforeTime.setAppointmentStartTime(jdhDispatchSnapshot.getServiceInfo().getAppointmentTime().getAppointmentStartTime());
            beforeTime.setAppointmentEndTime(jdhDispatchSnapshot.getServiceInfo().getAppointmentTime().getAppointmentEndTime());
        }
        DispatchCtxAppointmentTime afterTime = new DispatchCtxAppointmentTime();
        if (cmd.getAppointmentTime() != null) {
            afterTime.setAppointmentStartTime(DateUtil.toLocalDateTime(TimeUtils.timeStrToDate(cmd.getAppointmentTime().getAppointmentStartTime(), TimeFormat.LONG_PATTERN_LINE_NO_S)));
            afterTime.setAppointmentEndTime(DateUtil.toLocalDateTime(TimeUtils.timeStrToDate(cmd.getAppointmentTime().getAppointmentEndTime(), TimeFormat.LONG_PATTERN_LINE_NO_S)));
            afterTime.setIsImmediately(cmd.getAppointmentTime().getIsImmediately());
            afterTime.setDateType(cmd.getAppointmentTime().getDateType());
        } else {
            PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
            promiseIdRequest.setPromiseId(cmd.getPromiseId());
            PromiseDto promiseDto = promiseApplication.findByPromiseId(promiseIdRequest);
            if (promiseDto != null && promiseDto.getAppointmentTime() != null) {
                afterTime.setAppointmentStartTime(DateUtil.toLocalDateTime(promiseDto.getAppointmentTime().getAppointmentStartTime()));
                afterTime.setAppointmentEndTime(DateUtil.toLocalDateTime(promiseDto.getAppointmentTime().getAppointmentEndTime()));
                afterTime.setDateType(2);
                afterTime.setIsImmediately(false);
            }
        }
        // 设置信息的预约时间
        if (jdhDispatchSnapshot.getServiceInfo() != null) {
            jdhDispatchSnapshot.getServiceInfo().setBeforeTime(beforeTime);
            jdhDispatchSnapshot.getServiceInfo().setAppointmentTime(afterTime);
        } else {
            JdhDispatchServiceInfo jdhDispatchServiceInfo = new JdhDispatchServiceInfo();
            jdhDispatchServiceInfo.setBeforeTime(beforeTime);
            jdhDispatchServiceInfo.setAppointmentTime(afterTime);
            jdhDispatchSnapshot.setServiceInfo(jdhDispatchServiceInfo);
        }
        //更新存入数据库
        dispatchRepository.save(jdhDispatchSnapshot);
    }

    /**
     * 修改时间更新派单状态
     */
    private void modifyDateUpdateDispatchStatus(JdhDispatch jdhDispatchSnapshot, AngelWork angelWork) {
        // 骑手上门取消成功后派单状态修改为待派单更新出发时间
        if (AngelWorkTypeEnum.RIDER.getType().equals(angelWork.getWorkType())) {
            jdhDispatchSnapshot.setDispatchStatus(JdhDispatchStatusEnum.DISPATCH_COMPLETED.getStatus());
        }
        dispatchRepository.save(jdhDispatchSnapshot);
    }

    /**
     * 已派单成功，再次修改预约时间
     *
     * @param angelWorkSnapShot
     * @param cmd
     */
    private void modifyWorkDate(JdhDispatch jdhDispatchSnapshot, AngelWork angelWorkSnapShot, SubmitDispatchCmd cmd) {
        log.info("DispatchApplicationImpl -> modifyWorkDate start, angelWork:{}", JSON.toJSONString(angelWorkSnapShot));
        JdhAngelWorkModifyDateCmd jdhAngelWorkModifyDateCmd = new JdhAngelWorkModifyDateCmd();
        DomainAppointmentTime workTime = new DomainAppointmentTime();
        jdhAngelWorkModifyDateCmd.setServiceStartTime(workTime.formatAppointmentTime(cmd.getAppointmentTime().getAppointmentStartTime()));
        jdhAngelWorkModifyDateCmd.setServiceEndTime(workTime.formatAppointmentTime(cmd.getAppointmentTime().getAppointmentEndTime()));
        // 非骑手上门更新出发时间

        JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(angelWorkSnapShot.getVerticalCode());
        //预计时间处理
        O2oBusinessIdentifier businessIdentifier = O2oBusinessIdentifier.builder()
                .verticalCode(jdhVerticalBusiness.getVerticalCode())
                .serviceType(jdhVerticalBusiness.getServiceType())
                .businessMode(jdhVerticalBusiness.getBusinessModeCode())
                .build();
        log.info("[WorkCreateGenerateService.call],业务身份参数 businessIdentifier={}", com.jd.fastjson.JSON.toJSONString(businessIdentifier));

        // 计划出门时间，计划完成时间
        DispatchPlanTime dispatchPlanTime = jdhDispatchDomainService.parsePlanTime(jdhDispatchSnapshot.getServiceInfo());
        jdhAngelWorkModifyDateCmd.setPlanOutTime(dispatchPlanTime.getPlanOutTime());
        jdhAngelWorkModifyDateCmd.setPlanFinishTime(dispatchPlanTime.getPlanFinishTime());


        WorkBizParamCheckParam workBizParamCheckParam = new WorkBizParamCheckParam();
        workBizParamCheckParam.setWorkStartTime(jdhAngelWorkModifyDateCmd.getServiceStartTime());
        ExtResponse<AngelWorkPlanTime> dateExtResponse = workCreateAbility.planTimeCalculate(businessIdentifier, workBizParamCheckParam);
        jdhAngelWorkModifyDateCmd.setPlanCallTime(dateExtResponse.getData().getPlanCallTime());

        // 呼叫时间更新到工单,后续逻辑使用
        angelWorkSnapShot.setPlanCallTime(dateExtResponse.getData().getPlanCallTime());
        angelWorkSnapShot.setPlanOutTime(dispatchPlanTime.getPlanOutTime());
        angelWorkSnapShot.setPlanFinishTime(dispatchPlanTime.getPlanFinishTime());
        angelWorkSnapShot.setWorkStartTime(jdhAngelWorkModifyDateCmd.getServiceStartTime());
        angelWorkSnapShot.setWorkEndTime(jdhAngelWorkModifyDateCmd.getServiceEndTime());

        jdhAngelWorkModifyDateCmd.setWorkId(angelWorkSnapShot.getWorkId());

        Boolean workModifyRet = angelWorkApplication.modifyAngelWorkStartEndDate(jdhAngelWorkModifyDateCmd);
        if (!Boolean.TRUE.equals(workModifyRet)) {
            log.error("DispatchApplicationImpl -> modifyWorkDate end 更新工单预约时间失败, workId:{}", angelWorkSnapShot.getWorkId());
            throw new BusinessException(DispatchErrorCode.DISPATCH_MODIFY_DATE_FAIL);
        }
    }

    /**
     * 已派单成功，再次修改预约时间
     *
     * @param angelWorkSnapShot
     * @param cmd
     */
    private void modifyTaskDate(AngelWork angelWorkSnapShot, SubmitDispatchCmd cmd) {
        log.info("DispatchApplicationImpl -> modifyTaskDate start, angelWork:{}", JSON.toJSONString(angelWorkSnapShot));
        AngelTaskModifyDateCmd jdhAngelTaskModifyDateCmd = new AngelTaskModifyDateCmd();
        DomainAppointmentTime taskTime = new DomainAppointmentTime();
        jdhAngelTaskModifyDateCmd.setServiceStartTime(taskTime.formatAppointmentTime(cmd.getAppointmentTime().getAppointmentStartTime()));
        jdhAngelTaskModifyDateCmd.setServiceEndTime(taskTime.formatAppointmentTime(cmd.getAppointmentTime().getAppointmentEndTime()));
        jdhAngelTaskModifyDateCmd.setWorkId(angelWorkSnapShot.getWorkId());
        Boolean taskModifyRet = angelTaskApplication.modifyServiceDate(jdhAngelTaskModifyDateCmd);
        if (!Boolean.TRUE.equals(taskModifyRet)) {
            log.error("DispatchApplicationImpl -> modifyTaskDate end 更新任务单预约时间失败, workId:{}", angelWorkSnapShot.getWorkId());
            throw new BusinessException(DispatchErrorCode.DISPATCH_MODIFY_DATE_FAIL);
        }
    }

    /**
     * 取消当前派单任务并重新派单
     *
     * @param angelWork angelWork
     */
    private void cancelShip(AngelWork angelWork, SubmitDispatchCmd cmd) {
        if(Objects.equals(AngelWorkTypeEnum.CARE.getType(), angelWork.getWorkType()) || Objects.equals(AngelWorkTypeEnum.NURSE.getType(), angelWork.getWorkType())){
            List<AngelShipDto> shipDtos = angelWorkApplication.queryAngelShipList(AngelShipDBQuery.builder().workId(angelWork.getWorkId()).status(new HashSet<>(AngelShipStatusEnum.finishBeforeStatus())).build());
            if (CollUtil.isEmpty(shipDtos)) {
                return;
            }
            AngelWorkCancelShipCmd enumQuery = new AngelWorkCancelShipCmd();
            enumQuery.setOperator(cmd.getOperatePin());
            enumQuery.setStandCancelCode(AngelShipCancelCodeStatusEnum.NURSE_CANCEL.getType());
            enumQuery.setWorkId(String.valueOf(angelWork.getWorkId()));
            enumQuery.setUserPin(cmd.getOperatePin());
            enumQuery.setShipId(String.valueOf(shipDtos.get(0).getShipId()));
            angelWorkApplication.cancelShip(enumQuery);
        } else if (Objects.equals(AngelWorkTypeEnum.RIDER.getType(), angelWork.getWorkType())) {
            AngelWorkCancelShipCmd angelWorkCancelShipCmd = new AngelWorkCancelShipCmd();
            angelWorkCancelShipCmd.setWorkId(String.valueOf(angelWork.getWorkId()));
            angelWorkCancelShipCmd.setWorkType(angelWork.getWorkType());
            angelWorkCancelShipCmd.setOperateSource(cmd.getOperateSource());
            angelWorkApplication.cancelShipFormWork(angelWorkCancelShipCmd, AngelShipCancelCodeStatusEnum.ADJUST_APPOINT_TIME_CANCEL);
        }
    }

    /**
     * 修改时间成功事件
     */
    private void modifyDateSuccessEvent(AngelWork angelWorkSnapShot, SubmitDispatchCmd cmd) {
        log.info("DispatchApplicationImpl modifyDateSuccessEvent start");
        // 如果任务单已派单护士，修改工单、任务单即可，无需发起取消派单、重新指定派单流程
        if (angelWorkSnapShot != null && StringUtils.isNotBlank(angelWorkSnapShot.getAngelId())
                && (AngelWorkTypeEnum.NURSE.getType().equals(angelWorkSnapShot.getWorkType())
                    || AngelWorkTypeEnum.CARE.getType().equals(angelWorkSnapShot.getWorkType()))) {
            log.info("PromiseApplicationImpl -> modifyPromiseDispatch angelId:{}", angelWorkSnapShot.getAngelId());
            //查询护士信息
            AngelRequest angelRequest = new AngelRequest();
            angelRequest.setAngelId(Long.parseLong(angelWorkSnapShot.getAngelId()));
            JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
            // 护士状态正常直接修改预约时间，否则兜底为重新派单
            if (jdhAngelDto != null) {
                // 查询最新数据发送事件
                DispatchRepQuery dispatchReq = new DispatchRepQuery();
                dispatchReq.setPromiseId(cmd.getPromiseId());
                JdhDispatch dispatch = dispatchRepository.findValidDispatch(dispatchReq);
                // 修改预约时间，发送服务者已接单
                eventCoordinator.publish(EventFactory.newDefaultEvent(dispatch, DispatchEventTypeEnum.DISPATCH_SUCCESS_AFTER_MODIFY_DATE_SUCCESS, new DispatchCallbackEventBody(dispatch, dispatch, Long.parseLong(angelWorkSnapShot.getAngelId()), Boolean.FALSE)));
            }
        }
    }

    /**
     * 重派实验室
     */
    private void dispatchStore(Long promiseId) {
        log.info("DispatchApplicationImpl dispatchStore start");
        //根据PromiseId查询履约单，然后进行实验室派单
        PromiseDto promise = promiseApplication.findPromiseByPromiseId(PromiseIdRequest.builder().promiseId(promiseId).build());
        //检测人ID
        List<Long> patientIds = promise.getPatients().stream().map(PromisePatientDto::getPromisePatientId).collect(Collectors.toList());
        //组装分派实验室参数
        MedicalPromiseDispatchCmd medicalPromiseDispatchCmd = new MedicalPromiseDispatchCmd();
        medicalPromiseDispatchCmd.setPromiseId(promise.getPromiseId());
        medicalPromiseDispatchCmd.setVerticalCode(promise.getVerticalCode());
        medicalPromiseDispatchCmd.setServiceType(promise.getServiceType());
        medicalPromiseDispatchCmd.setStartAddress(promise.getStore().getStoreAddr());
        medicalPromiseDispatchCmd.setPromisePatientIdList(patientIds);
        // 底层失败会抛异常,不处理结果
        medicalPromiseApplication.storeDisPatch(medicalPromiseDispatchCmd);
    }

    /**
     * 修改时间重新派单
     * 由于历史逻辑耦合了工单、运单、任务单逻辑，这里单独提供取消预约逻辑，后续可以解耦运单、工单逻辑将逻辑放到服务者域
     */
    private void modifyDateReDispatch(JdhDispatch jdhDispatch, AngelWork angelWorkSnapShot, SubmitDispatchCmd cmd) {
        log.info("DispatchApplicationImpl modifyDateReDispatch start");
        if (angelWorkSnapShot != null && AngelWorkTypeEnum.RIDER.getType().equals(angelWorkSnapShot.getWorkType())) {
            AngelWorkReCallShipCmd angelWorkCancelShipCmd = new AngelWorkReCallShipCmd();
            angelWorkCancelShipCmd.setWorkId(String.valueOf(angelWorkSnapShot.getWorkId()));
            angelWorkCancelShipCmd.setWorkType(angelWorkSnapShot.getWorkType());
            angelWorkCancelShipCmd.setOperateSource(cmd.getOperateSource());
            angelWorkApplication.reCallShipFormWork(angelWorkCancelShipCmd, AngelShipCancelCodeStatusEnum.ADJUST_APPOINT_TIME_CANCEL);
        } else {
            log.info("DispatchApplicationImpl modifyDateReDispatch 护士护理重新派单");
            dispatchAngel(jdhDispatch, angelWorkSnapShot, cmd);
        }
    }

    /**
     * 派发服务者
     */
    private void dispatchAngel(JdhDispatch jdhDispatch, AngelWork angelWorkSnapShot, SubmitDispatchCmd cmd) {
        // 已有服务者接单，不需要重新派发
        if (angelWorkSnapShot != null && StringUtils.isNotBlank(angelWorkSnapShot.getAngelId()) && AngelWorkStatusEnum.canModifyStatus(angelWorkSnapShot.getWorkStatus())) {
            // 已接单，修改后工单状态重置
            angelWorkRepository.updateAngelWorkStatus(AngelWorkStatusDbCmd.builder().workStatus(AngelWorkStatusEnum.RECEIVED.getType()).workId(angelWorkSnapShot.getWorkId()).build());
            AngelTaskStatusCmd angelTaskStatusCmd = new AngelTaskStatusCmd();
            angelTaskStatusCmd.setWorkId(angelWorkSnapShot.getWorkId());
            angelTaskStatusCmd.setTaskStatus(AngelTaskStatusEnum.INIT.getType());
            angelTaskStatusCmd.setBizExtStatus(AngelBizExtStatusEnum.INIT_STATUS.getType());
            angelTaskRepository.updateStatusByWorkId(angelTaskStatusCmd);

            Date now = new Date();
            AngelWorkHistory angelWorkHistory = AngelWorkHistory.builder()
                    .workId(angelWorkSnapShot.getWorkId())
                    .operateEvent(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_RECEIVED.getCode())
                    .beforeStatus(angelWorkSnapShot.getWorkStatus())
                    .afterStatus(AngelWorkStatusEnum.RECEIVED.getType())
                    .creator(StringUtils.isNotBlank(cmd.getOperateErp()) ? cmd.getOperateErp(): cmd.getOperatePin())
                    .operateTime(now)
                    .createTime(now)
                    .yn(YnStatusEnum.YES.getCode())
                    .build();
            angelWorkHistoryRepository.save(angelWorkHistory);
            log.info("PromiseApplicationImpl -> dispatchAngel 服务者已接单,不再派发服务者 workId:{}", angelWorkSnapShot.getWorkId());
            return;
        }
        AngelDispatchCmd angelDispatchCmd = new AngelDispatchCmd();
        angelDispatchCmd.setDispatchId(jdhDispatch.getDispatchId());
        Boolean ret = angelDispatchLogic(angelDispatchCmd);
        if (!Boolean.TRUE.equals(ret)) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_MODIFY_DATE_ANGEL_FAIL);
        }
    }

    /**
     * 服务者派单逻辑
     * 多个方法调用，导致事务传递失效Transactional，提取独立方法
     *
     * @param cmd
     * @return
     */
    private Boolean angelDispatchLogic(AngelDispatchCmd cmd) {
        log.info("DispatchApplicationImpl -> angelDispatch cmd:{}", JSON.toJSONString(cmd));
        DispatchRepQuery dispatchRepQuery = new DispatchRepQuery();
        dispatchRepQuery.setDispatchId(cmd.getDispatchId());
        JdhDispatch dispatch = dispatchRepository.findValidDispatch(dispatchRepQuery);
        if (Objects.isNull(dispatch)) {
            log.info("DispatchApplicationImpl -> angelDispatch 派单任务已过期, 默认不处理 dispatch:{}", JSON.toJSONString(cmd));
            return false;
        }

        AngelDispatchContext context = DispatchApplicationConverter.INS.cmd2AngelDispatchContext(cmd);
        context.init(dispatch);
        //查询所需技能的详细信息
        List<String> skillCodeList = dispatch.getServiceInfo().getPatients().stream()
                .flatMap(appointmentPatient -> appointmentPatient.getServiceItems().stream())
                .flatMap(serviceItem -> Optional.ofNullable(serviceItem.getItemSkillList()).orElseGet(Collections::emptyList).stream())
                .map(JdhDispatchServiceItemSkillRel::getAngelSkillCode)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(skillCodeList)) {
            //查询技能信息
            List<JdhAngelSkillDict> skillList = angelSkillDictRepository.findList(AngelSkillDictRepQuery.builder().angelSkillCodeList(skillCodeList).build());
            if (CollectionUtils.isNotEmpty(skillList)) {
                List<String> serviceGroupIdList = skillList.stream().map(JdhAngelSkillDict::getServiceGroupId).distinct().collect(Collectors.toList());
                context.setServiceGroupIdList(serviceGroupIdList);
                List<String> angelSkillCodeList = skillList.stream().map(JdhAngelSkillDict::getAngelSkillCode).distinct().collect(Collectors.toList());
                context.setAngelSkillCodeList(angelSkillCodeList);
            }
        }
        //安全取出下单的sku，查询商品信息，判断serviceType
        Long skuId = Optional.ofNullable(dispatch)
                .map(JdhDispatch::getServiceInfo)
                .map(JdhDispatchServiceInfo::getPatients)
                .filter(CollectionUtils::isNotEmpty)
                .map(patients -> patients.get(0))
                .map(AppointmentPatient::getServiceItems)
                .filter(CollectionUtils::isNotEmpty)
                .map(serviceItems -> serviceItems.get(0))
                .map(ServiceItem::getServiceId)
                .orElse(null);
        if (Objects.nonNull(skuId)) {
            JdhSkuRequest jdhSkuRequest = new JdhSkuRequest();
            jdhSkuRequest.setSkuId(skuId);
            JdhSkuDto jdhSkuDto = productApplication.queryJdhSkuInfo(jdhSkuRequest);
            context.setSkuServiceType(Objects.nonNull(jdhSkuDto) ? jdhSkuDto.getServiceType() : null);
        }

        Boolean result = jdhDispatchDomainService.angelDispatch(context);
        //创建服务者工单
        if (context.getCreateAngelWork()) {
            JdhAngelWorkSaveCmd workSaveCmd = DispatchApplicationConverter.INS.dispatch2AngelWorkSaveCmd(context.getJdhDispatch());
            workSaveCmd.setAngelType(context.getAngelType());
            workSaveCmd.setAngelDeliveryType(context.getAngelDeliveryType());
            if (Objects.nonNull(workSaveCmd.getAngelId())) {
                //设置护士全兼职和服务站省市区
                JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(AngelRequest.builder().angelId(workSaveCmd.getAngelId()).build());
                if (Objects.nonNull(jdhAngelDto)) {
                    workSaveCmd.setJobNature(jdhAngelDto.getJobNature());
                    if (Objects.nonNull(jdhAngelDto.getStationId()) && Objects.equals(jdhAngelDto.getJobNature(), JobNatureEnum.FULL_TIME.getValue())) {
                        StationQuery stationQuery = new StationQuery();
                        stationQuery.setStationIds(Sets.newHashSet(jdhAngelDto.getStationId()));
                        List<AngelStationDto> angelStationDtos = stationApplication.queryJdhStation(stationQuery);
                        if (CollectionUtils.isNotEmpty(angelStationDtos)) {
                            AngelStationDto angelStationDto = angelStationDtos.get(0);
                            workSaveCmd.setProvinceCode(StringUtils.isNotBlank(angelStationDto.getProvinceCode()) ? Integer.valueOf(angelStationDto.getProvinceCode()) : null);
                            workSaveCmd.setCityCode(StringUtils.isNotBlank(angelStationDto.getCityCode()) ? Integer.valueOf(angelStationDto.getCityCode()) : null);
                            workSaveCmd.setDistrictCode(StringUtils.isNotBlank(angelStationDto.getDistrictCode()) ? Integer.valueOf(angelStationDto.getDistrictCode()) : null);
                            workSaveCmd.setTownCode(StringUtils.isNotBlank(angelStationDto.getCountyCode()) ? Integer.valueOf(angelStationDto.getCountyCode()) : null);
                        }
                    }
                }
            }
            angelWorkApplication.createAngelWork(workSaveCmd);
        }
        return result;
    }

    /**
     * 修改预约时间重新派单失败发送预警消息
     *
     * @param erp erp
     * @param promiseId promiseId
     * @param errorMsg errorMsg
     */
    private void failSendMsg(String erp, Long promiseId, String errorMsg) {
        try {
            if (StringUtils.isNotBlank(erp)) {
                dongDongRobotRpc.sendDongDongRobotMessage(String.format("您操作的履约单%s,修改预约时间失败,失败原因:%s,traceId:%s", promiseId, errorMsg, MDC.get("PFTID")), erp);
            }
            Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
            JSONObject jsonObject = robotAlarmMap.get("修改预约时间失败");
            if (Objects.isNull(jsonObject) || StringUtil.isBlank(jsonObject.getString("groupId"))) {
                log.info("DispatchEventSubscriber -> alarm disable robotAlarmMap={}", JsonUtil.toJSONString(robotAlarmMap));
                return;
            }
            dongDongRobotRpc.sendDongDongRobotMessage(String.format("履约单%s,修改预约时间失败,失败原因:%s,traceId:%s", promiseId, errorMsg, MDC.get("PFTID")),
                    jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
        } catch (Exception e) {
            log.error("DispatchApplicationImpl -> failSendMsg 发送消息失败", e);
        }
    }

    /**
     * 修改预约时间重新派单失败发送预警消息
     *
     * @param erp erp
     * @param promiseId promiseId
     */
    private void successSendMsg(String erp, Long promiseId, String startTime) {
        try {
            if (StringUtils.isNotBlank(erp)) {
                dongDongRobotRpc.sendDongDongRobotMessage(String.format("您操作的履约单%s,修改预约时间成功,请确保用户已知晓于%s时间点等待服务", promiseId, startTime), erp);
            }
        } catch (Exception e) {
            log.error("DispatchApplicationImpl -> successSendMsg 发送消息失败", e);
        }
    }

    /**
     * 映射服务站id
     *
     * @param angelStationId
     * @return
     */
    private String mappingShopNo(String angelStationId) {
        Map<String, String> angelStationMap = duccConfig.getAngelStationMap();
        if(MapUtils.isEmpty(angelStationMap)) {
            return angelStationId;
        }
        return StringUtils.isBlank(angelStationMap.get(angelStationId)) ? angelStationId : angelStationMap.get(angelStationId);
    }
}