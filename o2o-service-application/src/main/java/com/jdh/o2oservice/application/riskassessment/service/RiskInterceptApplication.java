package com.jdh.o2oservice.application.riskassessment.service;

import com.jdh.o2oservice.export.riskassessment.cmd.InvalidPatientRiskCmd;
import com.jdh.o2oservice.export.riskassessment.cmd.ModifyRiskInterceptCmd;
import com.jdh.o2oservice.export.riskassessment.cmd.RiskInterceptCmd;

/**
 * @ClassName RiskInterceptApplication
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 20:02
 */
public interface RiskInterceptApplication {

    /**
     * 执行风险拦截
     *
     * @param riskInterceptCmd
     * @return
     */
    boolean doRiskIntercept(RiskInterceptCmd riskInterceptCmd);

    /**
     * 修改拦截记录状态
     *
     * @param modifyRiskInterceptCmd
     * @return
     */
    boolean modifyRiskInterceptStatus(ModifyRiskInterceptCmd modifyRiskInterceptCmd);

    /**
     * 作废人维度风险评估单
     *
     * @param invalidPatientRiskCmd
     * @return
     */
    boolean invalidPatientRiskAssessment(InvalidPatientRiskCmd invalidPatientRiskCmd);
}
