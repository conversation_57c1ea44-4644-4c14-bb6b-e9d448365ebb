package com.jdh.o2oservice.application.product.service;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.export.product.cmd.RemoveServiceGroupCmd;
import com.jdh.o2oservice.export.product.cmd.SaveServiceCmd;
import com.jdh.o2oservice.export.product.cmd.SaveServiceGroupCmd;
import com.jdh.o2oservice.export.product.cmd.SaveServiceItemRelCmd;
import com.jdh.o2oservice.export.product.dto.*;
import com.jdh.o2oservice.export.product.query.JdhServiceGroupQuery;
import com.jdh.o2oservice.export.product.query.JdhServiceItemRelQuery;
import com.jdh.o2oservice.export.product.query.JdhServiceQuery;

import java.util.List;

/**
 * @InterfaceName:ProductServiceApplication
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/28 20:31
 * @Vserion: 1.0
 **/
public interface ProductServiceApplication {

    /**
     * 创建京东服务
     *
     * @param saveServiceCmd
     * @return
     */
    Boolean saveJdhService(SaveServiceCmd saveServiceCmd);

    /**
     * 查询京东服务下项目列表
     *
     * @return
     */
    List<ServiceItemDto> queryServiceItemList(JdhServiceQuery jdhServiceQuery);

    /**
     * 分页查询京东服务
     *
     * @param jdhServiceQuery
     * @return
     */
    PageDto<JdhServiceDto> queryJdhServicePage(JdhServiceQuery jdhServiceQuery);

    /**
     * 查询京东服务列表
     *
     * @param jdhServiceQuery
     * @return
     */
    List<JdhServiceDto> queryJdhServiceList(JdhServiceQuery jdhServiceQuery);

    /**
     * 查询京东服务项目关系
     *
     * @param serviceItemRelQuery
     * @return
     */
    List<JdhServiceItemRelDto> queryJdhServiceItemRelList(JdhServiceItemRelQuery serviceItemRelQuery);

    /**
     * 查询京东服务组套列表
     *
     * @param jdhServiceGroupQuery
     * @return
     */
    List<JdhServiceGroupDto> queryJdhServiceGroupList(JdhServiceGroupQuery jdhServiceGroupQuery);

    /**
     * 查询京东服务详情
     *
     * @param jdhServiceQuery
     * @return
     */
    JdhServiceDto queryJdhServiceDetail(JdhServiceQuery jdhServiceQuery);

    /**
     * 查询京东服务组套中的京东服务列表
     *
     * @param jdhServiceGroupQuery
     * @return
     */
    List<JdhServiceDto> queryJdhServiceListFromGroup(JdhServiceGroupQuery jdhServiceGroupQuery);

    /**
     * 分页查询京东服务组套
     *
     * @param jdhServiceGroupQuery
     * @return
     */
    PageDto<JdhServiceGroupDto> queryJdhServiceGroupPage(JdhServiceGroupQuery jdhServiceGroupQuery);

    /**
     * 分页查询服务项目关联关系
     *
     * @param serviceItemRelQuery
     * @return
     */
    PageDto<JdhServiceItemRelDto> queryJdhServiceItemRelPage(JdhServiceItemRelQuery serviceItemRelQuery);

    /**
     * 批量保存京东服务组套信息
     *
     * @param serviceGroupCmdList
     * @return
     */
    Boolean saveJdhServiceGroupList(List<SaveServiceGroupCmd> serviceGroupCmdList);

    /**
     * 删除京东服务组套
     *
     * @param removeServiceGroupCmd
     * @return
     */
    Boolean deleteServiceGroup(RemoveServiceGroupCmd removeServiceGroupCmd);

    /**
     *
     * @param jdhServiceGroupQuery
     * @return
     */
    List<JdhServiceGroupDto> queryJdhServiceGroupListWithNoDetail(JdhServiceGroupQuery jdhServiceGroupQuery);

    /**
     * 保存京东服务项目
     *
     * @param saveServiceItemCmds
     * @return
     */
    boolean saveServiceItem(List<SaveServiceItemRelCmd> saveServiceItemCmds);

    /**
     * 移除服务项目
     *
     * @param relationNo
     * @param updateUser
     * @return
     */
    boolean removeServiceItem(Long relationNo, String updateUser);
}
