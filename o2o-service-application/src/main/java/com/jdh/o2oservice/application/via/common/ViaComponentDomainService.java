package com.jdh.o2oservice.application.via.common;

import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.support.via.model.ViaConfig;
import com.jdh.o2oservice.core.domain.support.via.model.ViaStatusMapping;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssessmentDetailManDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/4 16:31
 */
public interface ViaComponentDomainService {


    /**
     * 解析到家检测服务的VIA页面的状态映射
     *
     * @param orderStatus
     * @param promiseStatus
     * @param medicalPromiseStatus
     * @param isImmediately        是否立即预约
     * @param viaConfig
     * @return
     */
    ViaStatusMapping parseHomeTestMapping(Integer orderStatus,
                                          Integer promiseStatus,
                                          List<Integer> medicalPromiseStatus,
                                          Boolean isImmediately,
                                          String stepCode,
                                          ViaConfig viaConfig);


    /**
     * 解析到家检测服务的VIA页面的状态映射
     *
     * @param orderStatus
     * @param promiseStatus
     * @param medicalPromiseStatus
     * @param isImmediately        是否立即预约
     * @param workStatus
     * @param viaConfig
     * @return
     */
    ViaStatusMapping parseHomeTestMappingV2(Integer orderStatus,
                                            Integer promiseStatus,
                                            List<Integer> medicalPromiseStatus,
                                            Boolean isImmediately,
                                            Integer workStatus,
                                            ViaConfig viaConfig,
                                            Integer riskAssessmentStatus);

    ViaStatusMapping parseHomeTestMappingV3(Integer orderStatus,
                                            Integer promiseStatus,
                                            List<Integer> medicalPromiseStatus,
                                            Boolean isImmediately,
                                            Integer workStatus,
                                            ViaConfig viaConfig,
                                            Integer riskAssessmentStatus,
                                            Boolean highRiskAssessmentFlag);

    /**
     * 快递检测模式解析
     * @param promiseStatus
     * @param medicalPromiseStatus
     * @param shipStatus
     * @param stepCode
     * @param viaConfig
     * @return
     */
    ViaStatusMapping parseHomeTransportTestMapping(Integer promiseStatus,
                                                   List<Integer> medicalPromiseStatus,
                                                   Integer shipStatus,
                                                   String stepCode,
                                                   ViaConfig viaConfig);

    /**
     * 查询患者头像
     *
     * @return
     */
    String queryPatientHeadImage(Integer gender, Integer age);
}
