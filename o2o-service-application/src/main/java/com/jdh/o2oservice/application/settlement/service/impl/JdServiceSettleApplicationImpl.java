package com.jdh.o2oservice.application.settlement.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.common.web.LoginContext;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.promise.PromiseExtApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.settlement.JdServiceSettleExtApplication;
import com.jdh.o2oservice.application.settlement.convert.SettlementApplicationConvert;
import com.jdh.o2oservice.application.settlement.handler.angel.AngelSettlementHandler;
import com.jdh.o2oservice.application.settlement.handler.angel.AngelSettlementHandlerManager;
import com.jdh.o2oservice.application.settlement.service.JdServiceSettleApplication;
import com.jdh.o2oservice.application.settlement.service.JdServiceSettleReadApplication;
import com.jdh.o2oservice.application.support.service.PricingServiceApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.base.annotation.AutoTestSupport;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.model.AbilityCode;
import com.jdh.o2oservice.base.model.DomainAbility;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.statemachine.AbilityExecutor;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.EasyExcelUtil;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.core.domain.angel.enums.JobNatureEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.product.bo.CleanPopProductItemExcelBO;
import com.jdh.o2oservice.core.domain.product.enums.ProductAggregateEnum;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.settlement.bo.*;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementUpdateContext;
import com.jdh.o2oservice.core.domain.settlement.context.SettlementConfigContext;
import com.jdh.o2oservice.core.domain.settlement.convert.SettlementDomainConvert;
import com.jdh.o2oservice.core.domain.settlement.enums.*;
import com.jdh.o2oservice.core.domain.settlement.model.*;
import com.jdh.o2oservice.core.domain.settlement.repository.AngelSettlementRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.JdhAngelWorkSettleSnapshotRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.query.AngelWorkSettleSnapshotDBQuery;
import com.jdh.o2oservice.core.domain.settlement.rpc.HySettleRpc;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import com.jdh.o2oservice.core.domain.settlement.service.SettlementConfigDomainService;
import com.jdh.o2oservice.core.domain.settlement.vo.AngelAddAccountAmountVo;
import com.jdh.o2oservice.core.domain.support.basic.enums.FeeAggregateTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdOrderFeeTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.PricingServiceSceneEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.bo.OrderRefundDetailBo;
import com.jdh.o2oservice.core.domain.trade.context.CalcOrderServiceFeeContext;
import com.jdh.o2oservice.core.domain.trade.enums.JdOrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.enums.TradeEventTypeEnum;
import com.jdh.o2oservice.core.domain.trade.event.OrderCompleteEventBody;
import com.jdh.o2oservice.core.domain.trade.event.OrderRefundAngelAountEventBody;
import com.jdh.o2oservice.core.domain.trade.model.*;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRefundTaskRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.service.JdhOrderDomainService;
import com.jdh.o2oservice.core.domain.trade.vo.AddressInfoValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.OrderAppointmentTimeValueObject;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDetailDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelProfessionRelDto;
import com.jdh.o2oservice.export.angel.query.AngelDetailRequest;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailDto;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkQuery;
import com.jdh.o2oservice.export.dispatch.dto.DispatchAppointmentTimeDto;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseSettleStatusCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.cmd.UpdateJdhSkuCmd;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import com.jdh.o2oservice.export.promise.dto.PromiseAppointmentTimeDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromiseStationDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.settlement.cmd.AngelInviteActivitySettlementCmd;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementDto;
import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;
import com.jdh.o2oservice.export.support.command.PricingServiceCalculateCmd;
import com.jdh.o2oservice.export.support.dto.PricingServiceCalculateResultDto;
import com.jdh.o2oservice.export.trade.dto.AddressInfoDTO;
import com.jdh.o2oservice.export.trade.dto.JdSettleAppoinmentDTO;
import com.jdh.o2oservice.export.trade.query.JdServiceSettleParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 服务结算
 * <AUTHOR>
 * @Date 2024/5/8 14:57
 **/
@Slf4j
@Service
public class JdServiceSettleApplicationImpl implements JdServiceSettleApplication, JdServiceSettleExtApplication {

    /** */
    @Resource
    private DuccConfig duccConfig;
    /** */
    @Resource
    AngelApplication angelApplication;

    @Resource
    private JdhSkuRepository jdhSkuRepository;
    /**
     *
     */
    @Autowired
    private JdServiceSettleReadApplication serviceSettleReadApplication;
    /**
     *
     */
    @Autowired
    private AngelSettlementRepository angelSettlementRepository;
    /**
     * 服务者履约单
     */
    @Resource
    private AngelPromiseApplication angelPromiseApplication;
    /**
     * promiseApplication
     */
    @Autowired
    private PromiseApplication promiseApplication;

    /**
     * 算价服务
     */
    @Resource
    private PricingServiceApplication pricingServiceApplication;

    /**
     * jdhOrderDomainService
     */
    @Resource
    private JdhOrderDomainService jdhOrderDomainService;
    /**
     * 商品
     */
    @Autowired
    private ProductApplication productApplication;

    /**
     *
     */
    @Autowired
    private VerticalBusinessRepository businessRepository;
    /**
     * angelSettlementHandlerManager
     */
    @Resource
    private AngelSettlementHandlerManager angelSettlementHandlerManager;
    /**
     * medicalPromiseApplication
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;
    /**
     * promiseExtApplication
     */
    @Resource
    private PromiseExtApplication promiseExtApplication;

    /**
     * conditions
     */
    private final Map<String, DomainAbility> conditions = Maps.newConcurrentMap();

    /**
     * actions
     */
    private final Map<String, DomainAbility> actions = Maps.newConcurrentMap();
    /**
     * applicationContext
     */
    @Resource
    private ApplicationContext applicationContext;
    /**
     * jdOrderApplication
     */
    @Autowired
    private JdOrderApplication jdOrderApplication;
    /**
     * jdOrderRefundTaskRepository
     */
    @Resource
    private JdOrderRefundTaskRepository jdOrderRefundTaskRepository;
    /**
     *
     */
    @Autowired
    private ProductServiceItemApplication productServiceItemApplication;
    /**
     *
     */
    @Resource
    private GenerateIdFactory generateIdFactory;
    /**
     *
     */
    @Autowired
    private ExecutorPoolFactory executorPoolFactory;
    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;
    /**
     * settleOrderInfoRpc
     */
    @Resource
    private SettleOrderInfoRpc settleOrderInfoRpc;
    /**
     * 检测单仓储层
     */
    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;
    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;
    /**
     * jdOrderItemRepository
     */
    @Resource
    private JdOrderItemRepository jdOrderItemRepository;

    /**
     * fileManageService
     */
    @Resource
    private FileManageService fileManageService;

    /**
     *
     */
    @Resource
    private SettlementConfigDomainService settlementConfigDomainService;

    /**
     * 地址服务
     */
    @Autowired
    private AddressRpc addressRpc;

    /**
     * jdhAngelWorkSettleSnapshotRepository
     */
    @Autowired
    private JdhAngelWorkSettleSnapshotRepository jdhAngelWorkSettleSnapshotRepository;
    /**
     * angelWorkApplication
     */
    @Resource
    private AngelWorkApplication angelWorkApplication;

    /**
     * hySettleRpc
     */
    @Autowired
    private HySettleRpc hySettleRpc;

    /**
     * 获取订单服务结算金额
     *
     * @param jdServiceSettleParam
     * @return
     */
    @Override
    @LogAndAlarm
    public ServerSettleAmountBo getOrderSettleAmount(JdServiceSettleParam jdServiceSettleParam) {
        log.info("JdServiceSettleApplicationImpl -> getOrderSettleAmount jdServiceSettleParam:{}", JsonUtil.toJSONString(jdServiceSettleParam));
        Long promiseId = jdServiceSettleParam.getPromiseId();
        PromiseDto promiseDto = queryPromiseById(promiseId);
        String verticalCode = promiseDto.getVerticalCode();
        if(!this.checkSettleVerticalCode(verticalCode)){
            return null;
        }
        if (Objects.equals(duccConfig.getAngelSettlementPricingServiceSwitch(), true)) {
            return getOrderSettleAmountByPricingService(jdServiceSettleParam, verticalCode);
        }
        JdSettleAppoinmentDTO jdSettleAppoinmentDTO = new JdSettleAppoinmentDTO();
        jdSettleAppoinmentDTO.setVerticalCode(verticalCode);
        dealOrderAddressAndAppointTime(jdSettleAppoinmentDTO,promiseDto);

        // 查询护士
        JdhAngelDetailDto jdhAngelDetailDto = this.getJdhAngelDetail(jdServiceSettleParam.getAngelId());
        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseId(promiseId);
        // 检测单列表
        List<MedicalPromiseDTO> medicalPromises = getMedicalPromises(medicalPromiseListRequest);
        if(CollUtil.isEmpty(medicalPromises)){
            log.error("JdServiceSettleApplicationImpl -> getOrderSettleAmount medicalPromises is null,promiseId={}",promiseId);
            return null;
        }

        // 服务费
        ServerSettleAmountBo serverSettleAmountBo = getServiceAmount(jdSettleAppoinmentDTO,medicalPromises,jdhAngelDetailDto);
        // 查询结算比例
        Map<String,BigDecimal> settleTypeAmountMap = new HashMap<>();
        // 护士结算：时段费+距离费+动态调整费
        BigDecimal feeTotal = getAngelDoorHomeFee(jdhAngelDetailDto,jdSettleAppoinmentDTO,settleTypeAmountMap,jdServiceSettleParam.getDispatchMarkupPrice(),medicalPromises);
        serverSettleAmountBo.setAngelFeeAmount(feeTotal);
        serverSettleAmountBo.setSettleTypeAmountMap(settleTypeAmountMap);
        serverSettleAmountBo.setSettleTotalAmount(serverSettleAmountBo.getSettleTotalAmount().add(feeTotal));
        serverSettleAmountBo.setOrderId(Objects.nonNull(jdServiceSettleParam.getOrderId()) ? jdServiceSettleParam.getOrderId() : jdServiceSettleParam.getPromiseId());
        serverSettleAmountBo.setAngelId(jdServiceSettleParam.getAngelId());
        serverSettleAmountBo.setWorkId(jdServiceSettleParam.getWorkId());
        serverSettleAmountBo.setPromiseId(jdServiceSettleParam.getPromiseId());

        if(jdServiceSettleParam.getSaveAngelsettleAmountSnapshot()){
            saveAngelSettleSnapshot(serverSettleAmountBo);
        }
        log.info("JdServiceSettleApplicationImpl -> getOrderSettleAmount end serverSettleAmountBo:{}", serverSettleAmountBo);
        return serverSettleAmountBo;
    }

    /**
     * 使用算价服务计算价格
     * @param jdServiceSettleParam
     * @param verticalCode
     * @return
     */
    private ServerSettleAmountBo getOrderSettleAmountByPricingService(JdServiceSettleParam jdServiceSettleParam, String verticalCode) {
        PricingServiceCalculateCmd cmd = new PricingServiceCalculateCmd();
        cmd.setVerticalCode(verticalCode);
        cmd.setScene(PricingServiceSceneEnum.ANGEL_PRE_INCOME_PRICE_CALCULATE.getScene());
        cmd.setOrderId(jdServiceSettleParam.getOrderId());
        cmd.setPromiseId(jdServiceSettleParam.getPromiseId());
        cmd.setAngelId(jdServiceSettleParam.getAngelId());
        Map<String, Object> factObjectMap = new HashMap<>();
        factObjectMap.put("dispatchMarkupPrice", jdServiceSettleParam.getDispatchMarkupPrice());
        cmd.setFactObjectMap(factObjectMap);
        PricingServiceCalculateResultDto calculateResultDto = pricingServiceApplication.calculatePriceForDetail(cmd);
        ServerSettleAmountBo serverSettleAmountBo = new ServerSettleAmountBo();
        serverSettleAmountBo.setAngelSkuServiceAmountMap(calculateResultDto.getSkuServiceAmountMap());

        //angelFeeAmount金额的费项组成设置
        //ArrayList<FeeAggregateTypeEnum> aggregateTypeEnums = Lists.newArrayList(FeeAggregateTypeEnum.TIME_PERIOD_FEE, FeeAggregateTypeEnum.HOME_VISIT_FEE, FeeAggregateTypeEnum.DYNAMIC_FEE);
        BigDecimal angelFeeAmount = BigDecimal.ZERO;
        Map<String, BigDecimal> settleTypeAmountMap = new HashMap<>();
        for (Map.Entry<String, BigDecimal> entry : calculateResultDto.getFeeAmountMap().entrySet()){
            JdOrderFeeTypeEnum feeTypeEnum = JdOrderFeeTypeEnum.getByName(entry.getKey());
            if (Objects.isNull(feeTypeEnum)) {
                continue;
            }
            if (!Objects.equals(feeTypeEnum.getAggregateTypeEnum(), FeeAggregateTypeEnum.SERVICE_FEE)) {
                angelFeeAmount = angelFeeAmount.add(entry.getValue());
                BigDecimal bigDecimal = settleTypeAmountMap.getOrDefault(feeTypeEnum.getAggregateTypeEnum().getCode(), BigDecimal.ZERO);
                bigDecimal = bigDecimal.add(entry.getValue());
                settleTypeAmountMap.put(feeTypeEnum.getAggregateTypeEnum().getCode(), bigDecimal);
            }
        }

        serverSettleAmountBo.setAngelFeeAmount(angelFeeAmount);
        serverSettleAmountBo.setSettleTypeAmountMap(settleTypeAmountMap);
        serverSettleAmountBo.setFeeAmountMap(calculateResultDto.getFeeAmountMap());
        serverSettleAmountBo.setSettleTotalAmount(calculateResultDto.getTotalPrice());
        serverSettleAmountBo.setMaterialFeeConfig(calculateResultDto.getMaterialFeeConfig());
        serverSettleAmountBo.setOrderId(Objects.nonNull(jdServiceSettleParam.getOrderId()) ? jdServiceSettleParam.getOrderId() : jdServiceSettleParam.getPromiseId());
        serverSettleAmountBo.setAngelId(jdServiceSettleParam.getAngelId());
        serverSettleAmountBo.setWorkId(jdServiceSettleParam.getWorkId());
        serverSettleAmountBo.setPromiseId(jdServiceSettleParam.getPromiseId());

        if(jdServiceSettleParam.getSaveAngelsettleAmountSnapshot()){
            saveAngelSettleSnapshot(serverSettleAmountBo);
        }
        log.info("JdServiceSettleApplicationImpl -> getOrderSettleAmount new end serverSettleAmountBo:{}", JSON.toJSONString(serverSettleAmountBo));
        return serverSettleAmountBo;
    }

    /**
     *
     * @param jdServiceSettleParam
     * @param verticalCode
     * @return
     */
    private Map<String, ServerSettleAmountBo> getOrderSettleAmountDiagramByPricingService(JdServiceSettleParam jdServiceSettleParam, String verticalCode) {
        PricingServiceCalculateCmd cmd = new PricingServiceCalculateCmd();
        cmd.setVerticalCode(verticalCode);
        cmd.setScene(PricingServiceSceneEnum.ANGEL_PRE_INCOME_PRICE_CALCULATE.getScene());
        cmd.setOrderId(jdServiceSettleParam.getOrderId());
        cmd.setPromiseId(jdServiceSettleParam.getPromiseId());
        cmd.setAngelId(jdServiceSettleParam.getAngelId());
        Map<String, Object> factObjectMap = new HashMap<>();
        factObjectMap.put("dispatchMarkupPrice", jdServiceSettleParam.getDispatchMarkupPrice());
        cmd.setFactObjectMap(factObjectMap);
        cmd.setJobNatureList(jdServiceSettleParam.getJobNatureList());
        Map<Integer, PricingServiceCalculateResultDto> calculateResultDtoMap = pricingServiceApplication.batchCalculatePriceForDetailByAngelJobNature(cmd);
        if (MapUtils.isEmpty(calculateResultDtoMap)) {
            return new HashMap<>();
        }
        Map<String, ServerSettleAmountBo> result = new HashMap<>();
        for (Map.Entry<Integer, PricingServiceCalculateResultDto> resultDtoEntry : calculateResultDtoMap.entrySet()) {

            Integer jobNature = resultDtoEntry.getKey();
            PricingServiceCalculateResultDto calculateResultDto = resultDtoEntry.getValue();

            ServerSettleAmountBo serverSettleAmountBo = new ServerSettleAmountBo();
            serverSettleAmountBo.setAngelSkuServiceAmountMap(calculateResultDto.getSkuServiceAmountMap());

            //angelFeeAmount金额的费项组成设置
            //ArrayList<FeeAggregateTypeEnum> aggregateTypeEnums = Lists.newArrayList(FeeAggregateTypeEnum.TIME_PERIOD_FEE, FeeAggregateTypeEnum.HOME_VISIT_FEE, FeeAggregateTypeEnum.DYNAMIC_FEE);
            BigDecimal angelFeeAmount = BigDecimal.ZERO;
            Map<String, BigDecimal> settleTypeAmountMap = new HashMap<>();
            for (Map.Entry<String, BigDecimal> entry : calculateResultDto.getFeeAmountMap().entrySet()){
                JdOrderFeeTypeEnum feeTypeEnum = JdOrderFeeTypeEnum.getByName(entry.getKey());
                if (Objects.isNull(feeTypeEnum)) {
                    continue;
                }
                if (!Objects.equals(feeTypeEnum.getAggregateTypeEnum(), FeeAggregateTypeEnum.SERVICE_FEE)) {
                    angelFeeAmount = angelFeeAmount.add(entry.getValue());
                    BigDecimal bigDecimal = settleTypeAmountMap.getOrDefault(feeTypeEnum.getAggregateTypeEnum().getCode(), BigDecimal.ZERO);
                    bigDecimal = bigDecimal.add(entry.getValue());
                    settleTypeAmountMap.put(feeTypeEnum.getAggregateTypeEnum().getCode(), bigDecimal);
                }
            }

            serverSettleAmountBo.setAngelFeeAmount(angelFeeAmount);
            serverSettleAmountBo.setSettleTypeAmountMap(settleTypeAmountMap);
            serverSettleAmountBo.setFeeAmountMap(calculateResultDto.getFeeAmountMap());
            serverSettleAmountBo.setMaterialFeeConfig(calculateResultDto.getMaterialFeeConfig());
            serverSettleAmountBo.setSettleTotalAmount(calculateResultDto.getTotalPrice());
            serverSettleAmountBo.setOrderId(Objects.nonNull(jdServiceSettleParam.getOrderId()) ? jdServiceSettleParam.getOrderId() : jdServiceSettleParam.getPromiseId());
            serverSettleAmountBo.setAngelId(jdServiceSettleParam.getAngelId());
            serverSettleAmountBo.setWorkId(jdServiceSettleParam.getWorkId());
            serverSettleAmountBo.setPromiseId(jdServiceSettleParam.getPromiseId());

            log.info("JdServiceSettleApplicationImpl -> getOrderSettleAmount new end serverSettleAmountBo:{}", JSON.toJSONString(serverSettleAmountBo));
            jdServiceSettleParam.getProfessionTitleCodeList().forEach(professionTitleCode -> {
                result.put(professionTitleCode + "-" + jobNature, serverSettleAmountBo);
            });
        }
        return result;
    }

    /**
     * verticalCode 是否符合算价场景 ducc
     * @param verticalCode
     * @return
     */
    private Boolean checkSettleVerticalCode(String verticalCode){
        if(StringUtils.isEmpty(verticalCode)){
            return false;
        }
        String settleVerticalCode = duccConfig.getSettleVerticalCode();
        if(settleVerticalCode.contains(verticalCode)){
            return true;
        }
        return false;
    }

    /**
     *
     * @param jdSettleAppoinmentDTO
     * @param promiseDto
     */
    private void dealOrderAddressAndAppointTime(JdSettleAppoinmentDTO jdSettleAppoinmentDTO,PromiseDto promiseDto){
        if(Objects.nonNull(promiseDto)){
            if(Objects.nonNull(promiseDto.getStore())){
                PromiseStationDto store = promiseDto.getStore();
                String storeAddr = store.getStoreAddr();
                BaseAddressBo jdAddressFromAddress = getBaseAddressBo(storeAddr);
                if(Objects.nonNull(jdAddressFromAddress)){
                    AddressInfoDTO addressInfo = new AddressInfoDTO();
                    addressInfo.setCityId(jdAddressFromAddress.getCityCode());
                    addressInfo.setProvinceId(jdAddressFromAddress.getProvinceCode());
                    addressInfo.setCountyId(jdAddressFromAddress.getDistrictCode());
                    addressInfo.setTownId(jdAddressFromAddress.getTownCode());
                    jdSettleAppoinmentDTO.setAddressInfo(addressInfo);
                }
            }
        }

        if(Objects.nonNull(promiseDto.getAppointmentTime())){
            PromiseAppointmentTimeDto appointmentTime = promiseDto.getAppointmentTime();
            if(Objects.nonNull(appointmentTime)){
                DispatchAppointmentTimeDto appointmentTimeDto = new DispatchAppointmentTimeDto();
                appointmentTimeDto.setAppointmentStartTime(DateUtil.formatDate(appointmentTime.getAppointmentStartTime(),CommonConstant.YMDHM));
                appointmentTimeDto.setIsImmediately(Objects.isNull(appointmentTime.getIsImmediately()) ? false : appointmentTime.getIsImmediately());
                jdSettleAppoinmentDTO.setAppointmentTimeDto(appointmentTimeDto);
            }
        }
    }

    /**
     * 查履约单
     * @param promiseId
     * @return
     */
    private PromiseDto queryPromiseById(Long promiseId){
        PromiseIdRequest request = new PromiseIdRequest();
        request.setPromiseId(promiseId);
        PromiseDto promiseDto = promiseApplication.findPromiseByPromiseId(request);
        return promiseDto;
    }

    /**
     *
     * @param storeAddr
     * @return
     */
    private BaseAddressBo getBaseAddressBo(String storeAddr){
        BaseAddressBo jdAddressFromAddress = addressRpc.getJDAddressFromAddress(storeAddr.trim());
        return jdAddressFromAddress;
    }
    /**
     *
     * @param serverSettleAmountBo
     */
    private void saveAngelSettleSnapshot(ServerSettleAmountBo serverSettleAmountBo) {
        JdhAngelWorkSettleSnapshot angelWorkSettleSnapshot = bulidJdhAngelWorkSettleSnapshot(serverSettleAmountBo);
        jdhAngelWorkSettleSnapshotRepository.saveOrUpdateAngelWorkSettleSnapshot(angelWorkSettleSnapshot);
    }

    /**
     * 保存算价因子快照
     * @param serverSettleAmountBo
     * @return
     */
    private JdhAngelWorkSettleSnapshot bulidJdhAngelWorkSettleSnapshot(ServerSettleAmountBo serverSettleAmountBo){
        JdhAngelWorkSettleSnapshot angelWorkSettleSnapshot = new JdhAngelWorkSettleSnapshot();
        angelWorkSettleSnapshot.setWorkId(serverSettleAmountBo.getWorkId());
        angelWorkSettleSnapshot.setAngelId(serverSettleAmountBo.getAngelId());
        angelWorkSettleSnapshot.setPromiseId(serverSettleAmountBo.getPromiseId());
        serverSettleAmountBo.setAngelId(serverSettleAmountBo.getAngelId());
        angelWorkSettleSnapshot.setSettleContext(JsonUtil.toJSONString(serverSettleAmountBo));
        return angelWorkSettleSnapshot;
    }

    /**
     * 保存算价因子快照
     * @param serverSettleAmountBo
     * @return
     */
    private JdOrderExt getJdOrderExtFeeFactor(ServerSettleAmountBo serverSettleAmountBo){
        JdOrderExt jdOrderExt = new JdOrderExt();
        jdOrderExt.setExtType(JdOrderExtTypeEnum.SERVICE_FEE_SNAPSHOT.getExtType());
        jdOrderExt.setExtContext(JsonUtil.toJSONString(serverSettleAmountBo));
        jdOrderExt.setYn(YnStatusEnum.YES.getCode());
        jdOrderExt.setOrderId(serverSettleAmountBo.getOrderId());
        jdOrderExt.setExtId(generateIdFactory.getId());
        jdOrderExt.setVersion(NumConstant.NUM_1);
        jdOrderExt.setCreateTime(new Date());
        jdOrderExt.setUpdateTime(new Date());
        return jdOrderExt;
    }

    /**
     * 护士完成单个服务开始结算
     * @param body
     */
    @Override
    @AutoTestSupport(skip = true)
    public void angelServiceFinishSettleAndEbs(MedicalPromiseEventBody body) {
        AngelServiceFinishSettlementContext context = SettlementApplicationConvert.INSTANCE.convertServiceFinishSettlementContext(body);
        context.setSettlementBusinessId(body.getPromisePatientId() + "_" + body.getServiceId());
        orderFinishServiceSettleRepeat(context,body.getServiceId());

        //CompletableFuture.runAsync(() -> angelSettleAndEbs(context), executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
        angelSettleAndEbs(context);
    }

    /**
     *
     * @param context
     */
    private void angelSettleAndEbs(AngelServiceFinishSettlementContext context){
        log.info("JdServiceSettleApplicationImpl.angelSettleAndEbs.start.context={}",JSON.toJSONString(context));
        this.queryNethpDocIdByAngelId(context);
        context.init(SettleEventTypeEnum.SERVER_SETTLE);
        AbilityExecutor executor = context.getExecutor();
        execute(executor, context);
        dealAddItemOrderInCome(context);
        log.info("JdServiceSettleApplicationImpl -> angelSettleAndEbs,context={}", JSON.toJSONString(context));

        if(context.getVoucherLastService()){
            // ext收入与ext支出
            List<OrderAngelSettleFeeBo> taskFeeInComeAmountList = dealParentTaskRefundDetail(context);
            if(CollUtil.isNotEmpty(taskFeeInComeAmountList)){
                Map<Long, List<OrderAngelSettleFeeBo>> medicalPromiseMap = taskFeeInComeAmountList.stream().collect(Collectors.groupingBy(OrderAngelSettleFeeBo::getOrderId));
                List<OrderAngelSettleFeeBo> feeInComeAmountList = context.getOrderAngelSettleDetailBo().getFeeInComeAmountList();
                for(OrderAngelSettleFeeBo orderAngelSettleFeeBo : feeInComeAmountList){
                    if(medicalPromiseMap.keySet().contains(orderAngelSettleFeeBo.getOrderId())){
                        List<OrderAngelSettleFeeBo> tempList = medicalPromiseMap.get(orderAngelSettleFeeBo.getOrderId());
                        orderAngelSettleFeeBo.setFeeInComeAmount(tempList.get(0).getFeeInComeAmount());
                    }
                }
            }
        }

        // 拉完成,先检查是否所有的履约单都完成，如果都完成并且当前履约单是最后一次拉订单完成
        if(context.getOrderLastService() && context.getFinishState()){
            if(orderFinishService(context, context.getServiceId())) {
                JdOrder jdOrder = JdOrder.builder().orderId(context.getJdOrderDetailBo().getOrderId()).build();
                eventCoordinator.publish((EventFactory.newDefaultEvent(jdOrder, TradeEventTypeEnum.SELF_ORDER_COMPLETE,new OrderCompleteEventBody(context.getJdOrderDetailBo().getOrderId()))));
            }
        }
        calcAngelSettleTime(context);
        AngelSettlementAndEbsDetail angelSettlementAndEbsDetail = SettlementApplicationConvert.INSTANCE.convertAngelSettlementAndEbsDetail(context);
        log.info("JdServiceSettleApplicationImpl -> angelSettleAndEbs,angelSettlementAndEbsDetail={}", JSON.toJSONString(angelSettlementAndEbsDetail));
        angelServiceToHyAndEbs(angelSettlementAndEbsDetail);
    }

    /**
     *
     * @param context
     */
    private void dealAddItemOrderInCome(AngelServiceFinishSettlementContext context){
        if(!CommonConstant.ONE_STR.equals(context.getFlag())){
            return;
        }
        Long medicalPromiseId = context.getMedicalPromiseId();
        if(Objects.isNull(medicalPromiseId)){
            return;
        }
        List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.queryMedicalPromiseList(
                MedicalPromiseListQuery.builder().allQuery(true).mergeMedicalId(medicalPromiseId).build());
        if(CollUtil.isEmpty(medicalPromiseList)){
            return;
        }
        List<JdOrder> jdOrderList = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(Long.valueOf(context.getSourceVoucherId())).build());
        if (CollUtil.isEmpty(jdOrderList)) {
            log.info("JdServiceSettleApplicationImpl.dealAddItemOrderInCome jdOrderList is null,promisePatientId={}",context.getPromisePatientId());
            return;
        }
        List<OrderAngelSettleFeeBo> feeInComeAmountList = context.getOrderAngelSettleDetailBo().getItemInComeAmountList();
        List<Long> orderIdList = jdOrderList.stream().map(JdOrder::getOrderId).collect(Collectors.toList());
        List<JdOrderItem> jdOrderItemList = jdOrderItemRepository.listByOrderIdList(orderIdList);
        Map<Long, List<JdOrderItem>> jdOrderItemListMap = jdOrderItemList.stream().collect(Collectors.groupingBy(JdOrderItem::getSkuId));
        medicalPromiseList = medicalPromiseList.stream().filter(item -> !item.getServiceId().equals(context.getServiceId())).collect(Collectors.toList());
        medicalPromiseList.stream().forEach(medicalPromise -> {
            List<JdOrderItem> itemList = jdOrderItemListMap.get(Long.parseLong(medicalPromise.getServiceId()));
            if(CollUtil.isNotEmpty(itemList)){
                JdOrderItem jdOrderItem = itemList.get(0);
                if(itemList.size() > 1){
                    Integer skuNum = itemList.stream().map(JdOrderItem::getSkuNum).reduce(Integer::sum).get();
                    jdOrderItem.setSkuNum(skuNum);
                    AtomicReference<BigDecimal> itemAmount = new AtomicReference<>(BigDecimal.ZERO);
                    itemList.forEach(item ->{
                        itemAmount.set(itemAmount.get().add(item.getItemAmount()));
                    });
                    BigDecimal itemAvgAmount = itemAmount.get().divide(BigDecimal.valueOf(skuNum), 2, RoundingMode.DOWN);
                    jdOrderItem.setItemAmount(itemAvgAmount);
                }
                OrderAngelSettleFeeBo orderAngelSettleFeeBo = new OrderAngelSettleFeeBo();
                orderAngelSettleFeeBo.setOrderId(jdOrderItem.getOrderId());
                orderAngelSettleFeeBo.setServiceId(medicalPromise.getServiceId());
                orderAngelSettleFeeBo.setItemInComeAmount(jdOrderItem.getItemAmount());
                feeInComeAmountList.add(orderAngelSettleFeeBo);
            }
        });
        log.info("JdServiceSettleApplicationImpl.dealAddItemOrderInCome feeInComeAmountList={}",JsonUtil.toJSONString(feeInComeAmountList));
    }
    /**
     *
     * @param context
     * @return
     */
    private List<OrderAngelSettleFeeBo> dealParentTaskRefundDetail(AngelServiceFinishSettlementContext context){
        Long parentId = context.getParentId();
        if(Objects.isNull(parentId)){
            Long orderId = context.getOrderId();
            JdOrder jdOrder = jdOrderApplication.queryJdOrderByOrderId(orderId);
            if(Objects.isNull(jdOrder)){

                return null;
            }
            parentId = jdOrder.getParentId();
        }

        List<OrderAngelSettleFeeBo> feeInComeAmountList = new ArrayList<>();
        if(Objects.nonNull(parentId) && parentId > 0){
            JdOrderRefundTask taskQuery = JdOrderRefundTask.builder().build();
            taskQuery.setParentId(parentId);
            List<JdOrderRefundTask> taskList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(taskQuery);
            for(JdOrderRefundTask task : taskList){
                OrderAngelSettleFeeBo orderAngelSettleFeeBo = getOrderAngelSettleFeeBo(task);
                if(Objects.nonNull(orderAngelSettleFeeBo)){
                    feeInComeAmountList.add(orderAngelSettleFeeBo);
                }
            }
        }



        return feeInComeAmountList;
    }

    /**
     *
     * @param jdOrderRefundTask
     * @return
     */
    private OrderAngelSettleFeeBo getOrderAngelSettleFeeBo(JdOrderRefundTask jdOrderRefundTask){
        String refundDetail = jdOrderRefundTask.getRefundDetail();
        if(StringUtils.isNotBlank(refundDetail)){
            OrderRefundDetailBo orderRefundDetailBo = JsonUtil.parseObject(refundDetail,OrderRefundDetailBo.class);
            BigDecimal feeInComeAmount = orderRefundDetailBo.getFeeInComeAmount();
            if(Objects.nonNull(feeInComeAmount) && feeInComeAmount.compareTo(BigDecimal.ZERO) >0){
                OrderAngelSettleFeeBo orderAngelSettleFeeBo = new OrderAngelSettleFeeBo();
                orderAngelSettleFeeBo.setFeeInComeAmount(feeInComeAmount);
                orderAngelSettleFeeBo.setOrderId(jdOrderRefundTask.getOrderId());
                return orderAngelSettleFeeBo;
            }
        }
        return null;
    }

    /**
     * 退款成功开始结算
     *
     * @param context
     */
    @Override
    @AutoTestSupport(skip = true)
    public void angelRefundSuccSettleAndEbs(AngelServiceFinishSettlementContext context) {
        context.init(SettleEventTypeEnum.REFUND_SERVER_SETTLE);
        AbilityExecutor executor = context.getExecutor();
        execute(executor, context);

        calcAngelSettleTime(context);
        AngelSettlementAndEbsDetail angelSettlementAndEbsDetail = SettlementApplicationConvert.INSTANCE.convertAngelSettlementAndEbsDetail(context);
        angelServiceToHyAndEbs(angelSettlementAndEbsDetail);

        if(!context.getAngelSettleService()){
            BigDecimal feeAmount = angelSettlementAndEbsDetail.getOrderAngelSettleDetailBo().getFeeOutComeAmount();
            if(context.getAngelSettleFee() || Objects.isNull(feeAmount)){
                feeAmount = BigDecimal.ZERO;
            }
            BigDecimal settleSubtractAmount = context.getAngelSkuServiceAmount().add(feeAmount);
            sendAngelSettleSubtractAmount(settleSubtractAmount,context.getPromiseId());
        }
    }

    /**
     * @param body
     */
    @Override
    public void orderFinishState(MedicalPromiseEventBody body) {
        log.info("SettleEventSubscriber -> orderFinishState body:{}",JSON.toJSONString(body));
        AngelServiceFinishSettlementContext context = SettlementApplicationConvert.INSTANCE.convertServiceFinishSettlementContext(body);
        context.setSettlementBusinessId(body.getPromisePatientId() + "_" + body.getServiceId());
        Boolean orderFinishState = orderFinishService(context,body.getServiceId());
        if(orderFinishState){
            Long orderId = context.getOrderId();
            if(Objects.isNull(orderId)){
                orderId = Long.parseLong(context.getSourceVoucherId());
            }
            context.setOrderId(orderId);
            JdOrderDetailBo jdOrderDetailBo = settleOrderInfoRpc.getSplitOrderSettleDetail(context.getOrderId(),context.getServiceId(),context.getPromiseId());
            JdOrder jdOrder = JdOrder.builder().orderId(jdOrderDetailBo.getOrderId()).build();
            eventCoordinator.publish((EventFactory.newDefaultEvent(jdOrder, TradeEventTypeEnum.SELF_ORDER_COMPLETE,new OrderCompleteEventBody(jdOrderDetailBo.getOrderId()))));
        }
    }

    /**
     * 完成服务结算防重
     * @param context
     * @return
     */
    private Boolean orderFinishService(AngelServiceFinishSettlementContext context,String serviceId){
        Long promiseId = context.getPromiseId();
        PromiseDto jdhPromise = promiseExtApplication.findVoucherIdByPromiseId(promiseId);
        if(Objects.isNull(jdhPromise)){
            return false;
        }
        Long voucherId = jdhPromise.getVoucherId();
        context.setVoucherId(voucherId);
        context.setPaymentTime(jdhPromise.getCreateTime());
        context.setSourceVoucherId(jdhPromise.getSourceVoucherId());
        List<Long> promiseIdList = new ArrayList<>();
        List<PromiseDto> result = promiseExtApplication.getPromiseByOrderItemId(jdhPromise.getSourceVoucherId());
        if(CollUtil.isEmpty(result)){
            promiseIdList.add(promiseId);
        }else{
            promiseIdList = result.stream().map(PromiseDto::getPromiseId).collect(Collectors.toList());
        }

        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseIdList(promiseIdList);
        // 检测单列表
        List<MedicalPromiseDTO> medicalPromises = getMedicalPromises(medicalPromiseListRequest);
        if(medicalPromises.size() == CommonConstant.ONE){
            return true;
        }
        List<MedicalPromiseDTO> medicalPromisesTemp = medicalPromises.stream().filter(medical ->
                medical.getServiceId().toString().equals(serviceId)).collect(Collectors.toList());
        // 退款中的，先不记收入
        if(CollUtil.isNotEmpty(medicalPromisesTemp)){
            List<MedicalPromiseDTO> medicalPromisesInvalid = medicalPromisesTemp.stream().filter(medical ->
                    !MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(medical.getStatus())).collect(Collectors.toList());
            if(CollUtil.isEmpty(medicalPromisesInvalid)){
                return true;
            }
            medicalPromisesInvalid = medicalPromisesInvalid.stream().filter(medical -> !(medical.getFreeze().equals(CommonConstant.ONE) ||
                    MedicalPromiseStatusEnum.INVALID.getStatus().equals(medical.getStatus()))).collect(Collectors.toList());
            if(CollUtil.isEmpty(medicalPromisesInvalid)){
                return true;
            }
        }
        return false;
    }

    @Override
    public String findAngelWorkSettleSnapshot(Long promiseId) {
        AngelWorkSettleSnapshotDBQuery query = new AngelWorkSettleSnapshotDBQuery();
        query.setPromiseId(promiseId);
        JdhAngelWorkSettleSnapshot settleSnapshot = jdhAngelWorkSettleSnapshotRepository.findAngelWorkSettleSnapshot(query);
        if(Objects.nonNull(settleSnapshot)) {
            return settleSnapshot.getSettleContext();
        }
        return "";
    }

    /**
     * 护士邀请活动结算
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.settlement.service.impl.JdServiceSettleApplicationImpl.angelInviteActivitySettlement")
    public Boolean angelInviteActivitySettlement(AngelInviteActivitySettlementCmd cmd) {
        log.info("JdServiceSettleApplicationImpl angelInviteActivitySettlement cmd={}", JSON.toJSONString(cmd));
        // 去重校验
        AngelSettlementQueryContext queryContext = new AngelSettlementQueryContext();
        queryContext.setAngelId(cmd.getAngelId());
        queryContext.setItemSourceId(cmd.getItemSourceId());
        queryContext.setItemType(cmd.getItemType());
        List<AngelSettlement> angelSettlements = angelSettlementRepository.querySettlementList(queryContext);
        log.info("JdServiceSettleApplicationImpl angelInviteActivitySettlement angelSettlements={}", JSON.toJSONString(angelSettlements));
        if (CollectionUtils.isNotEmpty(angelSettlements)){
            return false;
        }

        // 构建护士结算
        Long settleId = generateIdFactory.getId();
        List<AngelSettlement> angelSettlementList = this.buildAngelInviteActivitySettlement(cmd, settleId);

        // 构建护士结算明细
        Long settleDetailId = generateIdFactory.getId();
        List<AngelSettlementDetail> angelSettlementDetailList = this.buildAngelInviteActivitySettlementDetail(cmd, settleId, settleDetailId);

        // 保存护士结算&&明细
        Long count = angelSettlementRepository.batchSaveAngelSettlementAndDetail(angelSettlementList, angelSettlementDetailList);
        log.info("JdServiceSettleApplicationImpl batchSaveAngelSettlementAndDetail count={}", count);
        if (count>0){
            // 护士账户添加收入
            Boolean hySettleResult = this.addWithdrawAccountAmount(cmd);
            if (hySettleResult){
                // 同步互医状态-已同步
                AngelSettlementUpdateContext angelSettlementUpdateContext = new AngelSettlementUpdateContext();
                angelSettlementUpdateContext.setSettleIdList(Collections.singletonList(settleId));
                angelSettlementUpdateContext.setSyncStatus(1);
                angelSettlementRepository.updateSyncStatusBySettleId(angelSettlementUpdateContext);
                return true;
            }
        }
        return false;
    }

    /**
     * 清洗商品和项目结算价
     * @param ossKey
     * @return
     */
    @Override
    public Boolean cleanSkuItemSettlementPrice(String ossKey) {
        //获取文件流
        InputStream inputStream = fileManageService.get(ossKey, FileManageServiceImpl.FolderPathEnum.DATA_CLEAN);

        List<CleanSkuItemSettlementPriceExcelBO> importResult = EasyExcelUtil.read(inputStream, 1, CleanSkuItemSettlementPriceExcelBO.class);
        log.info("JdServiceSettleApplicationImpl -> cleanSkuItemSettlementPrice, importResult={}", JSON.toJSONString(importResult));

        for (CleanSkuItemSettlementPriceExcelBO bo : importResult) {
            if (StringUtils.equals(bo.getType(), "SKU") && StringUtils.isNotBlank(bo.getCode()) && Objects.nonNull(bo.getAngelSettlementPrice())) {
                BigDecimal angelSettlementPrice = new BigDecimal(bo.getAngelSettlementPrice());

                //保存结算价格相关信息
                SettlementConfigContext context = new SettlementConfigContext();
                List<ExternalDomainFeeConfigSaveBo> externalDomainFeeConfigList = Lists.newArrayList(ExternalDomainFeeConfigSaveBo.builder()
                        .domainCode(DomainEnum.PRODUCT).aggregateCode(ProductAggregateEnum.PRODUCT_SKU)
                        .aggregateId(bo.getCode()).settlementSubjectType(SettlementSubjectTypeEnum.NURSE)
                        .detailConfigList(Lists.newArrayList(SettlementFeeDetailSaveBo.builder().feeType(JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.getType()).feeAmount(angelSettlementPrice).build()))
                        .build());
                context.setExternalDomainFeeConfigList(externalDomainFeeConfigList);
                settlementConfigDomainService.batchSaveExternalDomainFeeConfig(context);

            } else if (StringUtils.equals(bo.getType(), "服务项目") && StringUtils.isNotBlank(bo.getCode()) && Objects.nonNull(bo.getAngelSettlementPrice())) {
                BigDecimal angelSettlementPrice = new BigDecimal(bo.getAngelSettlementPrice());

                //保存结算价格相关信息
                SettlementConfigContext context = new SettlementConfigContext();
                List<ExternalDomainFeeConfigSaveBo> externalDomainFeeConfigList = Lists.newArrayList(ExternalDomainFeeConfigSaveBo.builder()
                        .domainCode(DomainEnum.PRODUCT).aggregateCode(ProductAggregateEnum.PRODUCT_STANDARD_ITEM)
                        .aggregateId(String.valueOf(bo.getCode())).settlementSubjectType(SettlementSubjectTypeEnum.NURSE)
                        .detailConfigList(Lists.newArrayList(SettlementFeeDetailSaveBo.builder().feeType(JdOrderFeeTypeEnum.ANGEL_SERVICE_FEE.getType()).feeAmount(angelSettlementPrice).build()))
                        .build());
                context.setExternalDomainFeeConfigList(externalDomainFeeConfigList);
                settlementConfigDomainService.batchSaveExternalDomainFeeConfig(context);
            }
        }

        return true;
    }

    /**
     * 派单预估结算价
     * @param jdServiceSettleParam
     * @return
     */
    @Override
    @LogAndAlarm
    public Map<String, ServerSettleAmountBo> getOrderSettleAmountDiagram(JdServiceSettleParam jdServiceSettleParam) {
        if (CollectionUtils.isEmpty(jdServiceSettleParam.getJobNatureList()) || CollectionUtils.isEmpty(jdServiceSettleParam.getProfessionTitleCodeList())) {
            return new HashMap<>();
        }

        Long promiseId = jdServiceSettleParam.getPromiseId();
        PromiseDto promiseDto = queryPromiseById(promiseId);
        String verticalCode = promiseDto.getVerticalCode();
        if(!this.checkSettleVerticalCode(verticalCode)){
            return null;
        }
        if (Objects.equals(duccConfig.getAngelSettlementPricingServiceSwitch(), true)) {
            return getOrderSettleAmountDiagramByPricingService(jdServiceSettleParam, verticalCode);
        }
        JdSettleAppoinmentDTO jdSettleAppoinmentDTO = new JdSettleAppoinmentDTO();
        jdSettleAppoinmentDTO.setVerticalCode(verticalCode);
        dealOrderAddressAndAppointTime(jdSettleAppoinmentDTO,promiseDto);

        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseId(promiseId);
        // 检测单列表
        List<MedicalPromiseDTO> medicalPromises = getMedicalPromises(medicalPromiseListRequest);
        if(CollUtil.isEmpty(medicalPromises)){
            log.error("JdServiceSettleApplicationImpl -> getOrderSettleAmountDiagram medicalPromises is null,promiseId={}",promiseId);
            return null;
        }

        // 服务费
        Map<String, ServerSettleAmountBo> professionTitleSettleAmountMap = getServiceAmountDiagram(jdSettleAppoinmentDTO,medicalPromises, jdServiceSettleParam);

        Map<String, ServerSettleAmountBo> result =  new HashMap<>();
        List<Integer> jobNatureList = jdServiceSettleParam.getJobNatureList();

        for (Map.Entry<String, ServerSettleAmountBo> entry : professionTitleSettleAmountMap.entrySet()) {
            for (Integer jobNature : jobNatureList) {
                try {
                    ServerSettleAmountBo serverSettleAmountBo = new ServerSettleAmountBo();
                    ServerSettleAmountBo settleAmountBo = entry.getValue();
                    BeanUtil.copyProperties(settleAmountBo,serverSettleAmountBo);

                    //如果是全职护士，服务费置为0元
                    if(JobNatureEnum.FULL_TIME.getValue() == jobNature){
                        serverSettleAmountBo.setSettleTotalAmount(BigDecimal.ZERO);
                        Map<Long, BigDecimal> settleTypeAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
                        settleTypeAmountMap.keySet().forEach(key -> {
                            settleTypeAmountMap.put(key, BigDecimal.ZERO);
                        });
                    }


                    // 查询结算比例
                    Map<String,BigDecimal> settleTypeAmountMap = new HashMap<>();
                    // 护士结算：时段费+距离费+动态调整费
                    BigDecimal feeTotal = getAngelDoorHomeFee(jobNature,jdSettleAppoinmentDTO,settleTypeAmountMap,jdServiceSettleParam.getDispatchMarkupPrice(),medicalPromises);
                    serverSettleAmountBo.setAngelFeeAmount(feeTotal);
                    serverSettleAmountBo.setSettleTypeAmountMap(settleTypeAmountMap);
                    serverSettleAmountBo.setSettleTotalAmount(serverSettleAmountBo.getSettleTotalAmount().add(feeTotal));
                    serverSettleAmountBo.setOrderId(Objects.nonNull(jdServiceSettleParam.getOrderId()) ? jdServiceSettleParam.getOrderId() : jdServiceSettleParam.getPromiseId());
                    serverSettleAmountBo.setAngelId(jdServiceSettleParam.getAngelId());
                    serverSettleAmountBo.setWorkId(jdServiceSettleParam.getWorkId());
                    serverSettleAmountBo.setPromiseId(jdServiceSettleParam.getPromiseId());

                    if(jdServiceSettleParam.getSaveAngelsettleAmountSnapshot()){
                        saveAngelSettleSnapshot(serverSettleAmountBo);
                    }
                    log.info("JdServiceSettleApplicationImpl -> getOrderSettleAmountDiagram end serverSettleAmountBo:{}", serverSettleAmountBo);
                    result.put(entry.getKey() + "-" + jobNature, serverSettleAmountBo);
                } catch (Exception e) {
                    log.warn("JdServiceSettleApplicationImpl -> getOrderSettleAmountDiagram entry:{}, jobNature={}", JSON.toJSONString(entry), jobNature, e);
                }
            }
        }
        return result;
    }

    private Boolean addWithdrawAccountAmount(AngelInviteActivitySettlementCmd cmd) {
        AngelAddAccountAmountVo angelAddAccountAmountVo = new AngelAddAccountAmountVo();
        angelAddAccountAmountVo.setAccountId(getNethpDocIdByAngelId(cmd.getAngelId()));
        angelAddAccountAmountVo.setAmount(cmd.getSettleAmount());
        angelAddAccountAmountVo.setRecordTime(new Date());
        angelAddAccountAmountVo.setWithdrawalTime(cmd.getExpectSettleTime());
        angelAddAccountAmountVo.setChangeBusinessId(generateIdFactory.getIdStr());
        angelAddAccountAmountVo.setFeeType(SettleItemTypeEnum.ACTIVITY.getHuYiFeeType());
        angelAddAccountAmountVo.setOutBusinessData(JsonUtil.toJSONString(angelAddAccountAmountVo));
        return hySettleRpc.addWithdrawAccountAmount(angelAddAccountAmountVo);
    }

    private List<AngelSettlementDetail> buildAngelInviteActivitySettlementDetail(AngelInviteActivitySettlementCmd cmd, Long settleId, Long settleDetailId) {
        List<AngelSettlementDetail> angelSettlementDetailList = new ArrayList<>();
        AngelSettlementDetail angelSettlementDetail = new AngelSettlementDetail();
        angelSettlementDetail.setSettleId(settleId);
        angelSettlementDetail.setFeeName(SettleItemTypeEnum.getSettleTypeEnumByType(cmd.getItemType()).getDesc());
        angelSettlementDetail.setSettleAmount(cmd.getSettleAmount());
        angelSettlementDetail.setSettleDetailId(settleDetailId);
        angelSettlementDetailList.add(angelSettlementDetail);
        log.info("JdServiceSettleApplicationImpl buildAngelInviteActivitySettlementDetail angelSettlementDetailList={}", JSON.toJSONString(angelSettlementDetailList));
        return angelSettlementDetailList;
    }

    private List<AngelSettlement> buildAngelInviteActivitySettlement(AngelInviteActivitySettlementCmd cmd, Long settleId) {
        List<AngelSettlement> angelSettlementList = new ArrayList<>();
        AngelSettlement angelSettlement = new AngelSettlement();
        angelSettlement.setAngelId(cmd.getAngelId());
        angelSettlement.setSettlementType(SettleTypeEnum.INCOME.getType());
        angelSettlement.setItemType(cmd.getItemType());
        angelSettlement.setSettleAmount(cmd.getSettleAmount());
        angelSettlement.setSettleTime(new Date());
        angelSettlement.setExpectSettleTime(cmd.getExpectSettleTime());
        angelSettlement.setSettleStatus(SettleStatusEnum.FREEZE.getType());
        angelSettlement.setSettleId(settleId);
        angelSettlement.setItemSourceId(cmd.getItemSourceId());
        angelSettlement.setSyncStatus(0);
        angelSettlementList.add(angelSettlement);

        AngelSettlement angelSettlement2 = new AngelSettlement();
        BeanUtil.copyProperties(angelSettlement,angelSettlement2);
        angelSettlement2.setSettleId(generateIdFactory.getId());
        angelSettlement2.setSettleStatus(SettleStatusEnum.INIT.getType());
        angelSettlementList.add(angelSettlement2);

        log.info("JdServiceSettleApplicationImpl buildAngelInviteActivitySettlement angelSettlementList={}", JSON.toJSONString(angelSettlementList));
        return angelSettlementList;
    }


    /**
     *
     * @param angelId
     * @return
     */
    private Long getNethpDocIdByAngelId(Long angelId){
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelId(angelId);
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if(Objects.nonNull(jdhAngelDto) && Objects.nonNull(jdhAngelDto.getNethpDocId())){
            return jdhAngelDto.getNethpDocId();
        }else{
            throw new BusinessException(TradeErrorCode.ANGEL_INFO_NULL);
        }
    }

    /**
     * 作废成功开始结算
     *
     * @param context
     */
    @Override
    @AutoTestSupport(skip = true)
    public void invalidVoucherSettleAndEbs(AngelServiceFinishSettlementContext context) {
        context.init(SettleEventTypeEnum.REFUND_SERVER_SETTLE);
        AbilityExecutor executor = context.getExecutor();
        execute(executor, context);

        calcAngelSettleTime(context);
        AngelSettlementAndEbsDetail angelSettlementAndEbsDetail = SettlementApplicationConvert.INSTANCE.convertAngelSettlementAndEbsDetail(context);
        angelServiceToHyAndEbs(angelSettlementAndEbsDetail);

        if(!context.getAngelSettleService()){
            BigDecimal feeAmount = angelSettlementAndEbsDetail.getOrderAngelSettleDetailBo().getFeeOutComeAmount();
            if(context.getAngelSettleFee() || Objects.isNull(feeAmount)){
                feeAmount = BigDecimal.ZERO;
            }
            BigDecimal settleSubtractAmount = context.getAngelSkuServiceAmount().add(feeAmount);
            sendAngelSettleSubtractAmount(settleSubtractAmount,context.getPromiseId());
        }
    }
    /**
     *
     * @param settleSubtractAmount
     */
    private void sendAngelSettleSubtractAmount(BigDecimal settleSubtractAmount,Long promiseId){
        JdhPromise jdhPromise = JdhPromise.builder().promiseId(promiseId).build();
        eventCoordinator.publish((EventFactory.newDefaultEvent(jdhPromise, TradeEventTypeEnum.ORDER_REFUND_ANGEL_SETTLE_AMOUNT,
                new OrderRefundAngelAountEventBody(settleSubtractAmount,promiseId))));
    }

    /**
     *
     * @param angelSettlementAndEbsDetail
     */
    private void angelServiceToHyAndEbs(AngelSettlementAndEbsDetail angelSettlementAndEbsDetail) {
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = angelSettlementAndEbsDetail.getOrderAngelSettleDetailBo();
        if(Objects.isNull(orderAngelSettleDetailBo)){
            log.error("JdServiceSettleApplicationImpl -> angelServiceToHyAndEbs angelSettlementAndEbsDetail:{}", angelSettlementAndEbsDetail);
            return;
        }
        log.info("JdServiceSettleApplicationImpl -> angelServiceToHyAndEbs orderAngelSettleDetailBo={}", JSON.toJSONString(orderAngelSettleDetailBo));
        JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(angelSettlementAndEbsDetail.getVerticalCode());
        BusinessModeEnum businessModeEnum = BusinessModeEnum.getEnumByCode(jdhVerticalBusiness.getBusinessModeCode());
        angelSettlementAndEbsDetail.setItemType(businessModeEnum.getItemType());

        if(angelSettlementAndEbsDetail.getAngelSettleBack()){
            BigDecimal refundBackAmount = orderAngelSettleDetailBo.getRefundBackSkuAmount();
            if(Objects.nonNull(refundBackAmount) && refundBackAmount.compareTo(BigDecimal.ZERO) > 0){
                angelSettlementAndEbsDetail.setBusinessModeEnum(businessModeEnum);
                AngelSettlementHandler handler = angelSettlementHandlerManager.getHandler(EbsSettleSplitTypeEnum.SKU_FEE.getType() + EbsSettleTypeEnum.REFUND.getType());
                angelSettlementAndEbsDetail.setSettleAmout(refundBackAmount.negate());
                handler.prepareSettlementEbs(angelSettlementAndEbsDetail);
            }
            BigDecimal refundBackFeeAmount = orderAngelSettleDetailBo.getRefundBackFeeAmount();
            if(Objects.nonNull(refundBackFeeAmount) && refundBackFeeAmount.compareTo(BigDecimal.ZERO) > 0){
                angelSettlementAndEbsDetail.setBusinessModeEnum(businessModeEnum);
                AngelSettlementHandler handler = angelSettlementHandlerManager.getHandler(EbsSettleSplitTypeEnum.HOME_PERIOD_FEE.getType() + EbsSettleTypeEnum.REFUND.getType());
                angelSettlementAndEbsDetail.setSettleAmout(refundBackFeeAmount.negate());
                handler.prepareSettlementEbs(angelSettlementAndEbsDetail);
            }
            return;
        }

        // 1.保存结算流水与明细
        if(angelSettlementAndEbsDetail.getAngelSettleFlag()){
            executeSaveAngelSettleAndDetail(angelSettlementAndEbsDetail,orderAngelSettleDetailBo);
        }

        // 记收入：商品
        if(angelSettlementAndEbsDetail.getServiceInComeFlag()){
            List<OrderAngelSettleFeeBo> itemInComeAmountList = orderAngelSettleDetailBo.getItemInComeAmountList();
            if(CollUtil.isNotEmpty(itemInComeAmountList)){
                itemInComeAmountList.forEach(itemInComeAmountBo -> {
                    BigDecimal itemInComeAmount = itemInComeAmountBo.getItemInComeAmount();
                    String serviceId = StringUtil.isNotBlank(itemInComeAmountBo.getServiceId()) ? itemInComeAmountBo.getServiceId():angelSettlementAndEbsDetail.getServiceId();
                    JdOrderDetailBo jdOrderDetailBo = angelSettlementAndEbsDetail.getJdOrderDetailBo();
                    jdOrderDetailBo.setOrderId(itemInComeAmountBo.getOrderId() == null ? jdOrderDetailBo.getOrderId() : itemInComeAmountBo.getOrderId());
                    if(Objects.nonNull(itemInComeAmount) && itemInComeAmount.compareTo(BigDecimal.ZERO) > 0){
                        angelSettlementAndEbsDetail.setBusinessModeEnum(businessModeEnum);
                        AngelSettlementHandler handler = angelSettlementHandlerManager.getHandler(businessModeEnum.getItemType().toString()
                                + SettleTypeEnum.INCOME.getType());
                        angelSettlementAndEbsDetail.setServiceId(serviceId);
                        angelSettlementAndEbsDetail.setSettleAmout(itemInComeAmount);
                        angelSettlementAndEbsDetail.setOrderId(itemInComeAmountBo.getOrderId());
                        angelSettlementAndEbsDetail.setSettlementBusinessId(angelSettlementAndEbsDetail.getPromisePatientId() + "_" + itemInComeAmountBo.getOrderId());
                        handler.prepareSettlementEbs(angelSettlementAndEbsDetail);
                    }
                });
            }

        }

        // 记收入：订单其他费项
        String settlementBusinessId = angelSettlementAndEbsDetail.getSettlementBusinessId();
        if(angelSettlementAndEbsDetail.getFeeInComeFlag()){
            List<OrderAngelSettleFeeBo> feeInComeAmountList = orderAngelSettleDetailBo.getFeeInComeAmountList();
            if(CollUtil.isNotEmpty(feeInComeAmountList)){
                feeInComeAmountList.forEach(orderAngelSettleFeeBo -> {
                    if(Objects.nonNull(orderAngelSettleFeeBo.getFeeInComeAmount()) && orderAngelSettleFeeBo.getFeeInComeAmount().compareTo(BigDecimal.ZERO) > 0){
                        JdOrderDetailBo jdOrderDetailBo = angelSettlementAndEbsDetail.getJdOrderDetailBo();
                        jdOrderDetailBo.setOrderId(orderAngelSettleFeeBo.getOrderId() == null ? jdOrderDetailBo.getOrderId() : orderAngelSettleFeeBo.getOrderId());

                        angelSettlementAndEbsDetail.setBusinessModeEnum(businessModeEnum);
                        AngelSettlementHandler handler = angelSettlementHandlerManager.getHandler(SettleItemTypeEnum.FEE.getType().toString()
                                + SettleTypeEnum.INCOME.getType());
                        angelSettlementAndEbsDetail.setSettlementBusinessId(orderAngelSettleFeeBo.getOrderId() + "_" + angelSettlementAndEbsDetail.getPromiseId());
                        angelSettlementAndEbsDetail.setSettleAmout(orderAngelSettleFeeBo.getFeeInComeAmount());
                        handler.prepareSettlementEbs(angelSettlementAndEbsDetail);
                    }
                });
            }
        }

        // 记收入：平台服务费
        if(angelSettlementAndEbsDetail.getFeeInComeFlag()){
            BigDecimal platformServiceInComeAmount = orderAngelSettleDetailBo.getPlatformServiceInComeAmount();
            if(Objects.nonNull(platformServiceInComeAmount)){
                platformServiceInComeAmount = platformServiceInComeAmount.abs();
                if(platformServiceInComeAmount.compareTo(BigDecimal.ZERO) > 0) {
                    JdOrderDetailBo jdOrderDetailBo = angelSettlementAndEbsDetail.getJdOrderDetailBo();
                    jdOrderDetailBo.setOrderId(jdOrderDetailBo.getOrderId());
                    angelSettlementAndEbsDetail.setBusinessModeEnum(businessModeEnum);
                    AngelSettlementHandler handler = angelSettlementHandlerManager.getHandler(EbsSettleSplitTypeEnum.PLATFORM_SERVICE_FEE.getType().toString()
                            + SettleTypeEnum.INCOME.getType());
                    angelSettlementAndEbsDetail.setSettlementBusinessId(jdOrderDetailBo.getOrderId() + "_" + angelSettlementAndEbsDetail.getPromiseId());
                    angelSettlementAndEbsDetail.setSettleAmout(platformServiceInComeAmount);
                    handler.prepareSettlementEbs(angelSettlementAndEbsDetail);
                }
            }
        }

        if(angelSettlementAndEbsDetail.getAngelSettleFlag()){
            // 支出：护士服务费
            List<BigDecimal> itemOutComeAmountList = orderAngelSettleDetailBo.getItemOutComeAmountList();
            if(CollUtil.isNotEmpty(itemOutComeAmountList)){
                itemOutComeAmountList.forEach(itemOutComeAmount -> {
                    if(Objects.nonNull(itemOutComeAmount) && itemOutComeAmount.compareTo(BigDecimal.ZERO) > 0){
                        angelSettlementAndEbsDetail.setBusinessModeEnum(businessModeEnum);
                        AngelSettlementHandler handler = angelSettlementHandlerManager.getHandler(businessModeEnum.getItemType().toString()
                                + SettleTypeEnum.EXPEND.getType());
                        angelSettlementAndEbsDetail.setSettlementBusinessId(settlementBusinessId);
                        angelSettlementAndEbsDetail.setSettleAmout(itemOutComeAmount);
                        handler.prepareSettlementEbs(angelSettlementAndEbsDetail);
                    }
                });
            }

//         支出：护士其他费项
            BigDecimal fee4EbsOutComeAmount = orderAngelSettleDetailBo.getFee4EbsOutComeAmount();
            if(Objects.nonNull(fee4EbsOutComeAmount) && fee4EbsOutComeAmount.compareTo(BigDecimal.ZERO) > 0){
                angelSettlementAndEbsDetail.setBusinessModeEnum(businessModeEnum);
                AngelSettlementHandler handler = angelSettlementHandlerManager.getHandler(SettleItemTypeEnum.FEE.getType().toString()
                        + SettleTypeEnum.EXPEND.getType());
                angelSettlementAndEbsDetail.setItemType(SettleItemTypeEnum.FEE.getType());
                angelSettlementAndEbsDetail.setSettleAmout(fee4EbsOutComeAmount);

                handler.prepareSettlementEbs(angelSettlementAndEbsDetail);
            }

            // 支出：护士平台服务费（负数，收护士钱）
            BigDecimal platformServiceInComeAmount = orderAngelSettleDetailBo.getPlatformServiceInComeAmount();
            if(Objects.nonNull(platformServiceInComeAmount) && Objects.equals(duccConfig.getPlatformExpendSwitch(), true)){
                platformServiceInComeAmount = platformServiceInComeAmount.abs();
                if(platformServiceInComeAmount.compareTo(BigDecimal.ZERO) > 0) {
                    angelSettlementAndEbsDetail.setBusinessModeEnum(businessModeEnum);
                    AngelSettlementHandler handler = angelSettlementHandlerManager.getHandler(SettleItemTypeEnum.PLATFORM_SERVICE_FEE.getType().toString()
                            + SettleTypeEnum.EXPEND.getType());
                    angelSettlementAndEbsDetail.setItemType(SettleItemTypeEnum.PLATFORM_SERVICE_FEE.getType());
                    angelSettlementAndEbsDetail.setSettleAmout(platformServiceInComeAmount.negate());

                    handler.prepareSettlementEbs(angelSettlementAndEbsDetail);
                }
            }
        }

    }


    /**
     *
     * @param context
     */


    private void calcAngelSettleTime(AngelServiceFinishSettlementContext context) {
        context.setSettleTime(getAppointmentDate(context.getPromiseId()));
        if(!context.getAngelSettleFlag()){
            log.info("JdServiceSettleApplicationImpl -> calcAngelSettleTime angelSettleFlag is false angelId:{}", context.getAngelId());
            return;
        }
        Set<Long> angelBlackListSet = duccConfig.getAngelBlackListSet();
        if(angelBlackListSet.contains(context.getAngelId())){
            log.info("JdServiceSettleApplicationImpl -> calcAngelSettleTime 护士结算黑名单不结算 angelId:{}", context.getAngelId());
            context.setAngelSettleFlag(Boolean.FALSE);
            return;
        }
        this.bulidSettleTime(context);
        log.info("[SettleEventSubscriber->calcAngelSettleTime],context={}", JSON.toJSONString(context));
    }


    /**
     *
     * @param context
     */
    private void queryNethpDocIdByAngelId(AngelServiceFinishSettlementContext context) {
        AngelWorkDetailDto angelWorkDto = angelPromiseApplication.queryAngelWork(AngelWorkQuery.builder().promiseId(context.getPromiseId()).build());
        if (Objects.isNull(angelWorkDto) || Objects.isNull(angelWorkDto.getAngelId())) {
            return;
        }
        log.info("JdServiceSettleApplicationImpl.queryNethpDocIdByAngelId.angelWorkDto={}",JSON.toJSONString(angelWorkDto));
        context.setAngelId(Long.parseLong(angelWorkDto.getAngelId()));
        if (angelWorkDto.getWorkType().equals(1)) {
            log.info("JdServiceSettleApplicationImpl.queryNethpDocIdByAngelId.workType={}",JSON.toJSONString(angelWorkDto.getWorkType()));
            context.setNethpDocId(123456L);
            context.setJobNature(JobNatureEnum.NULL.getValue());
            return;
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelId(context.getAngelId());
        log.info("JdServiceSettleApplicationImpl.queryNethpDocIdByAngelId.after.context={}",JSON.toJSONString(context));
        JdhAngelDetailDto jdhAngelDto = this.getJdhAngelDetail(context.getAngelId());
        if (Objects.nonNull(jdhAngelDto) && Objects.nonNull(jdhAngelDto.getNethpDocId())) {
            context.setNethpDocId(jdhAngelDto.getNethpDocId());
            context.setJobNature(jdhAngelDto.getJobNature());
        } else {
            throw new BusinessException(TradeErrorCode.ANGEL_INFO_NULL);
        }
    }

    /**
     *
     * @param angelSettlementAndEbsDetail
     */
    private void executeSaveAngelSettleAndDetail(AngelSettlementAndEbsDetail angelSettlementAndEbsDetail,OrderAngelSettleDetailBo orderAngelSettleDetailBo) {
        log.info("JdServiceSettleApplicationImpl -> executeSaveAngelSettleAndDetail start orderId :{}", angelSettlementAndEbsDetail.getOrderId());
        // 1.保存结算流水与明细
        List<BigDecimal> itemOutComeAmountList = orderAngelSettleDetailBo.getItemOutComeAmountList();
        List<AngelSettlement> angelSettlementList = new ArrayList<>();
        List<AngelSettlementDetail> angelSettlementDetailList = new ArrayList<>();
        AngelSettleQuery angelSettleQuery = new AngelSettleQuery();
        if(CollUtil.isNotEmpty(itemOutComeAmountList)){
            itemOutComeAmountList.forEach(itemInComeAmount -> {
                angelSettleQuery.setSettlementBusinessId(angelSettlementAndEbsDetail.getSettlementBusinessId());
                angelSettleQuery.setItemType(angelSettlementAndEbsDetail.getItemType());
                AngelSettlementDto res = serviceSettleReadApplication.querySettlement(angelSettleQuery);
                if(Objects.isNull(res)){
                    if(Objects.nonNull(itemInComeAmount) && itemInComeAmount.compareTo(BigDecimal.ZERO) > 0){
                        Long settleId = generateIdFactory.getId();
                        AngelSettlement angelSettlement = SettlementDomainConvert.instance.
                                packAngelServiceSettlement(angelSettlementAndEbsDetail,itemInComeAmount,angelSettlementAndEbsDetail.getItemType());
                        angelSettlement.setSettleId(settleId);
                        angelSettlementList.add(angelSettlement);

                        Long settleDetailId = generateIdFactory.getId();
                        AngelSettlementDetail angelSettlementDetail = SettlementDomainConvert.instance.
                                packAngelSettlementDetail(settleId,angelSettlementAndEbsDetail.getItemType(),itemInComeAmount);
                        angelSettlementDetail.setSettleDetailId(settleDetailId);
                        angelSettlementDetailList.add(angelSettlementDetail);
                    }
                }
            });
        }

        BigDecimal feeOutComeAmount = orderAngelSettleDetailBo.getFeeOutComeAmount();
        if(Objects.nonNull(feeOutComeAmount)){
            angelSettleQuery.setOrderId(angelSettlementAndEbsDetail.getOrderId());
            angelSettleQuery.setItemType(SettleItemTypeEnum.FEE.getType());
            AngelSettlementDto res = serviceSettleReadApplication.querySettlement(angelSettleQuery);
            if(Objects.isNull(res)){
                if(Objects.nonNull(feeOutComeAmount) && feeOutComeAmount.compareTo(BigDecimal.ZERO) > 0){
                    Long settleId = generateIdFactory.getId();
                    AngelSettlement angelFeeSettlement = SettlementDomainConvert.instance.
                            packAngelServiceSettlement(angelSettlementAndEbsDetail,feeOutComeAmount,SettleItemTypeEnum.FEE.getType());
                    angelFeeSettlement.setSettleId(settleId);
                    angelSettlementList.add(angelFeeSettlement);
                    handleAngelSettlementDetailList(settleId,angelSettlementAndEbsDetail.getItemType(),
                            angelSettlementDetailList,orderAngelSettleDetailBo);
                }
            }else{
                orderAngelSettleDetailBo.setFeeOutComeAmount(null);
            }
        }
        /**如果当前是工单的最后一笔，处理聚合金额逻辑*/
        if (angelSettlementAndEbsDetail.getVoucherLastService()){
            handleMergeAngelSettlementLogic(angelSettlementAndEbsDetail,angelSettlementList);
        }
        angelSettlementRepository.batchSaveAngelSettlementAndDetail(angelSettlementList,angelSettlementDetailList);
    }

    /**
     *
     * @param settleId
     * @param itemType
     * @param orderAngelSettleDetailBo
     * @return
     */
    private List<AngelSettlementDetail> handleAngelSettlementDetailList(Long settleId,Integer itemType,
                                    List<AngelSettlementDetail> angelSettlementDetailList,OrderAngelSettleDetailBo orderAngelSettleDetailBo){
        Map<String, BigDecimal> feeAmountMap = orderAngelSettleDetailBo.getFeeAmountMap();
        if(CollUtil.isNotEmpty(feeAmountMap)){
            for (Map.Entry<String, BigDecimal> entry : feeAmountMap.entrySet()){
                JdOrderFeeTypeEnum feeTypeEnum = JdOrderFeeTypeEnum.getByName(entry.getKey());
                if (Objects.isNull(feeTypeEnum) || Objects.isNull(entry.getValue()) || entry.getValue().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                if (!Objects.equals(feeTypeEnum.getAggregateTypeEnum(), FeeAggregateTypeEnum.SERVICE_FEE)) {
                    Long settleDetailId = generateIdFactory.getId();
                    AngelSettlementDetail angelSettlementDetail = new AngelSettlementDetail();
                    angelSettlementDetail.setSettleId(settleId);
                    angelSettlementDetail.setFeeName(feeTypeEnum.getCode());
                    angelSettlementDetail.setSettleAmount(entry.getValue());
                    angelSettlementDetail.setSettleDetailId(settleDetailId);
                    angelSettlementDetailList.add(angelSettlementDetail);
                }
            }
        }else {
            Long settleDetailId = generateIdFactory.getId();
            AngelSettlementDetail angelSettlementDetail = SettlementDomainConvert.instance.
                    packAngelSettlementDetail(settleId,itemType,orderAngelSettleDetailBo.getFeeOutComeAmount());
            angelSettlementDetail.setSettleDetailId(settleDetailId);
            angelSettlementDetailList.add(angelSettlementDetail);
        }
        return angelSettlementDetailList;
    }

    /**
     * 处理聚合金额逻辑
     * @param angelSettlementAndEbsDetail
     * @param angelSettlementList
     */
    private void handleMergeAngelSettlementLogic(AngelSettlementAndEbsDetail angelSettlementAndEbsDetail, List<AngelSettlement> angelSettlementList) {
        log.info("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.angelSettlementAndEbsDetail={},angelSettlementList={}",JSON.toJSONString(angelSettlementAndEbsDetail),JSON.toJSONString(angelSettlementList));
        if(CollUtil.isEmpty(angelSettlementList)){
            return;
        }
        /**step1.算出当前的angelSettlementList结算的总和*/
        AtomicReference<BigDecimal> sum=new AtomicReference<>(new BigDecimal(0));
        if (Objects.nonNull(angelSettlementAndEbsDetail.getSettleAmout())){
            sum.updateAndGet(cur->cur.add(angelSettlementAndEbsDetail.getSettleAmout()));
        }
        Optional.ofNullable(angelSettlementList).map(List::stream).orElseGet(Stream::empty).forEach(angelSettlement -> {
            if (Objects.nonNull(angelSettlement.getSettleAmount())) {
                sum.updateAndGet(cur -> cur.add(angelSettlement.getSettleAmount()));
            }
        });
        log.info("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.sum={}",sum.get());
        /**step2.根据当前promiseId查询工单绑定流水List*/
        AngelSettlementQueryContext queryContext=new AngelSettlementQueryContext();
        queryContext.setPromiseId(angelSettlementAndEbsDetail.getPromiseId());
        queryContext.setSettleStatus(SettleStatusEnum.INIT.getType());
        log.info("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.queryContext={}",JSON.toJSONString(queryContext));
        List<AngelSettlement> angelSettlements = angelSettlementRepository.querySettlementList(queryContext);
        log.info("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.angelSettlements={}",JSON.toJSONString(angelSettlements));
        /**step3.构造新的聚合金额AngelSettlement*/
        AtomicReference<AngelSettlement> angelSettlementAggregate = new AtomicReference<>();
        Optional.ofNullable(angelSettlements).map(List::stream).orElseGet(Stream::empty).forEach(angelSettlement -> {
            if (Arrays.asList(SettleItemTypeEnum.TESTING.getType(),SettleItemTypeEnum.NURSING.getType()).contains(angelSettlement.getItemType())){
                angelSettlementAggregate.set(JSON.parseObject(JSON.toJSONString(angelSettlement), AngelSettlement.class));
            }
            log.info("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.angelSettlement={}",JSON.toJSONString(angelSettlement));
            sum.updateAndGet(cur -> cur.add(angelSettlement.getSettleAmount()));
            log.info("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.sum={}",JSON.toJSONString(sum.get()));
        });
        AngelSettlement angelSettlementAggre = angelSettlementAggregate.get();
        /**step4.如果db中没有，就在请求里*/
        if (Objects.isNull(angelSettlementAggre)){
            AngelSettlement angelSettlementSku=Optional.ofNullable(angelSettlementList).map(List::stream).orElseGet(Stream::empty).
                    filter(angelSettlementItem -> Arrays.asList(SettleItemTypeEnum.TESTING.getType(),
                            SettleItemTypeEnum.NURSING.getType(), SettleItemTypeEnum.FEE.getType()).contains(angelSettlementItem.getItemType())).findFirst().get();
            angelSettlementAggre=JSON.parseObject(JSON.toJSONString(angelSettlementSku),AngelSettlement.class);
        }
        log.info("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.angelSettlementAggre={}",JSON.toJSONString(angelSettlementAggre));
        angelSettlementAggre.setSettleAmount(sum.get());
        log.info("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.after.angelSettlementAggre={}",JSON.toJSONString(angelSettlementAggre));
        angelSettlementAggre.setSettleStatus(SettleStatusEnum.FREEZE.getType());
        /**step5.设置结算id*/
        angelSettlementAggre.setSettleId(generateIdFactory.getId());
        angelSettlementList.add(angelSettlementAggre);
        log.info("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.angelSettlementList={}",JSON.toJSONString(angelSettlementList));
    }



    /**
     *
     * @param context
     */
    private void bulidSettleTime(AngelServiceFinishSettlementContext context){
        JdhAngelInfoBo jdhAngelInfoBo = context.getJdhAngelInfoBo();
        if(Objects.isNull(jdhAngelInfoBo)){
            log.info("[SettleEventSubscriber->bulidSettleTime],jdhAngelInfoBo is null,promiseId={}", context.getPromiseId());
            return;
        }
        if(jdhAngelInfoBo.getJobNature() == 0){
            Date settleTime = context.getSettleTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(settleTime);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.add(Calendar.DATE,10);
            Date date = calendar.getTime();
            context.setExpectSettleTime(date);
        }else{
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, 1);
            calendar.set(Calendar.DAY_OF_MONTH, 10);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            context.setExpectSettleTime(calendar.getTime());
        }
    }

    /**
     *
     * @param promiseId
     * @return
     */
    private Date getAppointmentDate(Long promiseId){
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(promiseId);
        PromiseDto promiseDto = promiseApplication.findByPromiseId(promiseIdRequest);
        if(Objects.nonNull(promiseDto) && Objects.nonNull(promiseDto.getAppointmentTime())){
            PromiseAppointmentTimeDto appointmentTime = promiseDto.getAppointmentTime();
            if(Objects.nonNull(appointmentTime) && Objects.nonNull(appointmentTime.getAppointmentStartTime()) ){
                return appointmentTime.getAppointmentStartTime();
            }
        }
        return new Date();
    }

    /**
     * 只想配置的condition和action
     * 这个execute和状态机调用的差异是，executor不由履约单状态控制
     * @param executor
     * @param context
     */
    private void execute(AbilityExecutor executor, BusinessContext context){
        // 未查询到当前状态+事件配置的执行器
        if (Objects.isNull(executor)){
            log.error("JdServiceSettleApplicationImpl settlement 未找到状态机执行器");
            throw new SystemException(SystemErrorCode.CONFIG_ERROR);
        }
        List<String> validConditions = executor.getConditionCodes();
        for (String code : validConditions) {
            DomainAbility condition = conditions.get(code);
            condition.execute(context);
            log.info("JdServiceSettleApplicationImpl settlement conditionCode={}, context={}", code, JSON.toJSONString(context));
        }
        List<String> actionCodes = executor.getActionCodes();
        for (String code : actionCodes) {
            DomainAbility action = actions.get(code);
            action.execute(context);
            log.info("JdServiceSettleApplicationImpl settlement cationCode={}, context={}", code, JSON.toJSONString(context));
        }
    }


    /**
     * init
     */
    @PostConstruct
    public void init(){
        Map<String, DomainAbility> map = applicationContext.getBeansOfType(DomainAbility.class);
        for (DomainAbility ability : map.values()) {
            if (!Objects.equals(ability.getAbilityCode().domainType().getCode(), DomainEnum.SETTLE_MENT.getCode())){
                log.info("JdServiceSettleApplicationImpl init continue code={}", ability.getAbilityCode().getCode());
                continue;
            }
            if (Objects.equals(ability.getAbilityCode().getAbilityType(), AbilityCode.ACTION)){
                actions.put(ability.getAbilityCode().getCode(), ability);
            }else if(Objects.equals(ability.getAbilityCode().getAbilityType(), AbilityCode.CONDITION)){
                conditions.put(ability.getAbilityCode().getCode(), ability);
            }
        }
    }

    /**
     * 完成服务结算防重
     * @param context
     * @return
     */
    private void orderFinishServiceSettleRepeat(AngelServiceFinishSettlementContext context,String serviceId){
        Long promiseId = context.getPromiseId();

        PromiseDto jdhPromise = promiseExtApplication.findVoucherIdByPromiseId(promiseId);
        if(Objects.isNull(jdhPromise)){
            return;
        }
        Long voucherId = jdhPromise.getVoucherId();
        context.setVoucherId(voucherId);
        context.setPaymentTime(jdhPromise.getCreateTime());
        context.setSourceVoucherId(jdhPromise.getSourceVoucherId());

        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseId(promiseId);
        // 检测单列表
        List<MedicalPromiseDTO> medicalPromises = getMedicalPromises(medicalPromiseListRequest);
        if(medicalPromises.size() == CommonConstant.ONE){
            context.setOrderLastService(Boolean.TRUE);
            context.setVoucherLastService(Boolean.TRUE);
            MedicalPromiseSettleStatusCmd medicalPromiseSettleStatusCmd = new MedicalPromiseSettleStatusCmd();
            medicalPromiseSettleStatusCmd.setMedicalPromiseIds(medicalPromises.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList()));
            medicalPromiseSettleStatusCmd.setSettleStatus(CommonConstant.ONE);
            medicalPromiseApplication.updateSettleSatus(medicalPromiseSettleStatusCmd);
            return;
        }

        List<MedicalPromiseDTO> medicalPromisesTemp = medicalPromises.stream().filter(medical -> medical.getServiceId().toString().equals(serviceId)
                && medical.getPromisePatientId().toString().equals(context.getPromisePatientId().toString())).collect(Collectors.toList());

        // 退款中的，先不记收入
        if(CollUtil.isNotEmpty(medicalPromisesTemp)){
            List<MedicalPromiseDTO> medicalPromisesInvalid = medicalPromisesTemp.stream().filter(medical -> medical.getFreeze().equals(CommonConstant.ONE) ||
                    MedicalPromiseStatusEnum.INVALID.getStatus().equals(medical.getStatus())).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(medicalPromisesInvalid)){
                context.setServiceInComeFlag(Boolean.FALSE);
            }

            MedicalPromiseSettleStatusCmd medicalPromiseSettleStatusCmd = new MedicalPromiseSettleStatusCmd();
            medicalPromiseSettleStatusCmd.setMedicalPromiseIds(medicalPromisesTemp.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList()));
            medicalPromiseSettleStatusCmd.setSettleStatus(CommonConstant.ONE);
            medicalPromiseApplication.updateSettleSatus(medicalPromiseSettleStatusCmd);

            medicalPromisesTemp = medicalPromises.stream().filter(medical -> medical.getServiceId().toString().equals(serviceId)
                    && !medical.getPromisePatientId().toString().equals(context.getPromisePatientId().toString())
                    && medical.getSettleStatus() == CommonConstant.ZERO).collect(Collectors.toList());

            medicalPromisesTemp = medicalPromisesTemp.stream().filter(medical -> !(medical.getFreeze().equals(CommonConstant.ONE) ||
                    MedicalPromiseStatusEnum.INVALID.getStatus().equals(medical.getStatus()))).collect(Collectors.toList());
            if(CollUtil.isEmpty(medicalPromisesTemp)){
                context.setOrderLastService(Boolean.TRUE);
            }

            medicalPromises = medicalPromises.stream().filter(medical -> !serviceId.equals(medical.getServiceId().toString())
                    && medical.getSettleStatus().equals(CommonConstant.ZERO)).collect(Collectors.toList());

            medicalPromises = medicalPromises.stream().filter(medical -> !(medical.getFreeze().equals(CommonConstant.ONE) ||
                    MedicalPromiseStatusEnum.INVALID.getStatus().equals(medical.getStatus()))).collect(Collectors.toList());
            if(CollUtil.isEmpty(medicalPromises) && CollUtil.isEmpty(medicalPromisesTemp)){
                context.setVoucherLastService(Boolean.TRUE);
            }else{
                context.setFeeInComeFlag(Boolean.FALSE);
            }
        }else{
            log.error("JdServiceSettleApplicationImpl -> orderFinishServiceSettleRepeat context:{}",context);
        }
    }

    /**
     *
     * @param medicalPromiseListRequest
     * @return
     */
    private List<MedicalPromiseDTO> getMedicalPromises(MedicalPromiseListRequest medicalPromiseListRequest){
        List<MedicalPromiseDTO> medicalPromises = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
        return medicalPromises;
    }

    /**
     * 计算护士服务费
     * @param jdSettleAppoinmentDTO
     * @param jdhAngelDetailDto
     * @return
     */
    private ServerSettleAmountBo getServiceAmount(JdSettleAppoinmentDTO jdSettleAppoinmentDTO,List<MedicalPromiseDTO> medicalPromises,JdhAngelDetailDto jdhAngelDetailDto){
        ServerSettleAmountBo serverSettleAmountBo = new ServerSettleAmountBo();
        if (Objects.nonNull(jdhAngelDetailDto)){
            // 客户地址信息
            AddressInfoDTO addressInfo = jdSettleAppoinmentDTO.getAddressInfo();
            AssertUtils.nonNull(addressInfo, TradeErrorCode.ORDER_CALC_SERVICE_FEE_USER_ADDRESS_MISS);
            // 护士时薪
            // 2025-03-05 上海战役，调整自营护士时薪为0元
            BigDecimal angelhourlyage;
            if(JobNatureEnum.FULL_TIME.getValue() == jdhAngelDetailDto.getJobNature()){
                angelhourlyage = BigDecimal.ZERO;
            }else{
                angelhourlyage = getAngelhourlyage(addressInfo,jdhAngelDetailDto);
            }
//            // 订单商品信息
//            List<JdOrderItemDTO> jdOrderItemList = orderDetail.getJdOrderItemList();
//            AssertUtils.isNotEmpty(jdOrderItemList, BusinessErrorCode.SKU_INFO_QUERY_FAIL);
            AtomicReference<BigDecimal> serviceTotalAmount = new AtomicReference<>(BigDecimal.ZERO);
            Map<Long,BigDecimal> angelSkuServiceAmountMap = new HashMap<>();
            Map<Long,List<MedicalPromiseDTO>> medicalPromisesMap = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getServiceId));
            for(Map.Entry<Long,List<MedicalPromiseDTO>> entry : medicalPromisesMap.entrySet()){
                Long skuId = entry.getKey();
                List<MedicalPromiseDTO> list = entry.getValue();
                Set<Long> medicalPromiseSet = list.stream().map(MedicalPromiseDTO::getPromisePatientId).collect(Collectors.toSet());
                Integer serviceTime = calculateServiceTime(skuId);
                // 服务时长（小时）= 服务时间 * 服务难度
                BigDecimal serviceAmount = angelhourlyage.multiply(BigDecimal.valueOf(serviceTime)).divide(BigDecimal.valueOf(6000),2, BigDecimal.ROUND_DOWN);
                angelSkuServiceAmountMap.put(skuId,serviceAmount);
                serviceTotalAmount.set(serviceTotalAmount.get().add(serviceAmount.multiply(BigDecimal.valueOf(medicalPromiseSet.size()))));
            }

            serverSettleAmountBo.setSettleTotalAmount(serviceTotalAmount.get());
            serverSettleAmountBo.setAngelSkuServiceAmountMap(angelSkuServiceAmountMap);
            log.info("JdServiceSettleApplicationImpl -> getServiceAmount end serverSettleSnapshotBo:{}", serverSettleAmountBo);
        }
        return serverSettleAmountBo;
    }

    /**
     * 计算护士服务费
     * @param jdSettleAppoinmentDTO
     * @return
     */
    private Map<String, ServerSettleAmountBo> getServiceAmountDiagram(JdSettleAppoinmentDTO jdSettleAppoinmentDTO,List<MedicalPromiseDTO> medicalPromises, JdServiceSettleParam jdServiceSettleParam){
        Map<String, ServerSettleAmountBo> serverSettleAmountBoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(jdServiceSettleParam.getProfessionTitleCodeList())){
            for (String professionTitleCode : jdServiceSettleParam.getProfessionTitleCodeList()) {
                try {
                    ServerSettleAmountBo serverSettleAmountBo = new ServerSettleAmountBo();
                    // 客户地址信息
                    AddressInfoDTO addressInfo = jdSettleAppoinmentDTO.getAddressInfo();
                    AssertUtils.nonNull(addressInfo, TradeErrorCode.ORDER_CALC_SERVICE_FEE_USER_ADDRESS_MISS);
                    // 护士时薪
                    BigDecimal angelhourlyage = getAngelhourlyage(addressInfo,professionTitleCode);
                    AtomicReference<BigDecimal> serviceTotalAmount = new AtomicReference<>(BigDecimal.ZERO);
                    Map<Long,BigDecimal> angelSkuServiceAmountMap = new HashMap<>();
                    Map<Long,List<MedicalPromiseDTO>> medicalPromisesMap = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getServiceId));
                    for(Map.Entry<Long,List<MedicalPromiseDTO>> entry : medicalPromisesMap.entrySet()){
                        Long skuId = entry.getKey();
                        List<MedicalPromiseDTO> list = entry.getValue();
                        Set<Long> medicalPromiseSet = list.stream().map(MedicalPromiseDTO::getPromisePatientId).collect(Collectors.toSet());
                        Integer serviceTime = calculateServiceTime(skuId);
                        // 服务时长（小时）= 服务时间 * 服务难度
                        BigDecimal serviceAmount = angelhourlyage.multiply(BigDecimal.valueOf(serviceTime)).divide(BigDecimal.valueOf(6000),2, BigDecimal.ROUND_DOWN);
                        angelSkuServiceAmountMap.put(skuId,serviceAmount);
                        serviceTotalAmount.set(serviceTotalAmount.get().add(serviceAmount.multiply(BigDecimal.valueOf(medicalPromiseSet.size()))));
                    }

                    serverSettleAmountBo.setSettleTotalAmount(serviceTotalAmount.get());
                    serverSettleAmountBo.setAngelSkuServiceAmountMap(angelSkuServiceAmountMap);
                    log.info("JdServiceSettleApplicationImpl -> getServiceAmountDiagram end professionTitleCode={}, serverSettleSnapshotBo:{}", professionTitleCode, serverSettleAmountBo);
                    serverSettleAmountBoMap.put(professionTitleCode, serverSettleAmountBo);
                } catch (Exception e) {
                    log.warn("JdServiceSettleApplicationImpl -> getServiceAmountDiagram error addressInfo={}, professionTitleCode={}", JSON.toJSONString(jdSettleAppoinmentDTO), professionTitleCode, e);
                }
            }
        }
        return serverSettleAmountBoMap;
    }

    /**
     * 查询护士
     * @param angelId
     * @return
     */
    private JdhAngelDetailDto getJdhAngelDetail(Long angelId){
        AngelDetailRequest angelDetailRequest = new AngelDetailRequest();
        angelDetailRequest.setAngelId(angelId);
        angelDetailRequest.setIsQueryNotOpenSkill(false);
        JdhAngelDetailDto jdhAngelDetailDto = angelApplication.queryAngelDetail(angelDetailRequest);
        return jdhAngelDetailDto;
    }

    /**
     * 获取护士时薪
     * @param addressInfo
     * @param jdhAngelDetailDto
     * @return
     */
    private BigDecimal getAngelhourlyage(AddressInfoDTO addressInfo,JdhAngelDetailDto jdhAngelDetailDto){
        List<JdhAngelProfessionRelDto> jdhAngelProfessionRelList = jdhAngelDetailDto.getJdhAngelProfessionRelList();
        AssertUtils.isNotEmpty(jdhAngelProfessionRelList, TradeErrorCode.ANGEL_PROFESSION_INFO_NULL);
        String professionTitleCode = jdhAngelProfessionRelList.get(0).getProfessionTitleCode();
        return getAngelhourlyage(addressInfo, professionTitleCode);
    }

    /**
     * 获取护士时薪
     * @param addressInfo
     * @param professionTitleCode
     * @return
     */
    private BigDecimal getAngelhourlyage(AddressInfoDTO addressInfo,String professionTitleCode){
        AssertUtils.nonNull(addressInfo, TradeErrorCode.CITY_ID_NULL);
        AssertUtils.nonNull(addressInfo.getProvinceId(), TradeErrorCode.CITY_ID_NULL);

        Integer cityId = addressInfo.getCityId();
        if(addressInfo.getProvinceId() < 5){
            cityId = addressInfo.getProvinceId();
        }
        AssertUtils.nonNull(cityId, TradeErrorCode.CITY_ID_NULL);
        Map<String, String> cityLevelTypeMap = duccConfig.getCityLevelTypeMap();
        String cityLevel = cityLevelTypeMap.get(cityId.toString());
        AssertUtils.hasText(cityLevel, TradeErrorCode.CITY_INFO_NULL);

        // 计算服务费
        Map<String, JSONObject> serverSettleAmountMap = duccConfig.getServerSettleAmoutMap();
        JSONObject serverSettleAmountMapping = serverSettleAmountMap.get(cityLevel);
        Map<String,BigDecimal> serverAmountMap = serverSettleAmountMapping.getObject("serverAmountMap", Map.class);

        AssertUtils.hasText(professionTitleCode, TradeErrorCode.ANGEL_PROFESSION_INFO_NULL);

        BigDecimal cityServerAmount = serverAmountMap.get(professionTitleCode);
        AssertUtils.nonNull(cityServerAmount, TradeErrorCode.ANGEL_PROFESSION_AMOUNT_NULL);
        log.info("JdServiceSettleApplicationImpl -> getAngelhourlyage end cityServerAmount:{}", cityServerAmount);
        return cityServerAmount;
    }

    /**
     * 估算服务时长
     * 距离/10 + 服务时间
     * @return
     */
    private Integer calculateServiceTime(Long skuId){
        AtomicReference<Integer> serviceTime = new AtomicReference<>(0);
        JdhSkuRequest request = new JdhSkuRequest();
        request.setSkuId(skuId);
        request.setQuerySkuCoreData(false);
        request.setQueryServiceItem(true);
        JdhSkuDto jdhSkuDto = productApplication.queryJdhSkuInfo(request);
        if(Objects.nonNull(jdhSkuDto)){
            List<Long> serviceItemIdList = jdhSkuDto.getServiceItemIdList();
            Set<Long> serviceItemIdSet = new HashSet(serviceItemIdList);
            ServiceItemQuery itemQuery = ServiceItemQuery.builder().itemIds(serviceItemIdSet).build();
            List<ServiceItemDto> response = productServiceItemApplication.queryServiceItemList(itemQuery);
            log.info("JdServiceSettleApplicationImpl -> calculateServiceTime response:{}", JsonUtil.toJSONString(response));
            AtomicReference<Integer> technicalLevel = new AtomicReference<>(100);
            if(CollectionUtils.isNotEmpty(response)){
                List<ServiceItemDto> serviceItemDtos = response;
                if(CollUtil.isNotEmpty(serviceItemDtos)){
                    serviceItemDtos.forEach(serviceItemDto -> {
                        if(Objects.nonNull(serviceItemDto.getTechnicalLevel())){
                            technicalLevel.set(serviceItemDto.getTechnicalLevel());
                        }
                        serviceTime.updateAndGet(v -> v + (serviceItemDto.getServiceDuration() * technicalLevel.get()));
                    });
                }
            }
        }
        log.info("JdServiceSettleApplicationImpl -> calculateServiceTime serviceTime:{}", serviceTime.get());
        return serviceTime.get();
    }

    /**
     * 获取护士时段，上门，动态调整等费用
     * @param jdhAngelDetailDto
     * @param jdSettleAppoinmentDTO
     * @param settleTypeAmountMap
     * @param dispatchMarkupPrice
     * @param medicalPromises
     * @return
     */
    private BigDecimal getAngelDoorHomeFee(JdhAngelDetailDto jdhAngelDetailDto,JdSettleAppoinmentDTO jdSettleAppoinmentDTO,Map<String,BigDecimal> settleTypeAmountMap,
                                           BigDecimal dispatchMarkupPrice,List<MedicalPromiseDTO> medicalPromises){
        Integer jobNature = jdhAngelDetailDto.getJobNature();
        return getAngelDoorHomeFee(jobNature, jdSettleAppoinmentDTO, settleTypeAmountMap, dispatchMarkupPrice, medicalPromises);
    }

    /**
     * 获取护士时段，上门，动态调整等费用
     * @param jobNature
     * @param jdSettleAppoinmentDTO
     * @param settleTypeAmountMap
     * @param dispatchMarkupPrice
     * @param medicalPromises
     * @return
     */
    private BigDecimal getAngelDoorHomeFee(Integer jobNature,JdSettleAppoinmentDTO jdSettleAppoinmentDTO,Map<String,BigDecimal> settleTypeAmountMap,
                                           BigDecimal dispatchMarkupPrice,List<MedicalPromiseDTO> medicalPromises){
        CalcOrderServiceFeeContext context = bulidCalcOrderServiceFeeContext(jdSettleAppoinmentDTO,medicalPromises);
        BigDecimal feeTotal = BigDecimal.ZERO;
//        context.setSearchServiceUpgrade(true);
        List<ServiceFeeDetail> serviceFeeDetailList = jdhOrderDomainService.calcNoFreeOrderServiceFee(context);
        if(CollUtil.isNotEmpty(serviceFeeDetailList)){
            // 计算时段费
            Map<String, JSONObject> angelSettleRatioMap = duccConfig.getAngelSettleRatioMap();
            AssertUtils.nonNull(jobNature, TradeErrorCode.ANGEL_PROFESSION_INFO_NULL);
            for(ServiceFeeDetail serviceFeeDetail : serviceFeeDetailList){
                JSONObject serverSettleAmountMapping = angelSettleRatioMap.get(String.valueOf(serviceFeeDetail.getType()));
                if(Objects.nonNull(serverSettleAmountMapping)){
                    Map<String,BigDecimal> serverAmountMap = serverSettleAmountMapping.getObject("serverAmountMap", Map.class);
                    BigDecimal settleRatio = serverAmountMap.get(String.valueOf(jobNature));
                    BigDecimal fee = serviceFeeDetail.getFee();
                    if(Objects.nonNull(settleRatio) && settleRatio.compareTo(BigDecimal.valueOf(0)) >= 0 && fee.compareTo(BigDecimal.valueOf(0)) > 0){
                        BigDecimal feeSettle = fee.multiply(settleRatio).setScale(2,BigDecimal.ROUND_DOWN);
                        settleTypeAmountMap.put(serviceFeeDetail.getTips(),feeSettle);
                        feeTotal = feeTotal.add(feeSettle);
                    }
                }
            }
        }
        if(Objects.nonNull(dispatchMarkupPrice) && dispatchMarkupPrice.compareTo(BigDecimal.ZERO) >0){
            settleTypeAmountMap.put("派单加价",dispatchMarkupPrice);
            feeTotal = feeTotal.add(dispatchMarkupPrice);
        }

        log.info("JdServiceSettleApplicationImpl -> getAngelDoorHomeFee end feeTotal:{}", feeTotal);
        return feeTotal;
    }

    /**
     * 增强垂直业务身份
     * @param context
     */
    private void enhanceVerticalCode(CalcOrderServiceFeeContext context) {
        List<JdhSku> jdhSkus = jdhSkuRepository.queryMultiSku(context.getSkuIds().stream().map(s -> JdhSku.builder().skuId(Long.valueOf(s)).build()).filter(Objects::nonNull).collect(Collectors.toList()));
        Map<String, String> verticalCodeBySkuInfoMap = duccConfig.getVerticalCodeBySkuInfoMap();
        if (CollectionUtils.isNotEmpty(jdhSkus)) {
            String key = new StringBuilder(String.valueOf(jdhSkus.get(0).getChannelId())).append("*").append(jdhSkus.get(0).getServiceType()).toString();
            context.setVerticalCode(verticalCodeBySkuInfoMap.get(key));
            Integer serviceType = jdhSkus.get(0).getServiceType();
            Long channelId = jdhSkus.get(0).getChannelId();
            Map<String, Map<String,String>> channelServiceTypeConvertMap = duccConfig.getChannelServiceTypeConvertMap();
            Map<String,String> newChannelIdType = channelServiceTypeConvertMap.get(key);
            context.setChannelId(Long.valueOf(newChannelIdType.get("channelId")));
            context.setServiceType(Integer.valueOf(newChannelIdType.get("serviceType")));
            if(ServiceTypeNewEnum.KNIGHT_TEST.getType().equals(serviceType)){
                context.setServiceType(ServiceTypeNewEnum.ANGEL_TEST.getType());
            }
        }
    }
    /**
     *
     * @param jdSettleAppoinmentDTO
     * @return
     */
    private CalcOrderServiceFeeContext bulidCalcOrderServiceFeeContext(JdSettleAppoinmentDTO jdSettleAppoinmentDTO,List<MedicalPromiseDTO> medicalPromises){
        log.info("JdServiceSettleApplicationImpl.bulidCalcOrderServiceFeeContext.jdSettleAppoinmentDTO={}",JSON.toJSONString(jdSettleAppoinmentDTO));
        CalcOrderServiceFeeContext context = CalcOrderServiceFeeContext.builder().build();
        AddressInfoDTO addressInfo = jdSettleAppoinmentDTO.getAddressInfo();
        Set<String> skuIds = new HashSet<>();
        if(CollUtil.isNotEmpty(medicalPromises)){
            medicalPromises.forEach(item ->{
                skuIds.add(String.valueOf(item.getServiceId()));
            });
        }
        context.setSkuIds(skuIds);
        enhanceVerticalCode(context);
        if(Objects.nonNull(addressInfo)){
            AddressInfoValueObject addressInfoValueObject = new AddressInfoValueObject();
            addressInfoValueObject.setProvinceId(addressInfo.getProvinceId());
            addressInfoValueObject.setCityId(addressInfo.getCityId());
            addressInfoValueObject.setCountyId(addressInfo.getCountyId());
            addressInfoValueObject.setTownId(addressInfo.getTownId());
            context.setAddressInfo(addressInfoValueObject);
        }
        DispatchAppointmentTimeDto appointmentTimeDto = jdSettleAppoinmentDTO.getAppointmentTimeDto();
        if(Objects.nonNull(appointmentTimeDto)){
            OrderAppointmentTimeValueObject appointmentTimeValueObject = new OrderAppointmentTimeValueObject();
            appointmentTimeValueObject.setAppointmentStartTime(appointmentTimeDto.getAppointmentStartTime());
            appointmentTimeValueObject.setIsImmediately(appointmentTimeDto.getIsImmediately());
            context.setAppointmentTime(appointmentTimeValueObject);
        }
        context.setVerticalCode(jdSettleAppoinmentDTO.getVerticalCode());
        return context;
    }

    /**
     * 派单预估结算价
     *
     * @param jdServiceSettleParam
     * @return
     */
    @Override
    public String getOrderSettleSnapshotAmount(JdServiceSettleParam jdServiceSettleParam) {
        AngelWorkSettleSnapshotDBQuery query = new AngelWorkSettleSnapshotDBQuery();
        query.setPromiseId(jdServiceSettleParam.getPromiseId());
        JdhAngelWorkSettleSnapshot settleSnapshot = jdhAngelWorkSettleSnapshotRepository.findAngelWorkSettleSnapshot(query);
        if(Objects.nonNull(settleSnapshot)){
            return settleSnapshot.getSettleContext();
        }else {
            AngelWorkDto angelWork = angelWorkApplication.getAngelWorkByPromiseId(jdServiceSettleParam.getPromiseId());
            if(Objects.nonNull(angelWork)){
                jdServiceSettleParam.setWorkId(angelWork.getWorkId());
                ServerSettleAmountBo serverSettleAmountBo = getOrderSettleAmount(jdServiceSettleParam);
                return JsonUtil.toJSONString(serverSettleAmountBo);
            }
        }
        return "";
    }



}
