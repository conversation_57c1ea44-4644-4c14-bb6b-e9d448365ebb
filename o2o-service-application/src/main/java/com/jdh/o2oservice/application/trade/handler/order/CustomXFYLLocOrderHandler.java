package com.jdh.o2oservice.application.trade.handler.order;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.export.trade.dto.CustomOrderInfoDTO;
import com.jdh.o2oservice.export.trade.dto.CustomOrderProcessTrackDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 自定义消费医疗LOC订单（ordertype=75&sendpay811=1）
 * https://joyspace.jd.com/pages/JHRRSsFaHZMD4nsLVoOA
 * @Date 2024/8/31 下午2:42
 * <AUTHOR>
 **/
@Component
@Slf4j
public class CustomXFYLLocOrderHandler extends AbstractCustomOrderHandler implements CustomOrderStrategy {

    @Resource
    private DuccConfig duccConfig;

    @Override
    public String getKey() {
        return CustomOrderStrategy.XFYL_LOC_ORDER;
    }

    @Override
    public CustomOrderInfoDTO handle(CustomOrderHandlerContext receipt) {
        log.info("CustomXFYLLocOrderHandler handle receipt={}", JSON.toJSONString(receipt));
        if (!locServiceType.contains(receipt.getOrder().getServiceType())){
            log.info("CustomXFYLLocOrderHandler handle serviceType no mate");
            return null;
        }
        JSONObject customOrderInfoConfig = JSON.parseObject(duccConfig.getCustomOrderInfoConfig());
        // 构建标签
        CustomOrderInfoDTO res = this.buildCustomOrderLabelInfo(receipt.getOrder(), customOrderInfoConfig);
        log.info("CustomXFYLLocOrderHandler handle labelInfo res={}", JSON.toJSONString(res));

        List<JdOrderItem> orderItems = receipt.getOrderIdMappingOrderItemMap().get(receipt.getOrder().getOrderId());
        log.info("CustomXFYLLocOrderHandler handle orderItems={}", JSON.toJSONString(orderItems));

        // 履约单
        List<JdhPromise> promiseList = new ArrayList<>();
        orderItems.forEach(orderItem->{
            List<JdhPromise> jdhPromises = receipt.getSourceVoucherIdMappingPromiseMap().get(String.valueOf(orderItem.getOrderItemId()));
            if (CollectionUtils.isEmpty(jdhPromises)){
                return;
            }
            promiseList.addAll(jdhPromises);
        });
        log.info("CustomXFYLLocOrderHandler handle promiseList={}", JSON.toJSONString(promiseList));

        // 预约方式：免预约-0、在线预约-1、商家预约-2
        Integer promiseType = getPromiseType(orderItems, promiseList);
        log.info("CustomXFYLLocOrderHandler handle promiseType={}", promiseType);

        // 预约单状态全部为待预约
        boolean allWaitPromiseStatusFlag = false;
        // 待预约状态
        List<JdhPromise> allWaitPromiseList = new ArrayList<>();
        // 预约单状态不全部为待预约
        List<JdhPromise> noAllWaitPromiseList = new ArrayList<>();
        // 订单下的预约单全部核销
        boolean allCompletePromiseStatusFlag = true;

        if (CollectionUtils.isNotEmpty(promiseList)){
            // 预约单剔除（已核销、过期）
            List<JdhPromise> rejectCompleteExpirePromiseList = promiseList.stream().filter(p -> !Arrays.asList(JdhPromiseStatusEnum.COMPLETE.getStatus()
                    , JdhPromiseStatusEnum.EXPIRATION.getStatus()).contains(p.getPromiseStatus())).collect(Collectors.toList());
            log.info("CustomXFYLLocOrderHandler handle rejectCompleteExpirePromiseList={}", JSON.toJSONString(rejectCompleteExpirePromiseList));
            if (CollectionUtils.isNotEmpty(rejectCompleteExpirePromiseList)){
                allWaitPromiseStatusFlag = rejectCompleteExpirePromiseList.stream().allMatch(n -> waitPromiseStatus.contains(n.getPromiseStatus()));
                log.info("CustomXFYLLocOrderHandler handle allWaitPromiseStatusFlag={}", allWaitPromiseStatusFlag);

                allWaitPromiseList = rejectCompleteExpirePromiseList.stream().filter(n -> waitPromiseStatus.contains(n.getPromiseStatus())).collect(Collectors.toList());
                log.info("CustomXFYLLocOrderHandler handle allWaitPromiseList={}", JSON.toJSONString(allWaitPromiseList));

                noAllWaitPromiseList = rejectCompleteExpirePromiseList.stream().filter(n -> !waitPromiseStatus.contains(n.getPromiseStatus())).collect(Collectors.toList());
                log.info("CustomXFYLLocOrderHandler handle noAllWaitPromiseList={}", JSON.toJSONString(noAllWaitPromiseList));

                allCompletePromiseStatusFlag = rejectCompleteExpirePromiseList.stream().allMatch(n -> JdhPromiseStatusEnum.COMPLETE.getStatus().equals(n.getPromiseStatus()));
                log.info("CustomXFYLLocOrderHandler handle allCompletePromiseStatusFlag={}", allCompletePromiseStatusFlag);
            }
        }

        // 服务流程配置
        JSONObject processTrackObj = customOrderInfoConfig.getJSONObject("processTrack");

        // 待使用配置
        JSONObject waitForUseObj = processTrackObj.getJSONObject("XFYLLocOrder").getJSONObject("waitForUse");

        // 预约详情、列表配置
        JSONObject appointmentUrlObj = processTrackObj.getJSONObject("XFYLLocOrder").getJSONObject("appointmentUrl");

        if (ServiceTypeEnum.PHYSICAL.getServiceType().equals(receipt.getOrder().getServiceType()) && PromiseTypeEnum.ONLINE_APPOINTMENT.getCode().equals(promiseType)
                && allCompletePromiseStatusFlag && CollectionUtils.isNotEmpty(promiseList)){
            log.info("CustomXFYLLocOrderHandler handle 已消费-订单下的预约单全部核销 orderId={}", receipt.getOrder().getOrderId());
            // 已消费-订单下的预约单全部核销，仅体检订单展示服务流程
            CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSONObject.toJSONString(processTrackObj.getJSONObject("XFYLLocOrder").getJSONObject("consumed")), CustomOrderProcessTrackDTO.class);
            res.setProcessTrack(processTrack);

        }else if (PromiseTypeEnum.ONLINE_APPOINTMENT.getCode().equals(promiseType) && !Arrays.asList(OrderStatusEnum.ORDER_REFUNDING.getStatus()
                , OrderStatusEnum.ORDER_REFUND.getStatus()).contains(receipt.getOrder().getOrderStatus()) && allWaitPromiseStatusFlag){
            log.info("CustomXFYLLocOrderHandler handle 待使用-预约方式为在线预约&预约单状态全部为待预约 orderId={}", receipt.getOrder().getOrderId());
            // 待使用-预约方式为在线预约&预约单状态全部为待预约（预约单状态将已核销、过期、退款中、退款完成的预约单剔除）
            this.buildAllWaitPromiseCustomXFYLLocOrder(res, allWaitPromiseList, orderItems, waitForUseObj, appointmentUrlObj);

        }else if (PromiseTypeEnum.ONLINE_APPOINTMENT.getCode().equals(promiseType) && !Arrays.asList(OrderStatusEnum.ORDER_REFUNDING.getStatus()
                , OrderStatusEnum.ORDER_REFUND.getStatus()).contains(receipt.getOrder().getOrderStatus()) && CollectionUtils.isNotEmpty(noAllWaitPromiseList)){
            log.info("CustomXFYLLocOrderHandler handle 待使用-预约方式为在线预约&预约单状态不全部为待预约 orderId={}", receipt.getOrder().getOrderId());
            // 待使用-预约方式为在线预约&预约单状态不全部为待预约（预约单状态将已核销、过期、退款中、退款完成的预约单剔除）
            this.buildNoAllWaitPromiseCustomXFYLLocOrder(res, noAllWaitPromiseList, orderItems, waitForUseObj, appointmentUrlObj);

        }/*else if (PromiseTypeEnum.VENDER_APPOINTMENT.getCode().equals(promiseType)){
            log.info("CustomXFYLLocOrderHandler handle 待使用-预约方式为商家预约 orderId={}", receipt.getOrder().getOrderId());
            // 待使用-预约方式为商家预约
            res.setProcessTrack(JSON.parseObject(JSONObject.toJSONString(waitForUseObj.getJSONObject("merchantAppointment")), CustomOrderProcessTrackDTO.class));

        }else if (PromiseTypeEnum.NO_APPOINTMENT.getCode().equals(promiseType)){
            log.info("CustomXFYLLocOrderHandler handle 待使用-预约方式为免预约 orderId={}", receipt.getOrder().getOrderId());
            // 待使用-预约方式为免预约
            res.setProcessTrack(JSON.parseObject(JSONObject.toJSONString(waitForUseObj.getJSONObject("noAppointment")), CustomOrderProcessTrackDTO.class));

        }*/
        return res;
    }

    /**
     * 查询预约方式
     * @param orderItems
     * @param promiseList
     * @return
     */
    private Integer getPromiseType(List<JdOrderItem> orderItems, List<JdhPromise> promiseList) {
        JdOrderItem orderItem = orderItems.get(0);
        log.info("CustomXFYLLocOrderHandler getPromiseType orderItem={}", JSON.toJSONString(orderItem));
        if (StringUtils.isNotBlank(orderItem.getSkuFeatures())){
            JSONObject skuFeaturesObj = JSON.parseObject(orderItem.getSkuFeatures());
            JSONObject skuTagObj = skuFeaturesObj.getJSONObject("skuTag");
            if (skuTagObj != null){
                Integer appointType = skuTagObj.getInteger("appointType");
                log.info("CustomXFYLLocOrderHandler getPromiseType appointType={}", appointType);
                if (appointType != null){
                    return appointType;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(promiseList)){
            return promiseList.get(0).getPromiseType();
        }
        return null;
    }

    private void buildNoAllWaitPromiseCustomXFYLLocOrder(CustomOrderInfoDTO res, List<JdhPromise> noAllWaitPromiseList, List<JdOrderItem> orderItems, JSONObject waitForUseObj, JSONObject appointmentUrlObj) {
        noAllWaitPromiseList.sort((order1,order2)->order2.getUpdateTime().compareTo(order1.getUpdateTime()));
        // 取预约时间最近的一条预约记录的信息
        JdhPromise lastPromise = noAllWaitPromiseList.get(0);
        CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSONObject.toJSONString(waitForUseObj.getJSONObject("onlineAppointmentNoAllWait"))
                , CustomOrderProcessTrackDTO.class);
        JdhPromiseStatusEnum promiseStatusEnum = JdhPromiseStatusEnum.getEnumByCode(lastPromise.getPromiseStatus());
        if (promiseStatusEnum == null){
            log.info("CustomXFYLLocOrderHandler buildAllWaitPromiseCustomXFYLLocOrder promiseStatusEnum empty");
            return;
        }
        processTrack.setMessageTitle(String.format(processTrack.getMessageTitle(), promiseStatusEnum.getDesc()));
        UserName userName = lastPromise.getPatients().get(0).getUserName();
        if (userName == null){
            log.info("CustomXFYLLocOrderHandler buildAllWaitPromiseCustomXFYLLocOrder userName empty");
            return;
        }
        // 预约人姓名
        String patientName = "*"+userName.getName().substring(1);
        // 预约时间
        String appointmentTime = TimeUtils.localDateTimeToStr(lastPromise.getAppointmentTime().getAppointmentStartTime(), TimeFormat.SHORT_PATTERN_DOT);
        // 门店名称
        String storeName = lastPromise.getStore().getStoreName();
        processTrack.setMessage(String.format(processTrack.getMessage(), patientName, appointmentTime, storeName));
        try {
            // 点击服务流程卡片，当订单商品数量=1，进入预约单详情页；当订单商品数量＞1，进入预约单列表页
            String verticalCode = lastPromise.getVerticalCode();
            String serviceType = lastPromise.getServiceType();
            int skuNum = orderItems.stream().mapToInt(JdOrderItem::getSkuNum).sum();
            log.info("CustomXFYLLocOrderHandler buildAllWaitPromiseCustomXFYLLocOrder skuNum={},verticalCode={},serviceType={}", skuNum, verticalCode, serviceType);
            if (skuNum>1){
                // 进入预约单列表页
                String url = appointmentUrlObj.getString(verticalCode + "_" + serviceType + "_" + "list");
                log.info("CustomXFYLLocOrderHandler buildAllWaitPromiseCustomXFYLLocOrder list url={}", url);
                if (StringUtils.isNotBlank(url)){
                    processTrack.setUrl(MessageFormat.format(url, String.valueOf(res.getOrderId())));
                }
            }else {
                // 进入预约单详情页
                String url = appointmentUrlObj.getString(verticalCode + "_" + serviceType + "_" + "detail");
                log.info("CustomXFYLLocOrderHandler buildAllWaitPromiseCustomXFYLLocOrder detail url={}", url);
                if (StringUtils.isNotBlank(url)){
                    processTrack.setUrl(MessageFormat.format(url, String.valueOf(lastPromise.getPromiseId())));
                }
            }
        } catch (Exception e) {
            processTrack.setHiddenArrow(String.valueOf(NumConstant.NUM_1));
            log.error("CustomXFYLLocOrderHandler buildAllWaitPromiseCustomXFYLLocOrder url error e", e);
        }
        // 服务流程
        res.setProcessTrack(processTrack);
    }

    private void buildAllWaitPromiseCustomXFYLLocOrder(CustomOrderInfoDTO res, List<JdhPromise> allWaitPromiseList, List<JdOrderItem> orderItems, JSONObject waitForUseObj, JSONObject appointmentUrlObj) {
        allWaitPromiseList.sort((order1,order2)->order2.getUpdateTime().compareTo(order1.getUpdateTime()));
        // 取预约时间最近的一条预约记录的信息
        JdhPromise lastPromise = allWaitPromiseList.get(0);
        CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSONObject.toJSONString(waitForUseObj.getJSONObject("onlineAppointmentAllWait"))
                , CustomOrderProcessTrackDTO.class);
        try {
            // 点击服务流程卡片，当订单商品数量=1，进入预约单详情页；当订单商品数量＞1，进入预约单列表页
            String verticalCode = lastPromise.getVerticalCode();
            String serviceType = lastPromise.getServiceType();
            int skuNum = orderItems.stream().mapToInt(JdOrderItem::getSkuNum).sum();
            log.info("CustomXFYLLocOrderHandler buildAllWaitPromiseCustomXFYLLocOrder skuNum={},verticalCode={},serviceType={}", skuNum, verticalCode, serviceType);
            if (skuNum>1){
                // 进入预约单列表页
                String url = appointmentUrlObj.getString(verticalCode + "_" + serviceType + "_" + "list");
                log.info("CustomXFYLLocOrderHandler buildAllWaitPromiseCustomXFYLLocOrder list url={}", url);
                if (StringUtils.isNotBlank(url)){
                    processTrack.setUrl(MessageFormat.format(url, String.valueOf(res.getOrderId())));
                }
            }else {
                // 进入立即预约页
                String url = appointmentUrlObj.getString(verticalCode + "_" + serviceType + "_" + "appointment");
                log.info("CustomXFYLLocOrderHandler buildAllWaitPromiseCustomXFYLLocOrder appointment url={}", url);
                if (StringUtils.isNotBlank(url)){
                    processTrack.setUrl(MessageFormat.format(url, String.valueOf(orderItems.get(0).getOrderId()), String.valueOf(lastPromise.getPromiseId())));
                }
            }
        } catch (Exception e) {
            processTrack.setHiddenArrow(String.valueOf(NumConstant.NUM_1));
            log.error("CustomXFYLLocOrderHandler buildAllWaitPromiseCustomXFYLLocOrder url error e", e);
        }
        // 服务流程
        res.setProcessTrack(processTrack);
    }
}
