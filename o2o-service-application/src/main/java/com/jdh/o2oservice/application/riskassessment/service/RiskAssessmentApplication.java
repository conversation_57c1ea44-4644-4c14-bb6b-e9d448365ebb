package com.jdh.o2oservice.application.riskassessment.service;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessment;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentQuery;
import com.jdh.o2oservice.export.riskassessment.cmd.*;
import com.jdh.o2oservice.export.riskassessment.dto.*;
import com.jdh.o2oservice.export.riskassessment.request.RiskAssessmentDetailRequest;
import com.jdh.o2oservice.export.riskassessment.request.RiskAssessmentPageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @description 风险评估单application
 * @date 2025/6/23
 */
public interface RiskAssessmentApplication {


    /**
     * 保存风险评估单
     * @param riskAssessmentCmd 风险评估命令对象
     * @return 保存是否成功
     */
    Boolean createRiskAssessment(RiskAssessmentCmd riskAssessmentCmd);


    /**
     * 更新风险评估信息。
     * @param riskAssessmentCmd 风险评估命令对象，包含需要更新的信息。
     * @return 更新是否成功。
     */
    Boolean updateRiskAssessment(RiskAssessmentCmd riskAssessmentCmd);


    /**
     * 重新分配风险评估师和问题
     * @param riskAssessmentCmd 风险评估命令对象，包含需要重新分配的用户信息。
     * @return 重新分配是否成功。
     */
    Boolean reDispatchAssessmentUser(RiskAssessmentCmd riskAssessmentCmd);


    /**
     * 保存风险评估结果
     * @param resultCmd 需要更新的风险评估细节命令对象列表。
     * @return 更新操作是否成功。
     */
    Boolean saveRiskAssResult(RiskAssessmentResultCmd resultCmd);



    /**
     * 分页查询风险评估信息。
     * @param request 分页查询请求参数，包括页码、每页记录数等。
     * @return 分页查询结果，包含总记录数、当前页码、每页记录数和数据列表。
     */
    PageDto<RiskAssessmentPageDTO> queryRiskAssessmentPage( RiskAssessmentPageRequest request);


    /**
     * 保存风险评估回访信息
     * @param riskAssReturnVisitCmd 风险评估回访命令对象
     * @return 是否保存成功
     */
    Boolean saveReturnVisit(RiskAssReturnVisitCmd riskAssReturnVisitCmd);


    /**
     * 查询风险评估详情
     * @param request 风险评估详情请求对象
     * @return 风险评估详情DTO对象
     */
    RiskAssessmentDetailManDTO queryRiskAssDetailForMan(RiskAssessmentDetailRequest request);


    /**
     * 查询风险评估明细列表
     * @param request 风险评估明细请求对象
     * @return 风险评估明细列表
     */
    List<RiskAssDetailBaseDTO> queryRiskAssDetailList(RiskAssessmentDetailRequest request);

    /**
     * 查询风险评估详情
     */
    RiskAssUserDetailDTO queryRiskAssUserDetail(RiskAssessmentDetailRequest request);


    /**
     * 创建中风险评估。
     * @param recordId 风险评估命令对象，包含评估所需的信息。
     * @return true 如果创建成功，false 否则。
     */
    Boolean createMidRiskAssByRecord(Long  recordId);

    /**
     * 根据查询条件获取风险评估列表
     * @param query 查询条件对象
     * @return 符合条件的风险评估列表
     */
    List<RiskAssessment> queryRiskAssessmentList(RiskAssessmentQuery query);

    /**
     * 查询评估单并同步返回评估单明细
     *
     * @param invalidPatientRiskCmd
     * @return
     */
    RiskAssDto queryRiskAssWithDetail(InvalidPatientRiskCmd invalidPatientRiskCmd);

    /**
     * 修改风险预估单状态
     *
     * @param updateStatusCmd
     * @return
     */
    boolean updateRiskAssessmentStatus(RiskAssessmentUpdateStatusCmd updateStatusCmd);
}
