package com.jdh.o2oservice.application.trade.handler.order;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.via.util.UavUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessment;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.promise.model.PromiseAppointmentTime;
import com.jdh.o2oservice.core.domain.promise.model.PromiseStation;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.PromiseGoRpcService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.angelpromise.dto.AngelShipDto;
import com.jdh.o2oservice.export.angelpromise.query.GetDetailByPromiseIdQuery;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.trade.dto.CustomOrderInfoDTO;
import com.jdh.o2oservice.export.trade.dto.CustomOrderProcessTrackDTO;
import com.jdh.o2oservice.export.trade.dto.CustomOrderTransferInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;

/**
 * @Description 自定义消费医疗到家订单（ordertype=200&sendpay294=6）
 * https://joyspace.jd.com/pages/JHRRSsFaHZMD4nsLVoOA
 * @Date 2024/8/31 下午2:39
 * <AUTHOR>
 **/
@Component
@Slf4j
public class CustomXFYLHomeOrderHandler extends AbstractCustomOrderHandler implements CustomOrderStrategy {

    @Resource
    private DuccConfig duccConfig;

    @Autowired
    private ProductApplication productApplication;
    /**
     * verticalBusinessRepository
     */
    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;
    /**
     * promiseGoRpcService
     */
    @Autowired
    private PromiseGoRpcService promiseGoRpcService;

    /**
     * AngelWorkApplication
     */
    @Resource
    AngelWorkApplication angelWorkApplication;


    @Override
    public String getKey() {
        return CustomOrderStrategy.XFYL_HOME_ORDER;
    }

    @Override
    public CustomOrderInfoDTO handle(CustomOrderHandlerContext receipt) {
        log.info("CustomXFYLHomeOrderHandler handle receipt={}", JSON.toJSONString(receipt));
        if (!careTestServiceType.contains(receipt.getOrder().getServiceType())){
            log.info("CustomXFYLHomeOrderHandler handle serviceType no mate");
            return null;
        }

        // 构建标签
        CustomOrderInfoDTO res = this.buildCustomOrderLabelInfo(receipt.getOrder(), JSON.parseObject(duccConfig.getCustomOrderInfoConfig()));

        // 护士上门护理
        JSONObject nurseVisitCareObj = JSON.parseObject(duccConfig.getHomeCareOrderList()).getJSONObject("processTrack");

        // 护士上门检测
        JSONObject nurseVisitTestObj = JSON.parseObject(duccConfig.getHomeTestOrderList()).getJSONObject("processTrack");

        // 骑手上门检测
        JSONObject riderVisitTestObj = JSON.parseObject(duccConfig.getSelfTestOrderList()).getJSONObject("processTrack");

        // 业务身份
        JdhVerticalBusiness verticalBusiness = receipt.getVerticalBusinessMap().get(receipt.getOrder().getVerticalCode());

        if (BusinessModeEnum.ANGEL_CARE.getCode().equals(verticalBusiness.getBusinessModeCode())){
            log.info("CustomXFYLHomeOrderHandler handle 护士上门护理 orderId={}", receipt.getOrderId());
            // 护士上门护理
            buildNurseVisitCareInfo(receipt, res, nurseVisitCareObj);

        }else if (BusinessModeEnum.SELF_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())){
            log.info("CustomXFYLHomeOrderHandler handle 骑手上门检测 orderId={}", receipt.getOrderId());
            // 骑手上门检测
            buildRiderVisitTestInfo(receipt, res, riderVisitTestObj);

        }else if (BusinessModeEnum.ANGEL_TEST.getCode().equals(verticalBusiness.getBusinessModeCode())){
            log.info("CustomXFYLHomeOrderHandler handle 护士上门检测 orderId={}", receipt.getOrderId());
            // 护士上门检测
            buildNurseVisitTestInfo(receipt, res, nurseVisitTestObj);

        }

        return res;
    }

    /**
     * 构建护士上门检测信息
     * @param receipt
     * @param res
     * @param nurseVisitTestObj
     */
    private void buildNurseVisitTestInfo(CustomOrderHandlerContext receipt, CustomOrderInfoDTO res,JSONObject nurseVisitTestObj) {
        // 履约单
        JdhPromise promise = this.selectProcessTrackOptimalPromise(receipt);
        log.info("CustomXFYLHomeOrderHandler buildNurseVisitTestInfo promise={}", JSON.toJSONString(promise));
        if (Objects.isNull(promise)){
            return;
        }

        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus",receipt.getOrder().getOrderStatus());
        param.put("promiseStatus",promise.getPromiseStatus());
        param.put("medPromiseStatusList", this.getMedicalPromiseStatusByPromiseId(receipt, promise.getPromiseId()));
        log.info("CustomXFYLHomeOrderHandler buildRiderVisitTestInfo param={}", JSON.toJSONString(param));

        // 定列透传自定义对象
        CustomOrderTransferInfoDTO customOrderTransferInfoDTO = new CustomOrderTransferInfoDTO();
        buildCustomOrderTransferInfoAllServiceNodeInfoDTO(nurseVisitTestObj, customOrderTransferInfoDTO);

        if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("waitNurseReceiveOrder").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitTestInfo 待护士接单 orderId={}", receipt.getOrderId());
            JSONObject waitNurseReceiveOrder = nurseVisitTestObj.getJSONObject("waitNurseReceiveOrder");
            res.setProcessTrack(this.buildWaitNurseReceiveOrderProcessTrack(receipt.getPromiseIdMappingPromiseHistoryMap().get(promise.getPromiseId()), promise, waitNurseReceiveOrder));
            buildCustomOrderTransferInfoDTO(waitNurseReceiveOrder, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("waitNurseVisit").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitTestInfo 待护士上门 orderId={}", receipt.getOrderId());
            JSONObject waitNurseVisit = nurseVisitTestObj.getJSONObject("waitNurseVisit");
            res.setProcessTrack(this.buildWaitNurseVisitProcessTrack(receipt.getPromiseIdMappingAngelWorkMap().get(promise.getPromiseId()), promise, waitNurseVisit));
            buildCustomOrderTransferInfoDTO(waitNurseVisit, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("waitServiceProgress").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitTestInfo 护士服务中 orderId={}", receipt.getOrderId());
            JSONObject waitServiceProgress = nurseVisitTestObj.getJSONObject("waitServiceProgress");
            Long skuId = receipt.getOrderIdMappingOrderItemMap().get(receipt.getOrder().getOrderId()).get(0).getSkuId();
            JdhSkuDto jdhSkuDto = querySkuInfo(skuId);
            res.setProcessTrack(this.buildWaitServiceProgressProcessTrack(receipt.getPromiseIdMappingPromiseHistoryMap().get(promise.getPromiseId())
                    , receipt.getPromiseIdMappingAngelWorkMap().get(promise.getPromiseId()), promise,  jdhSkuDto.getServiceDuration(), waitServiceProgress));
            buildCustomOrderTransferInfoDTO(waitServiceProgress, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("underInspection").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitTestInfo 送检中 orderId={}", receipt.getOrderId());
            JSONObject underInspection = nurseVisitTestObj.getJSONObject("underInspection");
            res.setProcessTrack(this.buildUnderInspectionProcessTrack(receipt.getPromiseIdMappingPromiseHistoryMap().get(promise.getPromiseId()), promise, underInspection));
            buildCustomOrderTransferInfoDTO(underInspection, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("laboratoryTesting").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitTestInfo 实验室检测中 orderId={}", receipt.getOrderId());
            JSONObject laboratoryTesting = nurseVisitTestObj.getJSONObject("laboratoryTesting");
            res.setProcessTrack(this.buildLaboratoryTestingProcessTrack(promise, receipt.getPromiseIdMappingMedicalPromiseMap().get(promise.getPromiseId()), laboratoryTesting));
            buildCustomOrderTransferInfoDTO(laboratoryTesting, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("viewReport").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitTestInfo 报告已生成 orderId={}", receipt.getOrderId());
            JSONObject viewReport = nurseVisitTestObj.getJSONObject("viewReport");
            res.setProcessTrack(this.buildLaboratoryTestingProcessTrack(promise, receipt.getPromiseIdMappingMedicalPromiseMap().get(promise.getPromiseId()), viewReport));
            buildCustomOrderTransferInfoDTO(viewReport, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitTestObj.getJSONObject("serviceFinish").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitTestInfo 服务已完成 orderId={}", receipt.getOrderId());
            JSONObject serviceFinish = nurseVisitTestObj.getJSONObject("serviceFinish");
            res.setProcessTrack(this.buildServiceFinishProcessTrackHome(serviceFinish));
            buildCustomOrderTransferInfoDTO(serviceFinish, res, customOrderTransferInfoDTO, promise);
        }

        CustomOrderProcessTrackDTO processTrack = res.getProcessTrack();
        if(StrUtil.isNotBlank(processTrack.getUrl())){
            String orderDetailUrl = MessageFormat.format(processTrack.getUrl(), String.valueOf(receipt.getOrder().getOrderId()));
            processTrack.setUrl(orderDetailUrl);
            customOrderTransferInfoDTO.setJumpDetailUrl(orderDetailUrl);
        }
        res.setTransferInfo(customOrderTransferInfoDTO);
    }


    /**
     * 构建骑手上门检测信息
     * @param receipt
     * @param res
     * @param riderVisitTestObj
     */
    private void buildRiderVisitTestInfo(CustomOrderHandlerContext receipt, CustomOrderInfoDTO res, JSONObject riderVisitTestObj) {
        // 履约单
        JdhPromise promise = this.selectProcessTrackOptimalPromise(receipt);
        log.info("CustomXFYLHomeOrderHandler buildRiderVisitTestInfo promise={}", JSON.toJSONString(promise));
        if (Objects.isNull(promise)){
            return;
        }
        AngelShipDto angelShipDto;
        GetDetailByPromiseIdQuery getDetailByPromiseIdQuery = new GetDetailByPromiseIdQuery();
        getDetailByPromiseIdQuery.setPromiseId(promise.getPromiseId());
        angelShipDto = angelWorkApplication.getDetailByPromiseId(getDetailByPromiseIdQuery);
        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus",receipt.getOrder().getOrderStatus());
        param.put("promiseStatus",promise.getPromiseStatus());
        param.put("medPromiseStatusList", this.getMedicalPromiseStatusByPromiseId(receipt, promise.getPromiseId()));
        log.info("CustomXFYLHomeOrderHandler buildRiderVisitTestInfo param={}", JSON.toJSONString(param));

        // 定列透传自定义对象
        CustomOrderTransferInfoDTO customOrderTransferInfoDTO = new CustomOrderTransferInfoDTO();
        buildCustomOrderTransferInfoAllServiceNodeInfoDTO(riderVisitTestObj, customOrderTransferInfoDTO);

        if ((Boolean) AviatorEvaluator.compile(riderVisitTestObj.getJSONObject("waitInspectorReceiveOrder").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildRiderVisitTestInfo 待送检员接单 orderId={}", receipt.getOrderId());
            JSONObject waitInspectorReceiveOrder = riderVisitTestObj.getJSONObject("waitInspectorReceiveOrder");
            res.setProcessTrack(this.buildWaitNurseReceiveOrderProcessTrackHome(receipt, receipt.getPromiseIdMappingPromiseHistoryMap().get(promise.getPromiseId()), promise, receipt.getPromiseIdMappingMedicalPromiseMap().get(promise.getPromiseId()), waitInspectorReceiveOrder, angelShipDto));
            buildCustomOrderTransferInfoDTO(waitInspectorReceiveOrder, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(riderVisitTestObj.getJSONObject("waitInspectorVisit").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildRiderVisitTestInfo 待送检员上门 orderId={}", receipt.getOrderId());
            JSONObject waitInspectorVisit = riderVisitTestObj.getJSONObject("waitInspectorVisit");
            res.setProcessTrack(this.buildWaitNurseVisitProcessTrackHome(receipt, receipt.getPromiseIdMappingAngelWorkMap().get(promise.getPromiseId()), promise, receipt.getPromiseIdMappingMedicalPromiseMap().get(promise.getPromiseId()), waitInspectorVisit, angelShipDto));
            buildCustomOrderTransferInfoDTO(waitInspectorVisit, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(riderVisitTestObj.getJSONObject("inspectorService").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildRiderVisitTestInfo 送检员服务中 orderId={}", receipt.getOrderId());
            Long skuId = receipt.getOrderIdMappingOrderItemMap().get(receipt.getOrder().getOrderId()).get(0).getSkuId();
            JdhSkuDto jdhSkuDto = querySkuInfo(skuId);
            JSONObject inspectorService = riderVisitTestObj.getJSONObject("inspectorService");
            res.setProcessTrack(this.buildWaitServiceProgressProcessTrackHome(receipt, receipt.getPromiseIdMappingPromiseHistoryMap().get(promise.getPromiseId())
                    , receipt.getPromiseIdMappingAngelWorkMap().get(promise.getPromiseId()), promise, receipt.getPromiseIdMappingMedicalPromiseMap().get(promise.getPromiseId()),  jdhSkuDto.getServiceDuration(), inspectorService, angelShipDto));
            buildCustomOrderTransferInfoDTO(inspectorService, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(riderVisitTestObj.getJSONObject("underInspection").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildRiderVisitTestInfo 送检中 orderId={}", receipt.getOrderId());
            JSONObject underInspection = riderVisitTestObj.getJSONObject("underInspection");
            res.setProcessTrack(this.buildUnderInspectionProcessTrackHome(receipt, receipt.getPromiseIdMappingPromiseHistoryMap().get(promise.getPromiseId()), promise, receipt.getPromiseIdMappingMedicalPromiseMap().get(promise.getPromiseId()), underInspection, angelShipDto));
            buildCustomOrderTransferInfoDTO(underInspection, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(riderVisitTestObj.getJSONObject("laboratoryTesting").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildRiderVisitTestInfo 实验室检测中 orderId={}", receipt.getOrderId());
            JSONObject laboratoryTesting = riderVisitTestObj.getJSONObject("laboratoryTesting");
            res.setProcessTrack(this.buildLaboratoryTestingProcessTrackHome(receipt, receipt.getPromiseIdMappingMedicalPromiseMap().get(promise.getPromiseId()),promise, laboratoryTesting, angelShipDto));
            buildCustomOrderTransferInfoDTO(laboratoryTesting, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(riderVisitTestObj.getJSONObject("viewReport").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildRiderVisitTestInfo 报告已生成 orderId={}", receipt.getOrderId());
            JSONObject viewReport = riderVisitTestObj.getJSONObject("viewReport");
            res.setProcessTrack(this.buildLaboratoryTestingProcessTrackHome(receipt, receipt.getPromiseIdMappingMedicalPromiseMap().get(promise.getPromiseId()),promise, viewReport, angelShipDto));
            buildCustomOrderTransferInfoDTO(viewReport, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(riderVisitTestObj.getJSONObject("serviceFinish").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildRiderVisitTestInfo 服务已完成 orderId={}", receipt.getOrderId());
            JSONObject serviceFinish = riderVisitTestObj.getJSONObject("serviceFinish");
            res.setProcessTrack(this.buildServiceFinishProcessTrackHome(serviceFinish));
            buildCustomOrderTransferInfoDTO(serviceFinish, res, customOrderTransferInfoDTO, promise);
        }

        CustomOrderProcessTrackDTO processTrack = res.getProcessTrack();
        if(StrUtil.isNotBlank(processTrack.getUrl())){
            String orderDetailUrl = MessageFormat.format(processTrack.getUrl(), String.valueOf(receipt.getOrder().getOrderId()));
            processTrack.setUrl(orderDetailUrl);
            customOrderTransferInfoDTO.setJumpDetailUrl(orderDetailUrl);
        }
        res.setTransferInfo(customOrderTransferInfoDTO);
    }

    /**
     * 构建护士上门护理信息
     * @param receipt
     * @param res
     * @param nurseVisitCareObj
     */
    private void buildNurseVisitCareInfo(CustomOrderHandlerContext receipt, CustomOrderInfoDTO res, JSONObject nurseVisitCareObj) {
        // 履约单
        JdhPromise promise = this.selectProcessTrackOptimalPromise(receipt);
        log.info("CustomXFYLHomeOrderHandler buildNurseVisitCareInfo promise={}", JSON.toJSONString(promise));
        if (Objects.isNull(promise)){
            return;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("orderStatus",receipt.getOrder().getOrderStatus());
        param.put("promiseStatus",promise.getPromiseStatus());
        List<RiskAssessment> riskAssessmentList = receipt.getPromiseIdMappingRiskAssessmentMap().get(promise.getPromiseId());
        param.put("riskAssessmentStatus", CollectionUtils.isEmpty(riskAssessmentList) ? null : riskAssessmentList.get(0).getRiskAssessmentStatus());
        param.put("highRiskAssessmentFlag", highRiskFlag(receipt, promise));
        log.info("CustomXFYLHomeOrderHandler buildNurseVisitCareInfo param={}", JSON.toJSONString(param));

        // 定列透传自定义对象
        CustomOrderTransferInfoDTO customOrderTransferInfoDTO = new CustomOrderTransferInfoDTO();
        buildCustomOrderTransferInfoAllServiceNodeInfoDTO(nurseVisitCareObj, customOrderTransferInfoDTO);

        if ((Boolean) AviatorEvaluator.compile(nurseVisitCareObj.getJSONObject("waitRiskAssessment").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitCareInfo 待评估 orderId={}", receipt.getOrderId());
            JSONObject waitRiskAssessment = nurseVisitCareObj.getJSONObject("waitRiskAssessment");
            res.setProcessTrack(this.buildWaitRiskAssessmentV2(receipt, promise, waitRiskAssessment));
            buildCustomOrderTransferInfoDTO(waitRiskAssessment, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitCareObj.getJSONObject("waitNurseReceiveOrder").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitCareInfo 待护士接单 orderId={}", receipt.getOrderId());
            JSONObject waitNurseReceiveOrder = nurseVisitCareObj.getJSONObject("waitNurseReceiveOrder");
            res.setProcessTrack(this.buildWaitNurseReceiveOrderProcessTrackV2(receipt, receipt.getPromiseIdMappingPromiseHistoryMap().get(promise.getPromiseId()), promise, waitNurseReceiveOrder));
            buildCustomOrderTransferInfoDTO(waitNurseReceiveOrder, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitCareObj.getJSONObject("waitNurseVisit").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitCareInfo 待护士上门 orderId={}", receipt.getOrderId());
            JSONObject waitNurseVisit = nurseVisitCareObj.getJSONObject("waitNurseVisit");
            res.setProcessTrack(this.buildWaitNurseVisitProcessTrackV2(receipt, promise, waitNurseVisit));
            buildCustomOrderTransferInfoDTO(waitNurseVisit, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitCareObj.getJSONObject("nurseVisitProgress").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitCareInfo 护士上门中 orderId={}", receipt.getOrderId());
            JSONObject waitNurseVisit = nurseVisitCareObj.getJSONObject("nurseVisitProgress");
            res.setProcessTrack(this.buildNurseVisitProgressProcessTrackV2(receipt, promise, waitNurseVisit));
            buildCustomOrderTransferInfoDTO(waitNurseVisit, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitCareObj.getJSONObject("waitServiceProgress").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitCareInfo 护士服务中 orderId={}", receipt.getOrderId());
            JSONObject waitServiceProgress = nurseVisitCareObj.getJSONObject("waitServiceProgress");
            res.setProcessTrack(this.buildWaitServiceProgressProcessTrackV2(waitServiceProgress));
            buildCustomOrderTransferInfoDTO(waitServiceProgress, res, customOrderTransferInfoDTO, promise);

        }else if ((Boolean) AviatorEvaluator.compile(nurseVisitCareObj.getJSONObject("serviceFinish").getString("statusExpression"), Boolean.TRUE).execute(param)){
            log.info("CustomXFYLHomeOrderHandler buildNurseVisitCareInfo 服务完成 orderId={}", receipt.getOrderId());
            JSONObject serviceFinish = nurseVisitCareObj.getJSONObject("serviceFinish");
            CustomOrderProcessTrackDTO processTrack = this.buildServiceFinishProcessTrackHome(serviceFinish);
            res.setProcessTrack(processTrack);
            res.setServiceMessages(Collections.singletonList(this.buildServiceMessageExpireDate(promise)));
            buildCustomOrderTransferInfoDTO(serviceFinish, res, customOrderTransferInfoDTO, promise);

        }

        CustomOrderProcessTrackDTO processTrack = res.getProcessTrack();
        if(StrUtil.isNotBlank(processTrack.getUrl())){
            String orderDetailUrl = MessageFormat.format(processTrack.getUrl(), String.valueOf(receipt.getOrder().getOrderId()));
            processTrack.setUrl(orderDetailUrl);
            customOrderTransferInfoDTO.setJumpDetailUrl(orderDetailUrl);
        }
        res.setTransferInfo(customOrderTransferInfoDTO);

        this.customOrderReplaceWords(receipt, res,"xfylHomeCareOrderList");
    }


    /**
     * 查询SKU信息
     * @param skuId
     * @return
     */
    private JdhSkuDto querySkuInfo(Long skuId){
        try {
            Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder()
                    .querySkuCoreData(Boolean.TRUE)
                    //.queryServiceItem(Boolean.TRUE)
                    .skuIdList(Sets.newHashSet(skuId))
                    .build());
            log.info("CustomXFYLHomeOrderHandler querySkuInfo skuId={}, jdhSkuDtoMap={}", skuId, JSON.toJSONString(jdhSkuDtoMap));
            return jdhSkuDtoMap.get(skuId);
        }catch (Exception e){
            log.info("CustomXFYLHomeOrderHandler handle querySkuInfo exception",e);
            return null;
        }
    }

    /**
     * 待护士接单-服务流程
     * @param promiseHistories
     * @param promise
     * @param waitNurseReceiveOrderObj
     * @return
     */
    public CustomOrderProcessTrackDTO buildWaitNurseReceiveOrderProcessTrackHome(CustomOrderHandlerContext receipt, List<JdhPromiseHistory> promiseHistories, JdhPromise promise, List<MedicalPromiseDTO> medicalPromiseDTOS, JSONObject waitNurseReceiveOrderObj, AngelShipDto angelShipDto) {
        String aggregateStatus = waitNurseReceiveOrderObj.getString("aggregateStatus");
        if (AGGREGATE_STATUS_ETA.contains(aggregateStatus)){
            CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSON.toJSONString(waitNurseReceiveOrderObj), CustomOrderProcessTrackDTO.class);
            JdhVerticalBusiness jdhVerticalBusiness = receipt.getVerticalBusinessMap().get(receipt.getOrder().getVerticalCode());
            if (StringUtils.equals(BusinessModeEnum.SELF_TEST.getCode(),jdhVerticalBusiness.getBusinessModeCode())){
                String aggregateMsg = getProcessTrackMessage(aggregateStatus, jdhVerticalBusiness.getBusinessModeCode(),promise, medicalPromiseDTOS, angelShipDto);
                if (StringUtils.isNotBlank(aggregateMsg)) {
                    processTrack.setMessage(aggregateMsg);
                    return processTrack;
                }
            }
        }
        return this.buildWaitNurseReceiveOrderProcessTrack(promiseHistories,promise,waitNurseReceiveOrderObj);
    }


    /**
     * getProcessTrackMessage
     * @param aggregateStatus
     * @param businessMode
     * @return
     */
    private String getProcessTrackMessage(String aggregateStatus,String businessMode,JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList, AngelShipDto angelShipDto){
        String queryPromiseGoStatus = UavUtil.orderDetailQueryPromiseGoStatus(aggregateStatus, angelShipDto, medicalPromiseList);
        UserPromisegoRequestBo userPromisegoRequestBo = UserPromisegoRequestBo.builder()
                .aggregateStatus(queryPromiseGoStatus)
                .promiseId(jdhPromise.getPromiseId())
                .businessMode(businessMode)
                .queryTermScript(Boolean.TRUE)
                .deliveryType(angelShipDto != null ? angelShipDto.getType() : null)
                .build();

        //时间
        PromiseAppointmentTime promiseAppointmentTime = jdhPromise.getAppointmentTime();
        if (Objects.nonNull(promiseAppointmentTime)) {
            PromisegoRequestAppointmentTime appointmentTime = new PromisegoRequestAppointmentTime();
            appointmentTime.setDateType(promiseAppointmentTime.getDateType());
            appointmentTime.setImmediately(promiseAppointmentTime.getIsImmediately());
            appointmentTime.setAppointmentStartTime(promiseAppointmentTime.getAppointmentStartTime());
            appointmentTime.setAppointmentEndTime(promiseAppointmentTime.getAppointmentEndTime());
            userPromisegoRequestBo.setAppointmentTime(appointmentTime);
        }


        //地址
        PromiseStation store = jdhPromise.getStore();
        if (Objects.nonNull(store)) {
            PromisegoRequestAddress address = PromisegoRequestAddress.builder()
                    .provinceId(store.getProvinceCode())
                    .cityId(store.getCityCode())
                    .countyId(store.getDistrictCode())
                    .townId(store.getTownCode())
                    .provinceName(store.getProvinceName())
                    .cityName(store.getCityName())
                    .countyName(store.getDistrictName())
                    .townName(store.getTownName())
                    .fullAddress(store.getStoreAddr())
                    .build();
            userPromisegoRequestBo.setAppointmentAddress(address);
        }

        UserPromisegoBo userPromisegoBo = promiseGoRpcService.queryUserPromisego(userPromisegoRequestBo);
        String uavErrorMsg = UavUtil.orderDetailNavMainTitle(angelShipDto, userPromisegoBo, queryPromiseGoStatus);
        if (StringUtils.isNotBlank(uavErrorMsg)) {
            return uavErrorMsg;
        }

        jdhPromise.setUserPromisegoBo(userPromisegoBo);
        if (Objects.nonNull(userPromisegoBo) && Objects.nonNull(userPromisegoBo.getCurrScript())) {
            String scriptContent = userPromisegoBo.getCurrScript().getScriptContent();
            return HtmlUtil.cleanHtmlTag(scriptContent);
        }
        return null;
    }

    /**
     * 待护士上门-服务流程
     * @param angelWorks
     * @param promise
     * @param waitNurseVisitObj
     * @return
     */
    public CustomOrderProcessTrackDTO buildWaitNurseVisitProcessTrackHome(CustomOrderHandlerContext receipt, List<AngelWork> angelWorks, JdhPromise promise, List<MedicalPromiseDTO> medicalPromiseList, JSONObject waitNurseVisitObj, AngelShipDto angelShipDto) {
        String aggregateStatus = waitNurseVisitObj.getString("aggregateStatus");
        if (AGGREGATE_STATUS_ETA.contains(aggregateStatus)){
            CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSON.toJSONString(waitNurseVisitObj), CustomOrderProcessTrackDTO.class);
            JdhVerticalBusiness jdhVerticalBusiness = receipt.getVerticalBusinessMap().get(receipt.getOrder().getVerticalCode());
            if (StringUtils.equals(BusinessModeEnum.SELF_TEST.getCode(),jdhVerticalBusiness.getBusinessModeCode())){
                String aggregateMsg = getProcessTrackMessage(aggregateStatus, jdhVerticalBusiness.getBusinessModeCode(),promise, medicalPromiseList, angelShipDto);
                if (StringUtils.isNotBlank(aggregateMsg)) {
                    processTrack.setMessage(aggregateMsg);
                    return processTrack;
                }
            }
        }
        return this.buildWaitNurseVisitProcessTrack(angelWorks,promise,waitNurseVisitObj);

    }

    /**
     * 护士服务中-服务流程
     * @param promiseHistories
     * @param angelWorks
     * @param promise
     * @param waitServiceProgressObj
     * @return
     */
    public CustomOrderProcessTrackDTO buildWaitServiceProgressProcessTrackHome(CustomOrderHandlerContext receipt, List<JdhPromiseHistory> promiseHistories, List<AngelWork> angelWorks, JdhPromise promise, List<MedicalPromiseDTO> medicalPromiseList
            , Integer serviceDuration, JSONObject waitServiceProgressObj, AngelShipDto angelShipDto) {
        String aggregateStatus = waitServiceProgressObj.getString("aggregateStatus");
        if (AGGREGATE_STATUS_ETA.contains(aggregateStatus)){
            CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSON.toJSONString(waitServiceProgressObj), CustomOrderProcessTrackDTO.class);
            JdhVerticalBusiness jdhVerticalBusiness = receipt.getVerticalBusinessMap().get(receipt.getOrder().getVerticalCode());
            if (StringUtils.equals(BusinessModeEnum.SELF_TEST.getCode(),jdhVerticalBusiness.getBusinessModeCode())){
                String aggregateMsg = getProcessTrackMessage(aggregateStatus, jdhVerticalBusiness.getBusinessModeCode(),promise, medicalPromiseList, angelShipDto);
                if (StringUtils.isNotBlank(aggregateMsg)) {
                    processTrack.setMessage(aggregateMsg);
                    return processTrack;
                }
            }
        }
        return this.buildWaitServiceProgressProcessTrack(promiseHistories,angelWorks,promise,serviceDuration,waitServiceProgressObj);

    }

    /**
     * 送检中-服务流程
     * @param promiseHistories
     * @param promise
     * @param underInspectionObj
     * @return
     */
    public CustomOrderProcessTrackDTO buildUnderInspectionProcessTrackHome(CustomOrderHandlerContext receipt, List<JdhPromiseHistory> promiseHistories, JdhPromise promise, List<MedicalPromiseDTO> medicalPromiseList, JSONObject underInspectionObj, AngelShipDto angelShipDto) {
        String aggregateStatus = underInspectionObj.getString("aggregateStatus");
        if (AGGREGATE_STATUS_ETA.contains(aggregateStatus)){
            CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSON.toJSONString(underInspectionObj), CustomOrderProcessTrackDTO.class);
            JdhVerticalBusiness jdhVerticalBusiness = receipt.getVerticalBusinessMap().get(receipt.getOrder().getVerticalCode());
            if (StringUtils.equals(BusinessModeEnum.SELF_TEST.getCode(),jdhVerticalBusiness.getBusinessModeCode())){
                String aggregateMsg = getProcessTrackMessage(aggregateStatus, jdhVerticalBusiness.getBusinessModeCode(),promise, medicalPromiseList, angelShipDto);
                if (StringUtils.isNotBlank(aggregateMsg)) {
                    processTrack.setMessage(aggregateMsg);
                    return processTrack;
                }
            }
        }
        return this.buildUnderInspectionProcessTrack(promiseHistories,promise,underInspectionObj);
    }

    /**
     * 实验室检测中-服务流程
     * @param medicalPromiseList
     * @param laboratoryTestingObj
     * @return
     */
    public CustomOrderProcessTrackDTO buildLaboratoryTestingProcessTrackHome(CustomOrderHandlerContext receipt, List<MedicalPromiseDTO> medicalPromiseList, JdhPromise promise,JSONObject laboratoryTestingObj, AngelShipDto angelShipDto) {
        String aggregateStatus = laboratoryTestingObj.getString("aggregateStatus");
        if (AGGREGATE_STATUS_ETA.contains(aggregateStatus)){
            CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSON.toJSONString(laboratoryTestingObj), CustomOrderProcessTrackDTO.class);
            JdhVerticalBusiness jdhVerticalBusiness = receipt.getVerticalBusinessMap().get(receipt.getOrder().getVerticalCode());
            if (StringUtils.equals(BusinessModeEnum.SELF_TEST.getCode(),jdhVerticalBusiness.getBusinessModeCode())){
                String aggregateMsg = getProcessTrackMessage(aggregateStatus, jdhVerticalBusiness.getBusinessModeCode(),promise, medicalPromiseList, angelShipDto);
                if (StringUtils.isNotBlank(aggregateMsg)) {
                    processTrack.setMessage(aggregateMsg);
                    return processTrack;
                }
            }
        }
        return this.buildLaboratoryTestingProcessTrack(promise, medicalPromiseList,laboratoryTestingObj);
    }

}
