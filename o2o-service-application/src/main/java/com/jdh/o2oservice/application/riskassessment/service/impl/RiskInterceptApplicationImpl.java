package com.jdh.o2oservice.application.riskassessment.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.riskassessment.service.RiskAssessmentApplication;
import com.jdh.o2oservice.application.riskassessment.service.RiskInterceptApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.common.enums.RiskAssessmentStatusEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.support.riskintercept.enums.RiskInterceptEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.event.PromiseSubmitEventBody;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.support.riskintercept.cqe.RiskInterceptModifyStatusCmd;
import com.jdh.o2oservice.core.domain.support.riskintercept.event.RiskInterceptEventBody;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.support.riskintercept.context.JdhPromiseRiskInterceptContext;
import com.jdh.o2oservice.core.domain.support.riskintercept.cqe.RiskInterceptQuery;
import com.jdh.o2oservice.core.domain.support.riskintercept.enums.RiskInterceptCodeEnum;
import com.jdh.o2oservice.core.domain.support.riskintercept.enums.RiskPassStatusEnum;
import com.jdh.o2oservice.core.domain.support.riskintercept.enums.RiskWorkTagEnum;
import com.jdh.o2oservice.core.domain.support.riskintercept.factory.JdhPromiseRiskInterceptRecordFactory;
import com.jdh.o2oservice.core.domain.support.riskintercept.model.JdhPromiseRiskInterceptRecord;
import com.jdh.o2oservice.core.domain.support.riskintercept.repository.JdhPromiseRiskInterceptRecordRepository;
import com.jdh.o2oservice.export.riskassessment.cmd.*;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssDto;
import com.jdh.o2oservice.export.riskassessment.dto.RiskAssessmentDetailDto;
import com.jdh.o2oservice.export.trade.dto.ManRefundTipsInfoDto;
import com.jdh.o2oservice.export.trade.query.ManRefundTipsOrderParam;
import com.jdh.o2oservice.export.trade.query.ManRefundTipsParam;
import com.jdh.o2oservice.export.trade.query.RefundOrderParam;
import com.jdh.o2oservice.export.trade.query.RefundOrderSku;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName RiskInterceptApplicationImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 20:04
 */
@Service
@Slf4j
public class RiskInterceptApplicationImpl implements RiskInterceptApplication {

    @Resource
    private RiskAssessmentApplication riskAssessmentApplication;

    @Resource
    private JdhPromiseRiskInterceptRecordRepository jdhPromiseRiskInterceptRecordRepository;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private PromiseRepository promiseRepository;

    @Resource
    private TradeApplication tradeApplication;

    @Resource
    private RedisLockUtil redisLockUtil;

    private static final String REFUND_DESC = "评估师评估未通过退款";

    /**
     * 执行风险拦截
     *
     * @param riskInterceptCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public boolean doRiskIntercept(RiskInterceptCmd riskInterceptCmd) {
        List<JdhPromiseRiskInterceptRecord> interceptRecordList = Lists.newArrayList();

        //组装高风险项目评估拦截，可抽成模版方法
        JdhPromiseRiskInterceptContext context = new JdhPromiseRiskInterceptContext();
        context.setDomainCode(riskInterceptCmd.getDomainCode());
        context.setAggregateCode(riskInterceptCmd.getAggregateCode());
        context.setOrderId(riskInterceptCmd.getOrderId());
        context.setPromiseId(riskInterceptCmd.getPromiseId());
        context.setInterceptEventCode(riskInterceptCmd.getInterceptEventCode());
        context.setRiskCode(RiskInterceptCodeEnum.RISK_HIGH_ITEM_INTERCEPT.getRiskCode());
        context.setPromisePatientIds(riskInterceptCmd.getPromisePatientIds());
        context.setEventBody(riskInterceptCmd.getEventBody());

        RiskAssessmentCmd cmd = new RiskAssessmentCmd();
        cmd.setPromiseId(Long.valueOf(riskInterceptCmd.getPromiseId()));
        Boolean riskAssessment = riskAssessmentApplication.createRiskAssessment(cmd);
        if(riskAssessment) {
            context.setRiskWorkTag(RiskWorkTagEnum.NEED_TAG.getTag());
            context.setRiskPassStatus(RiskPassStatusEnum.INIT.getStatus());
        }else {
            context.setRiskWorkTag(RiskWorkTagEnum.NEED_NO_TAG.getTag());
            context.setRiskPassStatus(RiskPassStatusEnum.PASS.getStatus());
        }
        List<JdhPromiseRiskInterceptRecord> higtRecordList = JdhPromiseRiskInterceptRecordFactory.create(context);
        if(CollectionUtils.isNotEmpty(higtRecordList)) {
            interceptRecordList.addAll(higtRecordList);
        }

        jdhPromiseRiskInterceptRecordRepository.batchSave(interceptRecordList);

        //如果需要执行高风险评估，发送事件，可抽成模版方法
        if(RiskWorkTagEnum.needDo(context.getRiskWorkTag())) {
            JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(riskInterceptCmd.getPromiseId()).build());
            eventCoordinator.publish(EventFactory.newDefaultEvent(promise, PromiseEventTypeEnum.HIGH_RISK_ITEM_INTERCEPT,
                    RiskInterceptEventBody.builder().promiseId(promise.getPromiseId()).build()));
        }

        //检查是否需要拦截或者是否拦截已经通过，如果通过这发送拦截的事件，可抽成模版方法
        checkRiskInterceptPass(interceptRecordList);

        return Boolean.TRUE;
    }

    /**
     * 修改拦截记录状态
     *
     * @param modifyRiskInterceptCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public boolean modifyRiskInterceptStatus(ModifyRiskInterceptCmd modifyRiskInterceptCmd) {
        //参数检查
        if(Objects.isNull(modifyRiskInterceptCmd) || Objects.isNull(modifyRiskInterceptCmd.getPromiseId())) {
            log.error("RiskInterceptApplicationImpl -> modifyRiskInterceptStatus, 参数格式不正确");
            return Boolean.FALSE;
        }

        RiskInterceptQuery riskInterceptQuery = new RiskInterceptQuery();
        riskInterceptQuery.setRiskCode(modifyRiskInterceptCmd.getRiskCode());
        riskInterceptQuery.setPromiseId(modifyRiskInterceptCmd.getPromiseId());
        List<JdhPromiseRiskInterceptRecord> interceptRecordList = jdhPromiseRiskInterceptRecordRepository.queryRiskInterceptList(riskInterceptQuery);
        if(CollectionUtils.isEmpty(interceptRecordList)) {
            log.error("RiskInterceptApplicationImpl -> modifyRiskInterceptStatus, 没有查询到拦截记录信息");
            return Boolean.FALSE;
        }

        //更新拦截记录状态
        RiskInterceptModifyStatusCmd riskInterceptModifyStatusCmd = new RiskInterceptModifyStatusCmd();
        riskInterceptModifyStatusCmd.setRiskCode(modifyRiskInterceptCmd.getRiskCode());
        riskInterceptModifyStatusCmd.setPromiseId(modifyRiskInterceptCmd.getPromiseId());
        riskInterceptModifyStatusCmd.setPromisePatientIds(modifyRiskInterceptCmd.getPromisePatientIds());
        riskInterceptModifyStatusCmd.setRiskPassStatus(modifyRiskInterceptCmd.getRiskPassStatus());
        int result = jdhPromiseRiskInterceptRecordRepository.modifyRiskInterceptStatus(riskInterceptModifyStatusCmd);

        if(result <= 0) {
            log.error("RiskInterceptApplicationImpl -> modifyRiskInterceptStatus, 更新状态失败");
            return Boolean.FALSE;
        }


        //中高风险评估前置执行
        if(RiskPassStatusEnum.NO_PASS.getStatus().equals(riskInterceptModifyStatusCmd.getRiskPassStatus())) {
            log.info("RiskInterceptApplicationImpl -> modifyRiskInterceptStatus, 高风险检测未通过.申请当前被服务人的退款");
            //申请退款
            orderPatientRefund(interceptRecordList.get(0).getOrderId(), modifyRiskInterceptCmd, REFUND_DESC);
        }

        interceptRecordList.forEach(record -> {
            if(modifyRiskInterceptCmd.getPromisePatientIds().contains(record.getPromisePatientId())) {
                record.setRiskPassStatus(modifyRiskInterceptCmd.getRiskPassStatus());
            }
        });

        //检查是否需要拦截或者是否拦截已经通过，如果通过这发送拦截的事件
        checkRiskInterceptPass(interceptRecordList);
        return false;
    }

    /**
     * 作废人维度风险评估单
     *
     * @param invalidPatientRiskCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public boolean invalidPatientRiskAssessment(InvalidPatientRiskCmd invalidPatientRiskCmd) {
        if(Objects.isNull(invalidPatientRiskCmd)
                || Objects.isNull(invalidPatientRiskCmd.getPromiseId())
                || CollectionUtils.isEmpty(invalidPatientRiskCmd.getPromisePatientIds())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        //获取重试锁
        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.RISK_ASS_REFUND_LOCK_PREFIX, invalidPatientRiskCmd.getPromiseId());
        if(!redisLockUtil.tryLockWithRetry(redisKey, "1", RedisKeyEnum.RISK_ASS_REFUND_LOCK_PREFIX.getExpireTime(), 3, 1000L)){
            log.error("RiskInterceptApplicationImpl -> invalidPatientRiskAssessment,获取分布式锁失败!");
            throw new BusinessException(BusinessErrorCode.SAVE_OPTION_DOING);
        }

        try{
            RiskAssDto riskAssDto = riskAssessmentApplication.queryRiskAssWithDetail(invalidPatientRiskCmd);
            if(Objects.isNull(riskAssDto)) {
                return false;
            }

            if(RiskAssessmentStatusEnum.REFUSE.getStatus().equals(riskAssDto.getRiskAssessmentStatus())
                    || RiskAssessmentStatusEnum.INVALID.getStatus().equals(riskAssDto.getRiskAssessmentStatus())) {
                return true;
            }

            List<RiskAssessmentDetailDto> riskAssessmentDetailDtos = riskAssDto.getRiskAssessmentDetailDtos();
            List<RiskAssessmentDetailDto> detailDtoList = riskAssessmentDetailDtos.stream().filter(item -> !invalidPatientRiskCmd.getPromisePatientIds().contains(item.getPromisePatientId())).collect(Collectors.toList());
            List<RiskAssessmentDetailDto> invalidDetailDtoList = riskAssessmentDetailDtos.stream().filter(item -> invalidPatientRiskCmd.getPromisePatientIds().contains(item.getPromisePatientId())).collect(Collectors.toList());

            //检查是否所有的明细都失效了
            List<RiskAssessmentDetailDto> validDetailDtoList = detailDtoList.stream().filter(item ->
                            RiskAssessmentStatusEnum.WAITING_ASS.getStatus().equals(item.getRiskAssessmentStatus())
                                    || RiskAssessmentStatusEnum.PASS.getStatus().equals(item.getRiskAssessmentStatus()))
                    .collect(Collectors.toList());

            RiskAssessmentUpdateStatusCmd updateStatusCmd = new RiskAssessmentUpdateStatusCmd();
            updateStatusCmd.setRiskAssessmentId(riskAssDto.getRiskAssessmentId());
            updateStatusCmd.setPromiseId(riskAssDto.getPromiseId());
            if(CollectionUtils.isEmpty(validDetailDtoList)) {
                updateStatusCmd.setRiskAssessmentStatus(RiskAssessmentStatusEnum.INVALID.getStatus());
            }else {
                updateStatusCmd.setRiskAssessmentStatus(riskAssDto.getRiskAssessmentStatus());
            }

            if(CollectionUtils.isNotEmpty(invalidDetailDtoList)) {
                List<RiskAssessmentDetailCmd> riskAssessmentDetailCmds = Lists.newArrayList();
                RiskAssessmentDetailCmd detailCmd = new RiskAssessmentDetailCmd();
                detailCmd.setRiskAssessmentId(invalidDetailDtoList.get(0).getRiskAssessmentId());
                detailCmd.setPromisePatientId(invalidDetailDtoList.get(0).getPromisePatientId());
                detailCmd.setRiskAssPatientStatus(RiskAssessmentStatusEnum.INVALID.getStatus().shortValue());
                riskAssessmentDetailCmds.add(detailCmd);
                updateStatusCmd.setRiskAssessmentDetailCmds(riskAssessmentDetailCmds);
            }
            return riskAssessmentApplication.updateRiskAssessmentStatus(updateStatusCmd);
        }finally {
            redisLockUtil.unLock(redisKey);
        }

    }

    private void orderPatientRefund(String orderId, ModifyRiskInterceptCmd modifyRiskInterceptCmd, String refundDesc) {
        log.info("AngelServiceRecordEventConsumer orderRefund modifyRiskInterceptCmd={}", JSON.toJSONString(modifyRiskInterceptCmd));

        JdhPromise jdhPromise = promiseRepository.find(new JdhPromiseIdentifier(modifyRiskInterceptCmd.getPromiseId()));

        ManRefundTipsParam manRefundTipsParam = new ManRefundTipsParam();
        manRefundTipsParam.setPromiseId(modifyRiskInterceptCmd.getPromiseId());
        List<ManRefundTipsOrderParam> orderParamList = new ArrayList<>();
        ManRefundTipsOrderParam manRefundTipsOrderParam = ManRefundTipsOrderParam.builder()
                .orderId(Long.valueOf(orderId))
                .promisePatientId(modifyRiskInterceptCmd.getPromisePatientIds().get(0))
                .build();
        orderParamList.add(manRefundTipsOrderParam);
        manRefundTipsParam.setOrderList(orderParamList);
        // 查询运营端退款提示信息
        ManRefundTipsInfoDto manRefundTipsInfo = tradeApplication.queryManRefundTipsInfo(manRefundTipsParam);
        log.info("AngelServiceRecordEventConsumer orderRefund manRefundTipsParam={}, manRefundTipsInfo={}", com.alibaba.fastjson.JSON.toJSONString(manRefundTipsParam), com.alibaba.fastjson.JSON.toJSONString(manRefundTipsInfo));

        RefundOrderParam refundOrderParam = new RefundOrderParam();
        refundOrderParam.setOperator("护士");
        refundOrderParam.setOrderId(manRefundTipsInfo.getOrderId());
        refundOrderParam.setPromiseId(modifyRiskInterceptCmd.getPromiseId());
        refundOrderParam.setRefundAmount(manRefundTipsInfo.getSugRefundAmount());

        List<RefundOrderSku> refundOrderSkuList = new ArrayList<>();
        jdhPromise.getServices().forEach(service->{
            RefundOrderSku refundOrderSku = new RefundOrderSku();
            refundOrderSku.setServiceId(String.valueOf(service.getServiceId()));
            refundOrderSku.setPromisePatientId(modifyRiskInterceptCmd.getPromisePatientIds().get(0));
            refundOrderSkuList.add(refundOrderSku);
        });
        refundOrderParam.setRefundOrderSkuList(refundOrderSkuList);
        refundOrderParam.setRefundReason(refundDesc);
        refundOrderParam.setRefundSource("2");
        refundOrderParam.setRefundType(3);
        refundOrderParam.setVoucherId(jdhPromise.getVoucherId());
        // 申请退款
        Boolean orderRefundResult = tradeApplication.xfylOrderRefund(refundOrderParam);
        log.info("RiskInterceptApplicationImpl orderPatientRefund refundOrderParam={}, orderRefundResult={}",JSON.toJSONString(refundOrderParam), JSON.toJSONString(orderRefundResult));
    }

    /**
     * 检查拦截是否通过
     *
     * @param interceptRecordList
     * @return
     */
    private boolean checkRiskInterceptPass(List<JdhPromiseRiskInterceptRecord> interceptRecordList) {
        log.info("RiskInterceptApplicationImpl -> checkRiskInterceptPass, 风险拦截检查没有数据,不执行.interceptRecordList={}", JSON.toJSONString(interceptRecordList));
        if(CollectionUtils.isEmpty(interceptRecordList)) {
            return Boolean.FALSE;
        }
        List<JdhPromiseRiskInterceptRecord> waitingList = interceptRecordList.stream()
                .filter(item -> RiskWorkTagEnum.NEED_TAG.getTag().equals(item.getRiskWorkTag()) && RiskPassStatusEnum.INIT.getStatus().equals(item.getRiskPassStatus()))
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(waitingList)) {
            log.info("RiskInterceptApplicationImpl -> checkRiskInterceptPass, 有带执行的拦截记录，停止发送事件,interceptRecordList={}", JSON.toJSONString(interceptRecordList));
            return Boolean.FALSE;
        }

        JdhPromise jdhPromise = promiseRepository.find(new JdhPromiseIdentifier(interceptRecordList.get(0).getPromiseId()));
        List<JdhPromiseRiskInterceptRecord> passList = interceptRecordList.stream()
                .filter(item -> RiskWorkTagEnum.NEED_NO_TAG.getTag().equals(item.getRiskWorkTag())
                        || (RiskWorkTagEnum.NEED_TAG.getTag().equals(item.getRiskWorkTag()) && RiskPassStatusEnum.PASS.getStatus().equals(item.getRiskPassStatus())))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(passList)) {
            RiskInterceptEventBody body = new RiskInterceptEventBody();
            body.setPromiseId(interceptRecordList.get(0).getPromiseId());
            eventCoordinator.publish(EventFactory.newDefaultEvent(jdhPromise, PromiseEventTypeEnum.HIGH_RISK_ITEM_FAIL, body));
            log.error("RiskInterceptApplicationImpl -> checkRiskInterceptPass, 没有通过的拦截记录.interceptRecordList={}", JSON.toJSONString(interceptRecordList));
        }else {
            log.info("RiskInterceptApplicationImpl -> checkRiskInterceptPass, 有通过的拦截记录.interceptRecordList={}", JSON.toJSONString(interceptRecordList));
            RiskInterceptEventBody body = new RiskInterceptEventBody();
            body.setPromiseId(interceptRecordList.get(0).getPromiseId());
            eventCoordinator.publish(EventFactory.newDefaultEvent(jdhPromise, PromiseEventTypeEnum.HIGH_RISK_ITEM_PASS, body));

            //发送拦截的事件
            JdhPromiseRiskInterceptRecord record = passList.get(0);
            if(PromiseAggregateEnum.PROMISE.getDomainCode().getCode().equals(record.getInterceptDomainCode())
                    && PromiseAggregateEnum.PROMISE.getCode().equals(record.getInterceptAggregateCode())){
                PromiseEventTypeEnum eventTypeEnum = PromiseEventTypeEnum.getByCode(record.getInterceptEventCode());
                PromiseSubmitEventBody submitBody = null;
                if(Objects.nonNull(record.getRiskExtBo()) && StringUtils.isNotBlank(record.getRiskExtBo().getEventBody())){
                    submitBody = JSON.parseObject(record.getRiskExtBo().getEventBody(), PromiseSubmitEventBody.class);
                }
                log.info("RiskInterceptApplicationImpl -> checkRiskInterceptPass, submitBody={}, eventTypeEnum={}", JSON.toJSONString(submitBody), eventTypeEnum);
                eventCoordinator.publish(EventFactory.newDefaultEvent(jdhPromise, eventTypeEnum, submitBody));
            }else {
                log.error("RiskInterceptApplicationImpl -> checkRiskInterceptPass,未知的拦截事件,重点关注.record={}", JSON.toJSONString(record));
            }
        }

        //发送质控结束事件
        eventCoordinator.publish(EventFactory.newDefaultEvent(jdhPromise, PromiseEventTypeEnum.RISK_INTERCEPT_END,
                RiskInterceptEventBody.builder()
                        .promiseId(interceptRecordList.get(0).getPromiseId()).build()));
        return true;
    }
}
