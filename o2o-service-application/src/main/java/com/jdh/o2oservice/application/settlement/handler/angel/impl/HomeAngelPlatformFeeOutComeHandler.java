package com.jdh.o2oservice.application.settlement.handler.angel.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.settlement.context.SettlementEbsContext;
import com.jdh.o2oservice.application.settlement.handler.angel.AngelSettlementHandler;
import com.jdh.o2oservice.application.settlement.handler.angel.AngelSettlementHandlerManager;
import com.jdh.o2oservice.application.settlement.service.SettlementEbsApplication;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleMainBodyTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleSplitTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleItemTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlementAndEbsDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 护士上门（护士检测+护士服务）--订单的商品收入
 * <AUTHOR>
 * @date 2024
 */
@Slf4j
@Component
public class HomeAngelPlatformFeeOutComeHandler implements AngelSettlementHandler {

    /**
     * 业务模式（医护上门、骑手上门）必填
     */
    private BusinessModeEnum businessModeEnum;

    /**
     * 结算类型（收入、支出、冲收入） 必填
     */
    private EbsSettleTypeEnum ebsSettleTypeEnum = EbsSettleTypeEnum.EXPEND;

    /**
     * 费项拆分维度（商品、商品项目、上门（时段）费项） 必填
     */
    private EbsSettleSplitTypeEnum ebsSettleSplitTypeEnum = EbsSettleSplitTypeEnum.PLATFORM_SERVICE_FEE;

    /**
     * 结算对象, 非必填默认JD
     */
    private EbsSettleMainBodyTypeEnum ebsSettleMainBodyTypeEnum = EbsSettleMainBodyTypeEnum.ANGEL;
    /**
     * 结算推送ebs
     */
    @Autowired
    private SettlementEbsApplication settlementEbsApplication;

    /**
     * 结算前置计算构建对象
     *
     * @param angelSettlementAndEbsDetail
     * @return ebs
     */
    @Override
    public void prepareSettlementEbs(AngelSettlementAndEbsDetail angelSettlementAndEbsDetail) {
        log.info("HomeAngelPlatformFeeOutComeHandler#prepareSettlementEbs settlementEbsContext={}", JSON.toJSONString(angelSettlementAndEbsDetail));
        SettlementEbsContext settlementEbsContext = new SettlementEbsContext();
        settlementEbsContext.setBusinessModeEnum(angelSettlementAndEbsDetail.getBusinessModeEnum());
        settlementEbsContext.setEbsSettleTypeEnum(ebsSettleTypeEnum);
        settlementEbsContext.setEbsSettleSplitTypeEnum(ebsSettleSplitTypeEnum);
        settlementEbsContext.setEbsSettleMainBodyTypeEnum(ebsSettleMainBodyTypeEnum);
        settlementEbsContext.setSendEbsData(false);
        settlementEbsContext.setExtBusinessModel(angelSettlementAndEbsDetail);
        settlementEbsApplication.sendToEbs(settlementEbsContext);
    }

    /**
     * 结算前置计算构建对象
     *
     * @param angelSettlementAndEbsDetail
     */
    @Override
    public void afterEbsSend(AngelSettlementAndEbsDetail angelSettlementAndEbsDetail) {
        log.info("HomeAngelPlatformFeeOutComeHandler#afterEbsSend settlementEbsContext={}", JSON.toJSONString(angelSettlementAndEbsDetail));
    }

    /**
     * 结算前置计算构建对象
     *
     * @return ebs
     */
    @Override
    public List<String> settlementSceneCode() {
        String angelTestIncome = SettleItemTypeEnum.PLATFORM_SERVICE_FEE.getType().toString() + SettleTypeEnum.EXPEND.getType();
        return Stream.of(angelTestIncome).collect(Collectors.toList());
    }

    /**
     */
    @Override
    public void afterPropertiesSet() {
        if (CollUtil.isEmpty(this.settlementSceneCode())) {
            return;
        }
        for (String sceneCode : this.settlementSceneCode()) {
            AngelSettlementHandlerManager.register(sceneCode, this);
        }
    }
}
