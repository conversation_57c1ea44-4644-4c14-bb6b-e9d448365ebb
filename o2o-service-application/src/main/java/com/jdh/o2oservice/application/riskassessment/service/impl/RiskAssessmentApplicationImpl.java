package com.jdh.o2oservice.application.riskassessment.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angelpromise.service.AngelServiceRecordApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceIndicatorApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.riskassessment.context.RiskAssEventBody;
import com.jdh.o2oservice.application.riskassessment.service.RiskAssessmentApplication;
import com.jdh.o2oservice.application.riskassessment.service.convert.RiskAssessmentConvert;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.enums.RiskAssEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecord;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelServiceRecordIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessment;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessmentDetail;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelServiceRecordRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.RiskAssDetailRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.RiskAssessmentRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentDetailQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentPageQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentQuery;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.product.model.JdhQuestion;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhQuestionRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.QuestionDbQuery;
import com.jdh.o2oservice.core.domain.promise.bo.PatientExtBo;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.export.angelpromise.dto.AngelServiceRecordGroupQuestionDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelServiceRecordGroupQuery;
import com.jdh.o2oservice.export.product.dto.QuestionDTO;
import com.jdh.o2oservice.export.product.dto.QuestionGroupDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.PromiseDangerLevelRequest;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromiseExtendDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.riskassessment.cmd.*;
import com.jdh.o2oservice.export.riskassessment.dto.*;
import com.jdh.o2oservice.export.riskassessment.request.RiskAssessmentDetailRequest;
import com.jdh.o2oservice.export.riskassessment.request.RiskAssessmentPageRequest;
import com.jdh.o2oservice.export.support.dto.FileUrlDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/23
 */
@Service
@Slf4j
public class RiskAssessmentApplicationImpl implements RiskAssessmentApplication {

    /**
     * 自动注入的风险评估仓库，用于与数据库交互。
     */
    @Autowired
    private RiskAssessmentRepository riskAssessmentRepository;

    /**
     * 自动注入的风险评估明细仓库，用于与数据库交互。
     */
    @Autowired
    private RiskAssDetailRepository riskAssDetailRepository;

    /**
     * 自动注入的Redis锁工具，用于在多线程环境下对共享资源进行同步和互斥访问。
     */
    @Autowired
    private RedisLockUtil redisLockUtil;

    /**
     * 自动注入的PromiseApplication对象，用于调用Promise相关服务。
     */
    @Autowired
    private PromiseApplication promiseApplication;


    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    /**
     * 自动注入的产品服务指标应用程序，用于获取和更新产品服务指标信息。
     */
    @Autowired
    private ProductServiceIndicatorApplication productServiceIndicatorApplication;

    /**
     * 自动注入的产品服务指标应用程序，用于获取和更新产品服务指标信息。
     */
    @Autowired
    private ProductServiceItemApplication productServiceItemApplication;

    /**
     * 自动注入的生成ID工厂，用于生成唯一的ID。
     */
    @Autowired
    private GenerateIdFactory generateIdFactory;

    @Autowired
    private JdhQuestionRepository jdhQuestionRepository;

    @Autowired
    private JdOrderApplication jdOrderApplication;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    @Autowired
    private PromiseRepository promiseRepository;

    @Autowired
    private FileManageService fileManageApplication;

    @Autowired
    private JdhFileRepository jdhFileRepository;

   @Autowired
   private VerticalBusinessRepository businessRepository;

   @Autowired
   private DuccConfig duccConfig;

    @Resource
    private AngelServiceRecordRepository angelServiceRecordRepository;

    @Autowired
    private AngelServiceRecordApplication angelServiceRecordApplication;

    @Autowired
    private JdhSkuRepository jdhSkuRepository;

    /**
     * dongDongRobotRpc
     */
    @Lazy
    @Resource
    private DongDongRobotRpc dongDongRobotRpc;

    /**
     * @param riskAssessmentCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean createRiskAssessment(RiskAssessmentCmd riskAssessmentCmd) {
        //查询promise
        PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(riskAssessmentCmd.getPromiseId()).build());
        if (Objects.isNull(promiseDto)) {
            log.info("RiskAssessmentApplicationImpl->promiseDto,is null,promiseId={}", riskAssessmentCmd.getPromiseId());
            return Boolean.FALSE;
        }
        //查询项目
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(riskAssessmentCmd.getPromiseId()).build());
        if (CollectionUtils.isEmpty(medicalPromises)) {
            log.info("RiskAssessmentApplicationImpl,medicalPromises is null,promiseId={}", riskAssessmentCmd.getPromiseId());
            return Boolean.FALSE;
        }
        Set<Long> itemIds = medicalPromises.stream().map(MedicalPromise::getServiceItemId).map(Long::parseLong).collect(Collectors.toSet());


        List<Integer> risks = promiseApplication.queryPromiseDangerLevel(PromiseDangerLevelRequest.builder().promiseId(promiseDto.getPromiseId()).build());
        List<Integer> sortRisk = risks.stream().sorted(Comparator.comparing(Integer::intValue).reversed()).collect(Collectors.toList());

        if (!Objects.equals(CommonConstant.THREE,sortRisk.get(0))){
            log.info("RiskAssessmentApplicationImpl,highRisk is null,promiseId={}", riskAssessmentCmd.getPromiseId());
            return Boolean.FALSE;
        }

        //查找题目
        List<ServiceItemDto> serviceItemWithQuestions = productServiceItemApplication.queryServiceItemListWithQuestion(
                ServiceItemQuery.builder().itemIds(itemIds).groupCode(QuestionGroupTypeEnum.PREDANGERASSESSMENT.getCode()).build()
        );
        log.info("createRiskAssessment->serviceItemWithQuestions={}", JsonUtil.toJSONString(serviceItemWithQuestions));
        if (CollectionUtils.isEmpty(serviceItemWithQuestions)){
            log.info("RiskAssessmentApplicationImpl,serviceItemWithQuestions is null,promiseId={}", riskAssessmentCmd.getPromiseId());
            return Boolean.FALSE;
        }

        //过滤出风险评估的题目，并去重
        List<QuestionDTO> questionCmd = getQuestionDTOS(serviceItemWithQuestions);
        log.info("RiskAssessmentApplicationImpl,questionCmd={}", JsonUtil.toJSONString(questionCmd));
        if (CollectionUtils.isEmpty(questionCmd)){
            Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
            JSONObject jsonObject = robotAlarmMap.get("评估单没有配置题");
            dongDongRobotRpc.sendDongDongRobotMessage(String.format("高风险项目:%s,没有配置评估师评估的题目.履约单ID:%s", itemIds, riskAssessmentCmd.getPromiseId()), jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
            return Boolean.TRUE;
        }

        JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(promiseDto.getVerticalCode());
        //组装参数
        RiskAssessment riskAssessment = getRiskAssessment(medicalPromises, promiseDto, questionCmd, jdhVerticalBusiness);

        //保存
        riskAssessmentRepository.save(riskAssessment);


        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
        JSONObject jsonObject = robotAlarmMap.get("风险评估单创建通知");
        String link = "https://jdh-vercel-yf.jd.com/homeService/riskAssessDetail?riskAssessmentId="+String.valueOf(riskAssessment.getRiskAssessmentId());
        String message = String.format("您有一个风险评估任务，请在%s时间内完成，订单号：%s，点击%s链接去评估",CommonConstant.SIXTY_MINUTES_STR,riskAssessment.getOrderId(),link);
        dongDongRobotRpc.sendDongDongRobotMessage(message,jsonObject.getString("groupId"),jsonObject.getJSONArray("atUsers"));

        return Boolean.TRUE;
    }




    /**
     * 更新风险评估信息。
     *
     * @param riskAssessmentCmd 风险评估命令对象，包含需要更新的信息。
     * @return 更新是否成功。
     */
    @Override
    public Boolean updateRiskAssessment(RiskAssessmentCmd riskAssessmentCmd) {
        return null;
    }



    /**
     * 重新分配风险评估师和问题
     *
     * @param riskAssessmentCmd 风险评估命令对象，包含需要重新分配的用户信息。
     * @return 重新分配是否成功。
     */
    @Override
    public Boolean reDispatchAssessmentUser(RiskAssessmentCmd riskAssessmentCmd) {
        //删除原评估单和明细
        //创建评估单和明细
        return null;
    }

    /**
     * 更新风险评估单明细
     *
     * @param resultCmd 需要更新的风险评估细节命令对象列表。
     * @return 更新操作是否成功。
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean saveRiskAssResult(RiskAssessmentResultCmd resultCmd) {

        //加锁

        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.SAVE_RISK_LOCK, resultCmd.getRiskAssessmentId());
        String uuid = UUID.randomUUID().toString();
        if (!redisLockUtil.tryLock(lockKey, uuid, RedisKeyEnum.SAVE_RISK_LOCK.getExpireTime(), RedisKeyEnum.SAVE_RISK_LOCK.getExpireTimeUnit())) {
            log.error("[RiskAssessmentApplicationImpl.saveRiskAssResult],更新风险评估单结果 加锁失败!");
            throw new BusinessException(MedPromiseErrorCode.DOING_WAIT_MOMENT);
        }

        try {
            //保存答案和状态
            //如果此评估单问题全部完成，则更新评估单状态
            RiskAssessment riskAssessment = riskAssessmentRepository.queryRiskAssessment(RiskAssessmentQuery.builder().riskAssessmentId(resultCmd.getRiskAssessmentId()).build());

            //如果为空，或者风险评估单已经进入最终态，则不可再评估
            if (Objects.isNull(riskAssessment) ||
                    RiskAssessmentStatusEnum.getFinallyStatus().contains(riskAssessment.getRiskAssessmentStatus())){
                //异常
                return Boolean.FALSE;
            }

            //查询明细ID
            List<RiskAssDetailResultCmd> riskAssDetailResultCmds = resultCmd.getRiskAssDetailResultCmds();
            Set<Long> riskQuestionIds = riskAssDetailResultCmds.stream().map(RiskAssDetailResultCmd::getRiskQuestionId).collect(Collectors.toSet());
            List<RiskAssessmentDetail> riskAssessmentDetails = riskAssDetailRepository.queryRiskAssessmentDetailList(
                    RiskAssessmentDetailQuery.builder()
                            .riskAssessmentId(resultCmd.getRiskAssessmentId())
                            .build()
            );


            //判断明细数量是否一致
            if (CollectionUtils.isEmpty(riskAssessmentDetails)){
                //风险评估单无问题，异常
                return Boolean.FALSE;
            }

            List<RiskAssessmentDetail> paramDetails = riskAssessmentDetails.stream().filter(p -> riskQuestionIds.contains(p.getRiskQuestionId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(paramDetails) || !Objects.equals(paramDetails.size(), riskAssDetailResultCmds.size())){
                //问题已修改，异常
                return Boolean.FALSE;
            }

            Map<Long, Long> riskQuesIdToQuesId = paramDetails.stream().collect(Collectors.toMap(RiskAssessmentDetail::getRiskQuestionId, RiskAssessmentDetail::getQuestionId));
            log.info("quesCodeMedicalCertificate->riskQuesIdToQuesId{}",JsonUtil.toJSONString(riskQuesIdToQuesId));

            List<RiskAssessmentDetail> riskAssessmentDetailResult = RiskAssessmentConvert.INSTANCE.convert(riskAssDetailResultCmds);

            for (RiskAssessmentDetail riskAssessmentDetail : riskAssessmentDetailResult) {
                riskAssessmentDetail.setAssessmentUser(resultCmd.getOperator());
                riskAssessmentDetail.setAssessmentTime(new Date());
                if (StringUtil.isNotBlank(riskAssessmentDetail.getRiskQuestionRes())){
                    riskAssessmentDetail.setRiskQuestionStatus(RiskQuestionStatusEnum.ANSWER.getStatus());
                }
                riskAssessmentDetail.setRiskAssessmentStatus(resultCmd.getRiskAssessmentStatus());
            }

            Set<Long> quesCodeMedicalCertificate = duccConfig.getQuesCodeMedicalCertificate();
            log.info("quesCodeMedicalCertificate->{}",JsonUtil.toJSONString(quesCodeMedicalCertificate));

            //保存明细
            for (RiskAssessmentDetail riskAssessmentDetail : riskAssessmentDetailResult) {
                riskAssDetailRepository.save(riskAssessmentDetail);
                if (quesCodeMedicalCertificate.contains(riskQuesIdToQuesId.get(riskAssessmentDetail.getRiskQuestionId()))){
                    PatientExtBo extBo = new PatientExtBo();
                    if (StringUtil.isNotBlank(riskAssessmentDetail.getRiskQuestionRes())){
                        List<Long> lists = JsonUtil.parseArray(riskAssessmentDetail.getRiskQuestionRes(), Long.class);
                        List<String> strList = lists.stream().map(String::valueOf).collect(Collectors.toList());
                        extBo.setPatientDoctorAdviceFileList(strList);
                        JdhPromisePatient jdhPromisePatient = new JdhPromisePatient();
                        jdhPromisePatient.setPromisePatientId(resultCmd.getPromisePatientId());
                        jdhPromisePatient.setPatientExtBo(extBo);
                        jdhPromisePatient.setPromiseId(riskAssessment.getPromiseId());
                        promiseRepository.updatePromisePatient(jdhPromisePatient);
                    }

                }
            }

            //退款
            if (Objects.equals(RiskAssessmentStatusEnum.REFUSE.getStatus(),resultCmd.getRiskAssessmentStatus())){
                JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(riskAssessment.getPromiseId()).build());
                eventCoordinator.publish(EventFactory.newDefaultEvent(promise, RiskAssEventTypeEnum.RISK_ASS_PATIENT_REFUSE,
                        RiskAssEventBody.builder().riskAssessmentId(riskAssessment.getRiskAssessmentId()).promiseId(riskAssessment.getPromiseId()).promisePatientId(resultCmd.getPromisePatientId()).build()));
            }else {
                JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(riskAssessment.getPromiseId()).build());
                eventCoordinator.publish(EventFactory.newDefaultEvent(promise, RiskAssEventTypeEnum.RISK_ASS_PATIENT_PASS,
                        RiskAssEventBody.builder().riskAssessmentId(riskAssessment.getRiskAssessmentId()).promiseId(riskAssessment.getPromiseId()).promisePatientId(resultCmd.getPromisePatientId()).build()));
            }

            List<RiskAssessmentDetail> riskAssessmentDetailsAfter = riskAssDetailRepository.queryRiskAssessmentDetailList(
                    RiskAssessmentDetailQuery.builder()
                            .riskAssessmentId(resultCmd.getRiskAssessmentId())
                            .build()
            );
            log.info("saveRiskAssResult->riskAssessmentDetailsAfter={}", JsonUtil.toJSONString(riskAssessmentDetailsAfter));

            //判断是否全部完成
            //人维度只要有一个回答过，则就认为这个人就评估过
            Map<Long, List<RiskAssessmentDetail>> ppidToDetailAfter = riskAssessmentDetailsAfter.stream().collect(Collectors.groupingBy(RiskAssessmentDetail::getPromisePatientId));

            AtomicReference<Boolean> finish = new AtomicReference<>(Boolean.TRUE);
            int status = RiskAssessmentStatusEnum.PASS.getStatus();
            Set<Integer> finallyAssStatus = RiskAssessmentStatusEnum.getFinallyAssStatus();


            ppidToDetailAfter.forEach((ppid,detailAfter)->{
                RiskAssessmentDetail riskAssessmentDetail = detailAfter.stream().filter(p -> finallyAssStatus.contains(p.getRiskAssessmentStatus())).findFirst().orElse(null);
                if (Objects.isNull(riskAssessmentDetail)){
                    finish.set(Boolean.FALSE);
                }

            });

            //如果全部完成，判断是否有驳回的单子
            if (finish.get()){
                RiskAssessmentDetail riskAssessmentDetail = riskAssessmentDetailsAfter.stream().filter(p -> Objects.equals(RiskAssessmentStatusEnum.REFUSE.getStatus(), p.getRiskAssessmentStatus())).findFirst().orElse(null);
                if (Objects.nonNull(riskAssessmentDetail)){
                    status = RiskAssessmentStatusEnum.REFUSE.getStatus();
                }

                RiskAssessment updateRiskAssessment = new RiskAssessment();
                updateRiskAssessment.setId(riskAssessment.getId());
                updateRiskAssessment.setRiskAssessmentStatus(status);
                updateRiskAssessment.setAssessmentUser(resultCmd.getOperator());
                updateRiskAssessment.setAssessmentTime(new Date());

                Date assessmentDeadlineTime = riskAssessment.getAssessmentDeadlineTime();
                if (Objects.nonNull(assessmentDeadlineTime)){
                    //如果评估完成时间大于最晚评估时间，则超时
                    if (DateUtil.compare(new Date(), assessmentDeadlineTime) > 0){
                        updateRiskAssessment.setAssessmentTimeoutStatus(CommonConstant.ONE);
                    }
                }

                riskAssessmentRepository.save(updateRiskAssessment);

                //如果是通过的，发送事件评估通过，开始派单
                if (Objects.equals(RiskAssessmentStatusEnum.PASS.getStatus(),updateRiskAssessment.getRiskAssessmentStatus())){
                    JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(riskAssessment.getPromiseId()).build());
                    eventCoordinator.publish(EventFactory.newDefaultEvent(promise, RiskAssEventTypeEnum.RISK_ASS_PASS,
                            RiskAssEventBody.builder().riskAssessmentId(riskAssessment.getRiskAssessmentId()).promiseId(riskAssessment.getPromiseId()).build()));
                }
            }



        }finally {
            redisLockUtil.unLock(lockKey, uuid);
        }

        return Boolean.TRUE;
    }

    /**
     * 分页查询风险评估信息。
     *
     * @param request 分页查询请求参数，包括页码、每页记录数等。
     * @return 分页查询结果，包含总记录数、当前页码、每页记录数和数据列表。
     */
    @Override
    public PageDto<RiskAssessmentPageDTO> queryRiskAssessmentPage(RiskAssessmentPageRequest request) {
        RiskAssessmentPageQuery query = RiskAssessmentConvert.INSTANCE.convert(request);

        Set<Long> patientProvinceSet = Sets.newHashSet();
        Set<Long> patientCitySet = Sets.newHashSet();
        Set<Long> patientCountySet = Sets.newHashSet();
        Set<Long> patientTownSet = Sets.newHashSet();


        query.setPatientProvinceSet(patientProvinceSet);
        query.setPatientCitySet(patientCitySet);
        query.setPatientCountySet(patientCountySet);
        query.setPatientTownSet(patientTownSet);

        if (CollectionUtils.isNotEmpty(request.getAreas())){
            for (List<Long> area : request.getAreas()){
                if (area.size() == 1 ){
                    patientProvinceSet.add(area.get(0));
                }else if (area.size() == 2 ){
                    patientCitySet.add(area.get(1));
                }else  if (area.size() == 3 ){
                    patientCountySet.add(area.get(2));
                }else  if (area.size() == 4 ){
                    patientTownSet.add(area.get(3));
                }
            }
        }


        Page<RiskAssessment> riskAssessmentPage = riskAssessmentRepository.queryRiskAssessmentPage(query);
        return RiskAssessmentConvert.INSTANCE.convert(riskAssessmentPage);

    }


    /**
     * 保存风险评估回访信息。
     * @param riskAssReturnVisitCmd 回访命令对象，包含回访的详细信息。
     * @return 保存结果，true表示保存成功，false表示保存失败。
     */
    @Override
    public Boolean saveReturnVisit(RiskAssReturnVisitCmd riskAssReturnVisitCmd) {
        RiskAssessment riskAssessment = RiskAssessmentConvert.INSTANCE.convert(riskAssReturnVisitCmd);
        riskAssessment.setReturnVisitStatus(ReturnVisitStatusEnum.RETURN_VISIT.getStatus());
        return riskAssessmentRepository.saveReturnVisit(riskAssessment);
    }

    /**
     * 查询风险评估详情
     *
     * @param request 风险评估详情请求对象
     * @return 风险评估详情DTO对象
     */
    @Override
    @LogAndAlarm(jKey = "RiskAssessmentApplicationImpl.RiskAssessmentDetailRequest")
    public RiskAssessmentDetailManDTO queryRiskAssDetailForMan(RiskAssessmentDetailRequest request) {
        RiskAssessment riskAssessment = riskAssessmentRepository.queryRiskAssessment(
                RiskAssessmentQuery.builder()
                        .riskAssessmentId(request.getRiskAssessmentId())
                        .detailQuery(request.getDetailQuery())
                        .promiseId(request.getPromiseId())
                        .promisePatientId(request.getPromisePatientId())
                        .build()
        );
        RiskAssessmentDetailManDTO dto = RiskAssessmentConvert.INSTANCE.convert(riskAssessment);
        log.info("RiskAssessmentApplicationImpl queryRiskAssDetailForMan dto={}", JSON.toJSONString(dto));
        if (Objects.isNull(dto)){
            return null;
        }
        PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(riskAssessment.getPromiseId()).build());
        log.info("RiskAssessmentApplicationImpl queryRiskAssDetailForMan promiseDto={}", JSON.toJSONString(promiseDto));

        RiskAssOrderDetailDTO riskAssOrderDetailDTO = RiskAssessmentConvert.INSTANCE.getRiskAssOrderDetailDTO(riskAssessment, promiseDto);
        //组装医疗证明
        packMedicalCertificates(promiseDto, riskAssOrderDetailDTO);
        dto.setRiskAssOrderDetailDTO(riskAssOrderDetailDTO);

        if (Objects.isNull(riskAssessment) || !Boolean.TRUE.equals(request.getDetailQuery()) || CollectionUtils.isEmpty(riskAssessment.getRiskAssessmentDetailList())){
            return dto;
        }


        List<RiskAssessmentDetail> riskAssessmentDetailList = riskAssessment.getRiskAssessmentDetailList();

        List<Long> questionId = riskAssessmentDetailList.stream().map(RiskAssessmentDetail::getQuestionId).collect(Collectors.toList());
        List<JdhQuestion> questions = jdhQuestionRepository.findList(QuestionDbQuery.builder().quesIds(questionId).build());
        if (Objects.isNull(questions)){
            return dto;
        }
        Map<Long, JdhQuestion> quesIdToObj = questions.stream().collect(Collectors.toMap(JdhQuestion::getQuesId, p -> p));

        List<RiskAssUserDetailDTO> riskAssUserDetailDTOS = Lists.newArrayList();

        Map<Long, List<RiskAssessmentDetail>> ppidToDetail = riskAssessmentDetailList.stream().collect(Collectors.groupingBy(RiskAssessmentDetail::getPromisePatientId));

        log.info("queryRiskAssDetailForMan->ppidToDetail={}", JsonUtil.toJSONString(ppidToDetail));

        Map<Long, PromisePatientDto> ppidToPatientInfo = promiseDto.getPatients().stream().collect(Collectors.toMap(PromisePatientDto::getPromisePatientId, p -> p));


        Map<Long, List<MedicalPromise>> medicalPromiseMap = new HashMap<>();
        MedicalPromiseListQuery medicalPromiseListQuery = MedicalPromiseListQuery.builder().promiseId(riskAssessment.getPromiseId()).build();
        List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        if (CollectionUtils.isNotEmpty(medicalPromiseList)){
            medicalPromiseMap = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromise::getPromisePatientId));
        }
        Map<Long, List<MedicalPromise>> finalMedicalPromiseMap = medicalPromiseMap;
        Set<String> excludeQuestionCode = duccConfig.getExcludeQuestionCode();

        ppidToDetail.forEach((ppid, details)->{
            PromisePatientDto promisePatientDto = ppidToPatientInfo.get(ppid);

            RiskAssUserDetailDTO riskAssUserDetailDTO = RiskAssessmentConvert.INSTANCE.getRiskAssUserDetailDTO(ppid, details, promisePatientDto, promiseDto, finalMedicalPromiseMap, medicalPromiseList);
            List<RiskAssQuestionDTO> riskAssQuestionDTOS = Lists.newArrayList();
            riskAssUserDetailDTO.setRiskAssQuestionDTOS(riskAssQuestionDTOS);
            riskAssUserDetailDTOS.add(riskAssUserDetailDTO);
            for (RiskAssessmentDetail riskAssessmentDetail : details) {

                JdhQuestion jdhQuestion = null;

                if(StringUtil.isNotBlank(riskAssessmentDetail.getQuestionExt())){
                    jdhQuestion = JsonUtil.parseObject(riskAssessmentDetail.getQuestionExt(), JdhQuestion.class);
                }else {
                    jdhQuestion = quesIdToObj.get(riskAssessmentDetail.getQuestionId());
                }
                if (excludeQuestionCode.contains(String.valueOf(jdhQuestion.getTag()))){
                    continue;
                }

                RiskAssQuestionDTO riskAssQuestionDTO = new RiskAssQuestionDTO();


                riskAssQuestionDTO.setHighRisk(jdhQuestion.getHighRisk());
                riskAssQuestionDTO.setRiskQuestionId(riskAssessmentDetail.getRiskQuestionId());
                riskAssQuestionDTO.setQuestionRes(riskAssessmentDetail.getRiskQuestionRes());
                riskAssQuestionDTO.setId(riskAssessmentDetail.getId());
                riskAssQuestionDTO.setName(jdhQuestion.getName());
                riskAssQuestionDTO.setExtJson(jdhQuestion.getExtJson());
                riskAssQuestionDTO.setQuesCode(jdhQuestion.getQuesCode());
                riskAssQuestionDTO.setQuesDesc(jdhQuestion.getQuesDesc());
                riskAssQuestionDTO.setType(jdhQuestion.getType());
                riskAssQuestionDTO.setSort(jdhQuestion.getSort());
                riskAssQuestionDTO.setUnit(jdhQuestion.getUnit());
                riskAssQuestionDTO.setTag(jdhQuestion.getTag());

                riskAssQuestionDTOS.add(riskAssQuestionDTO);

                //定制逻辑，当下发的问题code是173649002233873时，指定前端做图片归属
                if(CommonConstant.DOCTOR_ADVICE_BACK.equals(jdhQuestion.getQuesCode())) {
                    riskAssQuestionDTO.setTag(99);
                }

                if(QuestionTypeEnum.UPLOAD.getType().equals(jdhQuestion.getType()) && StringUtils.isNotBlank(riskAssessmentDetail.getRiskQuestionRes())) {
                    List<Long> picList = JSON.parseArray(riskAssessmentDetail.getRiskQuestionRes(), Long.class);
                    List<JdhFileIdentifier> jdhFileIdentifierList = picList.stream().map(item -> new JdhFileIdentifier(item)).collect(Collectors.toList());
                    List<JdhFile> list = jdhFileRepository.findList(jdhFileIdentifierList, null);
                    if(CollectionUtils.isEmpty(list)) {
                        continue;
                    }
                    riskAssQuestionDTO.setFileUrlList(fileManageApplication.generateGetUrl(list, Boolean.TRUE, null));
                }
            }
            log.info("queryRiskAssDetailForMan->riskAssQuestionDTOS={}", JsonUtil.toJSONString(riskAssQuestionDTOS));
        });

        dto.setRiskAssUserDetailDTOS(riskAssUserDetailDTOS);

        return dto;
    }




    /**
     * 查询风险评估明细列表
     *
     * @param request 风险评估明细请求对象
     * @return 风险评估明细列表
     */
    @Override
    public List<RiskAssDetailBaseDTO> queryRiskAssDetailList(RiskAssessmentDetailRequest request) {

        List<RiskAssessmentDetail> riskAssessmentDetails = riskAssDetailRepository.queryRiskAssessmentDetailList(
                RiskAssessmentDetailQuery.builder()
                        .promiseId(request.getPromiseId())
                        .promisePatientId(request.getPromisePatientId())
                        .assessmentUser(request.getAssessmentUser())
                        .riskAssessmentId(request.getRiskAssessmentId())
                        .promiseIdList(request.getPromiseIdList())
                        .build()
        );
        return RiskAssessmentConvert.INSTANCE.convertList(riskAssessmentDetails);
    }

    /**
     * 查询风险评估详情
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "RiskAssessmentApplicationImpl.queryRiskAssUserDetail")
    public RiskAssUserDetailDTO queryRiskAssUserDetail(RiskAssessmentDetailRequest request) {
        RiskAssessmentDetailManDTO riskAssessmentDetailManDTO = this.queryRiskAssDetailForMan(request);
        if (Objects.isNull(riskAssessmentDetailManDTO)){
            return null;
        }
        RiskAssUserDetailDTO result = riskAssessmentDetailManDTO.getRiskAssUserDetailDTOS().get(0);
        List<RiskAssQuestionDTO> riskAssQuestionList = result.getRiskAssQuestionDTOS();
        if (CollectionUtils.isEmpty(riskAssQuestionList)){
            return result;
        }

        Iterator<RiskAssQuestionDTO> iterator = riskAssQuestionList.iterator();
        while (iterator.hasNext()) {
            RiskAssQuestionDTO riskAssQuestionDTO = iterator.next();
            // c端定详，移除未作答的题目
            if (request.getSourceChannel() != null && NumConstant.NUM_1.equals(request.getSourceChannel()) && StringUtils.isBlank(riskAssQuestionDTO.getQuestionRes())){
                iterator.remove();
                continue;
            }
        }
        return result;
    }

    /**
     * 创建中风险评估。
     *
     * @param recordId 风险评估命令对象，包含评估所需的信息。
     * @return true 如果创建成功，false 否则。
     */
    @Override
    @LogAndAlarm
    public Boolean createMidRiskAssByRecord(Long  recordId) {

        log.info("createMidRiskAssByRecord->recordId={}",recordId);
        AngelServiceRecord angelServiceRecord = angelServiceRecordRepository.find(AngelServiceRecordIdentifier.builder().recordId(recordId).build());
        log.info("createMidRiskAssByRecord->angelServiceRecord={}",JsonUtil.toJSONString(angelServiceRecord));

        if (Objects.isNull(angelServiceRecord)) {
            log.info("createMidRiskAssByRecord->angelServiceRecord is null,recordId={}",recordId);
            return Boolean.FALSE;
        }

        //查询promise
        PromiseDto promiseDto = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(angelServiceRecord.getPromiseId()).build());
        if (Objects.isNull(promiseDto)) {
            log.info("RiskAssessmentApplicationImpl->createMidRiskAssByRecord,promiseDto,is null,promiseId={}", angelServiceRecord.getPromiseId());
            return Boolean.FALSE;
        }

        List<Integer> risks = promiseApplication.queryPromiseDangerLevel(PromiseDangerLevelRequest.builder().promiseId(promiseDto.getPromiseId()).build());
        List<Integer> sortRisk = risks.stream().sorted(Comparator.comparing(Integer::intValue).reversed()).collect(Collectors.toList());

        if (!Objects.equals(CommonConstant.TWO,sortRisk.get(0))){
            log.info("RiskAssessmentApplicationImpl,createMidRiskAssByRecord is not mid risk,promiseId={},riskLevel={}",angelServiceRecord.getPromiseId(),sortRisk.get(0));
            return Boolean.FALSE;
        }


        AngelServiceRecordGroupQuery query= new AngelServiceRecordGroupQuery();
        query.setGroupCode(QuestionGroupTypeEnum.PRERECEIVEASSESSMENT.getCode());
        query.setServiceRecordId(recordId);
        AngelServiceRecordGroupQuestionDto queryRecordGroupQuestion = angelServiceRecordApplication.queryRecordGroupQuestion(query);
        log.info("createMidRiskAssByRecord->queryRecordGroupQuestion={}",JsonUtil.toJSONString(queryRecordGroupQuestion));

        if (Objects.isNull(queryRecordGroupQuestion)){
            return Boolean.FALSE;
        }


        //先查旧评估单
        RiskAssessment riskAssessmentOld = riskAssessmentRepository.queryRiskAssessment(RiskAssessmentQuery.builder().promiseId(angelServiceRecord.getPromiseId()).riskLevel(CommonConstant.TWO).build());
        log.info("createMidRiskAssByRecord->riskAssessmentOld={}",JsonUtil.toJSONString(riskAssessmentOld));

        if (Objects.isNull(riskAssessmentOld)){
            //组装
            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(angelServiceRecord.getPromiseId()).build());
            String serviceItemNames = medicalPromises.stream().map(MedicalPromise::getServiceItemName).distinct().collect(Collectors.joining(","));
            JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(promiseDto.getVerticalCode());
            String orderId = promiseDto.getSourceVoucherId();
            JdOrder jdOrder = jdOrderApplication.queryJdOrderByOrderId(Long.valueOf(orderId));
            RiskAssessment riskAssessment = RiskAssessmentConvert.INSTANCE.getRiskAssessment(promiseDto, jdhVerticalBusiness, medicalPromises, serviceItemNames, jdOrder, queryRecordGroupQuestion,generateIdFactory);
            JdhSku sku = jdhSkuRepository.find(new JdhSkuIdentifier(Long.valueOf(riskAssessment.getServiceId())));
            riskAssessment.setSkuServiceType(sku.getServiceType());
            riskAssessmentRepository.save(riskAssessment);
        }else {
            //组装
            List<RiskAssessmentDetail> riskAssessmentDetailList = RiskAssessmentConvert.INSTANCE.getRiskAssessmentDetails(queryRecordGroupQuestion, riskAssessmentOld, promiseDto,generateIdFactory);
            //查询这个ppid下是否有问题，如果有，则更新，如果没有，则新增
            List<RiskAssessmentDetail> riskAssessmentDetails = riskAssDetailRepository.queryRiskAssessmentDetailList(RiskAssessmentDetailQuery.builder().promiseId(promiseDto.getPromiseId()).promisePatientId(Long.valueOf(queryRecordGroupQuestion.getPromisePatientId())).build());
            if (CollectionUtils.isNotEmpty(riskAssessmentDetails)){
                Set<Long> ids = riskAssessmentDetails.stream().map(RiskAssessmentDetail::getId).collect(Collectors.toSet());
                riskAssDetailRepository.deleteByIds(ids);
            }
            riskAssDetailRepository.saveBatch(riskAssessmentDetailList);

        }

        return Boolean.TRUE;
    }

    /**
     * 根据查询条件获取风险评估列表
     * @param query 查询条件对象
     * @return 符合条件的风险评估列表
     */
    @Override
    @LogAndAlarm(jKey = "RiskAssessmentApplicationImpl.queryRiskAssessmentList")
    public List<RiskAssessment> queryRiskAssessmentList(RiskAssessmentQuery query) {
        return riskAssessmentRepository.queryRiskAssessmentList(query);
    }

    /**
     * 查询评估单并同步返回评估单明细
     *
     * @param invalidPatientRiskCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public RiskAssDto queryRiskAssWithDetail(InvalidPatientRiskCmd invalidPatientRiskCmd) {
        if(Objects.isNull(invalidPatientRiskCmd)
                || Objects.isNull(invalidPatientRiskCmd.getPromiseId())) {
            log.error("RiskAssessmentApplicationImpl -> queryRiskAssWithDetail, 参数不正确.invalidPatientRiskCmd={}", JSON.toJSONString(invalidPatientRiskCmd));
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        RiskAssessmentQuery query = new RiskAssessmentQuery();
        query.setPromiseId(invalidPatientRiskCmd.getPromiseId());
        List<RiskAssessment> riskAssessments = riskAssessmentRepository.queryRiskAssessmentList(query);
        if(CollectionUtils.isEmpty(riskAssessments)) {
            return null;
        }
        RiskAssessment riskAssessment = riskAssessments.get(0);
        RiskAssDto riskAssDto = RiskAssessmentConvert.INSTANCE.convertToRiskAssDto(riskAssessment);

        RiskAssessmentDetailQuery detailQuery = new RiskAssessmentDetailQuery();
        detailQuery.setPromiseId(invalidPatientRiskCmd.getPromiseId());
        List<RiskAssessmentDetail> riskAssessmentDetails = riskAssDetailRepository.queryRiskAssessmentDetailList(detailQuery);
        if(CollectionUtils.isEmpty(riskAssessmentDetails)){
            return riskAssDto;
        }

        List<RiskAssessmentDetailDto> riskAssessmentDetailDtos = RiskAssessmentConvert.INSTANCE.convertToRiskAssessmentDetailDtoList(riskAssessmentDetails);
        riskAssDto.setRiskAssessmentDetailDtos(riskAssessmentDetailDtos);
        return riskAssDto;
    }

    /**
     * 修改风险预估单状态
     *
     * @param updateStatusCmd
     * @return
     */
    @Override
    @LogAndAlarm
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRiskAssessmentStatus(RiskAssessmentUpdateStatusCmd updateStatusCmd) {
        //参数检查
        if(Objects.isNull(updateStatusCmd)
                || Objects.isNull(updateStatusCmd.getRiskAssessmentId())
                || Objects.isNull(updateStatusCmd.getRiskAssessmentStatus())) {
            log.error("RiskAssessmentApplicationImpl -> updateRiskAssessmentStatus, 参数不正确.updateStatusCmd={}", JSON.toJSONString(updateStatusCmd));
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        riskAssessmentRepository.updateRiskAssessmentStatus(updateStatusCmd.getRiskAssessmentId(), updateStatusCmd.getRiskAssessmentStatus());
        if(CollectionUtils.isNotEmpty(updateStatusCmd.getRiskAssessmentDetailCmds())) {
            for (RiskAssessmentDetailCmd riskAssessmentDetailCmd : updateStatusCmd.getRiskAssessmentDetailCmds()) {
                riskAssDetailRepository.updateRiskAssessmentDetailStatus(riskAssessmentDetailCmd.getRiskAssessmentId(), riskAssessmentDetailCmd.getPromisePatientId(), riskAssessmentDetailCmd.getRiskAssPatientStatus());
            }
        }
        return true;
    }


    /**
     * 获取风险评估对象。
     * @param medicalPromises 医疗承诺列表。
     * @param promiseDto 承诺dto对象。
     * @param questionCmd 问题命令列表。
     * @param jdhVerticalBusiness 垂直业务对象。
     * @return 风险评估对象。
     */
    private RiskAssessment getRiskAssessment(List<MedicalPromise> medicalPromises, PromiseDto promiseDto, List<QuestionDTO> questionCmd, JdhVerticalBusiness jdhVerticalBusiness) {
        RiskAssessment riskAssessment =  RiskAssessmentConvert.INSTANCE.getRiskAssessment(generateIdFactory, medicalPromises, promiseDto, questionCmd);
        riskAssessment.setRiskLevel(CommonConstant.THREE);
        String orderId = promiseDto.getSourceVoucherId();
        JdOrder jdOrder = jdOrderApplication.queryJdOrderByOrderId(Long.valueOf(orderId));
        if (Objects.nonNull(jdOrder)){
            riskAssessment.setOrderTime(jdOrder.getCreateTime());
            riskAssessment.setOrderId(jdOrder.getOrderId());
            riskAssessment.setPartnerSource(jdOrder.getPartnerSource());
        }
        JdhSku sku = jdhSkuRepository.find(new JdhSkuIdentifier(Long.valueOf(riskAssessment.getServiceId())));
        riskAssessment.setSkuServiceType(sku.getServiceType());
        riskAssessment.setBusinessMode(jdhVerticalBusiness.getBusinessModeCode());
        riskAssessment.setAssessmentDeadlineTime(DateUtil.offsetHour(promiseDto.getAppointmentTime().getAppointmentStartTime(),CommonConstant.NEGATIVE_ONE));
        log.info("RiskAssessmentApplicationImpl,riskAssessment={}", JsonUtil.toJSONString(riskAssessment));
        return riskAssessment;
    }

    /**
     * 根据服务项和排除的题目代码获取对应的题目DTO列表。
     * @param serviceItemWithQuestions 包含题目的服务项DTO列表。
     * @return 过滤后的题目DTO列表。
     */
    private  List<QuestionDTO> getQuestionDTOS(List<ServiceItemDto> serviceItemWithQuestions) {
        List<QuestionDTO> questionCmd = Lists.newArrayList();
        Set<Long> existQuestionId = new HashSet<>();
        for (ServiceItemDto serviceItemDto : serviceItemWithQuestions) {
            if (CollectionUtils.isEmpty(serviceItemDto.getQuestionGroupDtoList())){
                continue;
            }
            QuestionGroupDto questionGroupDto = serviceItemDto.getQuestionGroupDtoList().get(0);
            List<QuestionDTO> questionDTOS = questionGroupDto.getQuestionDTOS();
            if (CollectionUtils.isEmpty(questionDTOS)){
                continue;
            }

            for (QuestionDTO questionDTO : questionDTOS) {
                if ( existQuestionId.contains(questionDTO.getQuesId())){
                    continue;
                }
                existQuestionId.add(questionDTO.getQuesId());
                questionCmd.add(questionDTO);
            }
        }
        return questionCmd;
    }
    /**
     * 将医疗证明文件的 URL 添加到风险评估订单详细信息中。
     * @param promiseDto PromiseDto 对象，包含扩展属性列表。
     * @param riskAssOrderDetailDTO RiskAssOrderDetailDTO 对象，用于存储医疗证明文件的 URL。
     */
    private void packMedicalCertificates(PromiseDto promiseDto, RiskAssOrderDetailDTO riskAssOrderDetailDTO) {
        List<PromiseExtendDto> promiseExtends = promiseDto.getPromiseExtends();
        if (CollectionUtils.isNotEmpty(promiseExtends)){
            PromiseExtendDto promiseExtendDto = promiseExtends.stream().filter(p -> StringUtil.equals(PromiseExtendKeyEnum.MEDICAL_CERTIFICATE_FILE_IDS.getFiledKey(), p.getAttribute())).findFirst().orElse(null);
            if (Objects.nonNull(promiseExtendDto)){
                String value = promiseExtendDto.getValue();
                List<Long> fileIds = JsonUtil.parseArray(value, Long.class);
                List<JdhFileIdentifier> identifiers = Lists.newArrayList();
                for (Long fileId : fileIds){
                    JdhFileIdentifier jdhFileIdentifier = JdhFileIdentifier.builder().fileId(fileId).build();
                    identifiers.add(jdhFileIdentifier);
                }
                List<JdhFile> list = jdhFileRepository.findList(identifiers, null);
                if (CollectionUtils.isNotEmpty(list)){
                    List<FileUrlDto> medicalCertificates = Lists.newArrayList();
                    riskAssOrderDetailDTO.setMedicalCertificates(medicalCertificates);
                    Date expire = DateUtil.offsetMinute(new Date(),CommonConstant.NUMBER_THIRTY);
                    for (JdhFile file : list){
                        FileUrlDto fileUrlDto = new FileUrlDto();
                        fileUrlDto.setFileId(file.getFileId());
                        fileUrlDto.setUrl(fileManageApplication.getPublicUrl(file.getFilePath(),Boolean.TRUE,expire));
                        medicalCertificates.add(fileUrlDto);
                    }
                }

            }
        }
    }

}
