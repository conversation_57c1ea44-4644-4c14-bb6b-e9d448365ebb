package com.jdh.o2oservice.application.product.service;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.export.product.cmd.JdhSkuImportCmd;
import com.jdh.o2oservice.export.product.cmd.*;
import com.jdh.o2oservice.export.product.dto.*;
import com.jdh.o2oservice.export.product.query.*;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName ProductApplication
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 11:39
 **/
public interface ProductApplication {

    /**
     * 查询套餐列表
     * @param request
     * @return
     */
    List<ProviderServiceGoodsDto> queryServiceGoodsList(ProductServiceGoodsListRequest request);
    
    /**
     * 分页查询健康商品主数据
     *
     * @param request request
     * @return bo
     */
    PageDto<JdhSkuDto> queryPageJdhSkuInfo(JdhSkuPageRequest request);
    
    /**
     * 查询健康商品主数据
     *
     * @param request request
     * @return bo
     */
    JdhSkuDto queryJdhSkuInfo(JdhSkuRequest request);
    
    /**
     * 查询多个健康商品主数据
     *
     * @param request request
     * @return bo
     */
    Map<Long, JdhSkuDto> queryJdhSkuInfoList(JdhSkuListRequest request);
    
    /**
     * 查询健康商品主数据关联sku
     *
     * @return list
     */
    List<JdhSkuRelDto> queryJdhSkuRel(JdhSkuRelRequest request);

    /**
     * 批量查询健康商品主数据关联sku
     *
     * @return list
     */
    Map<Long, List<JdhSkuRelDto>> queryJdhSkuRelBatch(JdhSkuRelRequest request);
    
    /**
     * 保存健康商品主数据
     *
     * @param createJdhSkuCmd cmd
     * @return dto
     */
    Boolean addJdhSkuInfo(CreateJdhSkuCmd createJdhSkuCmd);
    
    /**
     * 更新健康商品主数据
     *
     * @param updateJdhSkuCmd cmd
     * @return dto
     */
    Boolean updateJdhSkuInfo(UpdateJdhSkuCmd updateJdhSkuCmd);
    
    /**
     * 更新健康商品主数据爆单开关
     *
     * @param updateJdhSkuSaleStatusCmd cmd
     * @return dto
     */
    Boolean updateJdhSkuSaleStatus(UpdateJdhSkuSaleStatusCmd updateJdhSkuSaleStatusCmd);
    
    /**
     * 校验健康商品主数据
     *
     * @param request request
     * @return bo
     */
    JdhSkuDto checkJdhSkuInfo(JdhSkuRequest request);
    
    /**
     * 校验健康商品主数据关联sku
     *
     * @return list
     */
    JdhSkuRelDto checkSubSku(JdhSkuRelRequest request);
    
    /**
     * 保存健康商品主数据关联sku
     *
     * @param createJdhSkuRelCmd cmd
     * @return dto
     */
    Boolean addJdhSkuInfoRel(CreateJdhSkuRelCmd createJdhSkuRelCmd);

    /**
     * 查看商品信息
     *
     * @return detailFloorBO
     */
    ProductDetailDTO queryProductDetailFloorBySku(ProductDetailFloorRequest detailFloorBO);

    /**
     * 根据标准项目集合查询多个商品信息
     *
     * @param jdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest
     * @return maps
     */
    Map<Long, List<JdhSkuDto>> queryMultiSkuByServiceItem(JdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest);

    /**
     * 列表地址分堆
     * @param request
     * @return
     */
    Map<String, GroupUserAddressDTO> listGroupAddress(OrderUserAddressQuery request);

    /**
     *
     * @param request
     * @return
     */
    JdhSkuDto queryAggregationJdhSkuInfo(JdhSkuRequest request);

    /**
     * 校验健康商品主数据
     *
     * @param request request
     * @return bo
     */
    JdhSkuDto checkJdSku(JdhSkuRequest request);

    /**
     * 校验健康商品服务项是否在同一个门店下
     *
     * @param jdhSkuDto jdhSkuDto
     * @return bo
     */
    Boolean checkSkuServiceItemAllInOneStore(JdhSkuDto jdhSkuDto, String stationId);

    /**
     * 分页查询商品项目关系
     *
     */
    PageDto<JdhSkuItemRelDto> queryPageJdhSkuItemRel(JdhSkuServiceItemRelPageRequest request);

    /**
     * 导入商品
     * @param cmd cmd
     * @return true
     */
    Boolean importSku(JdhSkuImportCmd cmd);

    /**
     * 根据主品查询加项品
     * @param request
     * @return
     */
    Map<Long, AddProductDto> queryAddProductDtoBySkuList(JdhAddProductRequest request);

    /**
     * 查询商品优惠券列表
     * @param request
     * @return
     */
    List<ProductCouponDTO> queryProductCouponList(ProductCouponRequest request);

    /**
     * 领取优惠券
     * @param cmd
     * @return
     */
    CouponGetResultDTO getCoupon(GetCouponCmd cmd);

    /**
     * 批量-领取优惠券
     * @param cmd
     * @return
     */
    CouponGetResultDTO batchGetCoupon(BatchGetCouponCmd cmd);

    /**
     * 查询服务需要的技能列表
     * @param skuNos
     * @return
     */
    Set<String> listSkillCodes(List<Long> skuNos);

    /**
     * 根据sku中serviceType替换文本
     * @param serviceType
     * @param scene
     * @param text
     * @return
     */
    String replaceWordsByServiceType(Integer serviceType, String scene, String text);

}