package com.jdh.o2oservice.application.support.service;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhImportExportTask;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.command.GeneratePutUrlCommand;
import com.jdh.o2oservice.export.support.command.PdfSignatureCmd;
import com.jdh.o2oservice.export.support.command.PutHttpFileCommand;
import com.jdh.o2oservice.export.support.dto.*;
import com.jdh.o2oservice.export.support.query.GetFileUrlRequest;
import com.jdh.o2oservice.export.support.query.PageFileTaskRequest;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 文件管理应用层
 * @author: yangxiyu
 * @date: 2024/3/20 4:21 下午
 * @version: 1.0
 */
public interface FileManageApplication {

    /**
     * 文件导出
     * @param param
     */
    Boolean export(Map<String, Object> param);

    /**
     * 文件导出并返回下载链接
     * @param param
     * @return
     */
    PutFileResultDto exportAndDownloadFile(Map<String, Object> param);

    /**
     * 文件导出
     * @param param
     */
    JdhImportExportTask exportTask(Map<String, Object> param);
    /**
     * 文件导入
     * @param param
     */
    Boolean importFile(Map<String, Object> param);

    /**
     * 文件导入导出任务分页查询
     * @param request
     * @return
     */
    PageDto<FileTaskDto> pageFileTask(PageFileTaskRequest request);

    /**
     * 获取文件访问链接
     * @param request
     * @return
     */
    String getFileUrl(GetFileUrlRequest request);


    /**
     * 生成文件上传的预签名链接
     * @param command
     * @return
     */
    FilePreSignedUrlDto generatePutUrl(GeneratePutUrlCommand command);

    /**
     * 使用httpUrl获取文件数据，并上传到OSS
     * @param command
     * @return
     */
    JdhFile putHttpFile(PutHttpFileCommand command);


    /**
     * 根据文件ID批量获取访问链接
     * @param command
     * @return
     */
    List<FilePreSignedUrlDto> generateGetUrl(GenerateGetUrlCommand command);

    /**
     * PDF文件追加签名
     * @param command
     * @return
     */
    PdfSignatureResult pdfSignature(PdfSignatureCmd command);

    /**
     * PDF文件签名,并保存
     * @param command
     * @return
     */
    List<PdfSignatureResult> pdfSignatureAndSave(PdfSignatureCmd command);

    /**
     * 上传文件,工具类接口,实际上传使用generatePutUrl生成预签名上传链接后使用生成的上传链接进行上传
     *
     * @param originFileName fileName
     * @param data
     * @return
     */
    FilePreSignedUrlDto upload(String originFileName, InputStream fileInputStream, LocalDateTime fileSaveExpireTime, GeneratePutUrlCommand data);

    /**
     * 获取文件访问链接
     * @param request
     * @return
     */
    List<FileUrlDto> getMultiFileUrl(GetFileUrlRequest request);
}
