package com.jdh.o2oservice.application.promise.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.convert.PromiseApplicationConverter;
import com.jdh.o2oservice.application.promise.convert.VoucherApplicationConverter;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.settlement.context.SettlementEbsContext;
import com.jdh.o2oservice.application.settlement.service.SettlementEbsApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.cache.RedisUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.VerticalEventBody;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleMainBodyTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleSplitTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.promise.context.*;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.event.BatchCreateVoucherEventBody;
import com.jdh.o2oservice.core.domain.promise.event.VoucherEventBody;
import com.jdh.o2oservice.core.domain.promise.factory.JdhVoucherFactory;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.VoucherRepPageQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.VoucherRepQuery;
import com.jdh.o2oservice.core.domain.promise.service.JdhPromiseDomainService;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhProcessDataTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.repository.query.JdOrderItemQuery;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.promise.cmd.*;
import com.jdh.o2oservice.export.promise.dto.FreezeStateDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.enums.VoucherOpEnum;
import com.jdh.o2oservice.export.promise.query.VoucherIdRequest;
import com.jdh.o2oservice.export.promise.query.VoucherPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务单 应用层服务
 *
 * <AUTHOR>
 * @date 2023/12/12
 */
@Slf4j
@Service
public class VoucherApplicationImpl implements VoucherApplication {

    @Resource
    private RedisLockUtil redisLockUtil;
    /**
     * voucher操作并发锁
     */
    private static final String VOUCHER_LOCK_KEY = "voucher:cmd:{0}";
    /**
     * eventCoordinator
     */
    @Autowired
    private EventCoordinator eventCoordinator;

    /**
     * jdhPromiseDomainService
     */
    @Autowired
    private JdhPromiseDomainService jdhPromiseDomainService;

    /**
     * jdhVoucherRepository
     */
    @Resource
    private VoucherRepository jdhVoucherRepository;

    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository jdhPromiseRepository;
    /**
     *
     */
    @Resource
    protected AngelWorkRepository angelWorkRepository;

    /**
     * promiseApplication
     */
    @Resource
    private PromiseApplication promiseApplication;

    /**
     * productApplication
     */
    @Autowired
    private ProductApplication productApplication;

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;

    /**
     *
     */
    @Resource
    private JdOrderItemRepository jdOrderItemRepository;

    /**
     * redisUtil
     */
    @Resource
    private RedisUtil redisUtil;

    @Resource
    private VerticalBusinessRepository businessRepository;

    @Resource
    private SettlementEbsApplication settlementEbsApplication;
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;


    /**
     * 批量创建服务单
     *
     * @param cmdList cmdList
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.batchCreateVoucher")
    public Boolean batchCreateVoucher(List<CreateVoucherCmd> cmdList) {
        try {
            log.info("VoucherApplicationImpl batchCreateVoucher cmdList={}", JSON.toJSONString(cmdList));
            List<JdhVoucher> exitVoucherList = jdhVoucherRepository.listByQuery(VoucherRepQuery.builder()
                    .sourceVoucherIds(cmdList.stream().map(CreateVoucherCmd::getSourceVoucherId).collect(Collectors.toList()))
                    .sourceType(cmdList.get(0).getSourceType()).build());

            List<CreateVoucherContext> voucherContextList = cmdList.stream().map(VoucherApplicationConverter.INSTANCE::cmd2Ctx).collect(Collectors.toList());
            BatchCreateVoucherContext batchVoucherContext = BatchCreateVoucherContext.builder()
                    .exitVoucherList(exitVoucherList)
                    .createVoucherContexts(voucherContextList)
                    .build();

            List<JdhVoucher> vouchers = JdhVoucherFactory.batchCreate(batchVoucherContext);
            log.info("VoucherApplicationImpl batchCreateVoucher vouchers={}", JSON.toJSONString(vouchers));
            Set<String> sourceVoucherIdList = new HashSet<>();
            for (JdhVoucher jdhVoucher : vouchers) {
                jdhVoucherRepository.save(jdhVoucher);
                sourceVoucherIdList.add(jdhVoucher.getSourceVoucherId());
            }

            // 发布批量产码逻辑
            Event event = EventFactory.newDefaultEvent(vouchers.get(NumConstant.NUM_0), PromiseEventTypeEnum.VOUCHER_BATCH_CREATE, new BatchCreateVoucherEventBody(sourceVoucherIdList));
            eventCoordinator.publish(event);
        } catch (Exception e) {
            log.error("VoucherApplicationImpl->batchCreateVoucher error", e);
            throw e;
        }

        return Boolean.TRUE;
    }

    /**
     * 创建JDH服务单
     * 1、POP需要听到订单拉完成后创建；
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.createVoucher")
    public List<PromiseDto> createVoucher(CreateVoucherCmd cmd) {
        CreateVoucherContext context = VoucherApplicationConverter.INSTANCE.cmd2Ctx(cmd);
        List<JdhVoucher> exitVoucherList = jdhVoucherRepository.listByQuery(VoucherRepQuery.builder().sourceVoucherId(cmd.getSourceVoucherId()).sourceType(cmd.getSourceType()).build());
        context.setExistVoucherList(exitVoucherList);
        log.info("VoucherApplicationImpl->createVoucher context={}", JSON.toJSONString(context));
        // 创建服务单
        JdhVoucher jdhVoucher = JdhVoucherFactory.create(context);
        jdhVoucherRepository.save(jdhVoucher);

        // 如果同步产码
        if(Boolean.TRUE.equals(cmd.getSyncCreatePromise())){
            List<PromiseDto> promiseDtoList = createPromise(CreatePromiseCmd.builder().voucherId(jdhVoucher.getVoucherId()).build());
            log.info("VoucherApplicationImpl-> createVoucher 同步产码 promiseDtoList={}", JSON.toJSONString(promiseDtoList));
            return promiseDtoList;
        }
        // 发布服务单创建完成事件。
        VerticalEventBody eventBody = VerticalEventBody.builder().verticalCode(jdhVoucher.getVerticalCode())
                .serviceType(jdhVoucher.getServiceType()).build();
        Event event = EventFactory.newDefaultEvent(jdhVoucher, PromiseEventTypeEnum.VOUCHER_CREATE,eventBody);
        eventCoordinator.publish(event);
        return new ArrayList<>();
    }


    /**
     * 创建履约码（码就是履约单）,创建码有三种方式
     * （1）POP需要反查LOC产的码；
     * （2）一卡万店需要我们自己产码；
     * （3）预留扩展的商家产码
     *
     * @param cmd
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.createPromise")
    public List<PromiseDto> createPromise(CreatePromiseCmd cmd){
        //反查服务单
        JdhVoucher jdhVoucher = jdhVoucherRepository.find(JdhVoucherIdentifier.builder().voucherId(cmd.getVoucherId()).build());
        log.info("VoucherApplicationImpl-> createPromise jdhVoucher={}", JSON.toJSONString(jdhVoucher));

        //构建上下文
        CreatePromiseContext context = new CreatePromiseContext(jdhVoucher, cmd);
        //填充sku数据
        Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder()
                .skuIdList(jdhVoucher.getVoucherItemList().stream().map(JdhVoucherItem::getServiceId).collect(Collectors.toSet()))
                .querySkuCoreData(Boolean.TRUE)
                .queryServiceItem(Boolean.TRUE)
                .build());
        context.setPromiseSkuMap(jdhSkuDtoMap);

        log.info("VoucherApplicationImpl-> createPromise context={}", JSON.toJSONString(context));
        context.initVertical();
        log.info("VoucherApplicationImpl-> createPromise init身份后 context={}", JSON.toJSONString(context));
        List<JdhPromise> promiseList = jdhPromiseDomainService.createPromise(context);
        log.info("VoucherApplicationImpl-> createPromise promiseList ={}", JSON.toJSONString(promiseList));

        jdhPromiseRepository.batchSave(promiseList);
        //发布服务单创建完成事件。
        promiseList.forEach(p -> {
            Event event = EventFactory.newDefaultEvent(p, PromiseEventTypeEnum.PROMISE_CREATED, null);
            eventCoordinator.publish(event);
        });
        Event event = EventFactory.newDefaultEvent(jdhVoucher, PromiseEventTypeEnum.VOUCHER_PROMISE_CREATE, null);
        eventCoordinator.publish(event);

        return PromiseApplicationConverter.INS.entity2DtoList(promiseList);
    }


    /**
     * 批量创建履约单
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.batchCreatePromise")
    public void batchCreatePromise(BatchCreatePromiseCmd cmd) {
        List<JdhVoucher> vouchers = jdhVoucherRepository.listByQuery(VoucherRepQuery.builder().sourceVoucherIds(new ArrayList<>(cmd.getSourceVoucherIdList())).build());
        log.info("VoucherApplicationImpl-> batchCreatePromise vouchers={}", JSON.toJSONString(vouchers));
        //构建上下文
        BatchCreatePromiseContext context = new BatchCreatePromiseContext(vouchers);
        //填充sku数据
        Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder()
                .skuIdList(vouchers.stream().flatMap(voucher -> voucher.getVoucherItemList().stream())
                        .map(JdhVoucherItem::getServiceId).collect(Collectors.toSet()))
                .querySkuCoreData(Boolean.TRUE)
                .queryServiceItem(Boolean.FALSE)
                .build());
        context.setPromiseSkuMap(VoucherApplicationConverter.INSTANCE.convert2PromiseSkuMap(jdhSkuDtoMap));
        log.info("VoucherApplicationImpl-> batchCreatePromise context={}", JSON.toJSONString(context));
        context.initVertical();
        log.info("VoucherApplicationImpl-> batchCreatePromise init身份后 context={}", JSON.toJSONString(context));

        List<JdhPromise> promiseList = jdhPromiseDomainService.batchCreatePromise(context);
        log.info("VoucherApplicationImpl-> batchCreatePromise promiseList={}", JSON.toJSONString(promiseList));
        jdhPromiseRepository.batchSave(promiseList);
        //发布服务单创建完成事件。
        promiseList.forEach(p -> {
            Event event = EventFactory.newDefaultEvent(p, PromiseEventTypeEnum.PROMISE_CREATED, null);
            eventCoordinator.publish(event);
        });
        for (JdhVoucher jdhVoucher : vouchers) {
            //发布服务单创建完成事件。
            Event event = EventFactory.newDefaultEvent(jdhVoucher, PromiseEventTypeEnum.VOUCHER_PROMISE_CREATE, null);
            eventCoordinator.publish(event);
        }
    }

    /**
     * 操作服务单
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.operateVoucher")
    public Boolean operateVoucher(VoucherOpCmd cmd) {
        VoucherOpEnum voucherOpEnum = cmd.getVoucherOpEnum();
        switch (voucherOpEnum) {
            case DELAY:
                return this.delayVoucher(DelayVoucherCmd.builder().voucherId(cmd.getVoucherId()).delayDate(cmd.getDelayDate()).build());
            case FREEZE:
                try {
                    this.freezeVoucher(FreezeVoucherCmd.builder().voucherId(cmd.getVoucherId()).freezeType(JdhProcessDataTypeEnum.PROCESS_PROMISE.getType()).build());
                    return Boolean.TRUE;
                } catch (Exception e) {
                    return Boolean.FALSE;
                }
            case INVALID:
                return this.invalidVoucher(InvalidVoucherCmd.builder().voucherId(cmd.getVoucherId()).invalidType(JdhProcessDataTypeEnum.PROCESS_PROMISE.getType()).build());
            case UN_FREEZE:
                return this.unFreezeVoucher(UnFreezeVoucherCmd.builder().voucherId(cmd.getVoucherId()).build());
            default:
                return Boolean.TRUE;
        }
    }

    /**
     * 启用服务单
     * 服务单全量状态：
     * status 服务单状态: 0 待生效、1、待服务、2 完成、 3 过期、4 作废
     * freeze 冻结状态 0 正常 1 冻结
     * 本功能：status 从待生效 变为 待服务
     *
     * @param cmd CMD
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.enableVoucher")
    public Boolean enableVoucher(EnableVoucherCmd cmd) {
        JdhVoucher jdhVoucher = jdhVoucherRepository.find(JdhVoucherIdentifier.builder().voucherId(cmd.getVoucherId()).build());
        // 已启用则不处理
        if (jdhVoucher.isEnable()){
            return Boolean.TRUE;
        }
        log.info("VoucherApplicationImpl -> enableVoucher jdhVoucher:{}", JSON.toJSONString(jdhVoucher));
        EnableVoucherContext enableJdhVoucherContext = new EnableVoucherContext(jdhVoucher, PromiseEventTypeEnum.VOUCHER_PROMISE_CREATE);
        log.info("VoucherApplicationImpl -> enableVoucher enableJdhVoucherContext:{}", JSON.toJSONString(enableJdhVoucherContext));
        jdhVoucher.enableVoucher(enableJdhVoucherContext);
        log.info("VoucherApplicationImpl -> enableVoucher 状态流转后 enableJdhVoucherContext:{}", JSON.toJSONString(enableJdhVoucherContext));

        jdhVoucherRepository.save(jdhVoucher);

        //自动预约，发布自动预约事件，数据落库完成后异步处理，不影响主流程
        if (Objects.nonNull(jdhVoucher.autoPromise()) && jdhVoucher.autoPromise()) {
            log.info("VoucherApplicationImpl -> enableVoucher 触发自动预约");
            Event autoAppointEvent = EventFactory.newDefaultEvent(jdhVoucher, PromiseEventTypeEnum.VOUCHER_AUTO_APPOINTMENT, null);
            eventCoordinator.publish(autoAppointEvent);
        }
        return Boolean.TRUE;
    }


    /**
     * 过期服务单
     * 服务单全量状态：
     * status 服务单状态: 0 待生效、1、待服务、2 完成、 3 过期、4 作废
     * freeze 冻结状态 0 正常 1 冻结
     * 本功能实现 status 变更为 3 过期
     *
     * @param cmd CMD
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.expireVoucher")
    public Boolean expireVoucher(ExpireVoucherCmd cmd) {
        JdhVoucher jdhVoucher = jdhVoucherRepository.find(JdhVoucherIdentifier.builder().voucherId(cmd.getVoucherId()).build());
        log.info("VoucherApplicationImpl -> expireVoucher jdhVoucher:{}", JSON.toJSONString(jdhVoucher));
        ExpireVoucherContext expireJdhVoucherContext = new ExpireVoucherContext(jdhVoucher, PromiseEventTypeEnum.VOUCHER_EXPIRE);
        log.info("VoucherApplicationImpl -> expireVoucher expireJdhVoucherContext:{}", JSON.toJSONString(expireJdhVoucherContext));
        jdhVoucher.expireJdhVoucher(expireJdhVoucherContext);
        log.info("VoucherApplicationImpl -> expireVoucher 状态流转后，expireJdhVoucherContext:{}", JSON.toJSONString(expireJdhVoucherContext));
        jdhVoucherRepository.save(jdhVoucher);

        List<JdhVoucherItem> voucherItemList = jdhVoucher.getVoucherItemList();

        //发布服务单过期事件
        Long serviceId = CollectionUtils.isNotEmpty(voucherItemList) ? voucherItemList.get(0).getServiceId() : null;
        VoucherEventBody eventBody = VoucherEventBody.builder()
                .voucherId(String.valueOf(jdhVoucher.getVoucherId()))
                .sourceVoucherId(jdhVoucher.getSourceVoucherId())
                .serviceId(serviceId)
                .expireTime(jdhVoucher.getExpireDate())
                .build();
        Event newEvent = EventFactory.newDefaultEvent(jdhVoucher, PromiseEventTypeEnum.VOUCHER_EXPIRE, eventBody);
        eventCoordinator.publish(newEvent);
        return Boolean.TRUE;
    }

    /**
     * 作废服务单
     *
     * @param cmd CMD
     * @return {@link Boolean}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.invalidVoucher")
    public Boolean invalidVoucher(InvalidVoucherCmd cmd) {
        String lockKey = MessageFormat.format(VOUCHER_LOCK_KEY, cmd.getVoucherId());
        return redisLockUtil.lockExecuteInTransaction(() -> {
            JdhVoucher jdhVoucher = jdhVoucherRepository.find(JdhVoucherIdentifier.builder().voucherId(cmd.getVoucherId()).build());
            log.info("VoucherApplicationImpl -> invalidVoucher jdhVoucher:{}", JSON.toJSONString(jdhVoucher));
            InvalidVoucherContext invalidJdhVoucherContext = new InvalidVoucherContext(jdhVoucher, PromiseEventTypeEnum.VOUCHER_INVALID);
            log.info("VoucherApplicationImpl -> invalidVoucher invalidJdhVoucherContext:{}", JSON.toJSONString(invalidJdhVoucherContext));
            if (jdhVoucher.getPromiseNum() == 1) {
                PromiseRepQuery query = new PromiseRepQuery();
                query.setVoucherIds(Lists.newArrayList(jdhVoucher.getVoucherId()));
                List<JdhPromise> promises = jdhPromiseRepository.findList(query);
                log.info("VoucherApplicationImpl -> invalidVoucher promises:{}", JSON.toJSONString(promises));
                JdhPromise promise = promises.get(0);
                PromiseInvalidCmd promiseInvalidCmd = new PromiseInvalidCmd();
                promiseInvalidCmd.setPromiseId(String.valueOf(promise.getPromiseId()));
                promiseInvalidCmd.setReason(cmd.getReason());
                promiseInvalidCmd.setVerticalCode(jdhVoucher.getVerticalCode());
                promiseInvalidCmd.setServiceType(jdhVoucher.getServiceType());
                promiseInvalidCmd.setPromisePatient(cmd.getPromisePatient());
                promiseInvalidCmd.setInvalidService(cmd.getInvalidService());
                promiseInvalidCmd.setInvalidType(cmd.getInvalidType());
                // promise作废后 voucher才能作废
                promiseApplication.invalid(promiseInvalidCmd);
                // promise作废后，voucher才能作废
                promise = jdhPromiseRepository.find(new JdhPromiseIdentifier(promise.getPromiseId()));
                log.info("VoucherApplicationImpl -> invalidVoucher promise:{}", JSON.toJSONString(promise));
                if (promise.isInvalid()) {
                    jdhVoucher.invalid(invalidJdhVoucherContext);
                    log.info("VoucherApplicationImpl -> invalidVoucher 状态流转后，invalidJdhVoucherContext:{}", JSON.toJSONString(invalidJdhVoucherContext));
                }
            }
            jdhVoucherRepository.save(jdhVoucher);
            return Boolean.TRUE;
        }, lockKey, 20, 10, 2000L);
    }

    /**
     * 服务单延期
     *
     * @param cmd CMD
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.delayVoucher")
    public Boolean delayVoucher(DelayVoucherCmd cmd) {
        JdhVoucher jdhVoucher = jdhVoucherRepository.find(JdhVoucherIdentifier.builder().voucherId(cmd.getVoucherId()).build());
        log.info("VoucherApplicationImpl -> delayVoucher jdhVoucher:{}", JSON.toJSONString(jdhVoucher));
        DelayVoucherContext delayVoucherContext = new DelayVoucherContext(jdhVoucher, cmd.getDelayDate(), PromiseEventTypeEnum.VOUCHER_DELAY);
        List<JdhPromise> jdhPromises = jdhPromiseRepository.findList(PromiseRepQuery.builder().voucherIds(Collections.singletonList(jdhVoucher.getVoucherId())).build());
        delayVoucherContext.setPromiseList(jdhPromises);
        log.info("VoucherApplicationImpl -> delayVoucher delayVoucherContext:{}", JSON.toJSONString(delayVoucherContext));

        jdhVoucher.delay(delayVoucherContext);

        jdhVoucherRepository.save(jdhVoucher);

        //发布服务单延期事件。
        Event newEvent = EventFactory.newDefaultEvent(jdhVoucher, PromiseEventTypeEnum.VOUCHER_DELAY, null);
        eventCoordinator.publish(newEvent);
        return Boolean.TRUE;
    }

    /**
     * 查询JDH凭证
     *
     * @param request request
     * @return {@link List}<{@link VoucherDto}>
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.pageQueryVoucher")
    public Response<PageDto<VoucherDto>> pageQueryVoucher(VoucherPageRequest request) {
        VoucherRepPageQuery repPageQuery = VoucherApplicationConverter.INSTANCE.request2RepQuery(request);
        repPageQuery.setPageNum(request.getPageNum());
        repPageQuery.setPageSize(request.getPageSize());
        Page<JdhVoucher> pageList = jdhVoucherRepository.findPageList(repPageQuery);
        List<VoucherDto> voucherDtoList = VoucherApplicationConverter.INSTANCE.voucher2DtoList(pageList.getRecords());
        PageDto pageDto = new PageDto();
        pageDto.setList(voucherDtoList);
        pageDto.setPageSize(pageList.getPages());
        pageDto.setPageNum(pageList.getCurrent());
        pageDto.setTotalPage(pageList.getPages());
        pageDto.setTotalCount(pageList.getTotal());
        return ResponseUtil.buildSuccResponse(pageDto);
    }

    /**
     * 通过id查找jdhVoucher
     *
     * @param voucherIdRequest voucherIdRequest
     * @return {@link VoucherDto}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.findByVoucherId")
    public VoucherDto findByVoucherId(VoucherIdRequest voucherIdRequest) {
        JdhVoucher jdhVoucher = jdhVoucherRepository.find(JdhVoucherIdentifier.builder().voucherId(voucherIdRequest.getVoucherId()).build());
        return VoucherApplicationConverter.INSTANCE.entity2Dto(jdhVoucher);
    }

    /**
     * 过期服务单结算
     *
     * @param settleCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "VoucherApplicationImpl.voucherExpireSettle")
    public Boolean voucherExpireSettle(VoucherExpireSettleCmd settleCmd) {
        //加锁防止重复记收
        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.VOUCHER_EXPIRE_SETTLE_LOCK, settleCmd.getVoucherId(), settleCmd.getServiceId());
        boolean lock = redisUtil.tryLock(redisKey, RedisKeyEnum.VOUCHER_EXPIRE_SETTLE_LOCK.getExpireTime(), RedisKeyEnum.VOUCHER_EXPIRE_SETTLE_LOCK.getExpireTimeUnit());
        if (!lock) {
            log.info("[VoucherApplicationImpl -> voucherExpireSettle],服务单已处理或处理中!");
            return Boolean.FALSE;
        }

        try {
            //记录服务单处理的条数
//            Long expireNum = redisUtil.incrExpire(RedisKeyEnum.getRedisKey(RedisKeyEnum.VOUCHER_EXPIRE_SETTLE_NUM_PREFIX, settleCmd.getVoucherId(), settleCmd.getServiceId()),
//                    RedisKeyEnum.VOUCHER_EXPIRE_SETTLE_NUM_PREFIX.getExpireTime(), RedisKeyEnum.VOUCHER_EXPIRE_SETTLE_NUM_PREFIX.getExpireTimeUnit());
            //检查是否需要记收
            PromiseRepQuery query = PromiseRepQuery.builder()
                    .voucherIds(Lists.newArrayList(Long.valueOf(settleCmd.getVoucherId())))
                    .promiseStatusList(Lists.newArrayList(JdhPromiseStatusEnum.COMPLETE.getStatus()))
                    .build();
            List<JdhPromise> jdhPromiseList = jdhPromiseRepository.findList(query);

            //目前没有次卡，兼容单次卡的逻辑
            if (CollectionUtils.isNotEmpty(jdhPromiseList)) {
                log.info("[VoucherApplicationImpl -> voucherExpireSettle],服务单已经完成,无需记收!");
                return Boolean.TRUE;
            }

            JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(Long.valueOf(settleCmd.getSourceVoucherId())));
            if (Objects.isNull(jdOrder)) {
                log.error("[VoucherApplicationImpl -> voucherExpireSettle],订单信息不存在!settleCmd={}", JSON.toJSONString(settleCmd));
                return Boolean.FALSE;
            }
            if (jdOrder.isInvalidStatus()) {
                log.error("[VoucherApplicationImpl -> voucherExpireSettle],订单已失效,不需要处理!!settleCmd={}", JSON.toJSONString(settleCmd));
                return Boolean.FALSE;
            }
            //处理4号订单的过期记实收
            if (jdOrder.getOrderType() != CommonConstant.FOUR) {
                log.error("[VoucherApplicationImpl -> voucherExpireSettle],非4号订单,不需要处理!settleCmd={}", JSON.toJSONString(settleCmd));
                return Boolean.FALSE;
            }
            //计算订单过期实收金额
            JdOrderItemQuery itemQuery = new JdOrderItemQuery();
            itemQuery.setOrderId(Long.valueOf(settleCmd.getSourceVoucherId()));
            itemQuery.setSkuId(settleCmd.getSkuId());
            List<JdOrderItem> jdOrderItemList = jdOrderItemRepository.findJdOrderItemList(itemQuery);
            if (CollectionUtils.isEmpty(jdOrderItemList)) {
                log.error("[VoucherApplicationImpl -> voucherExpireSettle],订单不存在订单明细,无法处理!settleCmd={}", JSON.toJSONString(settleCmd));
                return Boolean.FALSE;
            }

            BigDecimal itemTotalAmount = jdOrderItemList.stream().map(JdOrderItem::getItemAmount).reduce(BigDecimal.ZERO, (big1, big2) -> big1.add(big2));
            if (itemTotalAmount.compareTo(BigDecimal.ZERO) <= 0) {
                log.error("[VoucherApplicationImpl -> voucherExpireSettle],零元单不需要处理!settleCmd={}", JSON.toJSONString(settleCmd));
                return Boolean.FALSE;
            }
            //检查过期的数量是否和下单数量相等
//        BigDecimal settlePrice;
//        Integer skuNum = jdOrderItemList.get(0).getSkuNum();
//        if(skuNum.equals(expireNum.intValue())) {
//            settlePrice = itemAmount.subtract(itemAmount.divide(new BigDecimal(skuNum), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(skuNum - 1)));
//        }else {
//            settlePrice = itemAmount.divide(new BigDecimal(skuNum), 2, BigDecimal.ROUND_HALF_UP);
//        }
            settleCmd.setSettlePrice(itemTotalAmount);
            settleCmd.setPayTime(jdOrder.getPaymentTime());
            JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(jdOrder.getVerticalCode());
            SettlementEbsContext settlementEbsContext = new SettlementEbsContext();
            settlementEbsContext.setBusinessModeEnum(BusinessModeEnum.getEnumByCode(jdhVerticalBusiness.getBusinessModeCode()));
            settlementEbsContext.setEbsSettleTypeEnum(EbsSettleTypeEnum.INCOME);
            settlementEbsContext.setEbsSettleSplitTypeEnum(EbsSettleSplitTypeEnum.SKU_FEE);
            settlementEbsContext.setExtBusinessModel(settleCmd);
            settlementEbsContext.setEbsSettleMainBodyTypeEnum(EbsSettleMainBodyTypeEnum.VOUCHER);
            return settlementEbsApplication.sendToEbs(settlementEbsContext);
        } catch (Exception ex) {
            log.error("[VoucherApplicationImpl -> voucherExpireSettle],服务单过期记收失败!settleCmd={}", JSON.toJSONString(settleCmd), ex);
            UmpUtil.showWarnMsg(UmpKeyEnum.EXPIRE_VOUCHER_CAN_NOT_SETTLE_ERROR);
        }
        return Boolean.FALSE;
    }

    /**
     * @param voucherPageRequest
     * @return
     */
    @Override
    public List<VoucherDto> queryVoucherList(VoucherPageRequest voucherPageRequest) {
        List<JdhVoucher> exitVoucherList = jdhVoucherRepository.listByQuery(VoucherRepQuery.builder()
                .sourceVoucherId(voucherPageRequest.getSourceVoucherId()).filterPromise(Boolean.FALSE).build());
        return VoucherApplicationConverter.INSTANCE.voucher2DtoList(exitVoucherList);
    }

    /**
     * 服务单完成
     * 服务单全量状态：
     * status 服务单状态: 0 待生效、1、待服务、2 完成、 3 过期、4 作废
     * freeze 冻结状态 0 正常 1 冻结
     * 本功能：status 变为2
     *
     * @param cmd CMD
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.completeVoucher")
    public Boolean completeVoucher(CompleteVoucherCmd cmd) {
        JdhVoucher jdhVoucher = jdhVoucherRepository.find(JdhVoucherIdentifier.builder().voucherId(cmd.getVoucherId()).build());
        log.info("VoucherApplicationImpl -> completeVoucher jdhVoucher:{}", JSON.toJSONString(jdhVoucher));
        CompleteVoucherContext ctx = new CompleteVoucherContext(jdhVoucher, cmd.getPromiseId(), PromiseEventTypeEnum.getByCode(cmd.getSourceEvent()));
        log.info("VoucherApplicationImpl -> completeVoucher ctx:{}", JSON.toJSONString(ctx));
        jdhVoucher.completeVoucher(ctx);
        log.info("VoucherApplicationImpl -> completeVoucher 状态流转后 ctx:{}", JSON.toJSONString(ctx));
        jdhVoucherRepository.save(jdhVoucher);

        //发布服务单冻结事件
        Event event = EventFactory.newDefaultEvent(jdhVoucher, PromiseEventTypeEnum.VOUCHER_COMPLETE, null);
        eventCoordinator.publish(event);
        return Boolean.TRUE;
    }

    /**
     * 冻结服务单
     * 服务单全量状态：
     * status 服务单状态: 0 待生效、1、待服务、2 完成、 3 过期、4 作废
     * freeze 冻结状态 0 正常 1 冻结
     * 本功能：freeze 从0正常变为1冻结
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.freezeVoucher")
    public FreezeStateDto freezeVoucher(FreezeVoucherCmd cmd) {
        String lockKey = MessageFormat.format(VOUCHER_LOCK_KEY, cmd.getVoucherId());
        return redisLockUtil.lockExecuteInTransaction(() -> {
            JdhVoucher jdhVoucher = jdhVoucherRepository.find(JdhVoucherIdentifier.builder().voucherId(cmd.getVoucherId()).build());
            FreezeStateDto stateDto = null;
            // 只有一次履约
            if (jdhVoucher.getPromiseNum() == 1) {
                jdhVoucher.freezeVoucher();
                PromiseRepQuery query = new PromiseRepQuery();
                query.setVoucherIds(Lists.newArrayList(jdhVoucher.getVoucherId()));
                List<JdhPromise> promises = jdhPromiseRepository.findList(query);
                JdhPromise promise = promises.get(0);
                PromiseFreezeCmd promiseFreezeCmd = new PromiseFreezeCmd();
                promiseFreezeCmd.setPromiseId(String.valueOf(promise.getPromiseId()));
                promiseFreezeCmd.setReason(cmd.getReason());
                promiseFreezeCmd.setAllowPromiseStatus(cmd.getAllowPromiseStatus());
                promiseFreezeCmd.setFreeUser(cmd.getFreezeUser());
                promiseFreezeCmd.setFreezeService(cmd.getFreezeService());
                promiseFreezeCmd.setFreezeType(cmd.getFreezeType());
                stateDto = promiseApplication.freeze(promiseFreezeCmd);

            }

            // 冻结p
            jdhVoucherRepository.save(jdhVoucher);
            return stateDto;
            /**
             * 有加项包时，退款结果可能同时回调，每个加项包都会调一次作废，存在并发冲突；后续可以考虑和延迟队列整合；即发生并发冲突时，将命令提交到延迟队列；
             * 再通过消费延迟队列数据进行处理
             */
        }, lockKey, 20, 10, 2000L);
    }

    /**
     * 解冻服务单
     * 服务单全量状态：
     * status 服务单状态: 0 待生效、1、待服务、2 完成、 3 过期、4 作废
     * freeze 冻结状态 0 正常 1 冻结
     * 本功能：freeze 从1冻结变为0正常
     *
     * @param cmd cmd
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.promise.service.impl.VoucherApplicationImpl.unFreezeVoucher")
    public Boolean unFreezeVoucher(UnFreezeVoucherCmd cmd) {
        JdhVoucher jdhVoucher = jdhVoucherRepository.find(JdhVoucherIdentifier.builder().voucherId(cmd.getVoucherId()).build());
        Boolean unFreezeVoucher = jdhVoucher.unFreezeVoucher();
        if (unFreezeVoucher) {
            PromiseUnFreezeCmd promiseUnFreezeCmd = new PromiseUnFreezeCmd();
            promiseUnFreezeCmd.setVoucherId(cmd.getVoucherId());
            promiseUnFreezeCmd.setServiceType(jdhVoucher.getServiceType());
            promiseUnFreezeCmd.setVerticalCode(jdhVoucher.getVerticalCode());
            promiseUnFreezeCmd.setUnFreezeType(JdhProcessDataTypeEnum.PROCESS_PROMISE.getType());
            promiseApplication.unFreeze(promiseUnFreezeCmd);
            jdhVoucherRepository.save(jdhVoucher);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
