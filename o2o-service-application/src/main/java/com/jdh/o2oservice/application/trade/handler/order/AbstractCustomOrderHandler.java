package com.jdh.o2oservice.application.trade.handler.order;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseHistory;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.model.PromiseAppointmentTime;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.ScriptBo;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.UserPromisegoBo;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.support.query.BusinessModePageRequest;
import com.jdh.o2oservice.export.trade.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.geotools.referencing.wkt.Symbols;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description AbstractCustomOrderHandler
 * @Date 2024/9/2 下午5:09
 * <AUTHOR>
 **/
@Slf4j
public abstract class AbstractCustomOrderHandler {

    @Resource
    private JdhSkuRepository jdhSkuRepository;

    @Resource
    private ProductApplication productApplication;

    // 待预约状态
    List<Integer> waitPromiseStatus = Arrays.asList(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus(), JdhPromiseStatusEnum.CANCEL_SUCCESS.getStatus());

    // 排除订单状态（待付款、已取消、退款中、已退款）1,7,8,9
    List<Integer> rejectOrderStatusList = Arrays.asList(OrderStatusEnum.ORDER_WAIT_PAY.getStatus(), OrderStatusEnum.ORDER_CANCEL.getStatus()
            , OrderStatusEnum.ORDER_REFUNDING.getStatus(), OrderStatusEnum.ORDER_REFUND.getStatus());

    // care、test服务类型
    List<String> careTestServiceType =  Arrays.asList(ServiceTypeEnum.CARE.getServiceType(),ServiceTypeEnum.TEST.getServiceType());

    // loc 体检，医美，口腔，疫苗
    List<String> locServiceType = Arrays.asList(ServiceTypeEnum.PHYSICAL.getServiceType(), ServiceTypeEnum.COSMETIC.getServiceType()
            , ServiceTypeEnum.ORAL_CAVITY.getServiceType(), ServiceTypeEnum.VACCINE.getServiceType());

    public JdhPromise selectProcessTrackOptimalPromise(CustomOrderHandlerContext receipt){
        // 履约单
        List<JdhPromise> promiseList = receipt.getSourceVoucherIdMappingPromiseMap().get(receipt.getOrderIdMappingSourceVoucherIdMap().get(receipt.getOrder().getOrderId()));
        log.info("AbstractCustomOrderHandler selectProcessTrackOptimalPromise promiseList={}", JSON.toJSONString(promiseList));
        if (CollectionUtils.isEmpty(promiseList)){
            return null;
        }
        Map<Long, JdhPromise> promiseMap = promiseList.stream().collect(Collectors.toMap(JdhPromise::getPromiseId, Function.identity(), (key1, key2) -> key2));
        Map<Long, Integer> sortPromiseMap = new HashMap<>();
        for (JdhPromise promise : promiseList) {
            // 检验单状态
            List<Integer> medicalPromiseStatus = this.getMedicalPromiseStatusByPromiseId(receipt, promise.getPromiseId());
            if (!rejectOrderStatusList.contains(receipt.getOrder().getOrderStatus()) && JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus().equals(promise.getPromiseStatus())){
                log.info("AbstractCustomOrderHandler selectProcessTrackOptimalPromise 待使用-待护士接单 orderId={}", receipt.getOrder().getOrderId());
                // 待使用-待护士接单 !include(seq.list(1,7,8,9),orderStatus) && include(seq.list(2),promiseStatus)
                sortPromiseMap.put(promise.getPromiseId(), NumConstant.NUM_5);
                continue;
            }else if (!rejectOrderStatusList.contains(receipt.getOrder().getOrderStatus()) &&  Arrays.asList(JdhPromiseStatusEnum.APPOINTMENT_SUCCESS.getStatus(),JdhPromiseStatusEnum.SERVICE_READY.getStatus()).contains(promise.getPromiseStatus())){
                log.info("AbstractCustomOrderHandler buildNurseVisitTestInfo 待使用-待护士上门 orderId={}", receipt.getOrder().getOrderId());
                // 待使用-待护士上门 !include(seq.list(1,7,8,9),orderStatus) && promiseStatus == 3 !include(seq.list(1,7,8,9),orderStatus) && promiseStatus == 15
                sortPromiseMap.put(promise.getPromiseId(), NumConstant.NUM_4);
                continue;

            }else if (!rejectOrderStatusList.contains(receipt.getOrder().getOrderStatus()) && JdhPromiseStatusEnum.SERVICING.getStatus().equals(promise.getPromiseStatus())){
                log.info("AbstractCustomOrderHandler selectProcessTrackOptimalPromise 待使用-护士服务中 orderId={}", receipt.getOrder().getOrderId());
                // 待使用-护士服务中 !include(seq.list(1,7,8,9),orderStatus) && promiseStatus == 16
                sortPromiseMap.put(promise.getPromiseId(), NumConstant.NUM_3);
                continue;

            }else if (!rejectOrderStatusList.contains(receipt.getOrder().getOrderStatus()) && JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus().equals(promise.getPromiseStatus())
                    && !medicalPromiseStatus.contains(4) && !medicalPromiseStatus.contains(5) && !medicalPromiseStatus.contains(6)){
                log.info("AbstractCustomOrderHandler selectProcessTrackOptimalPromise 待使用-送检中 orderId={}", receipt.getOrder().getOrderId());
                // 待使用-送检中 !include(seq.list(1,7,8,9),orderStatus) && promiseStatus == 17 && !include(medPromiseStatusList,4) && !include(medPromiseStatusList,5) && !include(medPromiseStatusList,6)
                sortPromiseMap.put(promise.getPromiseId(), NumConstant.NUM_2);
                continue;

            }else if (!rejectOrderStatusList.contains(receipt.getOrder().getOrderStatus()) && JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus().equals(promise.getPromiseStatus())
                    && medicalPromiseStatus.contains(4) && !medicalPromiseStatus.contains(5) && !medicalPromiseStatus.contains(6)){
                log.info("AbstractCustomOrderHandler selectProcessTrackOptimalPromise 待使用-实验室检测中 orderId={}", receipt.getOrder().getOrderId());
                // 待使用-实验室检测中 !include(seq.list(1,7,8,9),orderStatus) && promiseStatus == 17 && include(medPromiseStatusList,4) && !include(medPromiseStatusList,5) && !include(medPromiseStatusList,6)
                sortPromiseMap.put(promise.getPromiseId(), NumConstant.NUM_1);
                continue;

            }else if (!rejectOrderStatusList.contains(receipt.getOrder().getOrderStatus()) && (JdhPromiseStatusEnum.COMPLETE.getStatus().equals(promise.getPromiseStatus())
                    || medicalPromiseStatus.contains(5) || medicalPromiseStatus.contains(6))){
                log.info("AbstractCustomOrderHandler selectProcessTrackOptimalPromise 待使用-报告已生成 orderId={}", receipt.getOrder().getOrderId());
                // 待使用-报告已生成 !include(seq.list(1,7,8,9),orderStatus) && ( promiseStatus == 11 || include(medPromiseStatusList,5) || include(medPromiseStatusList,6) )
                sortPromiseMap.put(promise.getPromiseId(), NumConstant.NUM_0);
                continue;

            }
        }
        log.info("AbstractCustomOrderHandler selectProcessTrackOptimalPromise sortPromiseMap={}", JSON.toJSONString(sortPromiseMap));
        if (MapUtils.isNotEmpty(sortPromiseMap)){
            Long promiseId = getKeyWithMaxValue(sortPromiseMap);
            log.info("AbstractCustomOrderHandler selectProcessTrackOptimalPromise promiseId={}", promiseId);
            if (promiseId != null){
                return promiseMap.get(promiseId);
            }
        }
        return null;
    }

    public Long getKeyWithMaxValue(Map<Long, Integer> map) {
        Long maxKey = null;
        int maxValue = Integer.MIN_VALUE;

        for (Map.Entry<Long, Integer> entry : map.entrySet()) {
            if (entry.getValue() > maxValue) {
                maxKey = entry.getKey();
                maxValue = entry.getValue();
            }
        }
        return maxKey;
    }

    public List<Integer> getMedicalPromiseStatusByPromiseId(CustomOrderHandlerContext receipt, Long promiseId){
        // 检验单状态
        List<Integer> medicalPromiseStatus = new ArrayList<>();
        List<MedicalPromiseDTO> medicalPromiseList = receipt.getPromiseIdMappingMedicalPromiseMap().get(promiseId);
        if (CollectionUtils.isNotEmpty(medicalPromiseList)){
            medicalPromiseStatus = medicalPromiseList.stream().map(MedicalPromiseDTO::getStatus).distinct().collect(Collectors.toList());
        }
        log.info("AbstractCustomOrderHandler getMedicalPromiseStatusByPromiseId medicalPromiseStatus={}", JSON.toJSONString(medicalPromiseStatus));
        return medicalPromiseStatus;
    }


    /**
     * 构建标签
     * @param order
     * @param customOrderInfoConfig
     * @return
     */
    public CustomOrderInfoDTO buildCustomOrderLabelInfo(JdOrder order, JSONObject customOrderInfoConfig){
        // 自定义订单信息
        CustomOrderInfoDTO res = new CustomOrderInfoDTO();
        res.setOrderId(order.getOrderId());
        // 标签
        res.setLabelInfo(this.buildLabelInfo(order.getServiceType(), customOrderInfoConfig));
        return res;
    }


    /**
     * 构建服务流程有效期
     * @param promise
     * @return
     */
    public CustomOrderServiceMessageDTO buildServiceMessageExpireDate(JdhPromise promise) {
        CustomOrderServiceMessageDTO serviceMessageDTO = new CustomOrderServiceMessageDTO();
        Date expireDate = Date.from(promise.getExpireDate().atZone(ZoneId.systemDefault()).toInstant());
        serviceMessageDTO.setMsg("有效期至:" + TimeUtils.dateTimeToStr(expireDate, TimeFormat.SHORT_PATTERN_LINE));
        return serviceMessageDTO;
    }

    /**
     * 获取护士||骑手 姓名
     * @param angelWorks
     * @return
     */
    public String getAngelName(List<AngelWork> angelWorks) {
        if (CollectionUtils.isEmpty(angelWorks)){
            return "";
        }
        // 首字母掩码
        return "*"+angelWorks.get(0).getAngelName().substring(1);
    }

    /**
     * 构建icon标签
     * @param serviceType
     * @param customOrderInfoConfig
     * @return
     */
    public CustomOrderLabelInfoDTO buildLabelInfo(String serviceType, JSONObject customOrderInfoConfig) {
        JSONObject labelInfoObj = customOrderInfoConfig.getJSONObject("labelInfo");
        String serviceTypeText = JSON.parseObject(labelInfoObj.getString("text")).getString(serviceType);
        if (StringUtils.isBlank(serviceTypeText)){
            log.info("AbstractCustomOrderStrategy buildLabelInfo serviceTypeText empty");
            return null;
        }
        CustomOrderLabelInfoDTO labelInfo = JSON.parseObject(JSON.toJSONString(labelInfoObj), CustomOrderLabelInfoDTO.class);
        labelInfo.setText(serviceTypeText);
        return labelInfo;
    }

    /**
     * 待护士接单
     * 正在通知护士接单，预计 <span id='appointTitleId'>{angelWorkReceiveTime}</span> 前接单
     * @param promiseHistories
     * @param processTrack
     * @return
     */
    public String getAngelWorkReceiveTime(List<JdhPromiseHistory> promiseHistories, CustomOrderProcessTrackDTO processTrack, JdhPromise promise){
        DuccConfig duccConfig = SpringUtil.getBean(DuccConfig.class);
        Map<String, Map<String, JSONObject>> dispatchDetailDurationMap = duccConfig.getDispatchDetailDurationMap();
        BusinessContext businessContext = new BusinessContext();
        businessContext.setServiceType(promise.getServiceType());
        businessContext.setVerticalCode(promise.getVerticalCode());
        businessContext.initVertical();
        Map<String, JSONObject> durationMap = dispatchDetailDurationMap.get(businessContext.getVerticalBusiness().getBusinessModeCode());
        if(MapUtils.isEmpty(durationMap)) {
            durationMap = dispatchDetailDurationMap.get("angelCare");
        }
        JSONObject jsonObject = BooleanUtils.isTrue(promise.getAppointmentTime().getIsImmediately()) ? durationMap.get("1") : durationMap.get("2");

        List<JdhPromiseHistory> promiseHistoryList = promiseHistories.stream().filter(ele -> JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus()
                .equals(ele.getAfterStatus())).sorted(Comparator.comparing(JdhPromiseHistory::getCreateTime).reversed()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(promiseHistoryList)) {
            log.info("[AbstractCustomOrderHandler -> getAngelWorkReceiveTime],没有预约中的履约单记录。");
            return null;
        }

        JdhPromiseHistory jdhPromiseHistory = promiseHistoryList.get(0);
        LocalDateTime promiseDateTime = TimeUtils.dateToLocalDateTime(jdhPromiseHistory.getCreateTime());
        LocalTime promiseTime = LocalTime.of(promiseDateTime.getHour(), promiseDateTime.getMinute());

        LocalTime startTime = LocalTime.parse(jsonObject.getString("startTime"), TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter);
        LocalTime endTime = LocalTime.parse(jsonObject.getString("endTime"), TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter);

        log.info("[AbstractCustomOrderHandler -> getAngelWorkReceiveTime],promiseTime={}, startTime={}, endTime={}",
                JSON.toJSONString(promiseTime), JSON.toJSONString(startTime), JSON.toJSONString(endTime));

        int dynamicCursorMinutes;
        if (Objects.isNull(jsonObject.getInteger("durationMinute"))){
            dynamicCursorMinutes = 30;
        }else {
            dynamicCursorMinutes = jsonObject.getInteger("durationMinute");
        }

        String timeStr;
        String msgTemp;
        if(promiseTime.isAfter(startTime.minusMinutes(dynamicCursorMinutes)) && promiseTime.isBefore(endTime)){
            promiseDateTime = promiseDateTime.plusMinutes(dynamicCursorMinutes);
            timeStr =  promiseDateTime.format(TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter);
            msgTemp = processTrack.getMessage();
        }else if(promiseTime.isAfter(endTime)){
            promiseDateTime = promiseDateTime.plusDays(CommonConstant.ONE);
            timeStr = startTime.format(TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter);
            msgTemp = processTrack.getMessageNight();
        }else {
            timeStr = startTime.format(TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter);
            msgTemp = processTrack.getMessageNight();
        }
        log.info("[AbstractCustomOrderHandler -> getAngelWorkReceiveTime],promiseDateTime={},promiseTime={}, startTime={}, endTime={}",
                JSON.toJSONString(promiseDateTime), JSON.toJSONString(promiseTime), JSON.toJSONString(startTime), JSON.toJSONString(endTime));

        log.info("[AbstractCustomOrderHandler -> getAngelWorkReceiveTime],LocalDateTimeMin={},LocalDateTimeMax={}", JSON.toJSONString(LocalDateTime.MIN), JSON.toJSONString(LocalDateTime.MAX));
        LocalDate today = LocalDate.now();
        if(promiseDateTime.isAfter(LocalDateTime.of(today, LocalTime.MIN)) && promiseDateTime.isBefore(LocalDateTime.of(today, LocalTime.MAX))){
            buildPromiseCurrNodeServiceTime(promise, TimeUtils.timeStrToDate(today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " " + timeStr + ":00", TimeFormat.LONG_PATTERN_LINE), null);
            return String.format(msgTemp, "[今天]" + timeStr);
        }else if(promiseDateTime.isAfter(LocalDateTime.of(today, LocalTime.MIN).plusDays(CommonConstant.ONE)) && promiseDateTime.isBefore(LocalDateTime.of(today, LocalTime.MAX).plusDays(CommonConstant.ONE))){
            buildPromiseCurrNodeServiceTime(promise, TimeUtils.timeStrToDate(today.plusDays(CommonConstant.ONE).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " " + timeStr + ":00", TimeFormat.LONG_PATTERN_LINE), null);
            return String.format(msgTemp, "[明天]" + timeStr);
        }else{
            buildPromiseCurrNodeServiceTime(promise, TimeUtils.localDateTimeToDate(promiseDateTime), null);
            return String.format(msgTemp, TimeUtils.localDateTimeToStr(promiseDateTime,TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE_NO_SSS));
        }
    }


    /**
     * 待骑手接单
     * 预估服务者接单时间  提交派单时间 + 30分钟
     * dynamicCursorMinutes=30
     * 正在通知护士接单，预计 <span id='appointTitleId'>{angelWorkReceiveTime}</span> 前接单
     * @param promiseHistories
     * @param promise
     * @return
     */
    public String getAngelRiderWorkReceiveTime(List<JdhPromiseHistory> promiseHistories, JdhPromise promise, Integer dynamicCursorMinutes){
        if (dynamicCursorMinutes == null){
            dynamicCursorMinutes = 30;
        }
        Date now = new Date();
        Date tomorrow = DateUtil.tomorrow().toJdkDate();
        Date submitDispatchTime = promise.getUpdateTime();

        if (CollectionUtils.isNotEmpty(promiseHistories)){
            List<JdhPromiseHistory> promiseHistoryList = promiseHistories.stream().filter(ele -> JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus()
                    .equals(ele.getAfterStatus())).sorted(Comparator.comparing(JdhPromiseHistory::getCreateTime).reversed()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(promiseHistoryList)){
                submitDispatchTime = promiseHistoryList.get(0).getCreateTime();
            }
        }

        DateTime receiveTime = DateUtil.offset(submitDispatchTime, DateField.MINUTE, dynamicCursorMinutes);
        if(DateUtil.isSameDay(now,receiveTime.toJdkDate())){
            return "[今天]" + TimeUtils.dateTimeToStr(receiveTime,TimeFormat.DATE_PATTERN_HM_SIMPLE);
        }else if(DateUtil.isSameDay(tomorrow,receiveTime.toJdkDate())){
            return "[明天]" + TimeUtils.dateTimeToStr(receiveTime,TimeFormat.DATE_PATTERN_HM_SIMPLE);
        }else{
            return TimeUtils.dateTimeToStr(receiveTime,TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE_NO_SSS);
        }
    }

    /**
     * 待护士上门
     * 预估服务者上门日期
     * 预计将于<span id='appointTitleId'>[{angelHomeDate}]{angelHomeStartTime}-{angelHomeEndTime}</span>上门
     * @param promise
     * @return
     */
    public String getAngelHomeDate(JdhPromise promise){
        Date now = new Date();
        Date tomorrow = DateUtil.tomorrow().toJdkDate();
        //如果是今天，展示"今天"，否则yyyy-MM-dd 日期
        LocalDateTime appointmentStartTime = promise.getAppointmentTime().getAppointmentStartTime();
        if(DateUtil.isSameDay(now,TimeUtils.localDateTimeToDate(appointmentStartTime))){
            return "今天";
        }else if(DateUtil.isSameDay(tomorrow,TimeUtils.localDateTimeToDate(appointmentStartTime))){
            return "明天";
        }else{
            return TimeUtils.localDateTimeToStr(appointmentStartTime,TimeFormat.SHORT_PATTERN_LINE);
        }
    }


    /**
     * 待护士上门
     * 预估服务者上门开始时间
     * 立即预约 1 - 开始时间  预约开始时间 + 1h
     * 非立即预约 1 - 开始时间  预约开始时间
     * 预计将于<span id='appointTitleId'>[{angelHomeDate}]{angelHomeStartTime}-{angelHomeEndTime}</span>上门
     * @param promise
     * @param dynamicCursorMinutes
     * @return
     */
    public String getAngelHomeStartTime(JdhPromise promise, Integer dynamicCursorMinutes){
        PromiseAppointmentTime appointmentTime = promise.getAppointmentTime();
        Boolean isImmediately = appointmentTime.getIsImmediately();
        if(isImmediately){
            DateTime startTime = DateUtil.offset(TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()), DateField.MINUTE, dynamicCursorMinutes);
            buildPromiseCurrNodeServiceTime(promise, startTime, null);
            return TimeUtils.dateTimeToStr(startTime,TimeFormat.DATE_PATTERN_HM_SIMPLE);
        }else{
            buildPromiseCurrNodeServiceTime(promise, TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentStartTime()), null);
            return TimeUtils.localDateTimeToStr(appointmentTime.getAppointmentStartTime(),TimeFormat.DATE_PATTERN_HM_SIMPLE);
        }
    }

    /**
     * 待护士上门
     * 预估服务者上门结束时间
     * 立即预约   结束时间  预约结束时间 + 1h
     * 非理解预约 结束时间  预约结束时间
     * 预计将于<span id='appointTitleId'>[{angelHomeDate}]{angelHomeStartTime}-{angelHomeEndTime}</span>上门
     * @param promise
     * @param dynamicCursorMinutes
     * @return
     */
    public String getAngelHomeEndTime(JdhPromise promise, Integer dynamicCursorMinutes){
        PromiseAppointmentTime appointmentTime = promise.getAppointmentTime();
        Boolean isImmediately = appointmentTime.getIsImmediately();
        if(isImmediately){
            DateTime startTime = DateUtil.offset(TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentEndTime()), DateField.MINUTE, dynamicCursorMinutes);
            buildPromiseCurrNodeServiceTime(promise, null, startTime);
            return TimeUtils.dateTimeToStr(startTime,TimeFormat.DATE_PATTERN_HM_SIMPLE);
        }else{
            buildPromiseCurrNodeServiceTime(promise, null, TimeUtils.localDateTimeToDate(appointmentTime.getAppointmentEndTime()));
            return TimeUtils.localDateTimeToStr(appointmentTime.getAppointmentEndTime(),TimeFormat.DATE_PATTERN_HM_SIMPLE);
        }
    }

    /**
     * 护士服务中
     * 预估服务者服务完成日期 如果是今天，展示"今天"，否则yyyy-MM-dd 日期
     * 预计将于<span id='appointTitleId'>[{angelServiceCompleteDate}]{angelServiceCompleteTime}</span>前完成服务
     * @param promiseHistories
     * @param promise
     * @return
     */
    public String getAngelServiceCompleteDate(List<JdhPromiseHistory> promiseHistories, JdhPromise promise){
        Date now = new Date();
        Date tomorrow = DateUtil.tomorrow().toJdkDate();
        Optional<JdhPromiseHistory> jdhPromiseHistory = promiseHistories.stream()
                .filter(ele -> JdhPromiseStatusEnum.SERVICING.getStatus().equals(ele.getAfterStatus()))
                .findFirst();
        Date servicingTime = promise.getUpdateTime();
        if(jdhPromiseHistory.isPresent()){
            JdhPromiseHistory history = jdhPromiseHistory.get();
            servicingTime = history.getCreateTime();
        }
        if(DateUtil.isSameDay(now,servicingTime)){
            return "今天";
        }else if(DateUtil.isSameDay(tomorrow,servicingTime)){
            return "明天";
        }else{
            return TimeUtils.dateTimeToStr(servicingTime,TimeFormat.SHORT_PATTERN_LINE);
        }
    }

    /**
     * 护士服务中
     * 预估服务者服务完成时间  变更服务中时间 + （所有sku服务时长总和）× 服务人数
     * 预计将于<span id='appointTitleId'>[{angelServiceCompleteDate}]{angelServiceCompleteTime}</span>前完成服务
     * @param promiseHistories
     * @param promise
     * @param serviceDuration
     * @return
     */
    public String getAngelServiceCompleteTime(List<JdhPromiseHistory> promiseHistories, JdhPromise promise, Integer serviceDuration){
        Optional<JdhPromiseHistory> jdhPromiseHistory = promiseHistories.stream()
                .filter(ele -> JdhPromiseStatusEnum.SERVICING.getStatus().equals(ele.getAfterStatus()))
                .findFirst();
        Date servicingTime = promise.getUpdateTime();
        if(jdhPromiseHistory.isPresent()){
            JdhPromiseHistory history = jdhPromiseHistory.get();
            servicingTime = history.getCreateTime();
        }
        int totalServiceTime = serviceDuration;
        long promisePatientNums = 1L;
        List<JdhPromisePatient> patients = promise.getPatients();
        log.info("AbstractCustomOrderHandler getAngelServiceCompleteTime patients={}", JSON.toJSONString(patients));
        if (CollectionUtils.isNotEmpty(patients)){
            promisePatientNums = patients.size();
        }
        DateTime completeTime = DateUtil.offset(servicingTime, DateField.MINUTE, (int) (totalServiceTime * promisePatientNums));
        buildPromiseCurrNodeServiceTime(promise, null, completeTime);
        return TimeUtils.dateTimeToStr(completeTime,TimeFormat.DATE_PATTERN_HM_SIMPLE);
    }

    /**
     * 送检中
     * 预估送检时间  服务变更完成时间 + 1小时
     * 样本正在送往实验室，预计<span id='appointTitleId'>{submitTestTime}</span>前送达
     * @param promiseHistories
     * @param promise
     * @param dynamicCursorMinutes
     * @return
     */
    public String getSubmitTestTime(List<JdhPromiseHistory> promiseHistories, JdhPromise promise, Integer dynamicCursorMinutes){
        if (dynamicCursorMinutes == null){
            dynamicCursorMinutes = 60;
        }
        Optional<JdhPromiseHistory> jdhPromiseHistory = promiseHistories.stream()
                .filter(ele -> JdhPromiseStatusEnum.SERVICE_COMPLETE.getStatus().equals(ele.getAfterStatus()))
                .findFirst();
        Date servicingCompleteTime = promise.getUpdateTime();
        if(jdhPromiseHistory.isPresent()){
            JdhPromiseHistory history = jdhPromiseHistory.get();
            servicingCompleteTime = history.getCreateTime();
        }

        DateTime submitTestTime = DateUtil.offset(servicingCompleteTime, DateField.MINUTE, dynamicCursorMinutes);
        buildPromiseCurrNodeServiceTime(promise, null, submitTestTime);
        return TimeUtils.dateTimeToStr(submitTestTime,TimeFormat.DATE_PATTERN_HM_SIMPLE);
    }

    /**
     * 实验室检测中
     * 预估出报告时间
     * 取实验室检测单变更检测中时间 + 实验室检测项检测时长总和（多个检测单，取最长）
     * 实验室检测中，预计<span id='appointTitleId'>{reportTime}</span>前出报告
     * @param medicalPromiseList
     * @return
     */
    public String getReportTime(JdhPromise promise, List<MedicalPromiseDTO> medicalPromiseList) {
        Date reportedTime = null;
        for (MedicalPromiseDTO medicalPromiseDTO : medicalPromiseList) {
            Date checkTime = medicalPromiseDTO.getCheckTime();
            Integer testDuration = medicalPromiseDTO.getTestDuration();
            if (Objects.nonNull(checkTime) && Objects.nonNull(testDuration)) {
                DateTime offset = DateUtil.offset(checkTime, DateField.MINUTE, testDuration);
                if (Objects.isNull(reportedTime)) {
                    reportedTime = offset;
                } else {
                    reportedTime = reportedTime.getTime() > offset.getTime() ? reportedTime : offset;
                }
            }
        }
        buildPromiseCurrNodeServiceTime(promise, null, reportedTime);
        return TimeUtils.dateTimeToStr(reportedTime, TimeFormat.DATE_PATTERN_HM_SIMPLE);
    }


    /**
     * 待护士接单-服务流程
     * @param promiseHistories
     * @param promise
     * @param waitNurseReceiveOrderObj
     * @return
     */
    public CustomOrderProcessTrackDTO buildWaitNurseReceiveOrderProcessTrack(List<JdhPromiseHistory> promiseHistories, JdhPromise promise, JSONObject waitNurseReceiveOrderObj) {
        CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSON.toJSONString(waitNurseReceiveOrderObj), CustomOrderProcessTrackDTO.class);
        // 正在通知护士接单，预计 <span id='appointTitleId'>{angelWorkReceiveTime}</span> 前接单
        String angelWorkReceiveTime = this.getAngelWorkReceiveTime(promiseHistories, processTrack, promise);
        processTrack.setMessage(angelWorkReceiveTime);
        return processTrack;
    }
    /**
     * 待护士接单-服务流程
     * @param promiseHistories
     * @param promise
     * @param waitNurseReceiveOrderObj
     * @return
     */
    public CustomOrderProcessTrackDTO buildRiderWaitNurseReceiveOrderProcessTrack(List<JdhPromiseHistory> promiseHistories, JdhPromise promise, JSONObject waitNurseReceiveOrderObj) {
        CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSON.toJSONString(waitNurseReceiveOrderObj), CustomOrderProcessTrackDTO.class);
        // 正在通知护士接单，预计 <span id='appointTitleId'>{angelWorkReceiveTime}</span> 前接单
        String angelWorkReceiveTime = this.getAngelRiderWorkReceiveTime(promiseHistories, promise, waitNurseReceiveOrderObj.getInteger("dynamicCursorMinutes"));
        processTrack.setMessage(String.format(processTrack.getMessage(), angelWorkReceiveTime));
        return processTrack;
    }

    /**
     * 待护士上门-服务流程
     * @param angelWorks
     * @param promise
     * @param waitNurseVisitObj
     * @return
     */
    public CustomOrderProcessTrackDTO buildWaitNurseVisitProcessTrack(List<AngelWork> angelWorks, JdhPromise promise, JSONObject waitNurseVisitObj) {
        CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSON.toJSONString(waitNurseVisitObj), CustomOrderProcessTrackDTO.class);
        // 预计将于<span id='appointTitleId'>[{angelHomeDate}]{angelHomeStartTime}-{angelHomeEndTime}</span>上门

        String angelHomeDate = this.getAngelHomeDate(promise);
        String angelHomeStartTime = this.getAngelHomeStartTime(promise, waitNurseVisitObj.getInteger("dynamicCursorMinutes"));
        log.info("buildWaitNurseVisitProcessTrack promise={}, angelHomeStartTime={}", JSON.toJSONString(promise), angelHomeStartTime);
        String angelHomeEndTime = this.getAngelHomeEndTime(promise, waitNurseVisitObj.getInteger("dynamicCursorMinutes"));
        log.info("buildWaitNurseVisitProcessTrack angelHomeEndTime={}", angelHomeEndTime);
        String angelTime = angelHomeDate + " " + angelHomeStartTime + "-" + angelHomeEndTime;
        // 护士姓名
        String angelName = this.getAngelName(angelWorks);
        processTrack.setMessage(String.format(processTrack.getMessage(), angelTime, angelName));
        return processTrack;
    }

    /**
     * 护士服务中-服务流程
     * @param promiseHistories
     * @param angelWorks
     * @param promise
     * @param waitServiceProgressObj
     * @return
     */
    public CustomOrderProcessTrackDTO buildWaitServiceProgressProcessTrack(List<JdhPromiseHistory> promiseHistories, List<AngelWork> angelWorks, JdhPromise promise
            , Integer serviceDuration, JSONObject waitServiceProgressObj) {
        CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSON.toJSONString(waitServiceProgressObj), CustomOrderProcessTrackDTO.class);


        // 预计将于<span id='appointTitleId'>[{angelServiceCompleteDate}]{angelServiceCompleteTime}</span>前完成服务
        String angelServiceCompleteTime = this.getAngelServiceCompleteTime(promiseHistories, promise, serviceDuration);
        String angelServiceCompleteDate = this.getAngelServiceCompleteDate(promiseHistories, promise);
        String angelTime = angelServiceCompleteDate + " " + angelServiceCompleteTime;
        // 护士姓名
        String angelName = this.getAngelName(angelWorks);
        processTrack.setMessage(String.format(processTrack.getMessage(), angelName, angelTime));
        return processTrack;
    }


    /**
     * 送检中-服务流程
     * @param promiseHistories
     * @param promise
     * @param underInspectionObj
     * @return
     */
    public CustomOrderProcessTrackDTO buildUnderInspectionProcessTrack(List<JdhPromiseHistory> promiseHistories, JdhPromise promise, JSONObject underInspectionObj) {
        CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSON.toJSONString(underInspectionObj), CustomOrderProcessTrackDTO.class);


        // 样本正在送往实验室，预计<span id='appointTitleId'>{submitTestTime}</span>前送达
        String submitTestTime = this.getSubmitTestTime(promiseHistories, promise, underInspectionObj.getInteger("dynamicCursorMinutes"));
        processTrack.setMessage(String.format(processTrack.getMessage(), submitTestTime));
        return processTrack;
    }

    /**
     * 实验室检测中-服务流程
     * @param medicalPromiseList
     * @param laboratoryTestingObj
     * @return
     */
    public CustomOrderProcessTrackDTO buildLaboratoryTestingProcessTrack(JdhPromise promise, List<MedicalPromiseDTO> medicalPromiseList, JSONObject laboratoryTestingObj) {
        CustomOrderProcessTrackDTO processTrack = JSON.parseObject(JSON.toJSONString(laboratoryTestingObj), CustomOrderProcessTrackDTO.class);


        // 实验室检测中，预计<span id='appointTitleId'>{reportTime}</span>前出报告
        String reportTime = this.getReportTime(promise, medicalPromiseList);
        processTrack.setMessage(String.format(processTrack.getMessage(), reportTime));
        return processTrack;
    }

    /**
     * 构建自定义透传对象
     * @param jsonObject jsonObject
     */
    public void buildCustomOrderTransferInfoAllServiceNodeInfoDTO(JSONObject jsonObject, CustomOrderTransferInfoDTO customOrderTransferInfoDTO) {
        if (jsonObject == null) {
            return;
        }
        List<CustomOrderTransferInfoDTO> customOrderTransferInfoDTOList = new ArrayList<>();
        jsonObject.forEach((key, value) -> {
            if (value instanceof JSONObject) {
                CustomOrderTransferInfoDTO customOrderTransfer = new CustomOrderTransferInfoDTO();
                if(((JSONObject) value).containsKey("aggregateType")) {
                    customOrderTransfer.setCurrServiceNodeType(((JSONObject) value).getInteger("aggregateType"));
                }
                if(((JSONObject) value).containsKey("aggregateStatus")) {
                    customOrderTransfer.setCurrServiceNodeEnName(((JSONObject) value).getString("aggregateStatus"));
                }
                if(((JSONObject) value).containsKey("messageTitle")) {
                    customOrderTransfer.setCurrServiceNodeCnName(((JSONObject) value).getString("messageTitle"));
                }
                customOrderTransferInfoDTOList.add(customOrderTransfer);
            }
        });
        customOrderTransferInfoDTO.setAllServiceNodeInfo(customOrderTransferInfoDTOList);
    }

    /**
     * 构建自定义透传对象
     * @param jsonObject jsonObject
     */
    public void buildCustomOrderTransferInfoDTO(JSONObject jsonObject, CustomOrderInfoDTO res, CustomOrderTransferInfoDTO customOrderTransferInfoDTO, JdhPromise promise) {
        if (jsonObject == null) {
            return;
        }
        if(jsonObject.containsKey("aggregateType")) {
            customOrderTransferInfoDTO.setCurrServiceNodeType(jsonObject.getInteger("aggregateType"));
        }
        if(jsonObject.containsKey("aggregateStatus")) {
            customOrderTransferInfoDTO.setCurrServiceNodeEnName(jsonObject.getString("aggregateStatus"));
        }
        if(jsonObject.containsKey("messageTitle")) {
            customOrderTransferInfoDTO.setCurrServiceNodeCnName(jsonObject.getString("messageTitle"));
        }
        if(res != null && res.getProcessTrack() != null) {
            customOrderTransferInfoDTO.setCurrServiceNodeDesc(res.getProcessTrack().getMessage());
        }
        if (promise != null) {
            if (promise.getAppointmentTime() != null) {
                String startTime = TimeUtils.localDateTimeToStr(promise.getAppointmentTime().getAppointmentStartTime(), TimeFormat.LONG_PATTERN_LINE);
                String endTime = TimeUtils.localDateTimeToStr(promise.getAppointmentTime().getAppointmentEndTime(), TimeFormat.LONG_PATTERN_LINE);
                customOrderTransferInfoDTO.setAppointmentServiceStartTime(startTime);
                customOrderTransferInfoDTO.setAppointmentServiceEndTime(endTime);
                customOrderTransferInfoDTO.setAppointmentServiceTime(startTime + "~" + endTime);
            }
            UserPromisegoBo userPromisegoBo = promise.getUserPromisegoBo();
            if (userPromisegoBo != null) {
                if (userPromisegoBo.getTermScript() != null) {
                    Long duration = userPromisegoBo.getTermScript().getDuration();
                    Long buffer = userPromisegoBo.getTermScript().getBuffer();
                    Date endTimeStart = userPromisegoBo.getTermScript().getBeginTime();
                    Date endTimeEnd = userPromisegoBo.getTermScript().getEndTime();
                    if (buffer != null && duration != null && endTimeStart != null) {
                        endTimeStart = DateUtil.offset(userPromisegoBo.getTermScript().getBeginTime(), DateField.SECOND, Math.toIntExact(duration-buffer)).toJdkDate();
                        // 如果buffer为0说明是时间点，不传结束时间
                        if (buffer == 0) {
                            endTimeEnd = null;
                        } else {
                            endTimeEnd = DateUtil.offset(userPromisegoBo.getTermScript().getBeginTime(), DateField.SECOND, Math.toIntExact(duration + buffer)).toJdkDate();
                        }
                    }
                    customOrderTransferInfoDTO.setServiceFinishTime(buildServiceTime(endTimeStart, endTimeEnd, "~"));
                }
                if (userPromisegoBo.getCurrScript() != null) {
                    Long duration = userPromisegoBo.getCurrScript().getDuration();
                    Long buffer = userPromisegoBo.getCurrScript().getBuffer();
                    Date endTimeStart = userPromisegoBo.getCurrScript().getBeginTime();
                    Date endTimeEnd = userPromisegoBo.getCurrScript().getEndTime();
                    if (buffer != null && duration != null && endTimeStart != null) {
                        endTimeStart = DateUtil.offset(userPromisegoBo.getCurrScript().getBeginTime(), DateField.SECOND, Math.toIntExact(duration-buffer)).toJdkDate();
                        // 如果buffer为0说明是时间点，不传结束时间
                        if (buffer == 0) {
                            endTimeEnd = null;
                        } else {
                            endTimeEnd = DateUtil.offset(userPromisegoBo.getCurrScript().getBeginTime(), DateField.SECOND, Math.toIntExact(duration + buffer)).toJdkDate();
                        }
                    }
                    customOrderTransferInfoDTO.setCurrServiceNodeFinishTime(buildServiceTime(endTimeStart, endTimeEnd, "~"));
                }
            }
        }
    }

    /**
     * 服务已完成
     * @return
     */
    public CustomOrderProcessTrackDTO buildServiceFinishProcessTrackHome(JSONObject jsonObject) {
        return JSON.parseObject(JSON.toJSONString(jsonObject), CustomOrderProcessTrackDTO.class);
    }

    /**
     * 拼接服务时间
     * @param start
     * @param end
     * @param dividedSymbols
     * @return
     */
    public String buildServiceTime(Date start, Date end, String dividedSymbols) {
        if (StringUtils.isBlank(dividedSymbols)) {
            dividedSymbols = "~";
        }
        if (start != null && end != null) {
            return TimeUtils.dateTimeToStr(start, TimeFormat.LONG_PATTERN_LINE) + dividedSymbols + TimeUtils.dateTimeToStr(end, TimeFormat.LONG_PATTERN_LINE);
        }
        if (start != null) {
            return TimeUtils.dateTimeToStr(start, TimeFormat.LONG_PATTERN_LINE);
        }
        if (end != null) {
            return TimeUtils.dateTimeToStr(end, TimeFormat.LONG_PATTERN_LINE);
        }
        return null;
    }

    /**
     *
     * @param promise
     * @param start
     * @param end
     */
    public void buildPromiseCurrNodeServiceTime(JdhPromise promise, Date start, Date end) {
        if (start == null && end == null) {
            return;
        }
        if (promise != null) {
            UserPromisegoBo userPromisegoBo = promise.getUserPromisegoBo();
            if (userPromisegoBo == null) {
                userPromisegoBo = new UserPromisegoBo();
            }
            ScriptBo currScriptBo = userPromisegoBo.getCurrScript();
            if (currScriptBo == null) {
                currScriptBo = new ScriptBo();
            }
            if (start != null) {
                currScriptBo.setBeginTime(start);
            }
            if (end != null) {
                currScriptBo.setEndTime(end);
            }
            userPromisegoBo.setCurrScript(currScriptBo);
            promise.setUserPromisegoBo(userPromisegoBo);
        }

    }

    /**
     * 定列文本替换
     * @param receipt
     * @param res
     * @param scene
     */
    public void customOrderReplaceWords(CustomOrderHandlerContext receipt, CustomOrderInfoDTO res, String scene){
        try {
            List<JdOrderItem> jdOrderItems = receipt.getOrderIdMappingOrderItemMap().get(receipt.getOrder().getOrderId());
            if (CollectionUtils.isEmpty(jdOrderItems)){
                log.info("AbstractCustomOrderHandler customOrderReplaceWords jdOrderItems empty");
                return;
            }
            Long skuId = jdOrderItems.get(0).getSkuId();
            JdhSku jdhSku = jdhSkuRepository.find(JdhSkuIdentifier.builder().skuId(skuId).build());
            if (Objects.isNull(jdhSku)){
                log.info("AbstractCustomOrderHandler customOrderReplaceWords jdhSku empty");
                return;
            }
            CustomOrderProcessTrackDTO processTrack = res.getProcessTrack();
            if (Objects.nonNull(processTrack)){
                if (StringUtils.isNotBlank(processTrack.getMessage())){
                    processTrack.setMessage(productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), scene, processTrack.getMessage()));
                }
                if (StringUtils.isNotBlank(processTrack.getMessageTitle())){
                    processTrack.setMessageTitle(productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), scene, processTrack.getMessageTitle()));
                }
                if (StringUtils.isNotBlank(processTrack.getMessageNight())){
                    processTrack.setMessageNight(productApplication.replaceWordsByServiceType(jdhSku.getServiceType(), scene, processTrack.getMessageNight()));
                }
            }
            res.setProcessTrack(processTrack);
        } catch (Exception e) {
            log.error("AbstractCustomOrderHandler customOrderReplaceWords error e");
        }
    }

}
