package com.jdh.o2oservice.export.ztools.query;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @description Ai解读
 */
@Data
public class AiReportReadRequest {

    private Integer version;//版本号

    private String keyWordFomart="以下是一位用户的检测报告结果，请给予报告结果解读和针对性建议，限制在 100 个字内，给出最重要最精华的内容，相关报告数据内容和患者的信息，我都会单独呈现给用户，无需重复说明；避免一些非常常规的建议，给出有用、易于执行，有利于改善症状的建议，但不能包括用药建议。该解读面向用户，请让用户觉得眼前一亮，觉得你的建议有用，不是敷衍：%s";//prompt
}
