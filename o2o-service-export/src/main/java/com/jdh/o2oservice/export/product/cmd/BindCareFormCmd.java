package com.jdh.o2oservice.export.product.cmd;

import com.jdh.o2oservice.export.product.dto.careform.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Data
public class BindCareFormCmd {

    private Long serviceItemId;//服务项目id

    private String userName;//操作者

    private PreReceiveAssessmentDto preReceiveAssessment;//接单前评估

    private PreServiceAssessmentDto preServiceAssessment;//服务前评估

    private PreServiceSignatureDto preServiceSignature;//知情签字

    private ServiceRecordDto serviceRecord;//服务记录

    private HealthEduDto healthEdu;//健康宣教

    private SignConfirmDto signConfirm;//签字确认

}
