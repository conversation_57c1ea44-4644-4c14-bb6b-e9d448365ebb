package com.jdh.o2oservice.export.product;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.product.dto.BizCategoryDTO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.JdhSkuItemRelDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.BizCategoryRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuServiceItemRelPageRequest;
import com.jdh.o2oservice.export.product.query.JdhSkuServiceItemRelRequest;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 商品服务项读服务
 *
 * <AUTHOR>
 * @date 2024/04/25
 */
public interface ProductServiceItemReadExport {

    /**
     * 根据标准项目集合查询多个商品信息
     *
     * @param jdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest
     * @return maps
     */
    Response<Map<Long, List<JdhSkuDto>>> queryMultiSkuByServiceItem(JdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest);

    /**
     * 根据标准项目集合及地址查询多个商品信息
     *
     * @param jdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest
     * @return maps
     */
    Response<Map<Long, List<JdhSkuDto>>> queryMultiSkuByServiceItemAndAddress(JdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest);

    /**
     * 分页查询商品项目关联关系
     *
     * @param request request
     * @return page
     */
    Response<PageDto<JdhSkuItemRelDto>> queryPageSkuServiceItemRel(@RequestBody JdhSkuServiceItemRelPageRequest request);

    /**
     * 查询指标一级类目
     * 查询表：jdh_common_biz_category
     * @param bizCategoryListRequest
     * @return
     */
    Response<List<BizCategoryDTO>> queryCommonIndicatorCategory(BizCategoryRequest bizCategoryListRequest);

    /**
     * 根据查询条件分页获取服务项列表。
     * @param serviceItemQuery 查询条件对象，包含查询参数。
     * @return 分页后的服务项列表。
     */
    Response<PageDto<ServiceItemDto>> queryServiceItemPage(ServiceItemQuery serviceItemQuery);

    /**
     * 根据标准项目集合及地址查询多个商品信息
     * @param jdhSkuServiceItemRelRequest
     * @return
     */
    Response<Map<Long, List<JdhSkuDto>>> batchQueryMultiSkuByServiceItemAndAddress(JdhSkuServiceItemRelRequest jdhSkuServiceItemRelRequest);

}