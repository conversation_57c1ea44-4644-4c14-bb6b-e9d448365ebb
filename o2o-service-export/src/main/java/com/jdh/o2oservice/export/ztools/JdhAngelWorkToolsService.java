package com.jdh.o2oservice.export.ztools;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.query.AutoBotsO2ORequest;
import com.jdh.o2oservice.export.ztools.cmd.AngelWorkExportFileCmd;
import com.jdh.o2oservice.export.ztools.cmd.FlushShipCmd;
import com.jdh.o2oservice.export.ztools.dto.FindJimParam;
import com.jdh.o2oservice.export.ztools.query.AngelWorkPageRequest;

/**
 * @ClassName JdhAngelWorkToolsService
 * @Description 工单工具类
 * <AUTHOR>
 * @Date 2024/9/7 14:47
 */
public interface JdhAngelWorkToolsService {

    /**
     * 工单导出文件
     *
     * @param angelWorkExportFileCmd
     * @return
     */
    Response<Boolean> exportFile(AngelWorkExportFileCmd angelWorkExportFileCmd);

    /**
     * 执行groovy脚本语言
     *
     * @param scriptName
     * @return
     */
    Response<Boolean> executeGroovyScript(String scriptName);

    /**
     *
     * @param flushShipCmd
     * @return
     */
    Response<Boolean> flushShipAddress(FlushShipCmd flushShipCmd);

    /**
     * 工单导出文件
     *
     * @param request
     * @return
     */
    Response<Boolean> exportServiceImg(AngelWorkPageRequest request);

    /**
     * 工单导出文件
     *
     * @param workId
     * @return
     */
    Response<Boolean> mockAngelWorkServiceImgUpload(Long workId);

    /**
     * 查询redis
     * @param findJimParam
     * @return
     */
    Response<String> findFromJim(FindJimParam findJimParam);


    Response<Boolean> searchAiRequest(AutoBotsO2ORequest autoBotsRequestBO);

}
