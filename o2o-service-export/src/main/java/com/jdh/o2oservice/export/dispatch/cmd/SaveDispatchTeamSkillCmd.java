package com.jdh.o2oservice.export.dispatch.cmd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName SaveDispatchTeamSkillCmd
 * @Description
 * <AUTHOR>
 * @Date 2025/7/21 14:51
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveDispatchTeamSkillCmd implements Serializable {

    /**
     * 派单小队ID
     */
    @NotNull(message = "派单小队ID不能为空")
    private Long dispatchTeamId;

    /**
     *
     */
    @NotNull(message = "小队技能ID不能为空")
    private String teamSkillStr;

    /**
     * 操作者(等于当前登录运营端erp)
     */
    private String operator;
}