package com.jdh.o2oservice.export.support;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.user.dto.UserFeedbackAggregationDTO;

import java.util.Map;

/**
 * @ClassName UserFeedbackGwExport
 * @Description
 * <AUTHOR>
 * @Date 2025/1/24 18:15
 **/
public interface UserFeedbackGwExport {

    /**
     * 提交用户反馈
     * @param param
     * @return
     */
    Response<UserFeedbackAggregationDTO> submitUserFeedback(Map<String, String> param);
}