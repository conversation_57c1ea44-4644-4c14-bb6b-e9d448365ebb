package com.jdh.o2oservice.export.angelpromise.cmd;

import lombok.Data;

import java.util.List;

/**
 * @ClassName QcAlarmCmd
 * @Description
 * <AUTHOR>
 * @Date 2025/6/11 15:17
 **/
@Data
public class QcAlarmCmd {

    /**
     * 质控点报警：siteAlarm
     */
    private String alarmType;

    /**
     * 租户编码
     */
    private String tenantNo;

    /**
     * 业务身份
     */
    private String	verticalCode;

    /**
     * 业务场景编码，服务类型
     */
    private String	sceneCode;

    /**
     * 质控单类型，1-护士 2-实验室
     */
    private Integer	qcRecordType;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 业务单号
     */
    private String orderId;

    /**
     * 履约单号
     */
    private String promiseId;

    /**
     * 质控项目编码
     */
    private String	itemNo;

    /**
     * 质控项目名称
     */
    private String	itemName;

    /**
     * 服务者编码
     */
    private String angelNo;

    /**
     * 服务者名称
     */
    private String angelName;

    /**
     * 时效类型，1-实时质控 2-后置质控
     */
    private Integer	timelyType;

    /**
     * 质控点明细
     */
    private List<QcAlarmDetailCmd> qcSiteDetailBos;

}