package com.jdh.o2oservice.export.dispatch.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName JdhDispatchTeamDTO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/17 13:11
 **/
@Data
public class JdhDispatchTeamDTO {

    /**
     * 派单小队ID
     */
    private Long dispatchTeamId;

    /**
     * 派单小队名称
     */
    private String dispatchTeamName;

    /**
     * 派单小队状态：0-停用 1-启用
     */
    private Integer dispatchTeamStatus;

    /**
     * 派单小队状态描述：0-停用 1-启用
     */
    private String dispatchTeamStatusDesc;

    /**
     * 备注
     */
    private String dispatchTeamRemark;

    /**
     * 小队负责人
     */
    private String dispatchTeamLeader;

    /**
     * 小队技能
     */
    private List<JdhDispatchTeamSkillRelDTO> teamSkillRelList;

    /**
     * 小队服务者
     */
    private List<JdhDispatchTeamAngelRelDTO> teamAngelRelList;

    /**
     * 小队技能描述
     */
    private String teamSkillDesc;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}