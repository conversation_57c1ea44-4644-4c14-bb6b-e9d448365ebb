package com.jdh.o2oservice.export.riskassessment.cmd;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/23
 */
@Data
public class RiskAssessmentUpdateStatusCmd {

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 风险评估单ID
     */
    private Long riskAssessmentId;

    /**
     * 风险评估单状态
     */
    private Integer riskAssessmentStatus;

    /**
     * 风险评估详细信息列表
     */
    private List<RiskAssessmentDetailCmd> riskAssessmentDetailCmds;

}
