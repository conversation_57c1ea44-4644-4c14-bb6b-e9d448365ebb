package com.jdh.o2oservice.export.product.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 查询单个检测单入参
 * @Author: wangpengfei144
 * @Date: 2024/4/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromiseDangerLevelRequest implements Serializable {

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 商品Id
     */
    private Long skuId;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 风险等级
     */
    private List<Integer> dangerLevelList;
}
