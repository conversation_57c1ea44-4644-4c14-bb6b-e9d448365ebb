package com.jdh.o2oservice.export.product.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 京东健康商品扩展主数据
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@SuppressWarnings("JdLowerCamelCaseVariableNaming")
@Data
public class JdhSkuDto {
    
    /**
     * <pre>
     * 主站商品id
     * </pre>
     */
    private Long skuId;
    
    /**
     * <pre>
     * 售卖状态 0-不可售卖 1-可售卖,仅控制是否可下单,非上下架
     * </pre>
     */
    private Integer saleStatus;
    
    /**
     * <pre>
     * 服务类型
     * </pre>
     */
    private Integer serviceType;

    /**
     * <pre>
     * 服务类型
     * </pre>
     */
    private String serviceTypeName;
    
    /**
     * <pre>
     * 服务时长,单位分钟
     * </pre>
     */
    private Integer serviceDuration;
    
    /**
     * <pre>
     * 服务资源类型集合 1-骑手 2-护士 3-护工 4-康复师,多个值
     * </pre>
     */
    private List<Integer> serviceResourceType;
    
    /**
     * <pre>
     * 需提前预约时间,单位小时
     * </pre>
     */
    private Integer advanceAppointTime;
    
    /**
     * <pre>
     * 未来可约天数,单位天
     * </pre>
     */
    private Integer maxScheduleDays;
    
    /**
     * <pre>
     * 每天可预约时间段,["08:00-12:00", "14:00-18:00"]
     * </pre>
     */
    private List<String> dayTimeFrame;
    
    /**
     * <pre>
     * 实验室分发规则 1-整单 2-时效 3-成本,多个值
     * </pre>
     */
    private List<Integer> stationAssignType;
    
    /**
     * <pre>
     * 是否需要投保 0-不需要 1-需要
     * </pre>
     */
    private Integer requiredInsure;
    
    /**
     * <pre>
     * 商品标签,多个
     * </pre>
     */
    private List<String> tags;
    
    /**
     * <pre>
     * 服务须知,JSON对象集合
     * </pre>
     */
    private List<JdhSkuServiceNoticeDto> serviceNotice;

    /**
     * <pre>
     * 服务流程图
     * </pre>
     */
    private String serviceProcessImgId;
    
    /**
     * <pre>
     * 服务流程图
     * </pre>
     */
    private String serviceProcessImg;
    
    /**
     * <pre>
     * 是否实名 0-否 1-是
     * </pre>
     */
    private Integer requiredRealName;
    
    /**
     * 弃用
     * 新的采样教程有三个字段组成，采样教程视频封面tutorialVideoThumbnailUrl；
     * 采样教程视频链接tutorialVideoUrl；采样教程图文轮播图tutorialCarouselUrl
     */
    @Deprecated
    private String tutorialUrl;
    
    /**
     * <pre>
     * 知情同意书地址
     * </pre>
     */
    private String informedConsentUrl;
    
    /**
     * <pre>
     * 预约模板id
     * </pre>
     */
    private Integer appointTemplateId;
    
    /**
     * <pre>
     * 最大年龄
     * </pre>
     */
    private Integer minAge;
    
    /**
     * <pre>
     * 最大年龄
     * </pre>
     */
    private Integer maxAge;

    /**
     * <pre>
     * 年龄范围,最小最大值逗号隔开 0,150
     * </pre>
     */
    private List<Integer> ageRange;
    
    /**
     * <pre>
     * 适用性别，用户性别 1-男 2-女
     * </pre>
     */
    private List<Integer> genderLimit;
    
    /**
     * <pre>
     * 客户确认信息类型,1-医嘱证明 2-换着签字
     * </pre>
     */
    private List<Integer> customerConfirmType;
    
    /**
     * <pre>
     * 服务记录类型,1-废料处理 2-服务记录 3-上传着装照片
     * </pre>
     */
    private List<Integer> serviceRecordType;
    
    /**
     * <pre>
     * 服务资源结算价
     * </pre>
     */
    private BigDecimal resourceSettlementPrice;
    
    /**
     * <pre>
     * 渠道id
     * </pre>
     */
    private Long channelId;
    
    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;
    
    /**
     * <pre>
     * 创建人pin
     * </pre>
     */
    private String createUser;
    
    /**
     * <pre>
     * 创建时间
     * </pre>
     */
    private Date createTime;
    
    /**
     * <pre>
     * 修改人pin
     * </pre>
     */
    private String updateUser;
    
    /**
     * <pre>
     * 更新时间
     * </pre>
     */
    private Date updateTime;
    
    /**
     * 门店组id
     */
    private String locGroupId;
    
    /**
     * 是否loc商品
     */
    private String isLoc;
    
    /**
     * sku状态
     */
    private Integer skuStatus;
    
    /**
     * 商品主图
     */
    private String imgDfsUrl;
    
    /** 店铺id */
    private String shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * yymb
     */
    private String yymb;
    
    /**
     * 商家名称
     */
    private String venderName;
    
    /**
     * productId
     */
    private String productId;
    
    /**
     * sku名称
     */
    private String skuName;
    
    /**
     * 到店商品类型标
     */
    private String ddsplx;
    
    /**
     * 同城到店业务商品
     * 1是，0或null 否
     */
    private String isddlm;
    
    /**
     * 是否测试商品
     */
    private String isTest;
    
    /**
     * 是否能用京券
     * 1或null：可以  0：否
     */
    private String isCanUseJQ;
    /**
     * 是否能用东券
     * 1或null：可以  0：否
     */
    private String isCanUseDQ;
    /**
     * 是否支持全球购
     * 1或null：可以  0：否
     */
    private String isGlobalPurchase;
    /**
     * 是否百亿补贴
     * 1和2都是有百亿补贴  0/无返回 表示没有百亿补贴
     */
    private String msbybt;
    
    /**
     * xfyl
     */
    private String xfyl;
    
    /**
     * locfwlx
     */
    private String locfwlx;
    
    /**
     * 商家id
     */
    private Long venderId;
    
    /**
     * spuId
     */
    private String spuId;
    
    /**
     * outerId
     */
    private String outerId;
    
    /**
     * 一级类目
     */
    private Integer firstCategoryId;
    
    /**
     * 二级类目
     */
    private Integer secondCategoryId;
    
    /**
     * 三级类目
     */
    private Integer thirdCategoryId;
    
    /**
     * <pre>
     * 服务项目列表
     * </pre>
     */
    List<ServiceItemDto> serviceItemList;
    
    /**
     * <pre>
     * 服务项目id列表,前端使用
     * </pre>
     */
    List<Long> serviceItemIdList;
    
    /**
     * <pre>
     * 管理服务项名称，多个使用、隔开，由服务端处理成字符串下发。
     * </pre>
     */
    String serviceItemNames;
    
    /**
     * <pre>
     * 升级加项skuId，多个使用、隔开，由服务端处理成字符串下发。
     * </pre>
     */
    String skuRelSkuIds;

    /**
     * <pre>
     * 京东价格
     * </pre>
     */
    private String jdPrice;

    /**
     * <pre>
     * 商品类型；0为主品，1为加项品
     * </pre>
     */
    private Integer skuType;

    /**
     * <pre>
     * 技术难度 0-1000
     * </pre>
     */
    private Integer technicalLevel;

    /**
     * <pre>
     * 购买后服务有效时间,单位天
     * </pre>
     */
    private Integer buyValidPeriod;

    /**
     * <pre>
     * 采样教程轮播图url集合，格式：[url,url]
     * </pre>
     */
    private List<String> tutorialCarouselUrl;

    /**
     * 采样教程视频
     */
    private String tutorialVideoUrl;

    /**
     * 采样教程视频封面图
     */
    private String tutorialVideoThumbnailUrl;

    /**
     * 采样方法说明 fileId
     */
    private String tutorialMethodUrl;

    /**
     * 采样方法说明跳转url
     */
    private String tutorialMethodJumpUrl;

    /**
     * <pre>
     * 采样教程轮播图url集合，格式：[url,url]
     * </pre>
     */
    private List<Long> tutorialCarousel;

    /**
     * 采样教程视频
     */
    private String tutorialVideo;

    /**
     * 采样教程视频封面图
     */
    private String tutorialVideoThumbnail;

    /**
     * 采样方法说明 fileId
     */
    private String tutorialMethod;

    /**
     * 商品简称
     */
    private String shortName;

    /**
     * 短标题
     */
    private String shortTitle;

    /**
     * 活动楼层配置
     */
    private List<ProductActivityFloorDto> activityFloors;

    /**
     * <pre>
     * 高优分配实验室
     * </pre>
     */
    private List<String> highQualityStoreId;

    /**
     * 护士基础结算价
     */
    private BigDecimal angelBasicSettlementPrice;

    /**
     * 业务流程类型 1-采样+送样+检测报告 2-仅采样+送样 3-仅检测报告
     */
    private Integer businessProcessType;

    /**
     * 取出可约时段列表最早开始时间
     * @return
     */
    public String getDayTimeFrameBegin(){
        if(dayTimeFrame == null || dayTimeFrame.size() == 0){
            return "";
        }
        // 时段正序
        List<String> sortDayTimeFrame = dayTimeFrame.stream().sorted().collect(Collectors.toList());
        return sortDayTimeFrame.get(0).split("-")[0];
    }

    /**
     * 取出可约时段列表最晚结束时间
     * @return
     */
    public String getDayTimeFrameEnd(){
        if(dayTimeFrame == null || dayTimeFrame.size() == 0){
            return "";
        }
        // 时段倒序
        List<String> sortDayTimeFrame = dayTimeFrame.stream().sorted(Collections.reverseOrder()).collect(Collectors.toList());
        String[] lastDayTime = sortDayTimeFrame.get(0).split("-");
        if(lastDayTime.length != 2){
            return "";
        }
        return lastDayTime[1];
    }

    /**
     * 护士基础结算价字符串
     * 保留两位小数
     * @return
     */
    public String getAngelBasicSettlementPriceStr(){
        return Objects.isNull(angelBasicSettlementPrice) ? "" : angelBasicSettlementPrice.setScale(2, RoundingMode.DOWN).toPlainString();
    }

    /**
     * 从getServiceItemList中取名称
     *
     * @return 顿号隔开的多个项目名称
     */
    public String getServiceItemNames() {
        if (StringUtils.isBlank(this.serviceItemNames) && !CollectionUtils.isEmpty(this.getServiceItemList())) {
            return this.getServiceItemList().stream().map(ServiceItemDto::getItemName).filter(Objects::nonNull).collect(Collectors.joining("、"));
        }
        return serviceItemNames;
    }

    public void setServiceItemNames(String serviceItemNames) {
        this.serviceItemNames = serviceItemNames;
    }

    /**
     * 从getServiceItemList中取项目id集合
     *
     * @return 已保存项目id集合
     */
    public List<Long> getServiceItemIdList() {
        if (CollectionUtils.isEmpty(this.serviceItemIdList) && !CollectionUtils.isEmpty(this.getServiceItemList())) {
            return this.getServiceItemList().stream().map(ServiceItemDto::getItemId).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return serviceItemIdList;
    }

    public void setServiceItemIdList(List<Long> serviceItemIdList) {
        this.serviceItemIdList = serviceItemIdList;
    }
}
