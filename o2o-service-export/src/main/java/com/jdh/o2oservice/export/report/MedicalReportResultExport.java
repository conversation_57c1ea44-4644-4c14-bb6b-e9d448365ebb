package com.jdh.o2oservice.export.report;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.report.cmd.MedPromisePosRateCmd;
import com.jdh.o2oservice.export.report.dto.MedPromisePositiveRateDTO;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/20
 */
public interface MedicalReportResultExport {

    /**
     * 查询阳性率
     * @param medPromisePosRateCmd 查询条件
     * @return 阳性率
     */
    Response<MedPromisePositiveRateDTO> queryMedPromisePositiveRate(MedPromisePosRateCmd medPromisePosRateCmd);
}
