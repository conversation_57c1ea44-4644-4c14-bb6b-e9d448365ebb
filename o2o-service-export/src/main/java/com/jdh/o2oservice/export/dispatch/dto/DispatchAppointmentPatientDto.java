package com.jdh.o2oservice.export.dispatch.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName DispatchAppointmentPatientDto
 * @Description
 * <AUTHOR>
 * @Date 2024/4/23 23:08
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DispatchAppointmentPatientDto {

    /**
     * 顾客ID
     */
    private Long promisePatientId;

    /**
     * 健康档案ID
     */
    private Long patientId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private Integer patientGender;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 服务项目列表
     */
    private List<DispatchServiceItemDto> serviceItems;
}