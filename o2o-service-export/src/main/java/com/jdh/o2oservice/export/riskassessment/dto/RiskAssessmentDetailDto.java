package com.jdh.o2oservice.export.riskassessment.dto;

import lombok.Data;

import java.util.Date;

/**
 * @ClassName RiskAssessmentDetailDto
 * @Description
 * <AUTHOR>
 * @Date 2025/7/21 14:35
 */
@Data
public class RiskAssessmentDetailDto {
    /**
     * 主键
     */
    private Long id;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 履约人ID
     */
    private Long promisePatientId;

    /**
     * 风险评估单ID
     */
    private Long riskAssessmentId;

    /**
     * 风险评估单问题ID
     */
    private Long riskQuestionId;

    /**
     * 题库问题ID
     */
    private Long questionId;

    /**
     * 风险评估单状态
     */
    private Integer riskQuestionStatus;

    /**
     * 问题结果
     */
    private String riskQuestionRes;

    /**
     * 风险评估师
     */
    private String assessmentUser;

    /**
     * 评估时间
     */
    private Date assessmentTime;

    /**
     * 风险评估单状态
     */
    private Integer riskAssessmentStatus;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    private Long promiseId;


    private String questionExt;
}
