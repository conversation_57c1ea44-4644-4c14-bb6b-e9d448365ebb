package com.jdh.o2oservice.export.dispatch.cmd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName SaveDispatchTeamCmd
 * @Description
 * <AUTHOR>
 * @Date 2025/7/21 14:16
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveDispatchTeamCmd implements Serializable {

    /**
     * 派单小队ID
     */
    private Long dispatchTeamId;

    /**
     * 派单小队名称
     */
    @Size(max = 20, message = "小队名称最多20字")
    private String dispatchTeamName;

    /**
     * 派单小队状态：0-停用 1-启用
     */
    private Integer dispatchTeamStatus;

    /**
     * 派单小队状态描述：0-停用 1-启用
     */
    private String dispatchTeamStatusDesc;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注最多500字")
    private String dispatchTeamRemark;

    /**
     * 小队负责人
     */
    @Size(max = 100, message = "小队负责人最多100字")
    private String dispatchTeamLeader;

    /**
     * 操作者(等于当前登录运营端erp)
     */
    private String operator;
}