package com.jdh.o2oservice.export.dispatch.query;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.Data;

import java.util.List;

/**
 * @ClassName DispatchTeamListRequest
 * @Description
 * <AUTHOR>
 * @Date 2025/7/17 13:11
 **/
@Data
public class DispatchTeamListRequest extends AbstractPageQuery {

    /**
     * 派单小队ID
     */
    private Long dispatchTeamId;

    /**
     * 派单小队名称-模糊查询
     */
    private String fuzzyDispatchTeamName;

    /**
     * 小队状态集合
     */
    private List<Integer> dispatchTeamStatusList;
}