package com.jdh.o2oservice.export.support.query;

import com.jdh.o2oservice.common.result.request.AbstractRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 查询外呼记录
 * @Date 2024/12/21 下午8:15
 * <AUTHOR>
 **/
@Data
public class QueryCallRecordRequest extends AbstractRequest implements Serializable {

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 通话ID，唯一确定一次通话
     */
    private String callId;

    /**
     *
     */
    private List<String> phoneList;

    /**
     * 显示AI总结
     */
    private Boolean showAISummary = false;
}
