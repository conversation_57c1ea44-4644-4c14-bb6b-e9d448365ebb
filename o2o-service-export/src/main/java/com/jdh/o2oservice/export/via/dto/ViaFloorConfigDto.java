package com.jdh.o2oservice.export.via.dto;

import lombok.Data;

import java.util.List;

@Data
public class ViaFloorConfigDto {


    /**
     * 背景图像
     */
    private String backGroundImage;

    /**
     * 主icon
     */
    private String mainIcon;

    /**
     * 主标题
     */
    private String mainTitle;

    /**
     * 标题
     */
    private String title;

    /**
     * icon
     */
    private String icon;

    /**
     * 卡片
     */
    private ViaCardInfoDto card;

    /**
     * 字段key
     */
    private String fieldKey;

    /**
     * 字段值
     */
    private String fieldValue;

    /**
     * viaStatus 展示的状态值
     */
    private Integer viaStatus;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 跳转链接
     */
    private String targetUrl;

    /**
     * 步骤状态
     */
    private String stepStatus;

    /**
     * 步骤条 编码
     */
    private String stepCode;

    /**
     * 步骤 描述
     */
    private String stepDesc;

    /**
     * 步骤状态
     */
    private String stepIcon;

    /**
     * 步骤标题
     */
    private String stepTitle;

    /**
     * 楼层编码
     */
    private String floorCode;

    /**
     * 楼层名称
     */
    private String floorName;

    /**
     * 通知提示
     */
    private String noticeTip;

    /**
     * 二维码生成要素
     */
    private String promiseQrCode;

    /**
     * 消费码
     */
    private String promiseCode;

    /**
     * 消费码密码
     */
    private String promiseCodePwd;

    /**
     * 消费码是否打小标
     */
    private Boolean promiseCodeMark;

    /**
     * 打标的icon
     */
    private String promiseCodeMarkIcon;

    /**
     * 消费码截屏保存按钮
     */
    private Boolean promiseCodeSaveBtn;

    /**
     * 表格类型
     */
    private String formType;

    /**
     * 字段入参
     */
    private String paramField;

    /**
     * 回显默认值
     */
    private String value;

    /**
     * 是否可点击
     */
    private Boolean disabled;

    /**
     * 动作
     */
    private ViaActionInfoDto action;

    /**
     * 表单项
     */
    private List<ViaFormItemDto> formItemList;

    /**
     * 分组数据
     */
    private List<ViaGroupInfoDto> groupInfoList;

    /**
     * 按钮集合
     */
    private List<ViaBtnInfoDto> btnList;

    /**
     * 协议
     */
    private ViaAgreementInfoDto agreeInfo;

    /**
     * 复制按钮
     */
    private Boolean copyBtn;

    /**
     * 展示详情
     */
    private Boolean showDetail;

    /**
     * 卡券实体
     */
    private List<ViaVoucherItemDto> voucherInfoDtos;

    /**
     * 异常说明
     */
    private String warmTip;

    /**
     * 楼层配置列表
     */
    private List<ViaFloorSubConfigDto> subConfigList;

    /**
     * 字段标签
     */
    private String fieldLabel;

    /**
     * 字段ID（前端使用）
     */
    private String fieldId;

    /**
     * 字段单位（前端使用）
     */
    private String fieldUnit;

    /**
     * 字段扩展类（前端使用）
     */
    private String fieldExtraClass;
}
