package com.jdh.o2oservice.export.riskassessment.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName RiskAssDto
 * @Description
 * <AUTHOR>
 * @Date 2025/7/21 14:34
 */
@Data
public class RiskAssDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 业务模式
     */
    private String businessMode;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 履约单ID
     */
    private Long promiseId;


    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 风险评估单ID
     */
    private Long riskAssessmentId;

    /**
     * 风险评估单状态
     */
    private Integer riskAssessmentStatus;

    /**
     * serviceId
     */
    private String serviceId;

    /**
     * 评估项目名称
     */
    private String serviceItemName;

    /**
     * 风险评估人
     */
    private String assessmentUser;

    /**
     * 风险评估人的用户类型
     */
    private Integer assessmentUserType;

    /**
     * 评估时间
     */
    private Date assessmentTime;

    /**
     * 回访状态
     */
    private Integer returnVisitStatus;

    /**
     * 回访人
     */
    private String returnVisitUser;

    /**
     * 回访时间
     */
    private Date returnVisitTime;

    /**
     * 回访记录
     */
    private String returnVisitRecord;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 预约时间
     */
    private Date appointmentTime;

    /**
     * 服务完成时间
     */
    private Date serviceFinishTime;

    /**
     * 用户省，京标
     */
    private Integer patientProvince;

    /**
     * 用户市，京标
     */
    private Integer patientCity;

    /**
     * 用户区，京标
     */
    private Integer patientCounty;

    /**
     * 用户镇，京标
     */
    private Integer patientTown;

    /**
     * 是否有效 1有效 0无效
     */
    private Integer yn;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 存储风险评估的详细信息列表。
     */
    private List<RiskAssessmentDetailDto> riskAssessmentDetailDtos;


    private Integer partnerSource;

    private Date assessmentDeadlineTime ;

    private Integer assessmentTimeoutStatus;

    private Integer riskLevel;

    private Integer skuServiceType;
}
