package com.jdh.o2oservice.export.support.command;

import com.jdh.o2oservice.common.result.request.AbstractRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Set;

/**
 * 获取文件访问的预签名链接
 * @author: yang<PERSON><PERSON>
 * @date: 2024/3/20 4:47 下午
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenerateGetUrlCommand extends AbstractRequest {
    /** 领域编码 */
    private String domainCode;
    /** 文件ID */
    private Set<Long> fileIds;

    /** 是否公网访问 */
    private Boolean isPublic;
    /**
     *
     */
    private Date expireTime;

    private Long taskId;
}
