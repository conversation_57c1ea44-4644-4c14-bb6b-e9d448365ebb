package com.jdh.o2oservice.export.angelpromise.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @author:lichen55
 * @Description: 任务单信息
 * @date 2024-05-27 15:54
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelTaskDto {

    /**
     * 任务单Id
     */
    private Long taskId;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 任务单状态：
     */
    private Integer	status;

    /**
     * 业务任务状态：
     */
    private Integer bizExtStatus;

    /**
     * 任务暂停状态：0正常，1退款暂停, 2取消暂停
     */
    private Integer stopStatus;

    /**
     * 扩展信息：被服务人姓名、出生年月日、性别、服务时间段（开始时间、结束时间）、服务地址、联系方式、就医记录图片、点子签名图片、
     */
    private String extend;

    /**
     * 被服务人Id
     */
    private String patientId;

    /**
     * 被服务人姓名
     */
    private String patientName;

    /**
     * 被服务人性别
     */
    private String patientGender;

    /**
     * 被服务人年龄
     */
    private String patientAge;

    /**
     * 被服务人地址
     */
    private String patientFullAddress;

    /**
     * 被服务人纬度
     */
    private Double patientAddressLat;

    /**
     * 被服务人经度
     */
    private Double patientAddressLng;

    /**
     * 服务开始时间
     */
    private Date serviceStartTime;

    /**
     * 服务结束时间
     */
    private Date serviceEndTime;

    /**
     * 计划出门时间
     */
    private Date planOutTime;

    /**
     * 就医证明图片地址
     */
    private List<String> medicalCertificateUrls;

    /**
     * 知情同意书签字地址
     */
    private String signatureLetterOfConsentUrl;

    /**
     * 知情同意书模版文件
     */
    private String letterOfConsentUrl;

    /**
     * 失效的，如果退款了，则disabled为true
     */
    private Boolean disabled;

    /**
     * 检验项信息
     */
    private List<AngelWorkMedPromiseDto> angelWorkServiceItems;

    /**
     * 服务时长分钟
     */
    private Integer serviceDuration;

    /**
     * 是否需要核验信息
     */
    private boolean needConfirm = true;

    /**
     * 服务者外呼
     */
    private AngelWorkCallDto angelWorkCall;

    /**
     * 检测项目名称
     */
    private List<String> serviceItemNameList;

    /**
     * 是否展示沟通记录
     */
    private boolean needCallRecordSheet;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 服务者Cpin
     */
    private String angelPin;
}
