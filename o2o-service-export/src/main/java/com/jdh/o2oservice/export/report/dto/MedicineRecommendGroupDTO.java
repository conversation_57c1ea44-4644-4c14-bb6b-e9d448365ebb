package com.jdh.o2oservice.export.report.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/9
 */
@Data
public class MedicineRecommendGroupDTO {

    /**
     * 用药方案组名称
     */
    private String groupName;
    /**
     * 用药方案组利益点
     */
    private String groupBenefit;
    /**
     * 是否显示该用药方案组
     */
    private Boolean show;
    /**
     * 用药方案组的显示顺序
     */
    private Integer showOrder;

    /**
     * 用药方案组中包含的具体药品信息列表。
     */
    private List<MedicineDetailDTO> medicineDTOList;
}
