package com.jdh.o2oservice.export.trade.dto;
import lombok.Data;
import java.io.Serializable;

/**
 * @Description 自定义订单服务流程
 * @Date 2024/8/28 下午8:51
 * <AUTHOR>
 **/
@Data
public class CustomOrderProcessTrackDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    private String messageTitle;

    /**
     * icon图标
     */
    private String icon;

    /**
     * icon图标
     */
    private String icon_dark;

    /**
     * 内容
     */
    private String message;

    /**
     * 夜间内容
     */
    private String messageNight;

    /**
     * 提交数据
     */
    private String submitData;

    /**
     * 跳转类型
     */
    private String jumpType;

    /**
     * 请求url
     */
    private String url;

    /**
     * 操作类型
     */
    private String opType;

    
    private String hiddenArrow;
}
