package com.jdh.o2oservice.export.angelpromise.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @ClassName:AngelRealTrackDto
 * @Description: 护士轨迹
 * @Author: yaoqinghai
 * @Date: 2024/5/8 21:44
 * @Vserion: 1.0
 **/
@Data
@Builder
public class AngelRealTrackDto {

    /**
     * 服务者pin
     */
    private String angelPin;

    /**
     * 服务者id
     */
    private String angelId;

    /**
     * 服务者头像
     */
    private String angelHeadImg;

    /**
     * 服务者姓名
     */
    private String angelName;

    /**
     * 服务者电话
     */
    private String angelPhone;

    /**
     * 方案交通方式
     */
    private String mode;

    /**
     * 方案整体距离（米）
     */
    private Double distance;

    /**
     * HH:mm格式的时间(最终选中的时间)
     */
    private String duration;

    /**
     * 护士位置经纬度
     */
    private String angelPosition;

    /**
     * 用户位置经纬度
     */
    private String userPosition;

    /**
     * 用户位置经纬度
     */
    private String stationPosition;

    /**
     * 预计接单时间
     */
    private String estimateGrabTime;

    /**
     * 预计取件时间
     */
    private String estimatePickUpTime;

    /**
     * 预计完成时间
     */
    private String estimateReceiveTime;

    /**
     * 运单状态
     */
    private Integer shipStatus;

    /**
     * 预计送达时间文案
     */
    private String estimateDeliveryTimeTip;

    /**
     * 护士轨迹外呼 true-显示 false-隐藏
     */
    private Boolean inspectTrackCall;
}
