package com.jdh.o2oservice.export.product.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * @ClassName:JdhServiceQuery
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/29 13:50
 * @Vserion: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhServiceQuery {

    /**
     * 服务id
     */
    private Set<Long> serviceIds;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 适用人群
     */
    private String serviceSuitable;

    /** */
    private int pageNum = 1;

    /** */
    private int pageSize = 10;

    /**x
     * 服务项目查询条件
     */
    private ServiceItemQuery serviceItemQuery;
}
