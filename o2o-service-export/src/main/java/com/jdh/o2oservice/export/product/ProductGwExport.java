package com.jdh.o2oservice.export.product;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.product.dto.*;
import com.jdh.o2oservice.export.promise.PromiseGwExport;

import java.util.List;
import java.util.Map;

/**
 * @ClassName ProductGwExport
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 11:30
 **/
public interface ProductGwExport {

    /**
     * 查询服务套餐列表
     * C端查询套餐和业务模式、场景绑定在一起，可能需要履约单数据
     * {@link PromiseGwExport#listServiceChildren(java.util.Map)}
     * @return
     */
    @Deprecated
    Response<List<ProviderServiceGoodsDto>> queryServiceGoodsList(Map<String, String> param);

    /**
     * 查看商品信息
     *
     * @return detailFloorBO
     */
    Response<ProductDetailDTO> queryProductDetailFloorBySku(Map<String, String> param);

    /**
     * 分组获取地址列表
     *
     * @param param
     * @return
     */
    Response<Map<String, GroupUserAddressDTO>> listGroupAddress(Map<String, String> param);

    /**
     * 查询商品优惠券列表
     *
     * @return param
     */
    Response<List<ProductCouponDTO>> queryProductCouponList(Map<String, String> param);

    /**
     * 领取优惠券
     *
     * @return param
     */
    Response<CouponGetResultDTO> getCoupon(Map<String, String> param);

    /**
     * 批量-领取优惠券
     *
     * @return param
     */
    Response<CouponGetResultDTO> batchGetCoupon(Map<String, String> param);

}