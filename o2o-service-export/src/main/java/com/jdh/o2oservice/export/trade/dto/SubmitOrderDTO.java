package com.jdh.o2oservice.export.trade.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * SubmitOrderDTO 提交订单返回实体
 *
 * <AUTHOR>
 * @version 2024/3/7 14:54
 **/
@Data
public class SubmitOrderDTO implements Serializable {

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 应付金额
     */
    private BigDecimal factOrderAmount;

    /**
     * 支付类型
     */
    private Integer payType;

    /**
     * 机构编码
     */
    private String companyId;

    /**
     * 订单过期时间：yyyyMMddHHmmss
     */
    private Date expiredTime;

    /**
     * sku信息
     */
    private List<SkuItemDTO> skuItemDTOList;

    /**
     * 商家信息
     */
    private List<VenderInfoDTO> venderInfoDTOList;

    /**
     * 无货的sku列表
     */
    List<SkuItemDTO> noStockSkuItemVoList;

    /**
     * 限购sku信息列表
     */
    private List<SkuItemLimitBuyDTO> skuItemLimitBuyDTOList;

    /**
     * 支付链接
     */
    private String orderPayUrl;

}
