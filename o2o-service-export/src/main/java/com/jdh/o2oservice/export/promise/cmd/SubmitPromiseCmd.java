package com.jdh.o2oservice.export.promise.cmd;

import com.jdh.o2oservice.common.result.request.AbstractCmd;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 提交预约单操作类
 *
 * @author: yang<PERSON><PERSON>
 * @date: 2022/8/30 10:11 下午
 * @version: 1.0
 */
@Data
public class SubmitPromiseCmd extends AbstractCmd implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 履约单ID
     */
    private String promiseId;
    /**
     * 唯一门店ID
     */
    private String storeId;
    /**
     * 短信验证码
     */
    private String smsCode;
    /**
     * 预约人信息
     */
    private SubmitUser user;
    /**
     * 提交基础信息
     */
    private AppointmentTime appointmentTime;

    /**
     * 履约单关联的服务详情
     */
    private List<PromiseServiceItem> services;

    /**
     * 预约人信息
     */
    private List<SubmitUser> users;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作角色
     * @see com.jdh.o2oservice.common.enums.OperatorRoleTypeEnum
     */
    private Integer operatorRoleType;

    /**
     * 意向护士
     */
    private IntendedNurse intendedNurse;

    /**
     * 联系人姓名
     */
    private String appointmentUserName;

    /**
     * 联系人手机号
     */
    private String appointmentPhone;

}
