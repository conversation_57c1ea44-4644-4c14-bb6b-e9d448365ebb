package com.jdh.o2oservice.export.promise.dto;

import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * PromisePatientDto
 *
 * <AUTHOR>
 * @date 2024/04/21
 */
@Data
public class PromisePatientDto implements Serializable {

    /**
     * promiseId
     */
    private Long promiseId;

    /**
     * promisePatientId
     */
    private Long promisePatientId;


    /**
     * version
     */
    private Integer version;

    /**
     * userPin
     */
    private String userPin;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 生日
     */
    private BirthdayDto birthday;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 婚否
     */
    private Integer marriage;

    /**
     * 预约人姓名
     */
    private UserNameDto userName;

    /**
     * 预约人手机号
     */
    private PhoneNumberDto phoneNumber;

    /**
     * 证件号码（加密）
     */
    private CredentialNumberDto credentialNum;

    /**
     * 亲属类型:1本人 21-父母 22-配偶 23-子女 34-其他
     */
    private Integer relativesType;
    /**
     *
     */
    private List<PromiseServiceDetailDto> serviceDetails;
    /**
     * 样本信息
     */
    private List<MedicalPromiseDTO> medicalPromiseDetails;
    /**
     * 头像
     */
    private String patientHeaderImage;
}
