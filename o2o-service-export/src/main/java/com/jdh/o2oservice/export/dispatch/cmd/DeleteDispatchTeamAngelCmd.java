package com.jdh.o2oservice.export.dispatch.cmd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @ClassName DeleteDispatchTeamAngelCmd
 * @Description
 * <AUTHOR>
 * @Date 2025/7/21 16:03
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteDispatchTeamAngelCmd {

    /**
     * 派单小队ID
     */
    @NotNull(message = "派单小队ID不能为空")
    private Long dispatchTeamId;

    /**
     * 服务者ID
     */
    @NotNull(message = "小队服务者ID不能为空")
    private Long angelId;

    /**
     * 操作者(等于当前登录运营端erp)
     */
    private String operator;
}