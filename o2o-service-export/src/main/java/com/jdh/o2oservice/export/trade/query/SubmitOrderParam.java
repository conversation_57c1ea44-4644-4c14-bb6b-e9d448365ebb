package com.jdh.o2oservice.export.trade.query;

import com.jdh.o2oservice.common.result.request.AbstractQuery;
import lombok.Data;

import java.util.List;

/**
 * SubmitOrderParam 提交订单入参
 *
 * <AUTHOR>
 * @version 2024/3/7 14:56
 **/
@Data
public class SubmitOrderParam extends AbstractQuery {

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 购物车相关内容
     */
    private CartInfoParam cartInfoParam;

    /**
     * sku信息 (结算页在立即购买环节会有修改数量的用户行为，这里只关注skuId 和数 量的数据)
     */
    private List<SkuInfoParam> skuInfoParamList;

    /**
     * 地址信息
     */
    private AddressUpdateParam addressUpdateParam;

    /**
     * 优惠卷
     */
    private CouponInfoParam couponInfoParam;

    /**
     * 优惠券信息
     */
    private List<TradeCouponInfoParam> couponTradeInfoParamList;

    /**
     * 发票信息
     */
    private InvoiceQueryParam invoiceQueryParam;

    /**
     * 配送方式
     */
    private ShipmentParam shipmentParam;

    /**
     * 时效
     */
    private PromiseParam promiseParam;

    /**
     * 支付方式
     */
    private PaymentParam paymentParam;

    /**
     * 订单的扩展参数
     */
    private OrderExtNodeParam orderExtNodeParam;

    /**
     * 备注信息
     */
    private RemarkParam remarkParam;

    /**
     * 红包
     */
    private HongBaoInfoParam hongBaoInfoParam;

    /**
     * 密码
     */
    private PaymentPasswordParam paymentPasswordParam;

    /**
     * 运费信息
     */
    private FreightInfoParam freightInfoParam;

    /**
     * 虚拟资产
     */
    private AssetInfoParam assetInfoParam;

    /**
     * 服务费
     */
    private ServiceFeeParam serviceFeeParam;

    /**
     * 合作方来源
     */
    private Integer partnerSource;

    /**
     * 外部渠道的订单号
     */
    private String partnerSourceOrderId;

    /**
     * 预约人信息
     */
    private List<Long> patientIds;

    /**
     * 预约时间
     */
    private AppointmentTimeParam appointmentTimeParam;

    /**
     * 新版结算页标记
     */
    private String usp;

    /**
     * 渠道
     */
    private String channelName;

    /**
     * 意向护士
     */
    private IntendedNurseParam intendedNurse;

    /**
     * 服务人员升级是否选中 true-选中 false-未选中
     */
    private Boolean serviceUpgradeSelected = false;

    /**
     * 是否已有采样盒
     */
    private Boolean preSampleFlag = false;
}
