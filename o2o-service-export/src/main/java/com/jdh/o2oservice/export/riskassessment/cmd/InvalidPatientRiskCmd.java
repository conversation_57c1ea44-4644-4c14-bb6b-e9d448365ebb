package com.jdh.o2oservice.export.riskassessment.cmd;

import lombok.Data;

import java.util.List;

/**
 * @ClassName RiskInterceptCmd
 * @Description
 * <AUTHOR>
 * @Date 2025/7/9 20:03
 */
@Data
public class InvalidPatientRiskCmd {

    /**
     * 领域编码
     */
    private String domainCode;

    /**
     * 聚合根编码
     */
    private String aggregateCode;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 履约单患者id
     */
    private List<Long> promisePatientIds;


}
