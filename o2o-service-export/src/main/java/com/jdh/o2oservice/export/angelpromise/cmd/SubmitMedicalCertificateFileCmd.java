package com.jdh.o2oservice.export.angelpromise.cmd;

import com.jdh.o2oservice.common.result.request.AbstractRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/9
 */
@Data
public class SubmitMedicalCertificateFileCmd extends AbstractRequest {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 提交的医疗证明文件ID列表
     */
    private List<Long> fileIds;
}
