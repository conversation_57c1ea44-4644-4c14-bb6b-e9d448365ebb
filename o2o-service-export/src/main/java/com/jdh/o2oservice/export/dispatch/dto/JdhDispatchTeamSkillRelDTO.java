package com.jdh.o2oservice.export.dispatch.dto;

import lombok.Data;

/**
 * @ClassName JdhDispatchTeamSkillRelDTO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/17 13:41
 **/
@Data
public class JdhDispatchTeamSkillRelDTO {

    /**
     * 派单小队ID
     */
    private Long dispatchTeamId;

    /**
     * 服务者技能code（全局唯一）
     */
    private String angelSkillCode;

    /**
     * 服务者技能名称
     */
    private String angelSkillName;

    /**
     * 服务类型 1:供应商体检项目 2:检测类 3:护理类
     */
    private Integer itemType;

    /**
     * 互医服务组id
     */
    private String serviceGroupId;
}