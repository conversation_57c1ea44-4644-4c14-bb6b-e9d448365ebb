package com.jdh.o2oservice.export.trade;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.trade.dto.*;

import java.util.List;
import java.util.Map;

/**
 * @author: yangxiyu
 * @date: 2024/1/9 11:22 上午
 * @version: 1.0
 */
public interface TradeGwExport {

    /**
     * 根据草稿数据获取结算页链接
     *
     * @param param param
     * @return {@link Response}<{@link Boolean}>
     */
    Response<TradeDraftDto> saveDraft(Map<String,String> param);

    /**
     * 结算页用户行为
     *
     * @param param 入参
     * @return OrderUserActionDTO
     */
    Response<OrderUserActionDTO> executeAction(Map<String,String> param);

    /**
     * 提交订单
     *
     * @param param 提单参数
     * @return SubmitOrderDTO
     */
    Response<SubmitOrderDTO> submitOrder(Map<String,String> param);

    /**
     * 订单支付收银台
     *
     * @param param 提单参数
     * @return SubmitOrderDTO
     */
    Response<String> getOrderPayUrl(Map<String,String> param);
    /**
     * 取消待支付订单
     *
     * @param paramMap
     * @return
     */
    Response<Boolean> cancelPayOrder(Map<String, String> paramMap);

    /**
     * 排期列表
     * @param param
     * @return
     */
    Response<AvaiableAppointmentTimeDTO> avaiableTime(Map<String,String> param);

    /**
     * 预约单退款
     * 1.待预约，预约失败的才可申请退款
     * 2.OCS查询付款类型列表
     * 3.退款拆分计算
     * @param param
     * @return
     */
    Response<Boolean> xfylOrderRefund(Map<String, String> param);

    /**
     * 获取订单退款原因列表
     *
     * @param param param
     * @return {@link Response}<{@link Boolean}>
     */
    Response<List<OrderCancelReasonDto>> getCancelReasons(Map<String,String> param);

    /**
     * 查询用户服务过的历史护士
     * @param param
     * @return
     */
    Response<List<ServiceHistoryAngelDTO>> queryServiceHistoryAngel(Map<String,String> param);

    /**
     * 查询意向服务者
     * @param param
     * @return
     */
    Response<IntendedAngelDTO> queryIntendedAngel(Map<String,String> param);
}
