package com.jdh.o2oservice.export.angelpromise.cmd;
import com.jdh.o2oservice.common.result.request.AbstractRequest;
import com.jdh.o2oservice.export.angelpromise.dto.ServiceRecordAnswerQuestionDTO;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

@Data
public class SubmitAngelServiceRecordCmd extends AbstractRequest implements Serializable {

    /**
     * 任务单id
     */
    private Long taskId;

    /**
     * 流程节点编码
     */
    private String flowCode;

    /**
     * 高风险确认
     *
     */
    private Boolean highRiskConfirm;

    /**
     * 提交问题答案
     */
    private List<ServiceRecordAnswerQuestionDTO> answerQuestionDTOList;

}
