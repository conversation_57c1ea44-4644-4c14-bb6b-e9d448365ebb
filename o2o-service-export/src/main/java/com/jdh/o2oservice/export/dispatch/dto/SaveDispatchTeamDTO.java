package com.jdh.o2oservice.export.dispatch.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SaveDispatchTeamDTO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/21 15:18
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveDispatchTeamDTO {

    /**
     * 成功数量
     */
    private Integer successNum;

    /**
     * 失败数量
     */
    private Integer failNum;
}