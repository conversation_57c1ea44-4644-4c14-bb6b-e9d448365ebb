package com.jdh.o2oservice.export.angelpromise.dto;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

@Data
public class AngelServiceRecordQuestionGroupDto implements Serializable {

    /**
     * 节点名称
     */
    private String name;

    /**
     * 节点备注
     */
    private String desc;

    /**
     * 节点唯一code码
     */
    private String code;

    /**
     * 是否展示 1展示 0否
     */
    private Integer show;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 服务信息
     */
    private AngelTaskDto angelServiceInfoDto;

    /**
     * 节点状态：0-未开始，1-进行中，2-已完成
     */
    private Integer status;

    /**
     * 题
     */
    private List<AngelServiceRecordQuestionDto> questionDTOS;

    /**
     * 按钮文案
     */
    private String buttonName;

    /**
     * 是否可以点击
     */
    private Boolean isClick = true;
}
