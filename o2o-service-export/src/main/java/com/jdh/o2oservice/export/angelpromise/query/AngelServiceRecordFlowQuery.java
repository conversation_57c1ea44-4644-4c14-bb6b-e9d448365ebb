package com.jdh.o2oservice.export.angelpromise.query;

import com.jdh.o2oservice.common.result.request.AbstractRequest;
import lombok.Data;
import java.io.Serializable;

@Data
public class AngelServiceRecordFlowQuery extends AbstractRequest implements Serializable {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 流程节点编码
     */
    private String flowCode;

    /**
     * 查询类型：0-默认，1-点击编辑后查询
     */
    private Integer queryType;

}