package com.jdh.o2oservice.base.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.TimeRangeEnum;

import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-08-06 18:00
 * @description 时间工具类
 */
public class TimeUtils {
    /**
     *
     */
    private TimeUtils() {

    }

    /**
     * 获取默认时间格式: yyyy-MM-dd HH:mm:ss
     */
    private static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER = TimeFormat.LONG_PATTERN_LINE.formatter;

    /**
     * 获取默认时间格式: yyyy-MM-dd
     */
    private static final DateTimeFormatter DEFAULT_DATE_FORMATTER = TimeFormat.SHORT_PATTERN_LINE.formatter;

    /**
     * 获取默认时间格式: yyyy-MM-dd HH:mm:ss
     */
    private static final DateTimeFormatter DEFAULT_DATETIME_WITH_MSES_FORMATTER = TimeFormat.LONG_PATTERN_WITH_MILSEC_LINE.formatter;

    /**
     * 获取当前时间
     *
     * @return
     */
    public static LocalDateTime getCurrentLocalDateTime() {
        return LocalDateTime.now();
    }

    /**
     * @return
     */
    public static String getCurrentDateTime() {
        return DEFAULT_DATETIME_FORMATTER.format(getCurrentLocalDateTime());
    }

    /**
     * @return
     */
    public static String getCurrentDateTimeWithMses() {
        return DEFAULT_DATETIME_WITH_MSES_FORMATTER.format(getCurrentLocalDateTime());
    }

    /**
     * @return
     */
    public static LocalDate getCurrentLocalDate() {
        return LocalDate.now();
    }


    /**
     * @return
     */
    public static String getCurrentDate() {
        return DEFAULT_DATE_FORMATTER.format(getCurrentLocalDate());
    }

    /**
     * LocalDateTime Date 相互转换
     *
     * @param date
     * @return
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /**
     * @param date
     * @return
     */
    public static LocalDate dateToLocalDate(Date date) {
        return dateToLocalDateTime(date).toLocalDate();
    }

    /**
     * @return
     */
    public static Date localDateTimeToDate() {
        LocalDateTime localDateTime = getCurrentLocalDateTime();
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * @param localDateTime
     * @return
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return null;
        }
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * @return
     */
    public static Date localDateToDate() {
        LocalDate localDate = getCurrentLocalDate();
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * @param localDate
     * @return
     */
    public static Date localDateToDate(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * @param time
     * @return
     */
    public static String localDateTimeToStr(LocalDateTime time) {
        return DEFAULT_DATETIME_FORMATTER.format(time);
    }

    /**
     * @param time
     * @return
     */
    public static String localDateTimeToMinuteStr(LocalDateTime time) {
        return TimeFormat.LONG_PATTERN_LINE_NO_S.formatter.format(time);
    }

    /**
     * @param time
     * @param format
     * @return
     */
    public static String localDateTimeToStr(LocalDateTime time, TimeFormat format) {
        return format.formatter.format(time);
    }

    public static String getAmPm(LocalDateTime dateTime) {
        Date date = TimeUtils.localDateTimeToDate(dateTime);
        return getAmPm(date);
    }

    public static String getAmPm(Date date) {
        //创建一个calendar对象，默认一个时间
        Calendar calendar = Calendar.getInstance();
        // 把time赋值给calendar
        calendar.setTime(date);
        //区分是上午还是下午  index 0为上午 1为下午
        int index = calendar.get(Calendar.AM_PM);
        if (0 == index) {
            return TimeRangeEnum.AM.getLabel();
        } else if (1 == index) {
            return TimeRangeEnum.PM.getLabel();
        }
        return null;
    }

    /**
     * @param time
     * @return
     */
    public static String localDateToStr(LocalDate time) {
        return DEFAULT_DATE_FORMATTER.format(time);
    }

    /**
     * @param date
     * @return
     */
    public static String localDateToStr(LocalDate date, TimeFormat format) {
        return format.formatter.format(date);
    }


    /**
     * @param data
     * @param isEpochMilli
     * @return
     */
    public static long dataToTimestamp(Date data, boolean isEpochMilli) {
        Instant instant = data.toInstant();
        if (isEpochMilli) {
            return instant.toEpochMilli();
        } else {
            return instant.getEpochSecond();
        }
    }

    /**
     * @param date
     * @param amount
     * @return
     */
    public static Date addDays(Date date, int amount) {
        return add(date, 5, amount);
    }

    /**
     * @param date
     * @param calendarField
     * @param amount
     * @return
     */
    public static Date add(Date date, int calendarField, int amount) {
        if (date == null) {
            throw new IllegalArgumentException("The date must not be null");
        } else {
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.add(calendarField, amount);
            return c.getTime();
        }
    }

    /**
     * @param date
     * @param format
     * @return
     */
    public static String dateTimeToStr(Date date, TimeFormat format) {
        if (date == null) {
            return "";
        }
        return dateToLocalDateTime(date).format(format.formatter);
    }

    /**
     * @param date
     * @param formatStr
     * @return
     */
    public static String dateTimeToStr(Date date, String formatStr) {
        if (date == null) {
            return "";
        }
        TimeFormat format = TimeFormat.getByPattern(formatStr);
        return dateToLocalDateTime(date).format(format.formatter);
    }


    /**
     * Date转时间字符串
     *
     * @param date
     * @return
     */
    public static String dateTimeToStr(Date date) {
        if (date == null) {
            return "";
        }
        return dateToLocalDateTime(date).format(DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * 获取几年后时间
     *
     * @param date   date
     * @param number number
     * @return
     */
    public static Date getPlusNumberYear(Date date, int number) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.plusYears(number);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取某天快结束的时间戳
     * date值为 2022-02-01 12:23:91 返回的值为 2022-02-01 23:59:59
     *
     * @param date
     * @return
     */
    public static Date getDateEnding(Date date) {
        LocalDate localDate = dateToLocalDate(date);
        LocalDateTime localDateTime = localDate.atTime(23, 59, 59);
        return localDateTimeToDate(localDateTime);
    }

    /**
     * 获取某天开始的时间戳
     * date值为 2022-02-01 12:23:91 返回的值为 2022-02-01 00:00:00
     *
     * @param date
     * @return
     */
    public static Date getDateStart(Date date) {
        LocalDate localDate = dateToLocalDate(date);
        LocalDateTime localDateTime = localDate.atTime(00, 00, 00);
        return localDateTimeToDate(localDateTime);
    }

    /**
     * 今天天0点
     *
     * @return
     */
    public static Date getinitDateStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 昨天0点
     *
     * @return
     */
    public static Date getYesterdayDateStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, calendar.get(Calendar.DAY_OF_MONTH) - 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 注意
     *
     * @param str
     * @param format
     * @return
     */
    public static Date timeStrToDate(String str, TimeFormat format) {
        LocalDateTime myDate = LocalDateTime.parse(str, format.formatter);
        return localDateTimeToDate(myDate);
    }

    /**
     * 注意
     *
     * @param str
     * @param format
     * @return
     */
    public static LocalDateTime timeStrToLocalDate(String str, TimeFormat format) {
        LocalDateTime myDate = LocalDateTime.parse(str, format.formatter);
        return myDate;
    }

    /**
     * 字符串转Date
     *
     * @param str
     * @return
     */
    public static Date strToDate(String str) {
        LocalDate myDate = LocalDate.parse(str, DEFAULT_DATE_FORMATTER);
        return localDateToDate(myDate);
    }

    /**
     * 获取开始时间 后面加 00:00:00
     *
     * @param date
     * @return
     */
    public static Date getStartTime(Date date) {
        if (null == date) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        try {
            return simpleDateFormat.parse(simpleDateFormat.format(date));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取开始时间 后面加 23:59:59
     *
     * @param date
     * @return
     */
    public static Date getEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, CommonConstant.ZERO);
        return calendar.getTime();
    }

    /**
     * 传入一个日期，判断时分是否在时间段范围内
     *
     * @param time
     * @param timeRange
     * @param splitStr
     * @return
     */
    public static Boolean dateBetweenTime(Date time, String timeRange, String splitStr) {
        String[] split = timeRange.split(splitStr);
        String startTimeStr = split[0];
        String endTimeStr = split[1];
        // 获取当前日期的年月日
        // 获取time的时分部分，并构建为今天的Date对象
        Date timeOnly = DateUtil.parse(DateUtil.format(time, "HH:mm"), "HH:mm");
        // 使用今天的日期构建开始时间和结束时间的Date对象
        Date startTime = DateUtil.parse(startTimeStr, "HH:mm");
        Date endTime = DateUtil.parse(endTimeStr, "HH:mm");
        // 比较时分
        return !timeOnly.before(startTime) && !timeOnly.after(endTime);
    }

    /**
     * 根据日期获取weekName
     *
     * @param localDate
     * @return
     */
    public static String getWeekName(LocalDate localDate) {
        DayOfWeek week = localDate.getDayOfWeek();
        switch (week) {
            case MONDAY:
                return "周一";
            case TUESDAY:
                return "周二";
            case WEDNESDAY:
                return "周三";
            case THURSDAY:
                return "周四";
            case FRIDAY:
                return "周五";
            case SATURDAY:
                return "周六";
            case SUNDAY:
                return "周日";
            default:
                return null;
        }
    }

    public static List<String> getDateRange(Date startDate, Date endDate) {
        List<String> dateList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        while (!calendar.getTime().after(endDate)) {
            dateList.add(sdf.format(calendar.getTime()));
            calendar.add(Calendar.DATE, 1);
        }

        return dateList;
    }

    /**
     * 向上取整点
     *
     * @param date
     * @return
     */
    public static Date roundUpToHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 将分钟和秒数设为0
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }


    /**
     * 合并时间范围：给定一个LIst<Date> 每一个item格式为：2024-06-18 12:00 代表的是：2024-06-18 12:00到 2024-06-18 13:00 时间范围，要求将其合并成一个完整的时间范围List 其数据格式为： public TimeRange(LocalDateTime startTime, LocalDateTime endTime) { this.startTime = startTime; this.endTime = endTime; }
     *
     * @param dateList
     * @return
     */
    public static List<TimeRange> mergeTimeRanges(List<Date> dateList) {
        if (dateList.isEmpty()) {
            return new ArrayList<>();
        }

        List<TimeRange> mergedRanges = new ArrayList<>();
        LocalDateTime previousEnd = null;

        for (Date date : dateList) {
            LocalDateTime currentStart = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalDateTime currentEnd = currentStart.plusHours(1); // Assuming each date represents a 1-hour range

            if (previousEnd == null || currentStart.isAfter(previousEnd)) {
                mergedRanges.add(new TimeRange(currentStart, currentEnd));
                previousEnd = currentEnd;
            } else {
                mergedRanges.get(mergedRanges.size() - 1).setEndTime(currentEnd);
                previousEnd = currentEnd;
            }
        }
        return mergedRanges;
    }

    /**
     * 判断给定的开始和结束时间是否处于时间范围内
     *
     * @param dateList
     * @param startDate
     * @param endDate
     * @return
     */
    public static boolean isWithinTimeRanges(List<Date> dateList, Date startDate, Date endDate) {
        LocalDateTime startTime = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime endTime = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        List<TimeRange> mergedRanges = mergeTimeRanges(dateList);
        for (TimeRange range : mergedRanges) {
            /*if ((startTime.isEqual(range.getStartTime()) || startTime.isAfter(range.getStartTime()))
                    && (endTime.isEqual(range.getEndTime()) || endTime.isBefore(range.getEndTime()))) {
                return true;
            }*/
            // 上面判断覆盖不住立即预约操作，调整为时间段存在交集操作；
            if (hasOverlap(startTime, endTime, range.getStartTime(), range.getEndTime())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断两个时段是否有交集
     *
     * @param sourceStartTime
     * @param sourceEndTime
     * @param destStartTime
     * @param destEndTime
     * @return
     */
    public static boolean hasOverlap(LocalDateTime sourceStartTime, LocalDateTime sourceEndTime, LocalDateTime destStartTime, LocalDateTime destEndTime) {
        return !(sourceEndTime.isBefore(destStartTime) || sourceEndTime.isEqual(destStartTime) || sourceStartTime.isAfter(destEndTime) || sourceStartTime.isEqual(destEndTime));
    }

    /**
     * 给定Date类型 要求向上取整秒 比如1718791200018 转化为Date类型的1718791200000
     *
     * @param date
     * @return
     */
    public static Date roundUpToSecond(Date date) {
        long timeInMillis = date.getTime();
        long roundedTime = (timeInMillis / 1000) * 1000; // 向下取整到秒
        if (timeInMillis % 1000 != 0) {
            roundedTime += 1000; // 向上取整到秒
        }
        return new Date(roundedTime);
    }

    public static Date floorDateToMinute(Date date) {
        long timeInMillis = date.getTime();
        long roundedTimeInMillis = timeInMillis / 60000 * 60000;
        return new Date(roundedTimeInMillis);
    }

    public static String getCurrTimeInterval() {
        LocalTime nowTime = LocalTime.now();
        return MessageFormat.format("{0}-{1}",
                TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter.format(nowTime),
                TimeFormat.DATE_PATTERN_HM_SIMPLE.formatter.format(nowTime.plusHours(CommonConstant.ONE)));
    }

    public static String timeStringFormatString(String originalDatetimeStr,String oriFormat,String targeFormat){
        // 解析为 DateTime 对象
        DateTime datetimeObj = DateTime.of(originalDatetimeStr, oriFormat);

        // 格式化为所需的字符串
        return datetimeObj.toString(targeFormat);
    }

    public static void main(String[] args) {
        String dates = "[1724050800000,1724054400000,1724058000000,1724061600000,1724065200000,1724068800000,1724072400000,1724076000000,1724137200000,1724140800000,1724144400000,1724148000000,1724151600000,1724155200000,1724158800000,1724162400000,1724223600000,1724227200000,1724230800000,1724234400000,1724238000000,1724241600000,1724245200000,1724248800000,1724310000000,1724313600000,1724317200000,1724320800000,1724324400000,1724328000000,1724331600000,1724335200000,1724396400000,1724400000000,1724403600000,1724407200000,1724410800000,1724414400000,1724418000000,1724421600000,1724482800000,1724486400000,1724490000000,1724493600000,1724497200000,1724500800000,1724504400000,1724508000000]";
        List<Date> dateList = JSON.parseArray(dates, Date.class);
        for (Date date : dateList) {
            System.out.println(DateUtil.format(date, "yyyy-MM-dd HH:mm:ss.SSS"));
        }
        List<TimeRange> timeRanges = mergeTimeRanges(dateList);
        System.out.println(JSON.toJSONString(timeRanges));

        String start = "2024-07-31 11:36:00";
        String end = "2024-07-31 13:00:00";
        DateTime startTime = DateUtil.parse(start, DEFAULT_DATETIME_FORMATTER);
        DateTime endTime = DateUtil.parse(end, DEFAULT_DATETIME_FORMATTER);
        boolean withinTimeRanges = isWithinTimeRanges(dateList, startTime, endTime);
        System.out.println(withinTimeRanges);


        System.out.println(dateTimeToStr(new Date(), "yyyy-MM-dd"));

    }

    public static boolean isStartTimeInRangeAndLessThanEndByOneHour(String rangeA, String rangeB) {
        int[] timeA = parseTimeRange(rangeA);
        int[] timeB = parseTimeRange(rangeB);

        int startA = timeA[0];
        int endA = timeA[1];
        int startB = timeB[0];
        int endB = timeB[1];

        // Check if startA is within rangeB considering possible cross-day
        boolean isStartInRange = isTimeInRange(startA, startB, endB);
        boolean isStartLessThanEndByOneHour = isLessThanEndByOneHour(startA, endB);

        return isStartInRange && isStartLessThanEndByOneHour;
    }

    private static int[] parseTimeRange(String range) {
        String[] times = range.split("-");
        return new int[]{parseTime(times[0]), parseTime(times[1])};
    }

    private static int parseTime(String time) {
        String[] parts = time.split(":");
        int hours = Integer.parseInt(parts[0]);
        int minutes = Integer.parseInt(parts[1]);
        return hours * 60 + minutes;
    }

    private static boolean isTimeInRange(int time, int start, int end) {
        if (start <= end) {
            // Normal case, no cross-day
            return time >= start && time < end;
        } else {
            // Cross-day case
            return time >= start || time < end;
        }
    }

    private static boolean isLessThanEndByOneHour(int start, int end) {
        if (start < end) {
            return (start + 60) <= end;
        } else {
            // When end is on the next day
            return (start + 60) <= (end + 24 * 60);
        }
    }


    public static boolean isStartTimeInRange(String rangeA, String rangeB) {
        int[] timeA = parseTimeRangeNew(rangeA);
        int[] timeB = parseTimeRangeNew(rangeB);

        int startA = timeA[0];
        int startB = timeB[0];
        int endB = timeB[1];

        // Check if startA is within rangeB considering possible cross-day
        return isTimeInRangeNew(startA, startB, endB);
    }

    private static int[] parseTimeRangeNew(String range) {
        String[] times = range.split("-");
        return new int[]{parseTimeNew(times[0]), parseTime(times[1])};
    }

    private static int parseTimeNew(String time) {
        String[] parts = time.split(":");
        int hours = Integer.parseInt(parts[0]);
        int minutes = Integer.parseInt(parts[1]);
        return hours * 60 + minutes;
    }

    private static boolean isTimeInRangeNew(int time, int start, int end) {
        if (start <= end) {
            // Normal case, no cross-day
            return time >= start && time < end;
        } else {
            // Cross-day case
            return time >= start || time < end;
        }
    }

    /**
     * 获取前后N天开始时间
     */
    public static Date getBeforeOrAfterDayStartTime(int num) {
        DateTime dateTime = DateUtil.beginOfDay(new Date());
        if (num != 0) {
            return DateUtil.offsetDay(dateTime, num);
        }
        return dateTime;
    }

    /**
     * 获取前后N分钟开始时间
     */
    public static Date getBeforeOrAfterMinuteStartTime(int num) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, num);
        return getMinuteStartTime(calendar.getTime());
    }

    /**
     * 获取开始时间 后面加 00:00:00
     *
     * @param date
     * @return
     */
    public static Date getMinuteEndTime(Date date) {
        if (null == date) {
            return null;
        }
        String dt = dateTimeToStr(date, TimeFormat.LONG_PATTERN_LINE_NO_S) + ":59";
        return strToDateTime(dt, TimeFormat.LONG_PATTERN_LINE);
    }

    /**
     * 获取开始时间 后面加 00:00:00
     *
     * @param date
     * @return
     */
    public static Date getMinuteStartTime(Date date) {
        if (null == date) {
            return null;
        }
        String dt = dateTimeToStr(date, TimeFormat.LONG_PATTERN_LINE_NO_S) + ":00";
        return strToDateTime(dt, TimeFormat.LONG_PATTERN_LINE);
    }

    /**
     *
     * @param str
     * @param format
     * @return
     */
    public static Date strToDateTime(String str, TimeFormat format) {
        LocalDateTime myDate = LocalDateTime.parse(str, format.formatter);
        return localDateTimeToDate(myDate);
    }
}




