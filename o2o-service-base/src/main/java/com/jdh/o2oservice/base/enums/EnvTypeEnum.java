package com.jdh.o2oservice.base.enums;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * 前端环境枚举
 * @author: yang<PERSON><PERSON>
 * @date: 2024/1/4 3:41 下午
 * @version: 1.0
 */
@Getter
public enum EnvTypeEnum {

    /**
     * 前端环境参数
     */
    JD_APP("jdapp", "京东app", 1),
    JD_TARO("jdTaro", "",2),
    JDH_APP("jdhapp", "健康app",3),
    JDH_E_APP("jdheapp", "E企健康",4),
    JDH_ME_APP("jdmeapp", "京me",5),
    MINI("miniprogram", "小程序",6),
    WX_WORK("wxwork", "企业微信",7),
    WX_XIN("wexin", "微信",8),
    QQ("qq", "",9),
    H5("h5", "",10),
    JDH_MINI_PROGRAM("miniprogram_jdh", "", 11),
    KANGKANG_MINI_PROGRAM("miniprogram_kangkang", "", 12),
    STORE_MINI_PROGRAM("miniprogram_store", "同城门店小程序", 13),
    JINGGOU_MINI_PROGRAM("miniprogram_jinggou", "京购小程序", 14),
    QQ_JINGGOU_MINI_PROGRAM("miniprogram_qq_jinggou", "qq京购小程序", 15),

    ;

    /**
     * 环境类型枚举
     *
     * @param code 代码
     */
    EnvTypeEnum(String code, String desc, Integer channelId) {
        this.code = code;
        this.desc = desc;
        this.channelId = channelId;
    }

    /**
     * 代码
     */
    private String code;
    private String desc;
    private Integer channelId;

    /**
     * ENV_MAP
     */
    private static final Map<String, EnvTypeEnum> ENV_MAP = Maps.newHashMap();

    /**
     *
     */
    static {
        for (EnvTypeEnum value : values()) {
            ENV_MAP.put(value.code, value);
        }
    }

    /**
     * get
     *
     * @param code 代码
     * @return {@link EnvTypeEnum}
     */
    public static EnvTypeEnum get(String code){
        if (StringUtils.isBlank(code)){
            return EnvTypeEnum.H5;
        }
        return Objects.isNull(ENV_MAP.get(code)) ? EnvTypeEnum.H5 : ENV_MAP.get(code);
    }
}
