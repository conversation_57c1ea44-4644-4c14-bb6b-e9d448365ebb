package com.jdh.o2oservice.base.event;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.model.Aggregate;
import org.slf4j.MDC;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 消费医疗事件
 * @author: yangxiyu
 * @date: 2022/8/10 6:30 下午
 * @version: 1.0
 */
public class Event {

    /** 事件所属领域 */
    private String domainCode;
    /** 事件所属聚合 */
    private String aggregateCode;
    /** 事件主体 */
    private String aggregateId;
    /** 事件类型编号 */
    private String eventCode;
    /** 事件ID */
    private Long eventId;
    /** 发布时间 */
    private LocalDateTime publishTime;

    private Integer version;

    /** 事件的分值，每个关注当前事件的处理器会为事件加一分 */
    private Integer score;
    /** 事件处理器编号集合 */
    private String consumerCodes;

    /**
     * 扩展数据
     */
    private String body;
    /**
     * 触发当前事件的前置事件ID，如果没有前置事件ID则 triggerId == eventId成立
     */
    private Long rootEventId;
    /**
     * 日志traceId
     */
    private String traceId;
    /**
     * 是否延迟发布事件
     */
    private Boolean delay = false;

    /** constructor empty */
    public Event() {
    }
    /** constructor all filed*/
    public Event(Aggregate aggregate, EventType eventType, Long eventId, EventBody body) {
       this(aggregate, eventType, eventId, body, null, Boolean.FALSE);
    }

    /** constructor all filed*/
    public Event(Aggregate aggregate, EventType eventType, Long eventId, EventBody body, LocalDateTime time, Boolean delay) {
        this.domainCode = eventType.getAggregateType().getDomainCode().getCode();
        this.aggregateCode = eventType.getAggregateType().getCode();
        if (aggregate.getIdentifier() == null){
            throw new NullPointerException("aggregateId is null");
        }

        this.aggregateId = aggregate.getIdentifier().serialize();
        this.eventCode = eventType.getCode();
        this.eventId = eventId;
        this.delay = delay;
        if (Objects.nonNull(time)){
            this.publishTime = time;
        }else {
            this.publishTime = LocalDateTime.now();
        }
        this.version = aggregate.version();

        // 获取当前事件注册的consumer，每个consumer 1分
        Integer s = EventConsumerRegister.getScore(eventType);
        this.score = (s == null ? 0 : s);
        this.consumerCodes = EventConsumerRegister.getConsumerCodes(eventType);

        if (Objects.nonNull(eventType.bodyClass()) && Objects.isNull(body)){
            throw new SystemException(SystemErrorCode.EVENT_BODY_IS_NULL);
        }
        this.body = JSON.toJSONString(body);
        this.traceId =  MDC.get("PFTID");
    }

    /**
     * 事件唯一ID
     */
    public Long getEventId() {
        return eventId;
    }

    /** */
    public LocalDateTime getPublishTime() {
        return publishTime;
    }

    public String getAggregateId() {
        return aggregateId;
    }

    /** */
    public Integer getScore() {
        return score;
    }
    /** */
    public String getConsumerCodes() {
        return consumerCodes;
    }
    /** */
    public String getAggregateCode() {
        return aggregateCode;
    }
    /** */
    public void setAggregateCode(String aggregateCode) {
        this.aggregateCode = aggregateCode;
    }

    /** */
    public String getEventCode() {
        return eventCode;
    }
    /** */
    public String getDomainCode() {
        return domainCode;
    }
    /** */
    public void setDomainCode(String domainCode) {
        this.domainCode = domainCode;
    }
    /** */
    public void setAggregateId(String aggregateId) {
        this.aggregateId = aggregateId;
    }
    /** */
    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }
    /** */
    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }
    /** */
    public void setPublishTime(LocalDateTime publishTime) {
        this.publishTime = publishTime;
    }
    /** */
    public void setScore(Integer score) {
        this.score = score;
    }
    /** */
    public void setConsumerCodes(String consumerCodes) {
        this.consumerCodes = consumerCodes;
    }

    /**
     * 减去分值
     */
    public void subtractScore(){
        this.score = this.score--;
    }

    /**
     * 事件完结
     */
    public void finish(){
        this.score = 0;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getRootEventId() {
        return rootEventId;
    }

    public void setRootEventId(Long rootEventId) {
        this.rootEventId = rootEventId;
    }
    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public Boolean getDelay() {
        return delay;
    }

    public Boolean isDelay() {
        return delay;
    }

    public void setDelay(Boolean delay) {
        this.delay = delay;
    }

    /**
     *
     * @param clazz
     * @param <T>
     * @return
     */
    public  <T> T parseAggregateId(Class<T> clazz){
        return JSON.parseObject(aggregateId, clazz);
    }

    @Override
    public String toString() {
        return "Event{" +
                "domainCode='" + domainCode + '\'' +
                ", aggregateCode='" + aggregateCode + '\'' +
                ", aggregateId='" + aggregateId + '\'' +
                ", eventCode='" + eventCode + '\'' +
                ", eventId=" + eventId +
                ", publishTime=" + publishTime +
                ", version=" + version +
                ", score=" + score +
                ", consumerCodes='" + consumerCodes + '\'' +
                ", body='" + body + '\'' +
                ", triggerId=" + rootEventId +
                ", delay=" + delay +
                '}';
    }
}
