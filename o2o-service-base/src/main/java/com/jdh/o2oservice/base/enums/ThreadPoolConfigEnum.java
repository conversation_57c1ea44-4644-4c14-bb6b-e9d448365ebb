package com.jdh.o2oservice.base.enums;

import java.util.concurrent.RejectedExecutionHandler;

/**
 * <AUTHOR>
 * @Description 权益提供方
 * @Date 2019/12/27 20:17
 */
public enum ThreadPoolConfigEnum {


    /**
     * 默认线程池大小，线程数时CPU的两倍
     */
    DEFAULT("DEFAULT", 8, 8, null),

    /**
     * 报警线程池大小，线程数时CPU的两倍
     */
    ALARM_POOL("ALARM_POOL", 8, 8, null),

    /**
     * C页面楼层处理
     */
    VIA_FLOOR_HAND_POOL("VIA_FLOOR_HAND_POOL", 50, 50, null),

    /**
     * 运营端订详数据处理
     */
    MAN_VIA_DETAIL_HAND_POOL("MAN_VIA_DATA_HAND_POOL", 20, 20, null),

    /**
     * 地址命中围栏处理
     */
    ADDRESS_GIS_HIT_POOL("ADDRESS_GIS_HIT_POOL", 4, 4, null),

    /**
     * 商品查询
     */
    SKU_MAN_HAND_POOL("SKU_MAN_HAND_POOL", 20, 20, null),

    /**
     * 商品查询
     */
    SKU_C_HAND_POOL("SKU_C_HAND_POOL", 50, 50, null),

    /**
     * 运营端导入
     */
    MAN_IMPORT_HAND_POOL("MAN_IMPORT_HAND_POOL", 5, 5, null),

    /**
     * 商品价格查询
     */
    SKU_PRICE_C_HAND_POOL("SKU_PRICE_C_HAND_POOL", 50, 50, null),

    /**
     * 自定义列订单查询
     */
    CUSTOM_ORDER_HAND_POOL("CUSTOM_ORDER_HAND_POOL", 50, 50, null),

    /**
     * OPS Pool
     */
    OPS_POOL("OPS_POOL", 4, 100, null),
    
    DISPATCH_POOL("DISPATCH_POOL", 4, Integer.MAX_VALUE, null),

    /**
     * 报告查询
     */
    REPORT_C_POOL("REPORT_C_POOL", 20, 20, null),
    VOICE_JOB_POOL("VOICE_JOB_POOL", 4, Integer.MAX_VALUE, null),


    /**
     * 长连接
     */
    WS_POOL("WS_POOL", 20, 20, null),


    /**
     * 操作日志记录线程池
     */
    OPLOG_SAVE_THREAD_POOL("OPLOG_SAVE_THREAD_POOL", 50, 50, null),



    /**
     * 操作日志记录线程池
     */
    OPLOG_QUERY_THREAD_POOL("OPLOG_QUERY_THREAD_POOL", 20, 20, null),

    LOW_PRIORITY_POOL("LOW_PRIORITY_POOL", 4, Integer.MAX_VALUE, null),
    /**
     * 获取图片exif
     */
    GET_IMG_EXIF("GET_IMG_EXIF", 50, 50, null),
    /**
     * 自定义列订单查询
     */
    CUSTOM_ORDER_DETAIL_HAND_POOL("CUSTOM_ORDER_DETAIL_HAND_POOL", 50, 100, null),

    /**
     * 京东物流运力缓存
     */
    FRESH_SHIP_CACHE_HAND_POOL("CUSTOM_ORDER_DETAIL_HAND_POOL", 50, 50, null),

    /**
     * 用户行为
     */
    EXECUTE_ACTION_HAND_POOL("EXECUTE_ACTION_HAND_POOL", 50, 50, null),
    ;
    
    /**
     * 线程池名字
     */
    private final String poolName;
    
    /**
     * 核心线程数量
     */
    private final int poolSize;
    /**
     * 阻塞任务队列的长度
     */
    private final int capacity;

    /**
     * 线程池的可用线程数和阻塞队列的容量全部打满后，执行此任务策略：
     * 1. CallerRunsPolicy ：这个策略重试添加当前的任务，他会自动重复调用 execute() 方法，直到成功。
     * 2. AbortPolicy ：对拒绝任务抛弃处理，并且抛出异常。
     * 3. DiscardPolicy ：对拒绝任务直接无声抛弃，没有异常信息。
     * 4. DiscardOldestPolicy ：对拒绝任务不抛弃，而是抛弃队列里面等待最久的一个线程，然后把拒绝任务加到队列。
     */
    private final RejectedExecutionHandler rejectedHandler;

    /**
     * @param poolName 线程池名字
     * @param poolSize 核心线程数量
     * @param capacity 阻塞任务队列的长度
     * @param rejectedHandler 拒绝策略
     */
    ThreadPoolConfigEnum(String poolName, int poolSize, int capacity, RejectedExecutionHandler rejectedHandler) {
        this.poolName = poolName;
        this.poolSize = poolSize;
        this.capacity = capacity;
        this.rejectedHandler = rejectedHandler;
    }

    public String getPoolName() {
        return poolName;
    }

    public int getPoolSize() {
        return poolSize;
    }

    public int getCapacity() {
        return capacity;
    }

    public RejectedExecutionHandler getRejectedHandler() {
        return rejectedHandler;
    }
}
