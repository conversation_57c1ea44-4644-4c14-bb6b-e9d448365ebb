package com.jdh.o2oservice.core.domain.settlement.convert;


import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.core.domain.settlement.bo.ExternalDomainFeeConfigSaveBo;
import com.jdh.o2oservice.core.domain.settlement.bo.SettlementFeeDetailSaveBo;
import com.jdh.o2oservice.core.domain.settlement.bo.query.SettlementConfigDomainQuery;
import com.jdh.o2oservice.core.domain.settlement.bo.ThirdOrderEnterpriseBo;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleItemTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleStatusEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.*;
import com.jdh.o2oservice.core.domain.settlement.repository.query.ExternalDomainFeeConfigQuery;
import com.jdh.o2oservice.export.support.dto.JdhEnterpriseAccountContractDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 结算转换类工具
 */
@Mapper
public interface SettlementDomainConvert {
    SettlementDomainConvert instance = Mappers.getMapper(SettlementDomainConvert.class);

    /**
     * 转换成mq消息对象
     *
     * @param jdhSettlementEbs
     * @return
     */
    @Mapping(source = "businessLine", target = "bussinessLine")
    @Mapping(source = "firstCategoryCode", target = "categoryCode")
    @Mapping(source = "newOrgId", target = "nwOrgId")
    @Mapping(target = "docCreateTime", source = "docCreateTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "appliedDate", source = "appliedDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    JdEbsMessageInfo modelToMqMessage(JdhSettlementEbs jdhSettlementEbs);

    /**
     *
     * @param query
     * @return
     */
    @Mapping(target = "domainCode", expression = "java(java.util.Objects.isNull(query.getDomainCode()) ? \"\" : query.getDomainCode().getCode())" )
    @Mapping(target = "aggregateCode", expression = "java(java.util.Objects.isNull(query.getAggregateCode()) ? \"\" : query.getAggregateCode().getCode())" )
    @Mapping(target = "settlementSubjectType", expression = "java(java.util.Objects.isNull(query.getSettlementSubjectType()) ? \"\" : query.getSettlementSubjectType().getType())" )
    @Mapping(target = "settlementSubjectSubType", expression = "java(java.util.Objects.isNull(query.getSettlementSubjectSubType()) ? \"\" : query.getSettlementSubjectSubType().getType())" )
    ExternalDomainFeeConfigQuery request2Query(SettlementConfigDomainQuery query);

    /**
     *
     * @param list
     * @return
     */
    List<ExternalDomainFeeConfig> domainConfigBo2Entity(List<ExternalDomainFeeConfigSaveBo> list);

    /**
     *
     * @param bo
     * @return
     */
    default ExternalDomainFeeConfig domainConfigBo2Entity(ExternalDomainFeeConfigSaveBo bo){
        if (bo == null) {
            return null;
        }

        ExternalDomainFeeConfig externalDomainFeeConfig = new ExternalDomainFeeConfig();
        externalDomainFeeConfig.setId(bo.getId());
        externalDomainFeeConfig.setExternalDomainCode(java.util.Objects.isNull(bo.getDomainCode()) ? "" : bo.getDomainCode().getCode());
        externalDomainFeeConfig.setAggregateId(bo.getAggregateId());

        externalDomainFeeConfig.setFeeConfigId(java.util.Objects.isNull(bo.getFeeConfigId()) ? com.jdh.o2oservice.base.util.SpringUtil.getBean(GenerateIdFactory.class).getId() : bo.getFeeConfigId());
        externalDomainFeeConfig.setExternalAggregateCode(java.util.Objects.isNull(bo.getAggregateCode()) ? "" : bo.getAggregateCode().getCode());
        externalDomainFeeConfig.setSettlementSubjectType(java.util.Objects.isNull(bo.getSettlementSubjectType()) ? "" : bo.getSettlementSubjectType().getType());
        externalDomainFeeConfig.setSettlementSubjectSubType(java.util.Objects.isNull(bo.getSettlementSubjectSubType()) ? "" : bo.getSettlementSubjectSubType().getType());
        externalDomainFeeConfig.setDetailConfigList(settlementFeeDetailSaveBoListToJdhSettlementFeeDetailConfigList(bo.getDetailConfigList(), externalDomainFeeConfig.getFeeConfigId()));
        externalDomainFeeConfig.setVersion(bo.getVersion());
        return externalDomainFeeConfig;
    }


    /**
     * 护士服务费收入
     * @param context
     * @return
     */
    @Named("packAngelServiceSettlement")
    default AngelSettlement packAngelServiceSettlement(AngelSettlementAndEbsDetail context, BigDecimal settleAmount, Integer itemType) {
        AngelSettlement angelSettlement = new AngelSettlement();
        angelSettlement.setOrderId(context.getOrderId());
        angelSettlement.setAngelId(context.getAngelId());
        angelSettlement.setJobNature(context.getJdhAngelInfoBo() != null ? context.getJdhAngelInfoBo().getJobNature() : context.getJobNature());
        angelSettlement.setSettlementBusinessId(context.getSettlementBusinessId());
        angelSettlement.setPromiseId(context.getPromiseId());
        angelSettlement.setPatientId(context.getPromisePatientId());
        angelSettlement.setSkuId(Long.parseLong(context.getServiceId()));
        angelSettlement.setSettlementType(SettleTypeEnum.INCOME.getType());
        angelSettlement.setItemType(itemType);
        angelSettlement.setSettleAmount(settleAmount);
        angelSettlement.setSettleTime(context.getSettleTime());
        angelSettlement.setExpectSettleTime(context.getExpectSettleTime());
        angelSettlement.setSettleStatus(SettleStatusEnum.INIT.getType());
        return angelSettlement;
    }

    /**
     * 护士收入明细
     * @param settleId
     * @param itemType
     * @param settleAmount
     * @return
     */
    @Named("packAngelSettlementDetail")
    default AngelSettlementDetail packAngelSettlementDetail(Long settleId,Integer itemType,BigDecimal settleAmount) {
        AngelSettlementDetail angelSettlementDetail = new AngelSettlementDetail();
        angelSettlementDetail.setSettleId(settleId);
        angelSettlementDetail.setFeeName(SettleItemTypeEnum.getSettleTypeEnumByType(itemType).getDesc());
        angelSettlementDetail.setSettleAmount(settleAmount);
        return angelSettlementDetail;
    }

    /**
     *
     * @param jdhEnterpriseAccountContractDto
     * @return
     */
    ThirdOrderEnterpriseBo convertToThirdOrderEnterpriseBo(JdhEnterpriseAccountContractDto jdhEnterpriseAccountContractDto);


    /**
     *
     * @param list
     * @param feeConfigId
     * @return
     */
    default List<JdhSettlementFeeDetailConfig> settlementFeeDetailSaveBoListToJdhSettlementFeeDetailConfigList(List<SettlementFeeDetailSaveBo> list, Long feeConfigId) {
        if ( list == null ) {
            return null;
        }

        List<JdhSettlementFeeDetailConfig> list1 = new ArrayList<JdhSettlementFeeDetailConfig>( list.size() );
        for ( SettlementFeeDetailSaveBo settlementFeeDetailSaveBo : list ) {
            JdhSettlementFeeDetailConfig feeDetailConfig = settlementFeeDetailSaveBoToJdhSettlementFeeDetailConfig(settlementFeeDetailSaveBo);
            feeDetailConfig.setFeeConfigId(feeConfigId);
            list1.add(feeDetailConfig);
        }

        return list1;
    }

    /**
     *
     * @param settlementFeeDetailSaveBo
     * @return
     */
    default JdhSettlementFeeDetailConfig settlementFeeDetailSaveBoToJdhSettlementFeeDetailConfig(SettlementFeeDetailSaveBo settlementFeeDetailSaveBo) {
        if (settlementFeeDetailSaveBo == null) {
            return null;
        }

        JdhSettlementFeeDetailConfig jdhSettlementFeeDetailConfig = new JdhSettlementFeeDetailConfig();
        jdhSettlementFeeDetailConfig.setId(settlementFeeDetailSaveBo.getId());
        jdhSettlementFeeDetailConfig.setFeeConfigDetailId(java.util.Objects.isNull(settlementFeeDetailSaveBo.getFeeConfigDetailId()) ? com.jdh.o2oservice.base.util.SpringUtil.getBean(GenerateIdFactory.class).getId() : settlementFeeDetailSaveBo.getFeeConfigDetailId());
        if (settlementFeeDetailSaveBo.getFeeType() != null) {
            jdhSettlementFeeDetailConfig.setFeeType(String.valueOf(settlementFeeDetailSaveBo.getFeeType()));
        }
        jdhSettlementFeeDetailConfig.setFeeAmount(settlementFeeDetailSaveBo.getFeeAmount());
        jdhSettlementFeeDetailConfig.setExtend(settlementFeeDetailSaveBo.getExtend());
        jdhSettlementFeeDetailConfig.setVersion(settlementFeeDetailSaveBo.getVersion());

        return jdhSettlementFeeDetailConfig;
    }
}
