package com.jdh.o2oservice.core.domain.settlement.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Objects;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/29 11:38 上午
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum SettleItemTypeEnum {
    // 1上门检测服务 2上门护理服务 3调整项 4 激励 5 其他费项
    TESTING(1, "服务费",68, "订单收入"),
    NURSING(2, "服务费",68, "订单收入"),
    ADJUST(3, "保底工资调整费",70, "调账收入"),
    INCENTIVE(4, "激励费",70, "任务激励"),
    FEE(5, "上门费",69, "订单收入"),
    OTHER(6, "其他费项",69, "订单收入"),
    ACTIVITY(7, "邀请佣金",70, "任务奖励"),

    ADJUST_TESTING(21, "人工调账-上门检测服务费",70, "调账收入"),
    ADJUST_NURSING(22, "人工调账-上门护理服务费",70, "调账收入"),
    ADJUST_ADJUST(23, "人工调账-保底工资",70, "调账收入"),
    ADJUST_INCENTIVE(24, "人工调账-激励奖金",70, "调账收入"),
    ADJUST_FEE(25, "人工调账-费项",70, "调账收入"),
    ADJUST_OTHER(26, "人工调账-其他",70, "调账收入"),
    PLATFORM_SERVICE_FEE(61, "平台服务费",92, "到家平台服务费"),
    ;


    /**
     *
     */
    private Integer type;

    /**
     *
     */
    private String desc;
    /**
     *
     */
    private Integer huYiFeeType;

    /**
     * 结算升级后描述
     */
    private String upgradeDesc;

    /**
     * @param type
     * @return
     */
    public static SettleItemTypeEnum getSettleTypeEnumByType(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        for (SettleItemTypeEnum settleTypeEnum : SettleItemTypeEnum.values()) {
            if (Objects.equals(settleTypeEnum.getType(), type)) {
                return settleTypeEnum;
            }
        }
        return null;
    }
    /**
     * @param type
     * @return
     */
    public static String getSettleTypeDescByType(Integer type) {
        if (Objects.isNull(type)) {
            return "";
        }
        for (SettleItemTypeEnum settleTypeEnum : SettleItemTypeEnum.values()) {
            if (Objects.equals(settleTypeEnum.getType(), type)) {
                return settleTypeEnum.getDesc();
            }
        }
        return "";
    }

    /**
     * 是否是订单收入
     * @param type
     * @return
     */
    public static boolean isOrderRevenue(Integer type) {
        if (Objects.isNull(type)) {
            return false;
        }
        ArrayList<Integer> typeList = Lists.newArrayList(SettleItemTypeEnum.TESTING.getType(), SettleItemTypeEnum.NURSING.getType(), SettleItemTypeEnum.FEE.getType(), SettleItemTypeEnum.OTHER.getType());
        return typeList.contains(type);
    }
}
