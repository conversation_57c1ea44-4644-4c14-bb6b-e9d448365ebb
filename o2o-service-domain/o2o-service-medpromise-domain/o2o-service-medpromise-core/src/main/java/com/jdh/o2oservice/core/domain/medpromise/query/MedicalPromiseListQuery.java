package com.jdh.o2oservice.core.domain.medpromise.query;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Description: 检测单分页查询query
 * @Author: wangpengfei144
 * @Date: 2024/4/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicalPromiseListQuery extends AbstractPageQuery {
    /**
     * 检测单ID
     */
    private Long medicalPromiseId;
    /**
     * 履约单ID
     */
    private Long promiseId;
    /**
     * 检测单状态
     */
    private Integer status;
    /**
     * 样本条码
     */
    private String specimenCode;
    /**
     * 检测项目ID
     */
    private String serviceItemId;
    /**
     * 耗材类型
     */
    private String materialType;

    /**
     * 实验室id
     */
    private String stationId;
    /** 用户唯一ID */
    private Long promisePatientId;
    /**
     * 受检者ID
     */
    private List<Long> promisePatientIdList;

    /**
     * 嵌套查询
     */
    private List<MedicalPromiseNestedQuery> medicalPromiseNestedQueries;

    /**
     * 样本条码列表
     */
    private List<String> specimenCodeList;

    /**
     * 是否查作废的单子,默认查询
     */
    private Boolean invalid = Boolean.TRUE;

   /**
     * 检测单IDs
     */
    private List<Long> medicalPromiseIds;

    /**
     * 履约单IDList
     */
    private List<Long> promiseIdList;

    /**
     * 是否查冻结的单子，默认查询
     */
    private Boolean freezeQuery = Boolean.TRUE;
    /**
     * 开始时间
     */
    private Date startDate;
    /**
     * 结束时间
     */
    private Date endDate;
    /**
     * 报告开始时间
     */
    private Date reportStartTime;
    /**
     * 报告结束时间
     */
    private Date reportEndTime;
    /**
     * 报告状态 0.未出报告，1.已出报告
     */
    private Integer reportStatus;
    /**
     * verticalCodeSet
     */
    private Set<String> verticalCodeSet;
    /**
     * 是否查询全部(包括yn=0)
     */
    private Boolean allQuery = Boolean.FALSE;

    /**
     * 排序规则，asc desc
     */
    private String orderBy;

    /** 合管检测单ID */
    private Long mergeMedicalId;
    /**
     * 检测单特殊标记（0.普通订单，1.加项订单）
     */
    private String flag;

    /**
     * 检查开始时间
     */
    private Date checkStartTime;
    /**
     * 检查结束时间
     */
    private Date checkEndTime;

    /** 服务单ID */
    private Long voucherId;

    /**
     * 是否查询项目详情（非实验室关联配置）
     */
    private Boolean itemSummaryDetail = Boolean.FALSE;

    /**
     * 服务条目id
     */
    private List<Long> serviceItemIds;
}
