package com.jdh.o2oservice.core.domain.medpromise.context;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 检测项目context
 * @Author: wangpengfei144
 * @Date: 2024/4/30
 */
@Data
public class JdhServiceItemContext {

    /**
     * <pre>
     * 京东门店id
     * </pre>
     */
    private String stationId;

    /**
     * <pre>
     * 项目id
     * </pre>
     */
    private String serviceItemId;

    /**
     * <pre>
     * 项目名
     * </pre>
     */
    private String serviceItemName;

    /**
     * <pre>
     * 项目英文名
     * </pre>
     */
    private String serviceItemNameEn;

    /**
     * <pre>
     * 实验室结算价格,单位:元
     * </pre>
     */
    private BigDecimal settlementPrice;

    /**
     * <pre>
     * 耗材包id
     * </pre>
     */
    private Long materialPackageId;

    /**
     * <pre>
     * 采样方式
     * </pre>
     */
    private String specimenWay;

    /**
     * <pre>
     * 样本类型 1鼻咽拭子采样、2唾液、3痰液、4粪便、5肛周拭子、6尿液、7指尖血、8干血斑、9阴道/宫颈采样拭子、10C13吹气袋（幽门螺旋杆菌检测）、11头发（带毛囊）、12静脉血
     * </pre>
     */
    private Integer specimenType;

    /**
     * <pre>
     * 检测方法学
     * </pre>
     */
    private Integer testWay;

    /**
     * <pre>
     * 样本保存时长,单位小时
     * </pre>
     */
    private Integer specimenPreserveDuration;

    /**
     * <pre>
     * 样本量,数值及单位
     * </pre>
     */
    private String specimenNum;

    /**
     * <pre>
     * 服务时长,单位分钟
     * </pre>
     */
    private Integer serviceDuration;

    /**
     * <pre>
     * 样本存放要求
     * </pre>
     */
    private String specimenPreserveCondition;

    /**
     * <pre>
     * 检测时长,单位分钟
     * </pre>
     */
    private Integer testDuration;

    /**
     * <pre>
     * 服务要求
     * </pre>
     */
    private String serviceCondition;

    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;

    /**
     * 实验室名称
     */
    private String stationName;

    /**
     * 报告展示类型，1.结构化，2.PDF
     */
    private Integer reportShowType;

}
