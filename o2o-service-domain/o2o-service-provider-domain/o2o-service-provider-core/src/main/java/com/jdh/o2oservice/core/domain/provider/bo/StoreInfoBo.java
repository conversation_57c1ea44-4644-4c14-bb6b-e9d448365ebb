package com.jdh.o2oservice.core.domain.provider.bo;

import com.jdh.o2oservice.export.support.dto.FileUrlDto;
import lombok.Data;

import java.util.List;

/**
 * @author: yang<PERSON>yu
 * @date: 2022/11/9 7:58 下午
 * @version: 1.0
 */
@Data
public class StoreInfoBo {
    /** */
    private Long channelNo;
    /** 京东门店编号 */
    private String jdStoreId;

    /** 外部门店编号 */
    private String storeOuterId;
    /** 服务门店状态（是否可用） */
    private Integer status;

    /** 门店名称 */
    private String storeName;

    /** 门店地址 */
    private String storeAddr;
    /** 门店电话,可能多个 */
    private String storePhone;


    /** 门店类型1公立医院 2私立医院 3专业体检机构 */
    private Integer storeType;
    /** 门店类型*/
    private String storeTypeLabel;
    /** 门店等级(公立医院) */
    private Integer storeLevel;
    /** 门店等级(公立医院) */
    private String storeLevelLabel;
    /** 京东-省编码 */
    private Integer provinceId;
    /** 省名称 */
    private String provinceName;
    /** 京东-市编码 */
    private Integer cityId;
    /** 市名称 */
    private String cityName;
    /** 京东-县/区编码 */
    private Integer countyId;
    /** 县/区名称 */
    private String countyName;

    /** 经度 */
    private String lng;
    /** 纬度 */
    private String lat;

    /** 是否支持电子报告回传 */
    private Integer reportSupport;
    /** 是否支持核销 */
    private Integer supportWriteOff;
    /** 门店营业时间 */
    private String storeHours;
    /** 门店备注 */
    private String storeRemark;

    /** 是否设置了门店备注 */
    private Integer storeRemarkStatus;

    /** 品牌编号 */
    private Long brandId;

    /** 品牌名称 */
    private String brandName;

    /**
     * 爆单开关
     */
    private Integer limitBuyStatus;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 围栏id
     */
    private String storeFenceId;

    /**
     *
     */
    private String storeId;

    /**
     * 天算渠道ID
     */
    private String channelRuleCode;
    
    /**
     * 门店配置信息
     */
    private String storeConfig;
    /**
     * 接入方式
     */
    private Integer dockingType;
    /**
     * 端接入方式,绑定的虚拟商家
     */
    private String firstChannelNo;

    /**
     * 资质图片列表
     */
    private List<Long> licenseFileIdList;

    /**
     * 资质图片列表
     */
    private List<FileUrlDto> licenseFileList;

    /**
     * 实验室支持报告类型
     */
    private List<Integer> reportFormatList;

    /**
     * 使用的系统版本
     */
    private String useSystemVersion;
}
