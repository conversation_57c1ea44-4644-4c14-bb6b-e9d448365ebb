package com.jdh.o2oservice.core.domain.promise.enums;

import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;

/**
 * 履约域 聚合类型
 */
public enum PromiseAggregateEnum implements AggregateCode {

    /** 服务单 */
    VOUCHER(DomainEnum.PROMISE, "voucher"),
    /** 履约单 */
    PROMISE(DomainEnum.PROMISE, "promise"),
    /** 履约单 - 历史 */
    PROMISE_HISTORY(DomainEnum.PROMISE, "promiseHistory"),
    /** 履约单 - 草稿 */
    PROMISE_DRAFT(DomainEnum.PROMISE, "draft"),
    ;

    /** */
    private final DomainCode domain;

    /** */
    private final String code;

    /** */
    PromiseAggregateEnum(DomainCode domain, String code) {
        this.domain = domain;
        this.code = code;
    }

    /** */
    @Override
    public DomainCode getDomainCode() {
        return domain;
    }

    /** */
    @Override
    public String getCode() {
        return code;
    }

}
