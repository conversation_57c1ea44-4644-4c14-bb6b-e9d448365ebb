package com.jdh.o2oservice.core.domain.promise.service;

import com.jdh.o2oservice.core.domain.promise.context.*;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;

import java.util.List;

/**
 * JdhPromiseDomainService
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
public interface JdhPromiseDomainService {


    /**
     * createPromise
     *
     * @param context 上下文
     * @return {@link List}<{@link JdhPromise}>
     */
    List<JdhPromise> createPromise(CreatePromiseContext context);

    /**
     * 批量创建履约单
     *
     * @param context 上下文
     * @return {@link List}<{@link JdhPromise}>
     */
    List<JdhPromise> batchCreatePromise(BatchCreatePromiseContext context);

    /**
     * 提交
     * @param context
     */
    Boolean submitPromise(PromiseSubmitAbilityContext context);

    /**
     * 提交预约草稿数据
     * @param context
     * @return
     */
    void submitDraft(SubmitAppointmentDraftContext context);

    /**
     *
     * @param context
     */
    void callback(PromiseCallbackAbilityContext context);

    /**
     * 核销
     * @param context
     */
    void writeOff(PromiseWriteOffContext context);

    /**
     * 是否需要发起派单
     *
     * @param context 上下文
     */
    Boolean requiredDispatch(PromiseDispatchContext context);

    /**
     * 是否需要创建实验室检测单
     * @param context 上下文
     * @return Boolean
     */
    Boolean requiredCreateMedicalPromise(PromiseCreateMedicalPromiseContext context);

    /**
     * 强制冻结
     */
    void freeze(PromiseFreezeContext context);

    /**
     * 作废
     * @param context
     */
    void invalid(PromiseInvalidContext context);





}
