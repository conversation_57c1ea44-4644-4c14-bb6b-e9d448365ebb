package com.jdh.o2oservice.core.domain.promise.enums;

import com.google.common.collect.Maps;
import com.jdh.o2oservice.base.event.EventType;
import com.jdh.o2oservice.base.event.VerticalEventBody;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.core.domain.promise.event.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 履约域事件类型枚举
 *
 * <AUTHOR>
 * @date 2024/01/24
 */
public enum PromiseEventTypeEnum implements EventType {

    /**
     * 服务单事件
     */
    /** 服务单 - 创建 */
    VOUCHER_CREATE(PromiseAggregateEnum.VOUCHER,  "voucherCreate","履约域服务单创建", VerticalEventBody.class),
    /** 服务单 - 批量创建 */
    VOUCHER_BATCH_CREATE(PromiseAggregateEnum.VOUCHER,  "voucherBatchCreate","履约域服务单批量创建", BatchCreateVoucherEventBody.class),
    /** 服务单 - 冻结 */
    VOUCHER_FREEZE(PromiseAggregateEnum.VOUCHER,  "voucherFreeze","履约域服务单冻结", null),
    /** 服务单 - 解冻 */
    VOUCHER_UN_FREEZE(PromiseAggregateEnum.VOUCHER,  "voucherUnFreeze","履约域服务单解冻", null),
    /** 服务单 - 完成 */
    VOUCHER_COMPLETE(PromiseAggregateEnum.VOUCHER,  "voucherComplete","履约域服务单完成", null),
    /** 服务单 - 过期 */
    VOUCHER_EXPIRE(PromiseAggregateEnum.VOUCHER,  "voucherExpire","履约域服务单过期", null),
    /** 服务单 - 作废 */
    VOUCHER_INVALID(PromiseAggregateEnum.VOUCHER,  "voucherInvalid","履约域服务单作废", null),
    /** 服务单 - 延期 */
    VOUCHER_DELAY(PromiseAggregateEnum.VOUCHER,  "voucherDelay","履约域服务单延期", null),
    /** 服务单 - 自动预约 */
    VOUCHER_AUTO_APPOINTMENT(PromiseAggregateEnum.VOUCHER,  "voucherAutoAppointment","履约域服务单自动预约", null),
    /** 服务单 - 产码完成 */
    VOUCHER_PROMISE_CREATE(PromiseAggregateEnum.VOUCHER,  "voucherPromiseCreate","履约域发码完成", null),

    /** 服务单 - 临近x天过期 */
    VOUCHER_CLOSE_TO_EXPIRATION_ONE_DAY(PromiseAggregateEnum.VOUCHER,  "voucherCloseOfExpirationOneDay","临近7天过期", null),



    /**
     * 履约单事件
     */
    /** 草稿数据 */
    PROMISE_DRAFT_SUBMIT(PromiseAggregateEnum.PROMISE_DRAFT,  "draftSubmit","草稿数据提交", null),
    PROMISE_CREATED(PromiseAggregateEnum.PROMISE,  "created","履约单创建成功", null),

    /** 提交预约 */
    PROMISE_SUBMIT(PromiseAggregateEnum.PROMISE,  "submit","用户提交预约事件", PromiseSubmitEventBody.class),
    PROMISE_AUTO_SUBMIT(PromiseAggregateEnum.PROMISE,  "autoSubmit","自动提交预约", PromiseSubmitEventBody.class),
    PROMISE_APPOINTMENT_SUCCESS(PromiseAggregateEnum.PROMISE,  "appointmentSuccess","预约成功", PromiseCallbackEventBody.class),
    PROMISE_APPOINTMENT_FAIL(PromiseAggregateEnum.PROMISE,  "appointmentFail","预约失败", PromiseCallbackEventBody.class),

    /** 修改预约 */
    PROMISE_USER_SUBMIT_MODIFY(PromiseAggregateEnum.PROMISE,  "userSubmitModify","用户提交修改预约事件", PromiseModifyEventBody.class),
    PROMISE_MODIFY_SUCCESS(PromiseAggregateEnum.PROMISE,  "modifySuccess","修改预约成功事件", PromiseCallbackEventBody.class),
    PROMISE_MODIFY_FAIL(PromiseAggregateEnum.PROMISE,  "modifyFail","修改预约失败事件", PromiseCallbackEventBody.class),

    /** 取消预约 */
    PROMISE_USER_SUBMIT_CANCEL(PromiseAggregateEnum.PROMISE,  "userSubmitCancel","用户提交取消预约事件", PromiseEventBaseBody.class),
    PROMISE_VENDER_SUBMIT_CANCEL(PromiseAggregateEnum.PROMISE,  "venderSubmitCancel","商家取消预约事件", PromiseEventBaseBody.class),
    PROMISE_CANCEL_SUCCESS(PromiseAggregateEnum.PROMISE,  "cancelSuccess","取消成功事件", PromiseCallbackEventBody.class),
    PROMISE_CANCEL_FAIL(PromiseAggregateEnum.PROMISE,  "cancelFail","取消失败事件", PromiseCallbackEventBody.class),

    /**
     * 回调通知命令
     */
    PROMISE_CALLBACK_APPOINTMENT_SUCCESS(PromiseAggregateEnum.PROMISE,  "callBackAppointmentSuccess","回调预约成功命令", PromiseCallbackEventBody.class),
    PROMISE_CALLBACK_APPOINTMENT_FAIL(PromiseAggregateEnum.PROMISE,  "callBackAppointmentFail","回调预约失败命令", PromiseCallbackEventBody.class),
    PROMISE_CALLBACK_MODIFY_SUCCESS(PromiseAggregateEnum.PROMISE,  "callBackModifySuccess","回调修改成功命令", PromiseCallbackEventBody.class),
    PROMISE_CALLBACK_MODIFY_FAIL(PromiseAggregateEnum.PROMISE,  "callBackModifyFail","回调修改失败命令", PromiseCallbackEventBody.class),
    PROMISE_CALLBACK_CANCEL_SUCCESS(PromiseAggregateEnum.PROMISE,  "callBackCancelSuccess","回调取消成功命令", PromiseCallbackEventBody.class),
    PROMISE_CALLBACK_CANCEL_FAIL(PromiseAggregateEnum.PROMISE,  "callBackCancelFail","回调取消失败命令", PromiseCallbackEventBody.class),
    // 到检
    PROMISE_CALLBACK_NOTICE_COMPLETE(PromiseAggregateEnum.PROMISE,  "notifyComplete","回调通知服务已完成", null),
    PROMISE_CALLBACK_NOTICE_WRITE_OFF(PromiseAggregateEnum.PROMISE,  "notifyWriteOff","核销", PromiseEventBaseBody.class),

    /**
     * 完成，作废，冻结，解冻，延期
     */
    PROMISE_COMPLETE(PromiseAggregateEnum.PROMISE,  "complete","履约单完成", null),
    PROMISE_INVALID(PromiseAggregateEnum.PROMISE,  "invalid","履约单作废", PromiseEventBaseBody.class),
    PROMISE_FREEZE(PromiseAggregateEnum.PROMISE,  "freeze","履约单冻结", PromiseEventBaseBody.class),
    PROMISE_UN_FREEZE(PromiseAggregateEnum.PROMISE,  "unFreeze","履约单冻结", PromiseEventBaseBody.class),
    PROMISE_DELAY(PromiseAggregateEnum.PROMISE,  "delay","履约单延期", PromiseEventBaseBody.class),

    /**
     * 核销回调，已经已核销事件
     */
    PROMISE_WRITE_OFF(PromiseAggregateEnum.PROMISE,  "writeOff","核销完成", PromiseEventBaseBody.class),



    PROMISE_DISPATCH(PromiseAggregateEnum.PROMISE,  "promiseDispatch","履约单派单", PromiseEventBaseBody.class),
    PROMISE_MODIFY_DISPATCH(PromiseAggregateEnum.PROMISE,  "promiseModifyDispatch","履约单修改派单", PromiseEventBaseBody.class),
    /**
     * 回调 派单成功，服务开始，服务中，服务完成
     */
    PROMISE_CALLBACK_DISPATCH_SUCCESS(PromiseAggregateEnum.PROMISE,  "callBackDispatchSuccess","回调派单成功", PromiseEventBaseBody.class),
    // 外部事件转内部回调事件 包括：护士出门，骑手接单
    PROMISE_CALLBACK_SERVICE_READY(PromiseAggregateEnum.PROMISE,  "callBackServiceReady","回调服务者待服务", PromiseEventBaseBody.class),
    // 外部事件转内部回调事件 包括：护士开始服务，护士消费码验证成功，骑手待取货
    PROMISE_CALLBACK_SERVICING(PromiseAggregateEnum.PROMISE,  "callBackServicing","回调履约服务中", PromiseEventBaseBody.class),
    // 外部事件转内部回调事件 包括：护士服务完成，骑手取货完成
    PROMISE_CALLBACK_SERVICE_COMPLETE(PromiseAggregateEnum.PROMISE,  "callBackServiceComplete","回调履约服务完成", PromiseEventBaseBody.class),
    // 外部事件转内部回调事件
    PROMISE_CALLBACK_COMPLETE(PromiseAggregateEnum.PROMISE,  "callBackComplete","回调履约完成", PromiseEventBaseBody.class),

    /**
     * 服务开始，服务中，服务完成
     */
    PROMISE_SERVICE_READY(PromiseAggregateEnum.PROMISE,  "serviceReady","服务者待服务", PromiseEventBaseBody.class),
    PROMISE_SERVICING(PromiseAggregateEnum.PROMISE,  "servicing","履约服务中", PromiseEventBaseBody.class),
    PROMISE_SERVICE_COMPLETE(PromiseAggregateEnum.PROMISE,  "serviceComplete","履约服务完成", PromiseEventBaseBody.class),


    /**
     * 提醒事件
     */
    APPOINTMENT_BEFORE_DAY_REMIND(PromiseAggregateEnum.PROMISE,  "appointmentBeforeDayRemind","预约前一天提醒", null),
    APPOINTMENT_CURRENT_DAY_REMIND(PromiseAggregateEnum.PROMISE,  "appointmentCurrentDayRemind","预约当天提醒", null),

    /**
     *
     */
    TIMEOUT_NOT_WRITTEN_OFF_TEN(PromiseAggregateEnum.PROMISE,  "timeoutNotWrittenOffTen","超时未核销10天", null),
    CLOSE_TO_EXPIRATION_THIRTY(PromiseAggregateEnum.PROMISE,  "closeOfExpirationThirty","临近过期30天", null),
    CLOSE_TO_EXPIRATION_SEVEN(PromiseAggregateEnum.PROMISE,  "closeOfExpirationSeven","临近过期7天", null),
    /**
     * 体检报告已出
     */
    PROMISE_REPORT_GENERATE(PromiseAggregateEnum.PROMISE,"promiseReportGenerate","体检报告已出",null),
    PROMISE_REPORT_GENERATE_TO_BUY(PromiseAggregateEnum.PROMISE,"promiseReportGenerateToBuy","体检报告已出",null),
    ;


    /** 事件所属领域，用于区分 */
    private PromiseAggregateEnum aggregate;

    /** 事件名称 */
    private String code;

    /** 事件说明 */
    private String desc;

    private Class<?> bodyClass;

    /** */
    PromiseEventTypeEnum(PromiseAggregateEnum aggregate, String code, String desc, Class<?> bodyClazz) {
        this.aggregate = aggregate;
        this.code = code;
        this.desc = desc;
        this.bodyClass = bodyClazz;
    }
    /** */
    @Override
    public AggregateCode getAggregateType() {
        return aggregate;
    }
    /** */
    @Override
    public String getCode() {
        return code;
    }
    /** */
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Class<?> bodyClass() {
        return bodyClass;
    }

    private static final Map<String, PromiseEventTypeEnum> EVENT_MAP = Maps.newHashMap();
    static {
        for (PromiseEventTypeEnum value : values()) {
            EVENT_MAP.put(value.code, value);
        }
    }

    public static PromiseEventTypeEnum getByCode(String code){
        if (StringUtils.isBlank(code)){
            return null;
        }
        return EVENT_MAP.get(code);

    }
}
