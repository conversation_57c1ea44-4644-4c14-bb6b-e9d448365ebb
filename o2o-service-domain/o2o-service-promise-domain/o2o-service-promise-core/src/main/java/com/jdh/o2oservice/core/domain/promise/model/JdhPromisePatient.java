package com.jdh.o2oservice.core.domain.promise.model;

import com.jdh.o2oservice.base.model.*;
import com.jdh.o2oservice.core.domain.support.basic.model.User;
import lombok.Data;

import java.util.Date;

/**
 * 履约单相关患者信息（数据promise的实体，不是聚合）
 * @author: yang<PERSON>yu
 * @date: 2024/4/14 10:46 上午
 * @version: 1.0
 */
@Data
public class JdhPromisePatient extends User implements Entity<JdhPromisePatientIdentifier> {

    /** 正常状态 */
    private static final Integer NORMAL = 0;
    /** 冻结 */
    private static final Integer FREEZE_STATUS = 1;
    /** 作废 */
    private static final Integer INVALID = 2;
    /** */
    private Long id;
    /** */
    private Long promiseId;
    /** */
    private Long promisePatientId;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;
    /**
     * 服务类型
     */
    private String serviceType;


    /**
     * 服务单ID
     */
    private Long voucherId;


    /**
     * 服务兑换来源id
     */
    private String sourceVoucherId;
    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新用户
     */
    private String updateUser;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;


    /**
     *
     * @param user
     */
    public void fill(User user) {
        this.userPin = user.getUserPin();
        this.patientId = user.getPatientId();
        this.birthday = user.getBirthday();
        this.gender = user.getGender();
        this.marriage = user.getMarriage();
        this.userName = user.getUserName();
        this.phoneNumber = user.getPhoneNumber();
        this.credentialNum = user.getCredentialNum();
        this.relativesType = user.getRelativesType();
    }

    /**
     *
     * @return
     */
    @Override
    public Integer version() {
        return version;
    }

    @Override
    public void versionIncrease() {
        version++;
    }

    @Override
    public JdhPromisePatientIdentifier getIdentifier() {
        return new JdhPromisePatientIdentifier(promisePatientId);
    }

}
