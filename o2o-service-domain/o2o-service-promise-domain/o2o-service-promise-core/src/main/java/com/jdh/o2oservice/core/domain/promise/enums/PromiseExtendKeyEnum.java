package com.jdh.o2oservice.core.domain.promise.enums;

/**
 * 履约单扩展的属性枚举
 * @author: yang<PERSON><PERSON>
 * @date: 2024/4/25 2:35 下午
 * @version: 1.0
 */
public enum PromiseExtendKeyEnum {
    /** 履约单扩展属性名称 */
    ORDER_PHONE("orderPhone"),
    /** 订单备注信息*/
    ORDER_REMARK("orderRemark"),
    FIRST_GENERATE_REPORT("firstGenerateReport"),
    LAST_GENERATE_REPORT("lastGenerateReport"),
    HAS_ADDED("hasAdded"),
    ORDER_ID("orderId"),
    OPERATOR("operator"),
    OPERATOR_ROLE_TYPE("operatorRoleType"),
    INTENDED_NURSE("intendedNurse"),
    /**
     * 预约人姓名
     */
    APPOINTMENT_USER_NAME("appointmentUserName"),
    PROMISE_REPORT_SMS("promiseReportSms"),
    PROMISE_ALL_REPORT_SMS("promiseALLReportSms"),

    ;

    private String filedKey;

    PromiseExtendKeyEnum(String filedKey) {
        this.filedKey = filedKey;
    }

    public String getFiledKey() {
        return filedKey;
    }
}
