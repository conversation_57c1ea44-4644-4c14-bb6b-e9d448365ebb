package com.jdh.o2oservice.core.domain.promise.model;

import cn.hutool.core.util.StrUtil;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import lombok.Data;

import java.util.Date;

/**
 * 履约单相关扩展
 * @author: yang<PERSON>yu
 * @date: 2024/4/14 10:46 上午
 * @version: 1.0
 */
@Data
public class JdhPromiseExtend {
    
    /**
     * id
     */
    private Long id;

    /**
     * 履约单id
     */
    private Long promiseId;


    /**
     * 属性名
     */
    private String attribute;


    /**
     * 属性值
     */
    private String value;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    /**
     * 下单电话
     *
     * @param phone 电话
     */
    public void setOrderPhone(String phone){
        this.setAttribute(PromiseExtendKeyEnum.ORDER_PHONE.getFiledKey());
        this.setValue(phone);
    }

    /**
     * 获取下单电话
     *
     * @return {@link String}
     */
    public String getOrderPhone(){
        return PromiseExtendKeyEnum.ORDER_PHONE.getFiledKey().equals(this.getAttribute()) ? this.getValue() : StrUtil.EMPTY;
    }

    /**
     * 下单备注
     *
     * @param remark remark
     */
    public void setOrderRemark(String remark){
        this.setAttribute(PromiseExtendKeyEnum.ORDER_REMARK.getFiledKey());
        this.setValue(remark);
    }

    /**
     * 下单备注
     *
     * @return {@link String}
     */
    public String getOrderRemark(){
        return PromiseExtendKeyEnum.ORDER_REMARK.getFiledKey().equals(this.getAttribute()) ? this.getValue() : StrUtil.EMPTY;
    }

    /**
     * 是否含有加项
     *
     * @param  hasAdded
     */
    public void setHasAdded(String hasAdded){
        this.setAttribute(PromiseExtendKeyEnum.HAS_ADDED.getFiledKey());
        this.setValue(hasAdded);
    }

    /**
     * 是否含有加项
     *
     * @return {@link String}
     */
    public String getHasAdded(){
        return PromiseExtendKeyEnum.HAS_ADDED.getFiledKey().equals(this.getAttribute()) ? this.getValue() : "0";
    }


}
