package com.jdh.o2oservice.core.domain.promise.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseExtend;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseExtQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepPageQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * l
 */
public interface PromiseRepository extends Repository<JdhPromise, JdhPromiseIdentifier> {


    int batchSave(List<JdhPromise> list);

        /**
         * findList
         *
         * @param query 查询
         * @return {@link List}<{@link JdhPromise}>
         */
    List<JdhPromise> findList(PromiseRepQuery query);

    /**
     * 只查询预约单
     *
     * @param query
     * @return
     */
    List<JdhPromise> findPromiseList(PromiseRepQuery query);

    /**
     * 查询扩展信息
     *
     * @param query
     * @return
     */
    List<JdhPromiseExtend> findPromiseExtList(PromiseExtQuery query);

    Page<JdhPromise> page(PromiseRepPageQuery query);

    /**
     *
     * @param query
     * @return
     */
    List<JdhPromise> findJdhPromiseList(PromiseRepQuery query);
    /**
     * findPromise
     *
     * @param query 查询
     * @return {@link JdhPromise}
     */
    JdhPromise findPromise(PromiseRepQuery query);

    /**
     *
     * @param appointmentId
     * @return
     */
    JdhPromise findAppointment(Long appointmentId);
    /**
     *
     * @param userPin
     * @param writeOffPwd
     * @return
     */
    JdhPromise findAppointment(String userPin,String writeOffPwd);

    /**
     *
     * @param codeId
     * @return
     */
    JdhPromise findByCodeId(String codeId);
    /**
     * 具有身份权限校验的履约单查询
     * @param codeId
     * @return
     */
    JdhPromise findByCodeId(String codeId, String verticalCode);

    JdhPromise findJdPromiseByOrderItem(String orderItemId, String codeRelationId);


    /**
     * 刷数据需要,根据订单号删除数据
     * @param orderIds
     * @return
     */
    Integer removeByOrderId(List<Long> orderIds);

    /**
     *
     * @param promisePatientIds
     * @return
     */
    List<JdhPromisePatient> listPatient(Set<Long> promisePatientIds);

    /**
     * 根据原单号来新增或者更新promise信息，返回promiseId
     * promiseServiceDetail及promiseExtend不做更新，只新增
     * @param jdhPromise
     * @return promiseId为Key，promisePatientId集合为value
     */
    Map<Long, List<Long>> insertOrUpdateBySourceVoucherId(JdhPromise jdhPromise);

    /**
     * 批量更新履约单
     *
     * @param jdhPromise
     * @return
     */
    int update(JdhPromise jdhPromise);

    /**
     * voucher关联的，已经完成履约服务的检测人数量
     * @param voucherId
     * @return
     */
    int countCompleteUser(Long voucherId);

    /**
     * 更新被服务人信息
     *
     * @param item
     * @return
     */
    int updatePromisePatient(JdhPromisePatient item);


    int savePromiseExtend(JdhPromiseExtend jdhPromiseExtend);
}
