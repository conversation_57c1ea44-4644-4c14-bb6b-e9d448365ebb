package com.jdh.o2oservice.core.domain.trade.context;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * @ClassName:OrderVoucherExtendContext
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/1/27 19:00
 * @Vserion: 1.0
 **/
@Data
@AllArgsConstructor
@Builder
public class OrderVoucherExtendContext {

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * autoPromise
     */
    private Boolean autoPromise;

    /**
     * skuId
     */
    private String skuId;

    /**
     * skuName
     */
    private String skuName;

    /**
     * 商家ID
     */
    private String venderId;

    /**
     * 草稿id
     */
    private String draftId;
    /**
     * 收获地址手机号
     */
    private String orderPhone;

    /**
     * 下单备注
     */
    private String orderRemark;

    /**
     * 履约被服务人数量
     */
    private Integer promisePatientNum;
    /**
     * 是否含有加项 0不包含 1包含
     */
    private Integer hasAdded;

    /**
     * 赠品对应主商品sku
     */
    private Long mainSkuId;

    /**
     * 赠品对应主商品名称
     */
    private String mainSkuName;

    /**
     * 意向护士
     */
    private IntendedNurseConText intendedNurse;

    /**
     * 是否已有采样盒
     */
    private Boolean preSampleFlag;

    /**
     * 合作方来源子渠道
     */
    private String saleChannelId;

    /**
     * 合作方来源
     */
    private Integer partnerSource;

    /**
     * 外部渠道的订单号
     */
    private String partnerSourceOrderId;
}
