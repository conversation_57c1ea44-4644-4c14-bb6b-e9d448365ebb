package com.jdh.o2oservice.core.domain.trade.factory;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.OrderSplitTypeEnum;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhServiceTypeCategoryRelation;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.JdhServiceTypeCategoryRelationRepository;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.bo.OrderEntityBO;
import com.jdh.o2oservice.core.domain.trade.bo.SkuInfoBO;
import com.jdh.o2oservice.core.domain.trade.context.IntendedNurseConText;
import com.jdh.o2oservice.core.domain.trade.context.OrderCompleteVoucherContext;
import com.jdh.o2oservice.core.domain.trade.context.OrderVoucherExtendContext;
import com.jdh.o2oservice.core.domain.trade.context.OrderVoucherItemContext;
import com.jdh.o2oservice.core.domain.trade.converter.JdOrderConverter;
import com.jdh.o2oservice.core.domain.trade.enums.JdOrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.OrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.*;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderExtRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderCalculationQueryServiceRpc;
import com.jdh.o2oservice.core.domain.trade.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName:JdOrderFactory
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2023/12/27 18:44
 * @Vserion: 1.0
 **/
@Slf4j
public class JdOrderFactory {

    /**
     * 初始化京东消费医疗订单领域模型
     *
     * @param orderEntityBO
     * @return
     */
    public static JdOrder createOrder(OrderEntityBO orderEntityBO) {
        JdOrder jdOrder = JdOrderConverter.INSTANCE.convertToJdOrder(orderEntityBO);
        List<SkuInfoBO> skuList = orderEntityBO.getSkuList();
        List<JdOrderItem> jdOrderItemList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(skuList)){
            jdOrderItemList = JdOrderConverter.INSTANCE.convertToJdOrderItemList(skuList);
        }
        JdOrderExtendVo jdOrderExtendVo = new JdOrderExtendVo();
        jdOrderExtendVo.setDraftId(orderEntityBO.getMedicalLocId());
        jdOrderExtendVo.setXfyl(orderEntityBO.getXfyl());
        jdOrderExtendVo.setPresale(orderEntityBO.getPresale());
        //补充订单信息
        if(OrderSplitTypeEnum.SPLIT_TYPE_INT_7.getSplitType().equals(orderEntityBO.getSplitType())){
            jdOrder.setOrderStatus(OrderStatusEnum.ORDER_WAIT_PAY.getStatus());
        } else if(OrderSplitTypeEnum.checkOrderPaid(orderEntityBO.getSplitType())){
            jdOrder.setOrderStatus(OrderStatusEnum.ORDER_PAID.getStatus());
        }
        //订单商品信息
        createJdOrderItemProperties(jdOrder, jdOrderItemList, orderEntityBO, jdOrderExtendVo);
        jdOrder.setJdOrderItemList(jdOrderItemList);
        //订单金额明细
        List<JdOrderMoney> jdOrderMoneyList = createOrderMoneyInfo(orderEntityBO, jdOrderItemList);
        jdOrder.setJdOrderMoneyList(jdOrderMoneyList);
        //订单状态
        JdOrderStatus jdOrderStatus = createOrderStatusInfo(orderEntityBO);
        jdOrder.setJdOrderStatusList(Lists.newArrayList(jdOrderStatus));
        //订单扩展信息
        List<JdOrderExt> jdOrderExtList = createJdOrderExt(orderEntityBO);
        jdOrder.setJdOrderExtList(jdOrderExtList);
        jdOrder.setExtend(JSON.toJSONString(jdOrderExtendVo));

        if (StringUtils.isNotBlank(orderEntityBO.getSendPayMap())){
            Map<String, String> sendPayMap = JSON.parseObject(orderEntityBO.getSendPayMap(), new TypeReference< Map<String, String>>(){});
            jdOrder.setSendPayMap(sendPayMap);
        }

        return jdOrder;
    }

    /**
     * 生成订单扩展信息
     *
     * @param orderEntityBO
     * @return
     */
    private static List<JdOrderExt> createJdOrderExt(OrderEntityBO orderEntityBO) {
        Map<String, String> orderExtMap = orderEntityBO.getOrderExtMap();
        if(MapUtils.isEmpty(orderExtMap)){
            return null;
        }
        GenerateIdFactory generateIdFactory = SpringUtil.getBean(GenerateIdFactory.class);
        List<JdOrderExt> jdOrderExtList = Lists.newArrayList();
        Iterator<Map.Entry<String, String>> iterator = orderExtMap.entrySet().iterator();
        while(iterator.hasNext()){
            JdOrderExt jdOrderExt = new JdOrderExt();
            jdOrderExtList.add(jdOrderExt);
            Map.Entry<String, String> next = iterator.next();
            jdOrderExt.setExtType(next.getKey());
            jdOrderExt.setExtContext(next.getValue());
            jdOrderExt.setYn(YnStatusEnum.YES.getCode());
            jdOrderExt.setOrderId(orderEntityBO.getOrderId());
            jdOrderExt.setExtId(generateIdFactory.getId());
            jdOrderExt.setVersion(NumConstant.NUM_1);
            jdOrderExt.setCreateTime(new Date());
            jdOrderExt.setUpdateTime(new Date());
        }
        return jdOrderExtList;
    }

    /**
     * 生成订单状态信息
     *
     * @param orderEntityBO
     * @return
     */
    private static JdOrderStatus createOrderStatusInfo(OrderEntityBO orderEntityBO) {
        JdOrderStatus jdOrderStatus = JdOrderStatus.builder().build();
        jdOrderStatus.setOrderId(orderEntityBO.getOrderId());
        jdOrderStatus.setStatus(OrderStatusEnum.ORDER_PAID.getStatus());
        jdOrderStatus.setVersion(NumConstant.NUM_1);
        jdOrderStatus.setYn(YnStatusEnum.YES.getCode());
        jdOrderStatus.setStatusTime(new Date());
        jdOrderStatus.setCreateTime(new Date());
        jdOrderStatus.setUpdateTime(new Date());
        return jdOrderStatus;
    }

    public static List<JdOrderMoney> createOrderMoneyInfo(Long jdOrderId, Long skuNo, List<JdOrderItem> jdOrderItemList) {
        OrderEntityBO orderEntityBO = new OrderEntityBO();
        orderEntityBO.setOrderId(jdOrderId);
        if(Objects.nonNull(skuNo)){
            orderEntityBO.setSkuNo(String.valueOf(skuNo));
        }
        return createOrderMoneyInfo(orderEntityBO, jdOrderItemList);
    }

    /**
     * 生成订单金额信息列表
     *
     * @param orderEntityBO
     * @param jdOrderItemList
     * @return
     */
    private static List<JdOrderMoney> createOrderMoneyInfo(OrderEntityBO orderEntityBO, List<JdOrderItem> jdOrderItemList) {
        OrderCalculationQueryServiceRpc orderCalculationQueryServiceRpc = SpringUtil.getBean(OrderCalculationQueryServiceRpc.class);

        //todo 可能返回空值
        String amountAndExpand = orderCalculationQueryServiceRpc.queryOrderSplitAmountAndExpand(orderEntityBO.getOrderId());
        List<JdOrderMoney> jdOrderMoneyList = new ArrayList<>();
        if(StringUtils.isBlank(amountAndExpand)){
            return jdOrderMoneyList;
        }

        List<OrderAmount> orderAmountList = JSON.parseArray(amountAndExpand, OrderAmount.class);
        log.info("JdOrderFactory -> createOrderMoneyInfo, orderId={}, orderAmountList={}", orderEntityBO.getOrderId(), JSON.toJSONString(orderAmountList));

        if (CollectionUtils.isEmpty(orderAmountList)){
            log.info("JdOrderFactory -> createOrderMoneyInfo 获取订单金额明细失败, orderId={}", orderEntityBO.getOrderId());
            return jdOrderMoneyList;
        }
        Map<Long,List<OrderAmount>> orderAmountMap = orderAmountList.stream().collect(Collectors.groupingBy(OrderAmount::getSkuId));

        for (JdOrderItem jdOrderItem : jdOrderItemList) {
            List<OrderAmount> orderAmounts = orderAmountMap.get(jdOrderItem.getSkuId());
            for (OrderAmount orderAmount : orderAmounts) {
                if (null == orderAmount.getSkuId()){
                    log.info("JdOrderFactory -> createOrderMoneyInfo, skuid is null,  orderAmount={}", JSON.toJSONString(orderAmount));
                    continue;
                }
                if (!orderEntityBO.getSkuNo().equals(orderAmount.getSkuId().toString())){
                    log.info("JdOrderFactory -> createOrderMoneyInfo, 一单多个sku，skuid不一致, orderEntityBO={}, orderAmount={}", JSON.toJSONString(orderEntityBO), JSON.toJSONString(orderAmount));
                    continue;
                }
                if (CollectionUtils.isEmpty(orderAmount.getAmountExpands())){
                    log.info("JdOrderFactory -> createOrderMoneyInfo skuid一致, 但是获取订单金额明细失败, orderId={}, skuId={}", orderEntityBO.getOrderId(), orderEntityBO.getSkuNo());
                    continue;
                }

                Map<Integer,List<OrderAmountExpand>> orderAmountExpandMap = orderAmount.getAmountExpands().stream().collect(Collectors.groupingBy(b -> b.getType()));
                for(Map.Entry<Integer, List<OrderAmountExpand>> orderAmountExpand: orderAmountExpandMap.entrySet()){
                    JdOrderMoney entity = new JdOrderMoney();
                    List<OrderAmountExpand> orderAmountExpandList = orderAmountExpand.getValue();
                    Integer amountType = orderAmountExpand.getKey();
                    BigDecimal amount = new BigDecimal(0);
                    for(OrderAmountExpand amountExpand : orderAmountExpandList){
                        amount = amount.add(amountExpand.getAmount());
                    }
                    if(amount.compareTo(BigDecimal.ZERO) == 0){
                        continue;
                    }
                    entity.setOrderId(jdOrderItem.getOrderId());
                    entity.setOrderItemId(jdOrderItem.getOrderItemId());
                    entity.setSkuId(jdOrderItem.getSkuId());
                    entity.setMoneyType(amountType);
                    entity.setAmount(amount);
                    entity.setVersion(NumConstant.NUM_1);
                    entity.setYn(YnStatusEnum.YES.getCode());
                    entity.setCreateTime(new Date());
                    entity.setUpdateTime(new Date());
                    jdOrderMoneyList.add(entity);
                }
            }
        }
        return jdOrderMoneyList;
    }

    /**
     * 补充订单明细信息
     *
     * @param jdOrderItemList
     * @param orderEntityBO
     * @param jdOrderExtendVo
     */
    private static void createJdOrderItemProperties(JdOrder jdOrder, List<JdOrderItem> jdOrderItemList, OrderEntityBO orderEntityBO, JdOrderExtendVo jdOrderExtendVo) {
        if(CollectionUtils.isEmpty(jdOrderItemList)){
            return;
        }
        List<SkuRefundVo> skuRefundVoList = Lists.newArrayList();
        SkuInfoRpc skuInfoRpc = SpringUtil.getBean(SkuInfoRpc.class);
        GenerateIdFactory generateIdFactory = SpringUtil.getBean(GenerateIdFactory.class);
        JdhServiceTypeCategoryRelationRepository jdhServiceTypeCategoryRelationRepository = SpringUtil.getBean(JdhServiceTypeCategoryRelationRepository.class);
        for (JdOrderItem jdOrderItem : jdOrderItemList) {
            //分布式id
            jdOrderItem.setOrderItemId(generateIdFactory.getId());
            //查询商品信息
            RpcSkuBO skuBO = skuInfoRpc.getCrsSkuBoBySkuId(String.valueOf(jdOrderItem.getSkuId()));
            jdOrder.setVenderId(String.valueOf(skuBO.getVenderId()));
            jdOrder.setVenderName(skuBO.getVenderName());
            //查询商品的服务类型信息
            JdhServiceTypeCategoryRelation categoryRelation = jdhServiceTypeCategoryRelationRepository.findCategoryRelation(skuBO.getThirdCategoryId(), NumConstant.NUM_3);
            jdOrderItem.setServiceType(categoryRelation.getServiceType());
            jdOrderItem.setOrderId(orderEntityBO.getOrderId());
            jdOrderItem.setCreateTime(new Date());
            jdOrderItem.setUserPin(orderEntityBO.getUserPin());
            //优惠信息
//            jdOrderItem.setItemDiscount();
//            jdOrderItem.setItemTotalAmount();
            //门店信息
            jdOrderItem.setStoreId(orderEntityBO.getStoreId());
            //商品商家信息
            jdOrderItem.setVenderId(Objects.isNull(skuBO.getVenderId()) ? "" : String.valueOf(skuBO.getVenderId()));
            //版本信息
            jdOrderItem.setVersion(NumConstant.NUM_1);
            //过期时间
            Date orderCreateTime = orderEntityBO.getOrderCreateTime();
            LocalDate localDate = TimeUtils.dateToLocalDate(orderCreateTime);
            LocalDateTime expireTime = localDate.plusDays(365).atTime(23, 59, 59);
            jdOrderItem.setSkuExpireDate(TimeUtils.localDateTimeToDate(expireTime));

            //订单扩展明细
            SkuRefundVo skuRefundVo = new SkuRefundVo();
            skuRefundVo.setRefundNum(NumConstant.NUM_0);
            skuRefundVo.setSkuId(jdOrderItem.getSkuId());
            skuRefundVo.setTotalNum(jdOrderItem.getSkuNum());
            skuRefundVo.setWriteOffNum(NumConstant.NUM_0);
            skuRefundVoList.add(skuRefundVo);
        }
        jdOrderExtendVo.setSkuRefundVoList(skuRefundVoList);
    }

    public static OrderCompleteVoucherContext createVoucherContext(JdOrderItem jdOrderItem, JdOrder jdOrder) {
        String env = SpringUtil.getProperty("spring.profiles.active");
        log.info("[JdOrderFactory->createVoucherContext],env={}", env);

        JdOrderExtRepository jdOrderExtRepository = SpringUtil.getBean(JdOrderExtRepository.class);
        List<JdOrderExt> exts = jdOrderExtRepository.findJdOrderExtList(jdOrder.getOrderId());
        String orderPhone = null;
        if (CollectionUtils.isNotEmpty(exts)) {
            Optional<JdOrderExt> optional = exts.stream().filter(ext -> Objects.equals(JdOrderExtTypeEnum.APPOINTMENT_INFO.getExtType(), ext.getExtType())).findAny();
            if (optional.isPresent()){
                OrderAppointmentInfoValueObject orderAppointmentInfo = JsonUtil.parseObject(optional.get().getExtContext(),OrderAppointmentInfoValueObject.class);
                orderPhone = orderAppointmentInfo.getAddressInfo().getMobile();
            }
            log.info("[JdOrderFactory->createVoucherContext],orderPhone={}", orderPhone);
        }else{
            log.info("[JdOrderFactory->createVoucherContext],jdOrderExtList={}", JSON.toJSONString(exts));

        }

        //构造扩展信息
        OrderVoucherExtendContext extendContext = buildExtend(exts, jdOrder, jdOrderItem);


        //构造履约单信息，达成一致，暂时不考虑一单多次的情况
        List<OrderVoucherItemContext> itemContextList = Lists.newArrayList();
        OrderVoucherItemContext itemContext = OrderVoucherItemContext.builder()
                .tag(NumConstant.NUM_0)
                .serviceId(jdOrderItem.getSkuId())
                .serviceType(jdOrderItem.getServiceType())
                .verticalCode(jdOrderItem.getVerticalCode())
                .branch(env)
                .build();
        itemContextList.add(itemContext);

        //构造服务单信息
        OrderCompleteVoucherContext voucherContext = OrderCompleteVoucherContext.builder()
                .verticalCode(jdOrderItem.getVerticalCode())
                .serviceType(jdOrderItem.getServiceType())
                .serviceId(String.valueOf(jdOrderItem.getSkuId()))
                .createUser(jdOrderItem.getUserPin())
                .branch(env)
                .extend(extendContext)
                .promiseNum(NumConstant.NUM_1)
                .sourceVoucherId(String.valueOf(jdOrderItem.getOrderItemId()))
                .sourceType(NumConstant.NUM_0)
                .userPin(jdOrderItem.getUserPin())
                .voucherItemList(itemContextList)
                .expireDate(jdOrderItem.getSkuExpireDate())
                .build();
        return voucherContext;
    }


    public static OrderCompleteVoucherContext createHomeOrderVoucherContext(JdOrderItem jdOrderItem, JdOrder jdOrder,OrderCompleteVoucherContext voucherContext) {
        String env = SpringUtil.getProperty("spring.profiles.active");
        if(Objects.isNull(voucherContext)){
            voucherContext = createVoucherContext(jdOrderItem,jdOrder);
        }else{
            List<OrderVoucherItemContext> itemContextList = voucherContext.getVoucherItemList();
            OrderVoucherItemContext itemContext = OrderVoucherItemContext.builder()
                    .tag(NumConstant.NUM_0)
                    .serviceId(jdOrderItem.getSkuId())
                    .serviceType(jdOrderItem.getServiceType())
                    .verticalCode(jdOrderItem.getVerticalCode())
                    .branch(env)
                    .build();
            itemContextList.add(itemContext);
        }
        return voucherContext;
    }

    public static JdOrderStatus createOrderStatus(Long jdOrderId, Integer status){
        return JdOrderStatus.builder()
                .status(status)
                .orderId(jdOrderId)
                .statusTime(new Date())
                .yn(YnStatusEnum.YES.getCode())
                .createTime(new Date())
                .version(NumConstant.NUM_1)
                .updateTime(new Date())
                .build();
    }

    /**
     * 查找订单电话好吗
     * @return
     */
    private static OrderVoucherExtendContext buildExtend(List<JdOrderExt> exts, JdOrder jdOrder,JdOrderItem jdOrderItem){

        String draftId = null;
        boolean autoPromise = false;
        String extend = jdOrder.getExtend();
        String saleChannelId = null;
        if(StringUtils.isNotBlank(extend)){
            JdOrderExtendVo jdOrderExtendVo = JSON.parseObject(extend, JdOrderExtendVo.class);
            draftId = jdOrderExtendVo.getDraftId();
            saleChannelId = jdOrderExtendVo.getSaleChannelId();
        }

        if(StringUtils.isNotBlank(draftId)){
            autoPromise = true;
        }

        // 构造赠品主品信息
        fillGiftOrderMainSku(jdOrder, jdOrderItem);

        //构造扩展信息
        OrderVoucherExtendContext extendContext = OrderVoucherExtendContext.builder()
                .skuId(String.valueOf(jdOrderItem.getSkuId()))
                .orderId(String.valueOf(jdOrderItem.getOrderId()))
                .draftId(draftId)
                .skuName(jdOrderItem.getSkuName())
                .autoPromise(autoPromise)
                .venderId(jdOrderItem.getVenderId())
                .orderRemark(jdOrder.getRemark())
                .promisePatientNum(jdOrderItem.getSkuNum())
                .mainSkuId(jdOrderItem.getMainSkuId())
                .mainSkuName(jdOrderItem.getMainSkuName())
                .build();

        // POP业务手机号从订单中获取，到家业务此时的业务身份为空
        VerticalBusinessRepository businessRepository = SpringUtil.getBean(VerticalBusinessRepository.class);
        JdhVerticalBusiness business = businessRepository.find(jdOrder.getVerticalCode());
        if (Objects.nonNull(business)){
            // vtp身份单独处理
            if(ServiceHomeTypeEnum.VTP_HOME_CARE.getVerticalCode().equals(jdOrder.getVerticalCode())
                || ServiceHomeTypeEnum.VTP_HOME_TEST.getVerticalCode().equals(jdOrder.getVerticalCode())
                || ServiceHomeTypeEnum.VTP_HOME_TEST_PHASE.getVerticalCode().equals(jdOrder.getVerticalCode())
            ){
                extendContext.setPromisePatientNum(1);
                extendContext.setOrderPhone(JdOrderExt.findHomeOrderPhone(exts));
            }
            //非 vtp 走历史逻辑
            else{
                if (Objects.equals(business.getBusinessModeCode(), BusinessModeEnum.POP_FREE.getCode())
                    || Objects.equals(business.getBusinessModeCode(), BusinessModeEnum.POP_LOC.getCode()) ){
                    extendContext.setPromisePatientNum(1);
                    extendContext.setOrderPhone(jdOrder.getOrderUserPhone());
                }else{
                    extendContext.setOrderPhone(JdOrderExt.findHomeOrderPhone(exts));
                }
            }
        }else{
            extendContext.setOrderPhone(JdOrderExt.findHomeOrderPhone(exts));
        }

        List<JdOrderExt> jdOrderExts = exts.stream().filter(e -> OrderExtTypeEnum.INTENDED_NURSE.getType().equals(e.getExtType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(jdOrderExts)){
            extendContext.setIntendedNurse(JSON.parseObject(jdOrderExts.get(0).getExtContext(), IntendedNurseConText.class));
        }

        List<JdOrderExt> appointmentExts = exts.stream().filter(e -> OrderExtTypeEnum.APPOINTMENT_INFO.getType().equals(e.getExtType())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(appointmentExts)){
            extendContext.setPreSampleFlag(JSON.parseObject(appointmentExts.get(0).getExtContext(),OrderAppointmentInfoValueObject.class).getPreSampleFlag());
        }

        if(Objects.nonNull(jdOrder.getPartnerSource())){
            extendContext.setPartnerSource(jdOrder.getPartnerSource());
        }

        if(StringUtils.isNotBlank(jdOrder.getPartnerSourceOrderId())){
            extendContext.setPartnerSourceOrderId(jdOrder.getPartnerSourceOrderId());
        }

        if(StringUtils.isNotBlank(saleChannelId)){
            extendContext.setSaleChannelId(saleChannelId);
        }

        log.info("[JdOrderFactory->buildExtend],extendContext={}", JSON.toJSONString(extendContext));
        return extendContext;
    }

    /**
     * 获取赠品订单主体商品信息
     * @param jdOrder jdOrder
     * @param jdOrderItem jdOrderItem
     */
    private static void fillGiftOrderMainSku(JdOrder jdOrder,JdOrderItem jdOrderItem) {
        try {
            if (jdOrderItem.getSkuId() == null || StringUtils.isBlank(jdOrder.getExtend())) {
                return;
            }
            JdOrderExtendVo jdOrderExtendVo = JSON.parseObject(jdOrder.getExtend(), JdOrderExtendVo.class);
            if (CollUtil.isEmpty(jdOrderExtendVo.getJdSkuRelationInfoVoList())) {
                return;
            }
            for (JdSkuRelationInfoVo jdSkuRelationInfoVo : jdOrderExtendVo.getJdSkuRelationInfoVoList()) {
                if (jdSkuRelationInfoVo.getRelationSkuId().equals(jdOrderItem.getSkuId())) {
                    jdOrderItem.setMainSkuId(jdSkuRelationInfoVo.getMainSkuId());
                    jdOrderItem.setMainSkuName(jdSkuRelationInfoVo.getMainSkuName());
                    break;
                }
            }
        } catch (Exception e) {
            log.error("JdOrderFactory fillGiftOrderMainSku", e);
        }

    }
}
