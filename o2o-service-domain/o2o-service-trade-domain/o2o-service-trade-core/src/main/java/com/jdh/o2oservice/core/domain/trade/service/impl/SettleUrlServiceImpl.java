package com.jdh.o2oservice.core.domain.trade.service.impl;

import com.jd.fastjson.JSONObject;
import com.jd.jr.order.export.rest.domain.cashier.CashierOrderInfoBean;
import com.jd.jr.order.export.rest.domain.cashier.GoodsInfo;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.pay.platform.api.domain.channel.GenPayIdRequestVo;
import com.jd.pay.platform.api.domain.channel.GenPayIdResult;
import com.jd.pay.platform.api.domain.request.PlatPayRequest;
import com.jd.pay.platform.api.domain.response.PlatPayResponse;
import com.jd.pay.platform.sdk.util.GenPayIdUtil;
import com.jd.paytrade.front.export.vo.acc.Enum.CashierTypeEnum;
import com.jd.paytrade.front.export.vo.acc.PaymentAccResVo;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.EnvTypeEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.reach.constant.ReachConstant;
import com.jdh.o2oservice.core.domain.trade.bo.GenerateSettleUrlContext;
import com.jdh.o2oservice.core.domain.trade.bo.OrderPayBo;
import com.jdh.o2oservice.core.domain.trade.context.SkipPayUrlContext;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.TradeInfoRpc;
import com.jdh.o2oservice.core.domain.trade.service.SettleUrlService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

/**
 * @author: yangxiyu
 * @date: 2024/1/4 3:34 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class SettleUrlServiceImpl implements SettleUrlService {
    /**
     *
     */
    @Resource
    private SkuInfoRpc skuInfoRpc;
    /**
     *
     */
    @Resource
    private TradeInfoRpc tradeInfoRpc;
    /**
     *
     */
    private String appId = "m_297Va7iP3u";
    /**
     *
     */
    public static final String MERCHANT_APP_ID = "m_health_qiye";
    /**
     *jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;
    /**
     * 收费商户名称
     */
    public static final String MERCHANT_NAME = "京东健康";

    /**
     *共享收银台参数：7395909e76dd9bffbabff0b063dcd461
     */
    @Value("${app.cashier.appKey}")
    private String appKey;

    /**
     * 小程序支付成功跳转url
     */
    @Value("${check.miniprogram.cashier.returnUrl}")
    private String miniProgramReturnUrl;

    @Resource
    private DuccConfig duccConfig;


    @Override
    public String generateSettleUrl(GenerateSettleUrlContext context) {
        log.info("SettleUrlServiceImpl->GenerateSettleUrlContext context modeCode={}", context.getVerticalBusiness().getBusinessModeCode());
        if (Objects.equals(context.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.POP_LOC.getCode())){
            return popLocUrl(context);
        }else{
            throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
        }
   }

    /**
     * 跳转收银台
     *
     * @param context
     * @return
     */
    @Override
    public String skipPayUrl(SkipPayUrlContext context) {
        EnvTypeEnum typeEnum = EnvTypeEnum.get(context.getEnvType());
        if (Objects.isNull(typeEnum)){
            throw new SystemException(SystemErrorCode.PARAM_NULL_ERROR,"EnvType");
        }
        log.info("SettleUrlServiceImpl skipPayUrl context:{} ", JsonUtil.toJSONString(context));
//        JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(context.getOrderId()));
        switch (typeEnum){
            case JDH_APP:
                return getJdhAppPayUrl(context);
            case JDH_MINI_PROGRAM:
                return getJdhProgramPayUrl(context);
//            case JDH_HY_MINI_PROGRAM:
//                return getJdhProgramPayUrl(context);
            case KANGKANG_MINI_PROGRAM:
                return getKangKangProgramPayUrl(context);
            case JINGGOU_MINI_PROGRAM:
                return getJingGouProgramPayUrl(context);
            case QQ_JINGGOU_MINI_PROGRAM:
                return getJingGouProgramPayUrl(context);
            default:
                return getJdAppPayUrl(context);
        }
    }

    /**
     * 获取康康小程序支付Url 需要将orderAmount*100 因为fee的单位是分 orderAmount是元
     * @param context
     * @return
     */
    private static String getKangKangProgramPayUrl(SkipPayUrlContext context) {
       return "plugin://jd@wxapp-deal/payment?dealId=orderId&fee=orderAmount"
               .replace("orderId",String.valueOf(context.getOrderId())).replace("orderAmount",new BigDecimal(context.getOrderAmount()).multiply(new BigDecimal(100)).toString());
    }

    private String getJingGouProgramPayUrl(SkipPayUrlContext context) {
        try {
            String jmiOrderDetailUrl = duccConfig.getJmiOrderDetailUrl();
            Map<String, String> jmiOrderDetailUrlDataMap = new HashMap<>(2);
            jmiOrderDetailUrlDataMap.put("orderId", String.valueOf(context.getOrderId()));
            StringSubstitutor failRedirectUrlSub = new StringSubstitutor(jmiOrderDetailUrlDataMap);
            jmiOrderDetailUrl= failRedirectUrlSub.replace(jmiOrderDetailUrl);
            jmiOrderDetailUrl = URLEncoder.encode(jmiOrderDetailUrl, "UTF-8");

            String payUrl = duccConfig.getJingGouMiniProgramPayUrl();
            Map<String, String> dataMap = new HashMap<>(2);
            dataMap.put("orderId", String.valueOf(context.getOrderId()));
            dataMap.put("orderPrice", new BigDecimal(context.getOrderAmount()).multiply(new BigDecimal(100)).toString());
            dataMap.put("failRedirectUrl", jmiOrderDetailUrl);
            StringSubstitutor sub = new StringSubstitutor(dataMap);
            return sub.replace(payUrl);
        } catch (Exception e) {
            log.error("SettleUrlServiceImpl getJingGouProgramPayUrl error e", e);
        }
        return "";
    }


    private String popLocUrl(GenerateSettleUrlContext context){
        EnvTypeEnum typeEnum = EnvTypeEnum.get(context.getEnvType());
        if (Objects.isNull(typeEnum)){
            throw new SystemException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        switch (typeEnum){
            case JD_APP:
                return jdAppUrl(context);
            default:
                return jdhAppUrl(context);
        }
    }

    /**
     * 商城的结算页url拼接
     * https://cf.jd.com/pages/viewpage.action?pageId=218645379
     * @return
     */
    private  String jdAppUrl(GenerateSettleUrlContext context) {
        log.info("SettleUrlServiceImpl->jdAppUrl start");
        RpcSkuBO rpcSkuBO = skuInfoRpc.getCrsSkuBoBySkuId(String.valueOf(context.getSkuId()));
        StringBuilder settleUrl =  new StringBuilder("openapp.jdmobile://virtual?params=");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("category", "jump");
        jsonObject.put("des", "orderInfoView");
        jsonObject.put("wareId", context.getSkuId());
        jsonObject.put("wareNum", "1");
        jsonObject.put("skuSource", 68);
        jsonObject.put("sourceType", 7);
//        jsonObject.put("param", new JsonObject());
        JSONObject extFlagJson = new JSONObject();
        if(StringUtils.isNotEmpty(context.getMedicalLocId())){
            extFlagJson.put("medicalLocId", context.getMedicalLocId());
        }
        if(StringUtils.isNotEmpty(context.getPt())){
            extFlagJson.put("pt", context.getPt());
        }
        // 选择门店则
        if(rpcSkuBO.needSelectStore()){
            extFlagJson.put("locVendorId", context.getStoreId());
        }
        if(Objects.nonNull(context.getBybtSceneStart()) && context.getBybtSceneStart()){
            extFlagJson.put("hasTenBillionSubsidySku", true);
        }
        jsonObject.put("extFlag", extFlagJson.toJSONString());
        if(Objects.nonNull(context.getPreSaleStart()) && context.getPreSaleStart()){
            jsonObject.put("skuSource", 4);
            jsonObject.put("businessMap", "{\"isPresale\": true}");
        }
        return settleUrl.append(jsonObject.toJSONString()).toString();
    }

    /**
     * 健康APP结算页
     * @return
     */
    private static String jdhAppUrl(GenerateSettleUrlContext context) {
        log.info("SettleUrlServiceImpl->jdhAppUrl start");
        StringBuilder settleUrl =  new StringBuilder("openApp.jdHealth://virtual?params=");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("category", "jump");
        jsonObject.put("des", "buynowtosettlement");
        jsonObject.put("hasSelectedOTC", "0");
//        {"":"1","":"21078745","wareId":"***********", "wareNum":1,"sourceType":2,"skuSource": 1,"orderType":0,"shopId":"21078745","extParams":{"medicalLocId":"AAAAAA"}}
        jsonObject.put("otcMergeSwitch", "1");
        jsonObject.put("uuidReferStoreId","");
        jsonObject.put("wareId", context.getSkuId());
        jsonObject.put("wareNum", "1");
        jsonObject.put("skuSource", 1);
        jsonObject.put("sourceType", 2);
        if(Objects.nonNull(context.getPreSaleStart()) && context.getPreSaleStart()){
            jsonObject.put("orderType", CommonConstant.FOUR);
            jsonObject.put("orderTypeForiOS", CommonConstant.ONE);
        }else{
            jsonObject.put("orderType", CommonConstant.ZERO);
            jsonObject.put("orderTypeForiOS", CommonConstant.ZERO);
        }

        jsonObject.put("shopId",  context.getStoreId());
        JSONObject extFlagJson = new JSONObject();
        if(StringUtils.isNotEmpty(context.getMedicalLocId())){
            extFlagJson.put("medicalLocId", context.getMedicalLocId());
        }
        jsonObject.put("extParams", extFlagJson);
        return settleUrl.append(jsonObject.toJSONString()).toString();
    }

    /**
     * 获取京东app收银台链接
     * @param context
     * @return
     */
    private  String getJdAppPayUrl(SkipPayUrlContext context) {
        AssertUtils.hasText(context.getClientIp(),TradeErrorCode.IP_ID_NULL);
        PlatPayRequest<GenPayIdRequestVo> platPayRequest = getPlatPayRequest(context);
        PlatPayResponse<GenPayIdResult> resultPlatPayResponse = tradeInfoRpc.genPayId(platPayRequest);
        if (resultPlatPayResponse == null) {
            throw new BusinessException(TradeErrorCode.ORDER_RPC_PAY_URL_ERROR);
        }
        // code = 0，表示成功
        if (ReachConstant.RESULT_CODE.equalsIgnoreCase(resultPlatPayResponse.getCode()) && resultPlatPayResponse.getResult() != null
                && StringUtils.isNotBlank(resultPlatPayResponse.getResult().getUrl())) {
            return resultPlatPayResponse.getResult().getUrl();
        }
        return "";
    }
    /**
     * 获取京东健康app收银台链接
     * @param context
     * @return
     */
    private  String getJdhAppPayUrl(SkipPayUrlContext context) {
        // context: orderId,userPin,orderAmount
        OrderPayBo orderPayBo = new OrderPayBo();
        orderPayBo.setOrderId(context.getOrderId());
        orderPayBo.setOrderAmount(context.getOrderAmount());
        orderPayBo.setUserPin(context.getUserPin());
        orderPayBo.setSubmitTime(System.currentTimeMillis());
        return tradeInfoRpc.getJdhAppPayUrl(orderPayBo);
    }
    /**
     * 获取京东健康小程序收银台链接
     * @param context
     * @return
     */
    private  String getJdhProgramPayUrl(SkipPayUrlContext context) {
        String returnUrl = "";
        returnUrl = miniProgramReturnUrl;
        // 获取收银台url
        OrderPayBo orderPayBo = new OrderPayBo();
        orderPayBo.setSkuName(context.getSkuName());
        orderPayBo.setSkuNo(context.getSkuId());
        orderPayBo.setSkuNum(context.getSkuNum());
        orderPayBo.setSkuPirce(context.getSkuPirce());
        orderPayBo.setOrderId(context.getOrderId());
        orderPayBo.setOrderAmount(context.getOrderAmount());
        // 单独区分支付完成后跳转链接
        orderPayBo.setReturnUrl(returnUrl);
        // 微信openId
        String openId = context.getOpenId() == null ? "" : context.getOpenId();
        // 获取收银台url
        PaymentAccResVo paymentAccResVo = tradeInfoRpc.cashierMiniprogram(MERCHANT_APP_ID,buildPayInfo(context.getUserPin(), openId, orderPayBo));

        String payUrl = "/pages/wechatpayBbc/wechatpayBbc?isHideHome=1&appId="+MERCHANT_APP_ID+"&openId="+openId+"&payParam="+paymentAccResVo.getSdkParam().getPayParam()+"&successUrl="+returnUrl;
        // 缓存收银台url，给京米使用
//        jimRedisService.setEx(RedisConstant.buildKey(RedisConstant.CASHIER_PAY_URL_PREFIX, String.valueOf(orderId)), payUrl, 2, TimeUnit.DAYS);
        return payUrl;
    }
    /**
     * 获取京东健康互医小程序收银台链接
     * @param context
     * @return
     */
    private  String getJdhHuYiProgramPayUrl(SkipPayUrlContext context) {
        return "";
    }


    private PlatPayRequest<GenPayIdRequestVo> getPlatPayRequest(SkipPayUrlContext context){
        // 跳转收银台接口其他数据
        PlatPayRequest<GenPayIdRequestVo> platPayRequest = new PlatPayRequest<>();
        // 用户pin
        platPayRequest.setPin(context.getUserPin());
        // 客户端类型
        com.jd.pay.platform.api.domain.client.ClientInfo client = new com.jd.pay.platform.api.domain.client.ClientInfo();
        client.setClient("m");
        client.setIp(context.getClientIp());
        client.setClientVersion("");
        platPayRequest.setClientInfo(client);
        // 请求体对象
        GenPayIdRequestVo genPayIdRequestVo = new GenPayIdRequestVo();
        genPayIdRequestVo.setAppId(appId);
        genPayIdRequestVo.setOrigin("h5");
        genPayIdRequestVo.setPaySign(GenPayIdUtil.genPaySign(appId, String.valueOf(context.getOrderId()), context.getOrderType(), context.getOrderAmount(), appKey));
        genPayIdRequestVo.setOrderId(String.valueOf(context.getOrderId()));
        genPayIdRequestVo.setOrderType(context.getOrderType());
        genPayIdRequestVo.setOrderPrice(context.getOrderAmount());
        genPayIdRequestVo.setPaySourceId("3");
        platPayRequest.setReqObj(genPayIdRequestVo);
        // 业务载体来源，m端
        platPayRequest.setSource("mcashier");

        return platPayRequest;
    }

    /**
     * 健康小程序收银台入参封装
     * @param userPin
     * @param openId
     * @param orderPayBo
     * @return
     */
    private CashierOrderInfoBean buildPayInfo(String userPin, String openId, OrderPayBo orderPayBo) {
        CashierOrderInfoBean cashierBean = new CashierOrderInfoBean();
        cashierBean.setAppId(MERCHANT_APP_ID);
        cashierBean.setOrderId(String.valueOf(orderPayBo.getOrderId()));
        cashierBean.setOrderType(CommonConstant.ORDER_TYPE_STR);
        cashierBean.setToType("10");
        //必须精确到小数点后2位, 单位元
        cashierBean.setTotal(orderPayBo.getOrderAmount());
        cashierBean.setCompanyId("6");
        cashierBean.setPin(userPin);
        cashierBean.setSuccessUrl(orderPayBo.getReturnUrl());
        //订单过期时间yyyyMMddHHmmss
        cashierBean.setExpiredTime(DateUtil.formatDate(DateUtil.addDays(new Date(),1),CommonConstant.YMDHMS2));
        //商户名称
        cashierBean.setMerchantName(MERCHANT_NAME);
        //支付方式
        cashierBean.setCashierType(CashierTypeEnum.JDPAY_MINIPRO.getType());
        //扩展字段，返回给收单方
        HashMap<String,String> map = new HashMap<>(8);
        map.put("orderAmount",orderPayBo.getOrderAmount());
        cashierBean.setPayAttach(JsonUtil.toJSONString(map));
        //商品列表
        ArrayList<GoodsInfo> goodsList = new ArrayList<>();
        GoodsInfo goodsInfo = new GoodsInfo();
        goodsInfo.setGoodsName(orderPayBo.getSkuName());
        goodsInfo.setGoodsCount(orderPayBo.getSkuNum());
        goodsInfo.setGoodsPrice(orderPayBo.getSkuPirce());
        goodsList.add(goodsInfo);
        cashierBean.setGoodsInfosList(goodsList);
        //扩展信息
        HashMap<String,String> extMap = new HashMap<>(8);
        extMap.put("openid", openId);
        //直接跳转回自定义成功页
        extMap.put("paySuccTag","directReturn");
        cashierBean.setExt(extMap);
        //支付成功页标题
        cashierBean.setPayFinishDesc("支付成功");
        //支付枚举
        cashierBean.setAgencyCode("10199");
        return cashierBean;
    }
}
