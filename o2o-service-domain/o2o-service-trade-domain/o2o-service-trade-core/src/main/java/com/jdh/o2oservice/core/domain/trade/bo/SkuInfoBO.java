package com.jdh.o2oservice.core.domain.trade.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @ClassName:SkuInfoBO
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2023/12/26 10:35
 * @Vserion: 1.0
 **/
@Data
public class SkuInfoBO {

    /**
     * 商品sku，一品一单拆分
     */
    private String skuNo;
    /**
     * 商品图片
     */
    private String skuImage;
    /**
     * 商品数量
     */
    private Integer skuNum;
    /**
     * 商品价格
     */
    private BigDecimal skuAmount;
    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 一级类目
     */
    private Integer cid1;

    /**
     * 二级类目
     */
    private Integer cid2;

    /**
     * 三级类目
     */
    private Integer cid3;
    /**
     * xfyl标
     */
    private Map<String, String> tags;

    /**
     * 商品属性标
     */
    private String skuFeatures;
}
