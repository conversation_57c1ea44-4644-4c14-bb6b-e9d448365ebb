package com.jdh.o2oservice.core.domain.trade.context;

import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.core.domain.support.patient.model.Patient;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import lombok.Data;

import java.util.List;

/**
 * OrderInfoQueryContext 获取订单详情入参
 *
 * <AUTHOR>
 * @version 2024/04/23 16:22
 **/
@Data
public class OrderInfoQueryContext extends BusinessContext{

    /**
     * 必需, 订单号
     */
    private String orderId;
    /**
     * 非必须,批量订单号, 如果查询批量订单, 订单号放这里（不给queryOrderInfo方法用）
     */
    private List<Long> orderIds;
    /**
     * 业务类型，1、获取详情、2、批量获取详情、3、check pin和订单号是否匹配
     */
    private Integer biz = 1;
    /**
     * 是否忽略订单状态,获取订单状态 1：获取订单状态 0：忽略订单状态
     */
    private Integer ignoreState = 1;
    /**
     * 站点类型
     */
    private int siteId = CommonConstant.BU_ID;
    /**
     * 是否查询预售信息
     */
    private boolean searchPresaleInfo;


    /**
     * 订单备注
     */
    private String remark;

    /**
     * 合作方来源
     */
    private Integer partnerSource;

    /**
     * 外部渠道的订单号
     */
    private String partnerSourceOrderId;

    /**
     * 预约人ID
     */
    private List<Long> patientIds;
    /**
     * 预约人对象
     */
    private List<Patient> patients;

    /**
     * 预约时间
     */
    private AppointmentTimeContext appointmentTimeContext;

    /**
     * 渠道
     */
    private String channelName;

    /**
     * 意向护士
     */
    private IntendedNurseConText intendedNurse;

    /**
     * 是否已有采样盒
     */
    private Boolean preSampleFlag = false;

    /**
     * PartnerSource下的子渠道号
     */
    private String saleChannelId;
}
