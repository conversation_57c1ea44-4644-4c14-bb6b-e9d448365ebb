package com.jdh.o2oservice.core.domain.trade.enums;

/**
 * OrderExtTypeEnum
 * @author: yaoqing<PERSON>
 * @date: 2024/01/22 11:13
 * @version: 1.0
 */
public enum OrderExtTypeEnum {
    /**
     * pop信息扩展节点
     */
    POP_INFO("popInfo", "pop信息扩展节点"),
    APPOINTMENT_INFO("appointmentInfo", "预约信息扩展节点"),
    SERVICE_FEE_INFO("serviceFeeInfo", "服务费信息扩展节点"),
    SERVICE_FEE_SNAPSHOT("serviceFeeSnapshot", "服务费快照"),
    MAIN_SKU_NAME("mainSkuName", "主品名称"),
    /**
     * 商品类型（"0":一般商品 "1":延保商品 "2":赠品结构（可能是赠品或者附件））
     */
    SKU_WARE_TYPE("wareType", "商品类型"),
    COUPON_INFO("couponInfo", "优惠券信息扩展节点"),
    INTENDED_NURSE("intendedNurse", "意向护士信息扩展节点"),
    AFS_BEFORE_ORDER_STATUS("afsBeforeOrderStatus", "售后前订单状态"),
    ORDER_ADDRESS("orderAddress","用户下单时收货地址"),
    AFS_SERVICE_RECORDS("afsServiceRecords", "售后服务单记录"),
    ;

    private String type;

    private String desc;

    OrderExtTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 判断是否存在的类型
     * @param type
     * @return
     */
    public static boolean exist(String type){
        for (OrderExtTypeEnum value : OrderExtTypeEnum.values()) {
            if(value.getType().equalsIgnoreCase(type)){
                return true;
            }
        }
        return false;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
