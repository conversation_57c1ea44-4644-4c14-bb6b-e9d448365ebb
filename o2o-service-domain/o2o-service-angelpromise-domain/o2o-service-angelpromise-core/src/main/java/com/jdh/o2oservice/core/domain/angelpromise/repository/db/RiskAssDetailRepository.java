package com.jdh.o2oservice.core.domain.angelpromise.repository.db;

import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessmentDetail;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentDetailQuery;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/26
 */
public interface RiskAssDetailRepository {



    /**
     * 保存风险评估细节信息。
     * @param riskAssessmentDetail 风险评估细节对象，包含要保存的详细信息。
     * @return true 如果保存成功，false 否则。
     */
    Boolean save(RiskAssessmentDetail riskAssessmentDetail);

    /**
     * 根据查询条件获取风险评估明细列表
     * @param query 查询条件对象
     * @return 风险评估明细列表
     */
    List<RiskAssessmentDetail> queryRiskAssessmentDetailList(RiskAssessmentDetailQuery query);

    /**
     * 保存风险评估细节信息。
     * @param riskAssessmentDetails 风险评估细节对象，包含要保存的详细信息。
     * @return true 如果保存成功，false 否则。
     */
    Boolean saveBatch(List<RiskAssessmentDetail> riskAssessmentDetails);

    Boolean deleteByIds(Set<Long> id);

    /**
     * 修改风险评估单明细状态
     *
     * @param riskAssessmentId
     * @param promisePatientId
     * @param riskAssPatientStatus
     * @return
     */
    boolean updateRiskAssessmentDetailStatus(Long riskAssessmentId, Long promisePatientId, Short riskAssPatientStatus);
}
