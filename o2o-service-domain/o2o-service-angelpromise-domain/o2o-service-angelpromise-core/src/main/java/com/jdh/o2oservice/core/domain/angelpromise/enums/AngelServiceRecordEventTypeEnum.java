package com.jdh.o2oservice.core.domain.angelpromise.enums;
import com.jdh.o2oservice.base.event.EventType;
import com.jdh.o2oservice.base.model.AggregateCode;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum AngelServiceRecordEventTypeEnum implements EventType {

    SERVICE_RECORD_EVALUATE_HIGH_RISK(AngelWorkAggregateEnum.SERVICE_RECORD, "evaluateHighRisk", "护理单评估结果高风险", null);


    private AngelWorkAggregateEnum aggregateCode;

    private String code;

    private String desc;

    private Class<?> bodyClass;

    /**
     * 事件所属的领域
     */
    @Override
    public AggregateCode getAggregateType() {
        return aggregateCode;
    }

    /**
     * 事件编码
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 事件描述
     */
    @Override
    public String getDesc() {
        return desc;
    }

    /**
     * @return
     */
    @Override
    public Class<?> bodyClass() {
        return bodyClass;
    }
}
