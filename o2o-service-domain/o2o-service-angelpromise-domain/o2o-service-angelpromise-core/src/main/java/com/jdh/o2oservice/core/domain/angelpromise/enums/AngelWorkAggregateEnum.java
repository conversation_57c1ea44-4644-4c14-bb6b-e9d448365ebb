package com.jdh.o2oservice.core.domain.angelpromise.enums;

import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;

/**
 * 业务域内聚合枚举
 * @author: ya<PERSON>qing<PERSON>
 * @date: 2024/01/22 11:13
 * @version: 1.0
 */
public enum AngelWorkAggregateEnum implements AggregateCode {

    /**
     * 工单
     */
    WORK(DomainEnum.ANGEL_PROMISE, "angelWork"),

    /**
     * 任务单
     */
    TASK(DomainEnum.ANGEL_PROMISE, "angelTask"),

    /**
     * 任务单
     */
    SHIP(DomainEnum.ANGEL_PROMISE, "angelShip"),

    /**
     * 护理单
     */
    SERVICE_RECORD(DomainEnum.ANGEL_PROMISE, "angelServiceRecord"),

    ;


    /** */
    private DomainCode domain;
    /** */
    private String code;

    AngelWorkAggregateEnum(DomainCode domain, String code) {
        this.domain = domain;
        this.code = code;
    }

    /**
     * 聚合编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return domain;
    }

    @Override
    public String getCode() {
        return code;
    }
}
