package com.jdh.o2oservice.core.domain.angelpromise.service.ability;

import com.jd.matrix.core.SimpleReducer;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.matrix.core.base.BaseDomainAbility;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.common.ext.DomainBusinessIdentifierCode;
import com.jdh.o2oservice.common.ext.ExtResponse;
import com.jdh.o2oservice.common.ext.O2oBusinessIdentifier;
import com.jdh.o2oservice.ext.ship.CreateShipExt;
import com.jdh.o2oservice.ext.ship.param.CancelDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.CreateDadaShipParam;
import com.jdh.o2oservice.ext.ship.param.DeliveryOrderDetailRequest;
import com.jdh.o2oservice.ext.ship.param.DeliveryTrackParam;
import com.jdh.o2oservice.ext.ship.reponse.CreateShipExtResponse;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryOrderDetailResponse;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryTrackResponse;
import com.jdh.o2oservice.ext.ship.reponse.ShipCallbackParamResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.Map;

/**
 * @ClassName:WorkShipCallTransferAbility
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/27 02:31
 * @Vserion: 1.0
 **/
@DomainAbility(name = "呼叫运力", parent = DomainBusinessIdentifierCode.DOMAIN_ANGEL_PROMISE_CODE)
@Slf4j
public class WorkShipCallTransferAbility extends BaseDomainAbility<O2oBusinessIdentifier, CreateShipExt> {

    /**
     * 呼叫运力
     *
     * @return
     */
    @LogAndAlarm(jKey = "WorkShipCallTransferAbility.callTransfer")
    public ExtResponse<CreateShipExtResponse> callTransfer(O2oBusinessIdentifier businessIdentifier, CreateDadaShipParam createDadaShipParam, Date planOutTime, String providerShopNo) {
        //获取业务扩展点
        CreateShipExt createShipExt= this.getExtension(CreateShipExt.class, businessIdentifier, SimpleReducer.singleOne());
        //执行业务扩展点
        return createShipExt.callTransfer(createDadaShipParam, planOutTime, providerShopNo);
    }

    /**
     * 重新呼叫运力
     *
     * @return
     */
    @LogAndAlarm(jKey = "WorkShipCallTransferAbility.reCallTransfer")
    public ExtResponse<Boolean> reCallTransfer(O2oBusinessIdentifier businessIdentifier, CreateDadaShipParam createDadaShipParam) {
        //获取业务扩展点
        CreateShipExt createShipExt= this.getExtension(CreateShipExt.class, businessIdentifier, SimpleReducer.singleOne());
        //执行业务扩展点
        return createShipExt.reCallTransfer(createDadaShipParam);
    }

    /**
     * 取消运力
     *
     * @return
     */
    @LogAndAlarm(jKey = "WorkShipCallTransferAbility.cancelTransfer")
    public ExtResponse<Boolean> cancelTransfer(O2oBusinessIdentifier businessIdentifier, CancelDadaShipParam cancelDadaShipParam) {
        //获取业务扩展点
        CreateShipExt createShipExt= this.getExtension(CreateShipExt.class, businessIdentifier, SimpleReducer.singleOne());
        //执行业务扩展点
        return createShipExt.cancelTransfer(cancelDadaShipParam);
    }

    /**
     * 查询运单详情
     *
     * @return
     */
    @LogAndAlarm(jKey = "WorkShipCallTransferAbility.getShipOrderDetail")
    public ExtResponse<DeliveryOrderDetailResponse> getShipOrderDetail(O2oBusinessIdentifier businessIdentifier, DeliveryOrderDetailRequest deliveryOrderDetailRequest) {
        //获取业务扩展点
        CreateShipExt createShipExt= this.getExtension(CreateShipExt.class, businessIdentifier, SimpleReducer.singleOne());
        //执行业务扩展点
        return createShipExt.getShipOrderDetail(deliveryOrderDetailRequest);
    }

    /**
     * 转换运力供应商状态回传参数
     *
     * @return
     */
    @LogAndAlarm(jKey = "WorkShipCallTransferAbility.parseToShipCallbackContext")
    public ExtResponse<ShipCallbackParamResponse> parseToShipCallbackContext(O2oBusinessIdentifier businessIdentifier, Map<String, Object> paramMap) {
        //获取业务扩展点
        CreateShipExt createShipExt= this.getExtension(CreateShipExt.class, businessIdentifier, SimpleReducer.singleOne());
        //执行业务扩展点
        return createShipExt.parseCallbackParam(paramMap);
    }

    @Override
    public CreateShipExt getDefaultExtension() {
        return new CreateShipExt() {
            /**
             * 呼叫运力
             *
             * @param createDadaShipParam
             * @param planOutTime
             * @param providerShopNo
             * @return
             */
            @Override
            public ExtResponse<CreateShipExtResponse> callTransfer(CreateDadaShipParam createDadaShipParam, Date planOutTime, String providerShopNo) {
                log.error("[CreateShipExt -> callTransfer], 没有命中扩展点!");
                throw new BusinessException(BusinessErrorCode.EXT_EXECUTE_ERROR);
            }

            /**
             * 重新呼叫运力
             *
             * @param createDadaShipParam
             * @return
             */
            @Override
            public ExtResponse<Boolean> reCallTransfer(CreateDadaShipParam createDadaShipParam) {
                log.error("[CreateShipExt -> reCallTransfer], 没有命中扩展点!");
                throw new BusinessException(BusinessErrorCode.EXT_EXECUTE_ERROR);
            }

            /**
             * 取消运力
             *
             * @param cancelDadaShipParam
             * @return
             */
            @Override
            public ExtResponse<Boolean> cancelTransfer(CancelDadaShipParam cancelDadaShipParam) {
                log.error("[CreateShipExt -> cancelTransfer], 没有命中扩展点!");
                throw new BusinessException(BusinessErrorCode.EXT_EXECUTE_ERROR);
            }

            /**
             * 运力供应商状态回传转换
             *
             * @param callbackParamMap
             * @return
             */
            @Override
            public ExtResponse<ShipCallbackParamResponse> parseCallbackParam(Map<String, Object> callbackParamMap) {
                log.error("[CreateShipExt -> parseCallbackParam], 没有命中扩展点!");
                throw new BusinessException(BusinessErrorCode.EXT_EXECUTE_ERROR);
            }

            /**
             * 查询起手轨迹
             *
             * @param deliveryTrackParam
             * @return
             */
            @Override
            public DeliveryTrackResponse getTransferTrack(DeliveryTrackParam deliveryTrackParam) {
                log.error("[CreateShipExt -> getTransferTrack], 没有命中扩展点!");
                throw new BusinessException(BusinessErrorCode.EXT_EXECUTE_ERROR);
            }

            /**
             * 查询运单详情
             *
             * @param deliveryOrderDetailRequest
             * @return
             */
            @Override
            public ExtResponse<DeliveryOrderDetailResponse> getShipOrderDetail(DeliveryOrderDetailRequest deliveryOrderDetailRequest) {
                log.error("[CreateShipExt -> getShipOrderDetail], 没有命中扩展点!");
                throw new BusinessException(BusinessErrorCode.EXT_EXECUTE_ERROR);
            }
        };
    }
}
