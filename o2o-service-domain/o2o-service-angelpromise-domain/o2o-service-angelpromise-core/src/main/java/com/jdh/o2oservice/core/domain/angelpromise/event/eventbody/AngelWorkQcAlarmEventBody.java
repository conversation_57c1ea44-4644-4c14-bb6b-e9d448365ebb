package com.jdh.o2oservice.core.domain.angelpromise.event.eventbody;

import com.jdh.o2oservice.base.event.EventBody;
import com.jdh.o2oservice.export.angelpromise.cmd.QcAlarmDetailCmd;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName AngelWorkQcAlarmEventBody
 * @Description
 * <AUTHOR>
 * @Date 2025/6/12 10:43
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AngelWorkQcAlarmEventBody implements EventBody {

    /**
     * 租户编码
     */
    private String tenantNo;

    /**
     * 业务身份
     */
    private String	verticalCode;

    /**
     * 业务场景编码，服务类型
     */
    private String	sceneCode;

    /**
     * 质控单类型，1-护士 2-实验室
     */
    private Integer	qcRecordType;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 业务单号
     */
    private String orderId;

    /**
     * 履约单号
     */
    private String promiseId;

    /**
     * 质控项目编码
     */
    private String	itemNo;

    /**
     * 质控项目名称
     */
    private String	itemName;

    /**
     * 服务者编码
     */
    private String angelNo;

    /**
     * 服务者名称
     */
    private String angelName;

    /**
     * 时效类型，1-实时质控 2-后置质控
     */
    private Integer	timelyType;

    /**
     * 质控点明细
     */
    private List<QcAlarmDetailCmd> qcSiteDetailBos;
}