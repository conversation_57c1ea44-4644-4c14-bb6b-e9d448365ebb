package com.jdh.o2oservice.core.domain.angelpromise.repository.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RiskAssessmentDetailQuery {

    /**
     * 风险评估单ID
     */
    private Long riskAssessmentId;

    /**
     * 履约患者的ID
     */
    private Long promisePatientId;

    /**
     * 履约患者的ID集合
     */
    private List<Long> promisePatientIds;

    /**
     * 风险评估单问题ID
     */
    private Long riskQuestionId;

    /**
     * 存储多个风险评估单问题的ID集合，用于查询关联的风险评估单问题。
     */
    private Set<Long> riskQuestionIds;

    /**
     * 存储多个风险评估单的ID集合，用于查询关联的风险评估单。
     */
    private Set<Long> riskAssessmentIds;

    /**
     * 是否要查无效单子,默认不查
     */
    private Boolean invalidRiskQuery;

    /**
     * 是否要查无效单子,默认不查
     */
    private Boolean invalidRiskDetailQuery;
    /**
     * 履约单ID
     */
    private Long promiseId;


    /**
     * 风险评估师
     */
    private String assessmentUser;

    /**
     * 履约单
     */
    private List<Long> promiseIdList;

}
