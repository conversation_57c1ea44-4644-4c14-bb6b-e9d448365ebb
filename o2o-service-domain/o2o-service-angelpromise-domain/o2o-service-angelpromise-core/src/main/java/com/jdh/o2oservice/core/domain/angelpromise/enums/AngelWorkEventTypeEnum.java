package com.jdh.o2oservice.core.domain.angelpromise.enums;

import com.jdh.o2oservice.base.event.EventType;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelWorkEventBody;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * AngelWorkEventTypeEnum
 * @author: yaoqing<PERSON>
 * @date: 2024/01/22 11:13
 * @version: 1.0
 */
@AllArgsConstructor
public enum AngelWorkEventTypeEnum implements EventType {
    /**
     *
     */
    ANGEL_WORK_EVENT_INIT(AngelWorkAggregateEnum.WORK, "workInit", "生成工单", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_WAIT_RECEIVED(AngelWorkAggregateEnum.WORK, "workWaitReceived", "等待接单", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_RECEIVED(AngelWorkAggregateEnum.WORK, "workReceived", "已接单", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_WAITING_SERVED(AngelWorkAggregateEnum.WORK, "workWaitingServed", "待服务", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_ARRIVED(AngelWorkAggregateEnum.WORK, "workArrived", "已到达", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_IN_SERVED(AngelWorkAggregateEnum.WORK, "workInServed", "服务中", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_DONE_SERVED(AngelWorkAggregateEnum.WORK, "workDoneServed", "上门结束", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_DELIVERING(AngelWorkAggregateEnum.WORK, "workDelivering", "送检中", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_FINISH_SERVED(AngelWorkAggregateEnum.WORK, "workFinishServed", "服务完成", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_REFUND_FREEZE_SERVED(AngelWorkAggregateEnum.WORK, "refundFreezeServed", "退款中", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_REFUND_UNFREEZE_SERVED(AngelWorkAggregateEnum.WORK, "refundUnFreezeServed", "退款解冻", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_REFUND_SERVED(AngelWorkAggregateEnum.WORK, "refundServed", "已退款", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_CANCEL_SERVED(AngelWorkAggregateEnum.WORK, "cancelServed", "已取消", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_C_USER_CANCEL_SERVED(AngelWorkAggregateEnum.WORK, "cUserCancelServed", "取消", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_SUBMIT_SERVICE_RECORDS(AngelWorkAggregateEnum.WORK, "submitServiceRecords", "护士提交服务记录", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_SERVICE_RECORDS_IMG_EXIF_CREATE(AngelWorkAggregateEnum.WORK, "serviceRecordsImgExifCreate", "护士提交服务记录生成图片扩展信息", AngelWorkEventBody.class),

    /**
     * B端接口调用，提供非客服或者运营人员的接口事件，C端接口不能使用这个事件
     */

    ANGEL_WORK_EVENT_B_REFUNDED_SERVED(AngelWorkAggregateEnum.WORK, "refundedServed", "已退款", AngelWorkEventBody.class),

    ANGEL_WORK_EVENT_B_CANCEL(AngelWorkAggregateEnum.WORK, "cancel", "取消", AngelWorkEventBody.class),

    //###################################服务者工单定时触达事件####################################
    ANGEL_WORK_PLAN_FINISH_REACH(AngelWorkAggregateEnum.WORK, "planFinishReach", "服务者工单计划完成事件触达", null),

    ANGEL_WORK_PLAN_OUT_REACH(AngelWorkAggregateEnum.WORK, "planOutReach", "服务者工单计划出门事件触达", null),

    ANGEL_WORK_END_REACH(AngelWorkAggregateEnum.WORK, "workEnd", "工单已到用户预约结束时间事件触达", null),

    ANGEL_WORK_START_REACH(AngelWorkAggregateEnum.WORK, "workStart", "工单已到用户预约开始时间事件触达", null),

    WORK_SERVICING_HOUR_BEFORE_REMIND(AngelWorkAggregateEnum.WORK, "angelServicingHourBeforeRemind", "工单服务前一小时提醒", null),

    //###################################服务质控触达事件####################################

    ANGEL_WORK_QC_ALARM(AngelWorkAggregateEnum.WORK, "workQcAlarm", "服务质控报警", AngelWorkEventBody.class),

    ;
    private AngelWorkAggregateEnum aggregateCode;

    private String code;

    private String desc;

    private Class<?> bodyClass;

    public static AngelWorkEventTypeEnum getEnumByEvent(String eventCode) {
        if (StringUtils.isBlank(eventCode)) {
            return null;
        }
        for (AngelWorkEventTypeEnum value : values()) {
            if (value.getCode().equals(eventCode)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 事件所属的领域
     */
    @Override
    public AggregateCode getAggregateType() {
        return aggregateCode;
    }

    /**
     * 事件编码
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 事件描述
     */
    @Override
    public String getDesc() {
        return desc;
    }

    /**
     * @return
     */
    @Override
    public Class<?> bodyClass() {
        return bodyClass;
    }

    /**
     * 检查是否是失效的状态
     *
     * @param eventCode
     * @return
     */
    public static boolean checkInvalidEvent(String eventCode) {
        if(StringUtils.isBlank(eventCode)){
            return false;
        }
        return AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_CANCEL_SERVED.getCode().equals(eventCode)
                || AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_REFUND_SERVED.getCode().equals(eventCode)
                || AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_B_CANCEL.getCode().equals(eventCode)
                || AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_B_REFUNDED_SERVED.getCode().equals(eventCode);
    }
}
