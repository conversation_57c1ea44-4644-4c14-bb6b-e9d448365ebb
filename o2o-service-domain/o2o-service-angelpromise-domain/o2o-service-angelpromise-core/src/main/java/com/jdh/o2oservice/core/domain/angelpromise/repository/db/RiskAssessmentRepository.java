package com.jdh.o2oservice.core.domain.angelpromise.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessment;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentPageQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/26
 */
public interface RiskAssessmentRepository {



    /**
     * 保存风险评估信息。
     * @param riskAssessment 风险评估对象，包含需要保存的信息。
     * @return 保存操作是否成功。
     */
    Boolean save(RiskAssessment riskAssessment);



    /**
     * 根据查询条件获取风险评估信息。
     * @param query 查询条件对象，包含要查询的风险评估的相关信息。
     * @return 满足查询条件的风险评估对象。
     */
    RiskAssessment queryRiskAssessment(RiskAssessmentQuery query);


    /**
     * 根据查询条件获取风险评估列表
     * @param query 查询条件对象
     * @return 符合条件的风险评估列表
     */
    List<RiskAssessment> queryRiskAssessmentList(RiskAssessmentQuery query);



    /**
     * 分页查询风险评估信息。
     * @param query 查询条件对象，包含分页信息和其他过滤条件。
     * @return 分页结果对象，包含当前页数据和分页信息。
     */
    Page<RiskAssessment> queryRiskAssessmentPage(RiskAssessmentPageQuery query);



    /**
     * 保存风险评估的回访记录。
     * @param riskAssessment 风险评估对象，包含需要保存的访问记录信息。
     * @return 保存操作是否成功。
     */
    Boolean saveReturnVisit(RiskAssessment riskAssessment);



    Boolean deleteRiskAssessment(RiskAssessment riskAssessment);

    /**
     * 修改风险评估单状态
     *
     * @param riskAssessmentId
     * @param riskAssessmentStatus
     * @return
     */
    boolean updateRiskAssessmentStatus(Long riskAssessmentId, Integer riskAssessmentStatus);
}
