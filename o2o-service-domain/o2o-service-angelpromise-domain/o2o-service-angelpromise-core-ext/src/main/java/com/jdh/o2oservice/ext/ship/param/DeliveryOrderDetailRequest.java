package com.jdh.o2oservice.ext.ship.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @ClassName DeliveryOrderDetailRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/28 17:39
 */
@Data
public class DeliveryOrderDetailRequest {

    /**
     * 消医侧订单编号
     */
    private String orderId;

    /**
     * 第三方供应商 订单编号
     */
    private String outOrderId;

    @JSONField(name = "order_id")
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
