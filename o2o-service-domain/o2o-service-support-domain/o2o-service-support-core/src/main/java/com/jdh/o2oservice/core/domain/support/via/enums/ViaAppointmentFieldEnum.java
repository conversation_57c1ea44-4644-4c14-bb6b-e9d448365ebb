package com.jdh.o2oservice.core.domain.support.via.enums;

/**
 * VIA预约信息属性
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/24 10:26
 */
public enum ViaAppointmentFieldEnum {

    /**
     * 采样教程楼层是属性
     */
    SEND_INFO("sendInfo", "寄件信息"),
    SHIP_INFO("appointmentTime", "预约事件信息"),
    ACCEPT_INFO("acceptInfo", "收件信息"),
    ;



    ViaAppointmentFieldEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    /**
     * field
     */
    private final String field;

    /**
     * desc
     */
    private final String desc;

    public String getField() {
        return field;
    }

    public String getDesc() {
        return desc;
    }
}
