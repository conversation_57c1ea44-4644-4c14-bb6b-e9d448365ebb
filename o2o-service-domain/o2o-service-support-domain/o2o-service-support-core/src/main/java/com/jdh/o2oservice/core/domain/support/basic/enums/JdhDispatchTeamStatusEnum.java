package com.jdh.o2oservice.core.domain.support.basic.enums;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * @ClassName JdhDispatchTeamStatusEnum
 * @Description
 * <AUTHOR>
 * @Date 2025/7/17 13:55
 **/
@Getter
public enum JdhDispatchTeamStatusEnum {

    /** 禁用 */
    DISPATCH_TEAM_STATUS_DISABLE(0,  "已禁用"),
    /** 启用 */
    DISPATCH_TEAM_STATUS_ENABLE(1,  "启用中"),
    ;

    /**
     * status
     */
    private Integer status;

    /**
     * desc
     */
    private String desc;

    /** */
    JdhDispatchTeamStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    private static final Map<Integer, JdhDispatchTeamStatusEnum> TYPE_MAP = Maps.newHashMap();
    static {
        for (JdhDispatchTeamStatusEnum value : values()) {
            TYPE_MAP.put(value.status, value);
        }
    }

    public static JdhDispatchTeamStatusEnum getByType(Integer type){
        if (Objects.isNull(type)){
            return null;
        }
        return TYPE_MAP.get(type);
    }

    public static String getDescByType(Integer type){
        if (Objects.isNull(type) || !TYPE_MAP.containsKey(type)){
            return null;
        }
        return TYPE_MAP.get(type).getDesc();
    }
}