package com.jdh.o2oservice.core.domain.support.reach.model;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportAggregateEnum;
import com.jdh.o2oservice.core.domain.support.reach.context.UpdateReachTriggerContext;
import com.jdh.o2oservice.core.domain.support.reach.valueobject.ReachTriggerDelayStrategy;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 触达任务trigger
 * <AUTHOR>
 * @date 2024-04-15-5:58 下午
 */
@Slf4j
@Data
public class JdhReachTrigger implements Aggregate<JdhReachTriggerIdentifier> {

    /**
     * id
     */
    private Long id;
    /**
     * triggerId
     */
    private Long triggerId;

    /**
     * triggerName
     */
    private String triggerName;

    /** 事件所属领域 */
    private String domainCode;
    /** 事件所属聚合 */
    private String aggregateCode;
    /** 事件类型编号 */
    private String eventCode;
    /** expressionStr执行时需要的参数由expressionFillFunction的函数获取返回*/
    private String expressionFillFunction;
    /**
     * 命中trigger的逻辑表达式
     */
    private String expressionStr;

    /**
     * 模版ID
     */
    private Long templateId;
    /**
     * 圈选触达目标群里的函数ID
     */
    private String selectUserFunctionId;

    /**
     * 策略
     */
    private ReachTriggerDelayStrategy strategy;

    /**
     * 删除标识
     */
    private Integer yn;

    /**
     * 根据expression匹配发送事件的聚合是否满足trigger条件
     * @return
     */
    public Boolean match(Map<String, Object> objectMap, String aggregateId){

        if (StringUtils.isBlank(expressionStr)){
            return Boolean.FALSE;
        }

        if (StringUtils.equals(expressionStr, Boolean.TRUE.toString())){
            return Boolean.TRUE;
        }

        objectMap.put("nowHour", LocalDateTime.now().getHour());
        // 当前时间戳
        objectMap.put("currentTimeMillis", System.currentTimeMillis());
        Expression expression = AviatorEvaluator.compile(expressionStr, true);
        try {
            return (Boolean) expression.execute(objectMap);
        }catch (Exception e){
            log.error("ReachTrigger match error aggregateId={}", aggregateId, e);
        }
        return Boolean.FALSE;
    }


    /**
     * 聚合所属领域编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.BASE;
    }

    /**
     * 获取domainCode字段
     *
     * @return {@link String}
     */
    public String getDomainCodeField(){
        return domainCode;
    }

    /**
     * getAggregateCodeField
     *
     * @return {@link String}
     */
    public String getAggregateCodeField(){
        return aggregateCode;
    }

    /**
     * 获取聚合编码
     *
     * @return {@link AggregateCode}
     */
    @Override
    public AggregateCode getAggregateCode() {
        return SupportAggregateEnum.REACH_TRIGGER;
    }

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return null;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {

    }

    /**
     * 获取标识符
     *
     * @return {@link JdhReachTriggerIdentifier}
     */
    @Override
    public JdhReachTriggerIdentifier getIdentifier() {
        return JdhReachTriggerIdentifier.builder().triggerId(triggerId).build();
    }

    /**
     * 删除
     */
    public void delete() {
        this.setYn(YnStatusEnum.NO.getCode());
    }

    /**
     * 更新
     *
     * @param ctx ctx
     */
    public void update(UpdateReachTriggerContext ctx) {
        this.setTriggerName(ctx.getTriggerName());
        this.setDomainCode(ctx.getDomainCode());
        this.setAggregateCode(ctx.getAggregateCode());
        this.setEventCode(ctx.getEventCode());
        this.setExpressionFillFunction(ctx.getExpressionFillFunction());
        this.setExpressionStr(ctx.getExpressionStr());
        this.setTemplateId(ctx.getTemplateId());
        this.setSelectUserFunctionId(ctx.getSelectUserFunctionId());
        this.setStrategy(StrUtil.isBlank(ctx.getStrategy()) ? null : JSON.parseObject(ctx.getStrategy(), ReachTriggerDelayStrategy.class));
    }
}
