package com.jdh.o2oservice.core.domain.support.rpc.bo;

import lombok.Data;

import java.util.Date;

/**
 * @ClassName SoundRecordingDetailBO
 * @Description
 * <AUTHOR>
 * @Date 2025/6/17 14:03
 **/
@Data
public class SoundRecordingDetailBO {

    /** */
    private String id;

    /** */
    private Long soundId;

    /** */
    private Long fileId;

    /** */
    private Long promiseId;

    /** */
    private Long workId;

    /** */
    private String soundText;

    /** */
    private String soundSummary;

    /** */
    private String tenantNo;

    /** */
    private String scene;

    /** */
    private String extend;

    /** */
    private String createUser;

    /** */
    private Date createTime;

    /** */
    private String updateUser;

    /** */
    private Date updateTime;

    /** */
    private String filePath;

    /** */
    private String fileName;

    /** */
    private String fileUrl;

    /** */
    private String outBusinessId;

    /** */
    private String soundStage;

    /** */
    private Integer soundParseStatus;

    /** */
    private Integer soundSummaryStatus;

    private Date soundStartTime;

    private Date soundEndTime;
}