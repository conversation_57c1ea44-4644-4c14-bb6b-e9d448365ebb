package com.jdh.o2oservice.core.domain.support.reach.model;

import com.jdh.o2oservice.base.model.Entity;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 触达任务配置
 * @author: yang<PERSON><PERSON>
 * @date: 2024/4/15 2:12 下午
 * @version: 1.0
 */
@Data
public class JdhReachTask implements Entity<JdhReachTaskIdentifier> {

    /** status状态 初始化 */
    public static final Integer INIT_STATUS = 0;
    /** status状态 执行失败 */
    public static final Integer FAIL_STATUS = -1;
    /** status状态 执行成功 */
    public static final Integer SUCCESS_STATUS = 1;
    /** */
    private static final Integer MAX_SIZE_ERROR_INFO = 1000;
    /** 任务ID */
    private Long taskId;
    /** */
    private Long eventId;
    /** 事件所属领域 */
    private String domainCode;
    /** 事件所属聚合 */
    private String aggregateCode;
    /** 事件类型编号 */
    private String eventCode;
    /** 事件主体 */
    private String aggregateId;
    /**
     *
     */
    private Map<String,Object> eventBody;
    /**
     * 任务执行状态
     */
    private Integer status;
    /**
     * 消息类型
     */
    private Integer type;
    /**
     * 执行失败的消息
     */
    private String errorInfo;
    /**
     * 模版ID
     */
    private Long templateId;

    /**
     * 圈选触达目标群里的函数ID
     */
    private String selectUserFunctionId;
    /**
     *
     */
    private Integer version;
    /**
     *
     */
    private LocalDateTime triggerTime;

    /**
     * 是否延迟触达
     */
    private Boolean delay;
    /**
     * task对应的触发ID，对于延迟触达，在任务执行时
     */
    private Long triggerId;

    @Override
    public Integer version() {
        return version;
    }

    @Override
    public void versionIncrease() {

    }

    @Override
    public JdhReachTaskIdentifier getIdentifier() {
        return new JdhReachTaskIdentifier(taskId);
    }

    /**
     * 任务执行成功
     */
    public void executeSuccess(){
        this.status = SUCCESS_STATUS;
    }

    /**
     * 设置发消息发送失败的提示信息
     * @param errorInfo
     */
    public void setErrorInfo(String errorInfo) {
        if (StringUtils.isNotBlank(errorInfo) && errorInfo.length() > MAX_SIZE_ERROR_INFO){
            this.errorInfo = errorInfo.substring(0,MAX_SIZE_ERROR_INFO);
        }else{
            this.errorInfo = errorInfo;
        }
    }
}
