package com.jdh.o2oservice.core.domain.support.reach.repository.db;

import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachTask;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachTaskIdentifier;

import java.util.List;

/**
 * 触达任务仓储
 * @author: yang<PERSON>yu
 * @date: 2024/4/15 2:48 下午
 * @version: 1.0
 */
public interface JdhReachTaskRepository extends Repository<JdhReachTask, JdhReachTaskIdentifier> {



    /**
     * 批量保存
     */
    int batchSave(List<JdhReachTask> tasks);

}
