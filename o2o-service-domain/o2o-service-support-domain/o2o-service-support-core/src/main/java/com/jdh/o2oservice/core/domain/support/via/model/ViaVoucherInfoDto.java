package com.jdh.o2oservice.core.domain.support.via.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 次卡服务凭证实体
 */
@Data
public class ViaVoucherInfoDto implements Serializable {
    /**
     * 主标题 卡劵1
     */
    private String mainTitle;
    /**
     * 总次数
     */
    private Integer totalNum;
    /**
     * 已经使用次数
     */
    private Integer usedNum;
    /**
     * 剩余次数
     */
    private Integer remainingNum;
    /**
     * 使用进度比例
     */
    private BigDecimal useProgressRate;
    /**
     * 到期时间
     */
    private Date expireDate;
    /**
     * 服务单列表
     */
    private List<ViaPromiseInfoDto> promiseInfoDtos;
    /**
     * 按钮集合 去使用？
     */
    private List<ViaBtnInfo> btnList;

    /**
     * voucher状态
     * 0-待生效（立即预约）
     * 1-待服务（立即预约）
     * 2-完成（已用完）
     * 3-过期（已过期）
     * 4-作废（退款成功）
     * 1000-冻结（退款中）
     */
    private Integer viaVoucherStatus;


}
