package com.jdh.o2oservice.core.domain.support.via.enums;

import lombok.Getter;

/**
 * 样本楼层属性
 *
 * <AUTHOR>
 * @date 2024/04/19
 */
@Getter
public enum ViaSpecimenFieldEnum {

    /**
     * 订单信息枚举字段
     */
    PATIENT_NAME("patientName","患者名称"),
    PATIENT_GENDER("patientGender","性别"),
    PATIENT_AGE("patientAge","年龄"),
    PATIENT_HEADER_IMAGE("patientHeaderImage","头像图片"),

    SPECIMEN_CODE("specimenCode","样本编码"),
    TESTING_STATUS("testingStatus","状态"),
    ABNORMAL_NUM("abnormalNum","异常项目数量"),
    REPORT_TIME("reportTime","出报告时间"),
    VIEW_REPORT_BTN("viewReportBtn","查看报告按钮"),

    ;


    /**
     * 字段
     *
     * @param field 字段
     * @param desc   desc
     */
    ViaSpecimenFieldEnum(String field, String desc) {
        this.field = field;
        this.desc = desc;
    }

    /**
     * field
     */
    private final String field;

    /**
     * desc
     */
    private final String desc;
}
