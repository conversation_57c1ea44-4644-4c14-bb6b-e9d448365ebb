package com.jdh.o2oservice.core.domain.support.reach.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.cache.RedisUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.config.ReachGroovyScriptsUtil;
import com.jdh.o2oservice.base.constatnt.CacheConstant;
import com.jdh.o2oservice.base.enums.PrivacyNumberBindModelEnum;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.ErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.ducc.model.PrivacyNumberDuccConfig;
import com.jdh.o2oservice.base.ducc.model.ReachRuleDuccItem;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.core.domain.support.basic.enums.DelayTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.XfylDelayServiceRpc;
import com.jdh.o2oservice.core.domain.support.patient.model.Patient;
import com.jdh.o2oservice.core.domain.support.patient.repository.PatientRepository;
import com.jdh.o2oservice.core.domain.support.reach.bo.ReachUserBO;
import com.jdh.o2oservice.core.domain.support.reach.constant.ReachConstant;
import com.jdh.o2oservice.core.domain.support.reach.context.*;
import com.jdh.o2oservice.core.domain.support.reach.enums.*;
import com.jdh.o2oservice.core.domain.support.reach.ext.ReachServiceSelectDataExt;
import com.jdh.o2oservice.core.domain.support.reach.ext.ReachServiceSelectUserExt;
import com.jdh.o2oservice.core.domain.support.reach.ext.dto.ReachUser;
import com.jdh.o2oservice.core.domain.support.reach.ext.param.SelectReachDataParam;
import com.jdh.o2oservice.core.domain.support.reach.ext.param.SelectReachUserParam;
import com.jdh.o2oservice.core.domain.support.reach.factory.ReachFactory;
import com.jdh.o2oservice.core.domain.support.reach.model.*;
import com.jdh.o2oservice.core.domain.support.reach.repository.db.JdhReachMessageRepository;
import com.jdh.o2oservice.core.domain.support.reach.repository.db.ReachRuleRepository;
import com.jdh.o2oservice.core.domain.support.reach.repository.query.PrivacyNumberQuery;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.MessageReachFactory;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.MessageReachRpc;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.PrivacyNumberRpc;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.ReachSmsRpc;
import com.jdh.o2oservice.core.domain.support.reach.repository.rpc.bo.BatchSendSmsBO;
import com.jdh.o2oservice.core.domain.support.reach.rpc.JdAppMessageRpc;
import com.jdh.o2oservice.core.domain.support.reach.rpc.LocJmMessageRpc;
import com.jdh.o2oservice.core.domain.support.reach.rpc.NewnethpDiagMsgRpc;
import com.jdh.o2oservice.core.domain.support.reach.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.reach.service.ReachDomainService;
import com.jdh.o2oservice.core.domain.support.reach.service.ability.ReachMessageArgAbility;
import com.jdh.o2oservice.core.domain.support.reach.service.ability.ReachMessageArgAbilityFactory;
import com.jdh.o2oservice.core.domain.support.reach.valueobject.ReachMessageBizType;
import groovy.lang.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jdh.o2oservice.core.domain.support.reach.constant.ReachConstant.SMS_CODE_CACHE_EXPIRE;

/**
 * 触达领域服务接口实现
 * <AUTHOR>
 * @date 2023-12-21-6:19 下午
 */
@Service
@Slf4j
public class ReachDomainServiceImpl implements ReachDomainService {
    /**
     * 默认的延迟时间
     */
    private static final long DEFAULT_DELAY_SECONDS = 180;
    /**
     * smsReachRpc
     */
    @Resource
    private Cluster jimClient;

    /**
     * redisUtil
     */
    @Resource
    private RedisUtil redisUtil;

    /**
     * smsReachRpc
     */
    @Resource
    private MessageReachFactory messageReachFactory;

    /**
     * reachMessageArgAbilityFactory
     */
    @Resource
    private ReachMessageArgAbilityFactory reachMessageArgAbilityFactory;

    /**
     * patientRepository
     */
    @Autowired
    private PatientRepository patientRepository;

    /**
     * ducc配置
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * reachRuleRepository
     */
    @Resource
    private ReachRuleRepository reachRuleRepository;
    /** */
    @Resource
    private JdhReachMessageRepository jdhReachMessageRepository;
    /**
     *
     */
    @Resource
    private ReachSmsRpc reachSmsRpc;
    /**
     *
     */
    @Resource
    private NewnethpDiagMsgRpc newnethpDiagMsgRpc;
    /** */
    @Resource
    private LocJmMessageRpc locJmMessageRpc;
    /**
     * xfylDelayServiceRpc
     */
    @Resource
    private XfylDelayServiceRpc xfylDelayServiceRpc;

    /**
     * reachStoreProducer
     */
    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    /**
     * generateIdFactory
     */
    @Autowired
    private GenerateIdFactory generateIdFactory;

    /**
     *
     */
    @Value("${topics.reach.task.topic}")
    private String taskTopic;

    @Value("${topics.jdhReachStoreConsumer.reachNoticeTopic}")
    private String reachNoticeTopic;
    /**
     *
     */
    @Resource
    private JdAppMessageRpc jdAppMessageRpc;

    /**
     *
     */
    @Resource
    private DongDongRobotRpc dongDongRobotRpc;
    /**
     * 根据配置的函数唯一ID查询触达任务圈选的人群
     */
    private Map<String, ReachServiceSelectUserExt> selectUserFunctionMap = Maps.newConcurrentMap();
    /**
     * 填充触达模版参数的查询函数
     */
    private Map<String, ReachServiceSelectDataExt> selectDataFunction = Maps.newConcurrentMap();

    /**
     *
     */
    @Resource
    private ApplicationContext applicationContext;

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        Map<String, ReachServiceSelectUserExt> map = applicationContext.getBeansOfType(ReachServiceSelectUserExt.class);
        for (ReachServiceSelectUserExt ext : map.values()) {
            selectUserFunctionMap.put(ext.functionId(), ext);
        }

        Map<String, ReachServiceSelectDataExt> selectDataExtMap = applicationContext.getBeansOfType(ReachServiceSelectDataExt.class);
        for (ReachServiceSelectDataExt ext : selectDataExtMap.values()) {
            selectDataFunction.put(ext.functionId(), ext);
        }
        log.info("ReachDomainServiceImpl->init SelectUserExt={}, SelectDataExt={}", JSON.toJSONString(selectUserFunctionMap.keySet()), JSON.toJSONString(selectDataFunction.keySet()));
    }

    /**
     * checkSmsCode
     *
     * @param userPin      用户PIN
     * @param phone        电话
     * @param verticalCode verticalCode
     * @param serviceType  服务类型
     * @param smsCode      smsCode
     * @return boolean
     */
    @Override
    @LogAndAlarm
    public boolean checkSmsCode(String userPin, String phone, String verticalCode, String serviceType, String smsCode) {
        MessageReachRpc messageReachRpc = messageReachFactory.createReachRpc(ReachTypeEnum.REACH_SMS);

        String key = this.generateCacheKey(userPin, phone, verticalCode, serviceType);
        String cacheCode = messageReachRpc.getSmsCode(key);
        if (StringUtils.isNotBlank(cacheCode) && cacheCode.equals(smsCode)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 发送短信验证码
     *
     * @param ctx CTX
     * @return {@link Boolean}
     */
    @Override
    public Boolean sendSmsCode(SmsCodeSendContext ctx) {
        //获取真实手机号
        String realPhone = getRealPhone(ctx);

        //生成验证码
        String code = MaskPhoneUtil.verificationCode();

        ReachRule reachRule = reachRuleRepository.findByEvent("verificationCode");

        List<ReachRuleItem> list = filter(reachRule.getRuleItemList(), ctx.getVerticalCode(), ctx.getServiceType());
        if (CollectionUtil.isEmpty(list)) {
            ErrorCode errorCode = SystemErrorCode.CONFIG_ERROR.formatDescription("未配置短信验证码规则，请联系管理员配置！");
            throw new BusinessException(errorCode);
        }

        //发送
        log.info("ReachDomainServiceImpl -> sendReach execute start list={}", JSON.toJSONString(list));
        for (ReachRuleItem reachRuleItem : list) {
            // 校验是否超过频率限制
            try {
                checkLimit(reachRuleItem, ctx.getUserPin(), realPhone);
            } catch (Exception e) {
                log.error("限制错误", e);
            }

            ReachTemplate reachTemplate = reachRuleItem.getReachTemplate();
            reachTemplate.setMobileNum(realPhone);
            String[] params = {code};
            reachTemplate.setTemplateParams(params);

            // 调整为发送前保存缓存，短信达到上线后，查询日志可以获取验证码，进行验证
            String key = this.generateCacheKey(ctx.getUserPin(), realPhone, ctx.getVerticalCode(), ctx.getServiceType());
            jimClient.setEx(key, code, SMS_CODE_CACHE_EXPIRE, TimeUnit.SECONDS);

            MessageReachRpc messageReachRpc = messageReachFactory.createReachRpc(ReachTypeEnum.REACH_SMS);
            messageReachRpc.sendMessage(reachTemplate);
        }
        return Boolean.TRUE;
    }

    /**
     * getRealPhone
     *
     * @param ctx CTX
     * @return {@link String}
     */
    private String getRealPhone(SmsCodeSendContext ctx) {
        PhoneNumber phoneNumber = ctx.getPhoneNumber();
        phoneNumber.decrypt();
        String realPhone = "";
        if (phoneNumber.isMask()) {
            Patient patient = patientRepository.findById(Long.parseLong(ctx.getPatientId()));
            realPhone = patient.getPhone();
        } else {
            realPhone = phoneNumber.getPhone();
        }
        return realPhone;
    }

    @Override
    public String generateCacheKey(String userPin, String phone, String verticalCode, String serviceType) {
        return ReachConstant.SMS_CODE_CACHE_PRE + userPin + "_" + phone + "_" + verticalCode + "_" + serviceType;
    }

    @Override
    public String generateCacheKeyByUnique(String uniqueStr) {
        return ReachConstant.SMS_CODE_CACHE_PRE + uniqueStr;
    }

    /**
     * 获取隐私号码
     *
     * @param query 查询
     * @return {@link PrivacyNumber}
     */
    @Override
    public PrivacyNumber getPrivacyNumber(PrivacyNumberQuery query) {
        if (StrUtil.isEmpty(query.getPhoneNumber())) {
            throw new BusinessException(SupportErrorCode.PRIVACY_NUMBER_PHONE_NUMBER_NULL_ERROR);
        }
        PrivacyNumberRpc privacyNumberRpc = SpringUtil.getBean(PrivacyNumberRpc.class);
        //后续可以根据身份来区分 调用隐私号的不同能力。
        buildPrivacyNumberConfigByVertical(query);
        //目前仅AXE模式，如果有其他模式需要通过字段bindModel做路由处理
        return privacyNumberRpc.getPrivacyNumber(query);
    }

    /**
     * 发送触达
     *
     * @param reachContext
     * @return
     */
    @Override
    public Boolean sendReach(ReachContext reachContext) {
        log.info("ReachDomainServiceImpl -> sendReach reachContext:{}", JSON.toJSONString(reachContext));
        ReachRule reachRule = reachContext.getReachRule();
        List<ReachRuleItem> ruleItemList = reachRule.getRuleItemList();
        if (CollectionUtil.isEmpty(ruleItemList)) {
            return true;
        }
        // 过滤垂直业务身份，业务类型 排序
        List<ReachRuleItem> list = filter(ruleItemList, reachContext.getVerticalCode(), reachContext.getServiceType());
        log.info("ReachDomainServiceImpl -> sendReach execute start list={}", JSON.toJSONString(list));
        for (ReachRuleItem reachRuleItem : list) {
            ReachTemplate reachTemplate = reachRuleItem.getReachTemplate();
            reachTemplate.setMobileNum(reachContext.getPhone());
            //组装消息参数
            List<ReachTemplateContentFillRule> fillRules = reachTemplate.getFillRules();
            if (CollectionUtil.isNotEmpty(fillRules)) {
                String[] params = buildMessageArgs(reachContext, fillRules);
                reachTemplate.setTemplateParams(params);
            }
            MessageReachRpc messageReachRpc = messageReachFactory.createReachRpc(reachRuleItem.getReachType());
            messageReachRpc.sendMessage(reachTemplate);
        }
        return Boolean.TRUE;
    }

    /**
     * 执行触达任务
     *
     * @param context
     * @return
     */
    @Override
    public void buildReachMessage(ExecuteReachTaskContext context) {
        // 延迟任务在执行时需要再check一次
        JdhReachTask reachTask = context.getReachTask();
        JdhReachTrigger trigger = context.getJdhReachTrigger();
        if (Objects.equals(reachTask.getDelay(), Boolean.TRUE) && Objects.nonNull(trigger)
                && StringUtils.isNotBlank(trigger.getExpressionFillFunction())){
            ReachServiceSelectDataExt ext = selectDataFunction.get(trigger.getExpressionFillFunction());
            SelectReachDataParam param = new SelectReachDataParam();
            param.setDomainCode(reachTask.getDomainCode());
            param.setAggregateCode(reachTask.getAggregateCode());
            param.setAggregateId(reachTask.getAggregateId());
            param.setEventId(reachTask.getEventId());
            Map<String, Object> data = ext.selectData(param);
            if(!trigger.match(data, reachTask.getAggregateId())){
                log.info("ReachDomainServiceImpl->buildReachMessage delay task not match taskId={}", reachTask.getTaskId());
                return;
            }
            log.info("ReachDomainServiceImpl->buildReachMessage delay task match");
        }

        // 本次触达任务命中的人群
        selectUser(context);
        log.info("ReachDomainServiceImpl->selectUser context={}", JSON.toJSONString(context));

        // 过滤用户
        ReachMessageBizType messageBizType = context.getMessageType();
        messageBizType.filterUser(context.getUsers(), context.getTemplate().getType());
        log.info("ReachDomainServiceImpl->filterUser context={}", JSON.toJSONString(context));
        if (CollectionUtils.isEmpty(context.getUsers())){
            return;
        }
        // 解析触达参数
        parseParam(context);
        log.info("ReachDomainServiceImpl->parseParam context={}", JSON.toJSONString(context));

        // 构建消息
        if (CollectionUtils.isNotEmpty(context.getUsers())) {
            // 创建消息，京麦通知不保存消息
            if (!Objects.equals(context.getTemplate().getType(), ReachTypeEnum.JM_MESSAGE.getCode())){
                List<JdhReachMessage> messages = ReachFactory.createReachMessages(context);
                context.setMessages(messages);
            }
        }
    }


    /**
     * 发送消息
     *
     * @param context
     * @return
     */
    @Override
    public void sendReachMessage(ExecuteReachTaskContext context) {
        log.info("ReachDomainServiceImpl->sendReachMessage context={}", JSON.toJSONString(context));
        if (CollectionUtils.isEmpty(context.getUsers())){
            log.info("ReachDomainServiceImpl->sendReachMessage ending users is empty");
            return;
        }
        JdhReachTask reachTask = context.getReachTask();
        // 发送短信，存在批量推送的场景
        if (Objects.equals(reachTask.getType(), ReachTypeEnum.REACH_SMS.getCode())) {
            sendSms(context);
        } else if (Objects.equals(reachTask.getType(), ReachTypeEnum.APP_NOTIFY.getCode())) {
            sendAppNotify(context);
        } else if (Objects.equals(reachTask.getType(), ReachTypeEnum.PUSH.getCode())) {
            push(context);
        } else if (Objects.equals(reachTask.getType(), ReachTypeEnum.JM_MESSAGE.getCode())) {
            pushJmMessage(context);
        }else if (Objects.equals(reachTask.getType(), ReachTypeEnum.MASTER_APP_MESSAGE.getCode())) {
            pushJdAppMessage(context);
        } else if(Objects.equals(reachTask.getType(), ReachTypeEnum.STORE_PROGRAM_MQ_MESSAGE.getCode())) {
            pushMq(context);
        } else if(Objects.equals(reachTask.getType(), ReachTypeEnum.ME_ROBOT_MESSAGE.getCode())) {
            sendMeRobotMessage(context);
        }
    }

    /**
     * 到店小程序推送mq消息到全渠道，全渠道触达商家微信小程序消息
     * @param context
     */
    private void pushMq(ExecuteReachTaskContext context) {
        log.info("[ReachDomainServiceImpl -> pushMq],context={}", JSON.toJSONString(context));
        Map<ReachUserBO, ReachTemplateParamBO[]> userParams = context.getUserParams();
        if(MapUtils.isEmpty(userParams)){
            log.info("[ReachDomainServiceImpl -> pushMq],参数信息为空!context={}", JSON.toJSONString(context));
            return;
        }

        List<Map.Entry<ReachUserBO, ReachTemplateParamBO[]>> reachTemplateList = userParams.entrySet().stream().collect(Collectors.toList());
        Map.Entry<ReachUserBO, ReachTemplateParamBO[]> reachUserEntry = reachTemplateList.get(0);
        ReachTemplateParamBO[] templateParam = reachUserEntry.getValue();

        Map<String, Object> msgBodyMap = new HashMap<>(16);
        for (ReachTemplateParamBO reachTemplateParamBO : templateParam) {
            msgBodyMap.put(reachTemplateParamBO.getFiledKey(), reachTemplateParamBO.getValue());
        }
        Long businessId = generateIdFactory.getId();
        msgBodyMap.put("businessId", businessId);
        msgBodyMap.put("msgSceneType", context.getTemplate().getMessageBizType());

        try {
            Message message = new Message(reachNoticeTopic, JSON.toJSONString(msgBodyMap), String.valueOf(businessId));
            log.info("[ReachDomainServiceImpl -> pushMq],message={}", JSON.toJSONString(message));
            reachStoreProducer.send(message);
        } catch (JMQException e) {
            log.error("[ReachDomainServiceImpl -> pushMq],发送消息异常!", e);
        }
    }

    /**
     * 初始化触达任务
     *
     * @param triggers
     * @param event
     * @param templates
     * @return
     */
    @Override
    public List<JdhReachTask> initTask(List<JdhReachTrigger> triggers, Event event, List<JdhReachTemplate> templates) {
        if (CollectionUtils.isEmpty(triggers)) {
            return Collections.emptyList();
        }

        // 根据配置的函数获取规则表达式计算需要的数据
        Map<String, Object> dataMap = Maps.newHashMap();
        Set<String> functionIds = triggers.stream().map(JdhReachTrigger::getExpressionFillFunction).collect(Collectors.toSet());
        for (String functionId : functionIds) {
            if (StringUtils.isBlank(functionId)) {
                continue;
            }
            ReachServiceSelectDataExt ext = selectDataFunction.get(functionId);
            SelectReachDataParam param = new SelectReachDataParam();
            param.setDomainCode(event.getDomainCode());
            param.setAggregateCode(event.getAggregateCode());
            param.setAggregateId(event.getAggregateId());
            param.setEventId(event.getEventId());
            Map<String, Object> data = ext.selectData(param);
            dataMap.putAll(data);
        }
        // 事件透传参数
        if (StringUtils.isNotEmpty(event.getBody())) {
            dataMap.put("eventBody", JSON.parseObject(event.getBody()));
        }
        log.info("ReachDomainServiceImpl initTask dataMap={}", JSON.toJSONString(dataMap));
        List<JdhReachTask> tasks = Lists.newArrayList();
        Map<Long, JdhReachTemplate> templateMap = templates.stream().collect(Collectors.toMap(JdhReachTemplate::getTemplateId, Function.identity(), (o, n) -> o));
        Map<Integer, Long> hitTrigger = Maps.newHashMap();
        for (JdhReachTrigger trigger : triggers) {
            log.info("ReachDomainServiceImpl initTask trigger={}", JSON.toJSONString(trigger));
            JdhReachTemplate template = templateMap.get(trigger.getTemplateId());
            // trigger命中
            if (trigger.match(dataMap, event.getAggregateId())) {
                log.info("ReachDomainServiceImpl initTask命中条件 trigger={}", JSON.toJSONString(trigger));
                // 同一个事件同一种触达类型，每次只能命中一个，命中多个存在配置问题，预警处理
                if (hitTrigger.containsKey(template.getType())){
                    UmpUtil.showWarnMsg(UmpKeyEnum.REACH_EVENT_TRIGGER_MULTI, hitTrigger, trigger.getTriggerId());
                    continue;
                }
                JdhReachTask task = ReachFactory.createReachTask(event, trigger, template);
                if (Objects.nonNull(task)){
                    tasks.add(task);
                }
               hitTrigger.put(template.getType(), trigger.getTriggerId());
            }
        }
        return tasks;
    }




    @Override
    public void readMessage(ReadingMessageBO messageBO) {

        if (Objects.nonNull(messageBO.getMessageId())){
            //消息检查
            JdhReachMessageIdentifier jdhReachMessageIdentifier = new JdhReachMessageIdentifier();
            jdhReachMessageIdentifier.setMessageId(Long.valueOf(messageBO.getMessageId()));
            JdhReachMessage jdhReachMessage = jdhReachMessageRepository.find(jdhReachMessageIdentifier);
            if(Objects.isNull(jdhReachMessage)){
                log.error("[ReachApplicationImpl.readMessage],触达消息信息不存在!");
                throw new BusinessException(SupportErrorCode.REACH_MESSAGE_NOT_EXIST);
            }
            jdhReachMessage.setIsRead(ReachMsgReadEnum.IS_READ.getCode());
            //已读更新
            jdhReachMessageRepository.save(jdhReachMessage);
        }else{
            JdhReachMessage jdhReachMessage = new JdhReachMessage();
            jdhReachMessage.setReachType(ReachTypeEnum.APP_NOTIFY.getCode());
            jdhReachMessage.setUserPin(messageBO.getUserPin());
            jdhReachMessage.setAppId(messageBO.getAppId());
            jdhReachMessageRepository.batchReading(jdhReachMessage);
        }

    }

    @Override
    public void submit(List<JdhReachTask> tasks) {

        // 保存task
        if (CollectionUtils.isNotEmpty(tasks)) {
            // 发送MQ
            List<Message> rapidMessages = Lists.newArrayList();
            for (JdhReachTask task : tasks) {
                LocalDateTime now = LocalDateTime.now();
                long cur =  now.atZone(ZoneId.systemDefault()).toEpochSecond();
                // 即时发送的触达任务
                if (Objects.isNull(task.getTriggerTime()) || now.isAfter(task.getTriggerTime())){
                    Message message = new Message(taskTopic, String.valueOf(task.getTaskId()), String.valueOf(task.getTaskId()));
                    rapidMessages.add(message);
                    // 发送延迟触达任务
                }else {
                    try {
                        long taskTimestamp = task.getTriggerTime().atZone(ZoneId.systemDefault()).toEpochSecond();
                        long delaySeconds = taskTimestamp - cur;
                        if (delaySeconds < DEFAULT_DELAY_SECONDS){
                            delaySeconds = DEFAULT_DELAY_SECONDS;
                        }
                        xfylDelayServiceRpc.addDelayTaskForSec(task.getTaskId().toString(),
                                DelayTypeEnum.O2O_REACH_DELAY, (int)delaySeconds);
                        log.info("ReachDomainServiceImpl->submit 提交延迟触达任务 taskId={}", task.getTaskId());
                    }catch (Exception e){
                        log.error("ReachApplicationImpl->submitTask 添加延迟任务失败! taskId={}", task.getTaskId());
                    }
                }
            }

            try {
                if (CollectionUtils.isNotEmpty(rapidMessages)){
                    reachStoreProducer.send(rapidMessages);
                }
            }catch (Exception e){
                log.error("ReachApplicationImpl->submitTask reachStoreProducer send error", e);
                throw new SystemException(SystemErrorCode.JMQ_SEND_ERROR);
            }
        }
    }

    /**
     * 发送短信验证码(指定唯一ID)
     *
     * @param ctx CTX
     * @return {@link Boolean}
     */
    @Override
    public Boolean sendSmsCodeByUniqueId(SmsCodeSendContext ctx) {
        //获取真实手机号
        String realPhone = ctx.getPhoneNumber().getPhone();

        //生成验证码
        String code = MaskPhoneUtil.verificationCode();

        ReachRule reachRule = reachRuleRepository.findByEvent("verificationCode");

        List<ReachRuleItem> list = filter(reachRule.getRuleItemList(), ctx.getVerticalCode(), ctx.getServiceType());
        if (CollectionUtil.isEmpty(list)) {
            ErrorCode errorCode = SystemErrorCode.CONFIG_ERROR.formatDescription("未配置短信验证码规则，请联系管理员配置！");
            throw new BusinessException(errorCode);
        }

        //发送
        log.info("ReachDomainServiceImpl -> sendReach execute start list={}", JSON.toJSONString(list));
        for (ReachRuleItem reachRuleItem : list) {
            // 校验是否超过频率限制
            checkLimit(reachRuleItem, ctx.getUserPin(), realPhone);
            ReachTemplate reachTemplate = reachRuleItem.getReachTemplate();
            reachTemplate.setMobileNum(realPhone);
            String[] params = {code};
            reachTemplate.setTemplateParams(params);

            // 调整为发送前保存缓存，短信达到上线后，查询日志可以获取验证码，进行验证
            String key = this.generateCacheKeyByUnique(ctx.getUniqueRedisStr());
            jimClient.setEx(key, code, SMS_CODE_CACHE_EXPIRE, TimeUnit.SECONDS);

            MessageReachRpc messageReachRpc = messageReachFactory.createReachRpc(ReachTypeEnum.REACH_SMS);
            messageReachRpc.sendMessage(reachTemplate);
        }
        return Boolean.TRUE;
    }

    /**
     * 发送短信
     * @param context
     */
    private void sendSms(ExecuteReachTaskContext context) {
        BatchSendSmsBO sendSmsBO = new BatchSendSmsBO();
        JdhReachTemplate template = context.getTemplate();
        JdhReachChannelAccount channelAccount = context.getChannelAccount();
        ReachMessageBizType messageType = context.getMessageType();
        sendSmsBO.setSenderNum(channelAccount.getChannelAccountNo());
        sendSmsBO.setTemplateId(Long.valueOf(template.getChannelTemplateId()));
        Map<String, JdhReachMessage> messageMap = context.getMessages().stream().collect(Collectors.toMap(JdhReachMessage::getUserPhone, Function.identity(), (o, n) -> o));
        for (ReachUserBO user : context.getUsers()) {
            sendSmsBO.setPhone(user.getPhone());
            sendSmsBO.setToken(channelAccount.getChannelToken());
            if (MapUtils.isNotEmpty(context.getUserParams())){
                sendSmsBO.setTemplateParam(context.getUserParams().get(user));
            }
            JdhReachMessage message = messageMap.get(user.getPhone());
            sendSmsBO.setMessageId(message.getMessageId());
            // 护士端的短信发送走互医，互医侧有黑白名单等配置
            ReachSendResult result = null;
            if (Objects.equals(ReachNotifyAppEnum.ANGEL.getCode(), messageType.getAppId())) {
                result = newnethpDiagMsgRpc.sendSms(sendSmsBO);
            } else {
                result = reachSmsRpc.send(sendSmsBO);
            }
            if (!result.getSuccess()) {
                message.setErrorInfo(result.getErrorInfo());
                context.addSendFailMessage(message);
            }
        }
    }

    /**
     * 异步发送站内通知消息
     *
     * @param context
     */
    private void sendAppNotify(ExecuteReachTaskContext context) {
        Map<String, JdhReachMessage> messageMap = context.getMessages().stream().collect(Collectors.toMap(JdhReachMessage::getUserPin, Function.identity(), (o, n) -> o));
        context.getUsers().parallelStream().forEach(user -> {
            JdhReachMessage message = messageMap.get(user.getPin());
            PushInAppMsgBo msgBo = new PushInAppMsgBo(message, context);
            ReachSendResult result = newnethpDiagMsgRpc.pushInAppMsg(msgBo);
            if (!result.getSuccess()) {
                message.setErrorInfo(result.getErrorInfo());
                context.addSendFailMessage(message);
            }
        });
    }

    /**
     * 异步发送站外push消息
     *
     * @param context
     */
    private void push(ExecuteReachTaskContext context) {
        Map<String, JdhReachMessage> messageMap = context.getMessages().stream().collect(Collectors.toMap(JdhReachMessage::getUserPin, Function.identity(), (o, n) -> o));
        context.getUsers().parallelStream().forEach(user -> {
            JdhReachMessage message = messageMap.get(user.getPin());
            Map<ReachUserBO, String> pushUrl = context.getReachUrl();
            PushAppMsgBo msgBo = new PushAppMsgBo();
            msgBo.setMessageId(message.getMessageId());
            msgBo.setTitle(message.getMessageTitle());
            msgBo.setContent(message.getMessagePayload());
            msgBo.setToUserPin(message.getUserPin());
            msgBo.setUseVoice(Objects.nonNull(context.getTemplate()) && Objects.nonNull(context.getTemplate().getExtend()) ? context.getTemplate().getExtend().getUseVoice() : null);
            msgBo.setPushVoiceConfig(Objects.nonNull(context.getTemplate()) && Objects.nonNull(context.getTemplate().getExtend()) && StringUtils.isNotBlank(context.getTemplate().getExtend().getVoiceConfig()) ? JSON.parseObject(context.getTemplate().getExtend().getVoiceConfig(), PushVoiceConfigBO.class) : null);
            if (Objects.nonNull(pushUrl)) {
                // 链接地址
                msgBo.setUri(pushUrl.get(user));
            }
            ReachSendResult result = newnethpDiagMsgRpc.pushAppMsg(msgBo);
            if (!result.getSuccess()) {
                message.setErrorInfo(result.getErrorInfo());
                context.addSendFailMessage(message);
            }
        });
    }

    /**
     * 异步发送站外push消息
     *
     * @param context
     */
    private void pushJmMessage(ExecuteReachTaskContext context) {
        context.getUsers().parallelStream().forEach(user -> {
            LocJmMessageBO messageBO = new LocJmMessageBO(context, user);
            locJmMessageRpc.pushLocJmMessage(messageBO);
        });
    }



    /**
     * 京东主站APP push消息
     * @param context
     */
    private void pushJdAppMessage(ExecuteReachTaskContext context) {

        Map<String, JdhReachMessage> messageMap = context.getMessages().stream().collect(Collectors.toMap(JdhReachMessage::getUserPin, Function.identity(), (o,n)->o));
        context.getUsers().parallelStream().forEach(user -> {

            try {
                log.error("pushJdAppMessage info pushJdAppMessage user={}", JSON.toJSONString(user));
                JdhReachMessage message = messageMap.get(user.getPin());
                ReachTemplateParamBO[] params = context.getUserParams().get(user);

            // 解析校验模版参数
            Map<String,ReachTemplateParamBO> paramMap = Arrays.stream(params).collect(Collectors.toMap(ReachTemplateParamBO::getFiledKey, Function.identity(), (o, n)->o));
            ReachTemplateParamBO image = paramMap.get(JdAppPushFiledEnum.S_IMAGE_PATH.getCode());
            ReachTemplateParamBO landPageUrl = paramMap.get(JdAppPushFiledEnum.LAND_PAGE_URL.getCode());
            ReachTemplateParamBO contentOrder = paramMap.get(JdAppPushFiledEnum.TEXT_CONTENT_ORDER.getCode());
            ReachTemplateParamBO alertOrder = paramMap.get(JdAppPushFiledEnum.MSG_ALERT_ORDER.getCode());
            ReachTemplateParamBO contentTime = paramMap.get(JdAppPushFiledEnum.TEXT_CONTENT_TIME.getCode());
            ReachTemplateParamBO alertTime = paramMap.get(JdAppPushFiledEnum.MSG_ALERT_TIME.getCode());


            // 构建参数
            PushJdAppContentBO contentBO = new PushJdAppContentBO();
            contentBO.setPin(StringUtils.trim(user.getPin()));
            contentBO.setTaskId(String.valueOf(message.getMessageId()));
            contentBO.setSImgPath(StringUtils.trim(image.getValue()));
            contentBO.setLandPageUrl(StringUtils.trim(landPageUrl.getValue()));

            ExtraAttr extraAttr = new ExtraAttr();
            extraAttr.setTextPlanId(context.getTemplate().getChannelTemplateId());
            Map<String,String> textContentParam = new HashMap<>();
            Map<String,String> textMsgAlertParam = new HashMap<>();
            textContentParam.put("order", JdAppPushFiledEnum.splitValue(contentOrder));
            if (Objects.nonNull(contentTime)){
                textContentParam.put("time", contentTime.getValue());
            }
            textMsgAlertParam.put("order", JdAppPushFiledEnum.splitValue(alertOrder));
            if (Objects.nonNull(alertTime)){
                textMsgAlertParam.put("time", alertTime.getValue());
            }

            extraAttr.setTextContentParam(textContentParam);
            extraAttr.setTextMsgAlertParam(textMsgAlertParam);
            contentBO.setExtraAttr(extraAttr);

            jdAppMessageRpc.pushAppMessage(contentBO);
            }catch (Exception e){
                log.error("pushJdAppMessage error", e);
            }
        });
    }

    /**
     * 京ME机器人触达
     * @param context
     */
    private void sendMeRobotMessage(ExecuteReachTaskContext context) {
        Map<String, JdhReachMessage> messageMap = context.getMessages().stream().collect(Collectors.toMap(JdhReachMessage::getUserPin, Function.identity(), (o, n) -> o));
        Map<String, List<ReachUserBO>> groupMap = context.getUsers().stream().collect(Collectors.groupingBy(user -> user.getGroupId() == null ? "" : user.getGroupId()));
        for (Map.Entry<String, List<ReachUserBO>> entry : groupMap.entrySet()) {//无群组ID，则执行单聊发送
            if (StringUtils.isEmpty(entry.getKey())) {
                entry.getValue().parallelStream().forEach(reachUser -> {
                    //获取消息内容
                    JdhReachMessage message = messageMap.get(reachUser.getPin());
                    dongDongRobotRpc.sendDongDongRobotMessage(message.getMessagePayload(), reachUser.getPin());
                });
            } else {
                List<ReachUserBO> reachUserBOList = entry.getValue();
                JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(reachUserBOList));
                //获取消息内容
                JdhReachMessage jdhReachMessage = messageMap.get(reachUserBOList.get(0).getPin());
                dongDongRobotRpc.sendDongDongRobotMessage(jdhReachMessage.getMessagePayload(), entry.getKey(), jsonArray);
            }
        }
    }

    /**
     * 解析模版中的参数
     * 获取模版配置的参数解析规则，根据解析规则配置需要的参数查询函数，selectFunctionIds
     *
     * @param context
     */
    @SuppressWarnings("JdJDMethodBodyCount")
    private void parseParam(ExecuteReachTaskContext context) {
        JdhReachTemplate template = context.getTemplate();
        JdhReachTask reachTask = context.getReachTask();
        List<ReachParamParse> paramParse = template.getParamParse();
        Set<String> functionIds = Sets.newHashSet();

        Map<String, Object> data = Maps.newHashMap();
        data.put("nowDay", TimeUtils.getCurrentDate());
        // 事件透传参数
        if (MapUtils.isNotEmpty(reachTask.getEventBody())) {
            data.put("eventBody", reachTask.getEventBody());
            log.info("ReachDomainServiceImpl->parseParam eventBody={}", JSON.toJSONString(reachTask.getEventBody()));
        }
        if (CollectionUtils.isNotEmpty(paramParse)) {
            // 遍历执行获取源数据的函数
            for (ReachParamParse reachParamParse : paramParse) {
                if (CollectionUtils.isNotEmpty(reachParamParse.getSelectFunctionIds())) {
                    for (String selectFunctionId : reachParamParse.getSelectFunctionIds()) {
                        if (!functionIds.contains(selectFunctionId)) {
                            ReachServiceSelectDataExt function = selectDataFunction.get(selectFunctionId);
                            if (Objects.isNull(function)){
                                log.warn("ReachDomainServiceImpl->parseParam function is null functionId={}", selectFunctionId);
                                throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
                            }
                            SelectReachDataParam param = new SelectReachDataParam();
                            param.setAggregateId(reachTask.getAggregateId());
                            param.setDomainCode(reachTask.getDomainCode());
                            param.setAggregateCode(reachTask.getAggregateCode());
                            param.setEventId(reachTask.getEventId());
                            Map<String, Object> functionData = function.selectData(param);
                            log.info("ReachDomainServiceImpl->parseParam functionId={} functionData={}", selectFunctionId, JSON.toJSONString(functionData));

                            if (Objects.nonNull(functionData)) {
                                data.putAll(functionData);
                            }
                            functionIds.add(selectFunctionId);
                        }
                    }
                }
            }
            Map<ReachUserBO, ReachTemplateParamBO[]> userParams = Maps.newHashMap();
            for (ReachUserBO user : context.getUsers()) {

                data.put("user", user);
                // 根据源数据和解析规则，解析出当前的模版填充的参数值
                ReachTemplateParamBO[] params = new ReachTemplateParamBO[paramParse.size()];
                for (int i = 0; i < paramParse.size(); i++) {
                    ReachParamParse parse = paramParse.get(i);
                    Object value = parse.parse(data);
                    ReachTemplateParamBO paramBO = new ReachTemplateParamBO();
                    paramBO.setFiledKey(parse.getFiledKey());
                    paramBO.setValue(Objects.toString(value, null));
                    paramBO.setProtocol(parse.getProtocol());
                    paramBO.setIsShort(parse.getIsShort());
                    params[i] = paramBO;
                }
                log.info("ReachDomainServiceImpl->parseParam user={} params={}", JSON.toJSONString(user), JSON.toJSONString(params));
                userParams.put(user, params);
            }
            context.setUserParams(userParams);
        }

        log.info("ReachDomainServiceImpl->parseParam data={}", JSON.toJSONString(data));

        // 解析url
        ReachParamParse pushUrlParse = template.getUrlParse();
        if (Objects.nonNull(pushUrlParse)) {
            if (CollectionUtils.isNotEmpty(pushUrlParse.getSelectFunctionIds())) {
                pushUrlParse.getSelectFunctionIds().removeAll(functionIds);
                // 只执行url需要的function
                if (CollectionUtils.isNotEmpty(pushUrlParse.getSelectFunctionIds())) {
                    for (String selectFunctionId : pushUrlParse.getSelectFunctionIds()) {
                        ReachServiceSelectDataExt function = selectDataFunction.get(selectFunctionId);
                        SelectReachDataParam param = new SelectReachDataParam();
                        param.setAggregateId(reachTask.getAggregateId());
                        param.setDomainCode(reachTask.getDomainCode());
                        param.setAggregateCode(reachTask.getAggregateCode());
                        param.setEventId(reachTask.getEventId());
                        Map<String, Object> functionData = function.selectData(param);
                        if (Objects.nonNull(functionData)) {
                            data.putAll(functionData);
                        }
                    }
                }
            }

            Map<ReachUserBO, String> pushUrlMap = Maps.newHashMap();
            for (ReachUserBO user : context.getUsers()) {
                data.put("user", user);
                // 根据源数据和解析规则，解析出当前的模版填充的参数值
                Object value = pushUrlParse.parse(data);
                String url= Objects.toString(value, null);
                log.info("ReachDomainServiceImpl->parseParam user={}, parseUrl={}", JSON.toJSONString(user), url);
                pushUrlMap.put(user, url);
            }
            context.setReachUrl(pushUrlMap);
        }
    }

    /**
     * 圈选触达的目标人群
     * @param context
     */
    private void selectUser(ExecuteReachTaskContext context){
        JdhReachTask reachTask = context.getReachTask();
        log.info("ReachDomainServiceImpl->selectUser reachTask={}", JSON.toJSONString(reachTask));
        SelectReachUserParam param = new SelectReachUserParam();
        BeanUtils.copyProperties(reachTask, param);
        ReachServiceSelectUserExt ext = selectUserFunctionMap.get(reachTask.getSelectUserFunctionId());
        List<ReachUser> users = ext.selectUsers(param);
        if (CollectionUtils.isNotEmpty(users)){
            List<ReachUserBO> userBos = Lists.newArrayList();
            users.forEach(e->{
                userBos.add(new ReachUserBO(e));
            });

            context.setUsers(userBos);
            log.info("ReachDomainServiceImpl->selectUser users={}", JSON.toJSONString(users));
            return;
        }else{
            throw new BusinessException(SupportErrorCode.REACH_TARGET_IS_EMPTY);
        }
    }


    /**
     * 组装消息参数
     * @param reachContext
     * @param fillRules
     * @return
     */
    private String[] buildMessageArgs(ReachContext reachContext, List<ReachTemplateContentFillRule> fillRules) {
        String[] params = new String[fillRules.size()];
        int index = 0;
        for (ReachTemplateContentFillRule rule : fillRules) {
            //groovy脚本执行
            if(TemplateParamFetchType.FETCH_TYPE_GROOVY.getType().equals(rule.getFetchParamType())){
                params[index++] = this.executeGroovy(reachContext, rule.getGroovy().getKey());
            }
            //方法执行
            if (TemplateParamFetchType.FETCH_TYPE_BUILDIN.getType().equals(rule.getFetchParamType())){
                params[index++] = this.executeMethod(reachContext, rule.getMethodKey());
            }
        }
        return params;
    }

    /**
     * 执行Groovy
     *
     * @param reachContext reachContext
     * @param key          key
     * @return {@link String}
     */
    private String executeGroovy(ReachContext reachContext, String key){
        ReachGroovyScriptsUtil scriptsUtil = SpringUtil.getBean(ReachGroovyScriptsUtil.class);
        Script script = scriptsUtil.getScript(key);
        script.getBinding().setVariable("context", reachContext);
        //return (String)scriptsUtil.runScript(script);
        return Convert.convert(String.class,scriptsUtil.runScript(script));
    }

    /**
     * 执行本地方法
     * @param reachContext
     * @param methodKey
     * @return
     */
    private String executeMethod(ReachContext reachContext, String methodKey){
        ReachMessageArgAbility reachMessageArgAbility = reachMessageArgAbilityFactory.createReachMessageArgAbility(ReachMessageArgAbilityCode.getReachMessageArgAbilityCode(methodKey));
        return reachMessageArgAbility.execute(reachContext);
    }
    
    /**
     * 根据获取隐私号配置信息
     * @param query
     *
     */
    private void buildPrivacyNumberConfigByVertical(PrivacyNumberQuery query) {
        List<PrivacyNumberDuccConfig> list = duccConfig.getPrivacyNumberRuleMapping();
        if (CollectionUtil.isEmpty(list)) {
            ErrorCode errorCode = SystemErrorCode.PARAM_NULL_ERROR.formatDescription("隐私号配置信息为空");
            throw new BusinessException(errorCode);
        }
        List<PrivacyNumberDuccConfig> configList = JSON.parseArray(JSON.toJSONString(list), PrivacyNumberDuccConfig.class);
        for (PrivacyNumberDuccConfig config : configList) {
            if (config.getVerticalCode().equals(query.getVerticalCode()) && config.getServiceTypeList().contains(query.getServiceType())
                && PrivacyNumberBindModelEnum.getModelTypeNameByType(query.getBindModel()) != null
                && config.getBindModel().equalsIgnoreCase(Objects.requireNonNull(PrivacyNumberBindModelEnum.getModelTypeNameByType(query.getBindModel())))) {
                query.setAccount(config.getAccount());
                query.setTenant(config.getTenant());
                query.setPasswd(config.getPassword());
                query.setBindDurationSeconds(config.getExpirationSeconds());
                query.setCallDisplayType(config.getCallDisplayType());
            }
        }
        
        if (StringUtils.isBlank(query.getAccount()) || StringUtils.isBlank(query.getPasswd()) || StringUtils.isBlank(query.getTenant())) {
            ErrorCode errorCode = SystemErrorCode.PARAM_NULL_ERROR.formatDescription("当前业务未配置隐私号租户信息");
            throw new BusinessException(errorCode);
        }
    }
    
    /**
     * 通过业务身份，服务类型过滤模板
     *
     * @return
     */
    private List<ReachRuleItem> filter(List<ReachRuleItem> ruleItemList, String verticalCode, String serviceType) {
        
        if (CollectionUtil.isEmpty(ruleItemList)) {
            log.warn("ReachDomainServiceImpl -> filter ruleItemList is empty");
            return Collections.emptyList();
        }
        
        List<ReachRuleItem> list = ruleItemList.stream().filter(v -> v.getVerticalCode().equalsIgnoreCase(verticalCode))
                .filter(s -> s.getServiceTypeList().contains(serviceType))
                .sorted(Comparator.comparing(ReachRuleItem::getIndex)).collect(Collectors.toList());
        log.info("ReachDomainServiceImpl -> filter, verticalCode={}, serviceType={}, result={}", verticalCode, serviceType, JSON.toJSONString(list));
        return list;
    }
    
    /**
     * 发送频率限制
     *
     * @param reachRuleItem item
     * @param userPin pin
     * @param phone phone
     */
    private void checkLimit(ReachRuleItem reachRuleItem, String userPin, String phone) {
        if (reachRuleItem == null || CollectionUtil.isEmpty(reachRuleItem.getLimitingFrequency())) {
            return;
        }
        Long count;
        // 按照自然顺序排序，命中第一个即返回
        List<ReachRuleDuccItem.LimitingFrequency> list = reachRuleItem.getLimitingFrequency().stream().sorted(Comparator.comparing(ReachRuleDuccItem.LimitingFrequency::getSort)).collect(Collectors.toList());
        String errorMsg = null;
        // 先执行各个限制规则，并生成缓存，记录第一个错误信息并
        for (ReachRuleDuccItem.LimitingFrequency limitingFrequency : list) {
            String key = null;
            if (LimitingFrequecyTypeEnum.PHONE.getType().equalsIgnoreCase(limitingFrequency.getLimitType())) {
                key = CacheConstant.SMS_CODE_LIMIT_PREFIX + ":" + limitingFrequency.getLimitSceneName() + ":" + phone;
            } else if (LimitingFrequecyTypeEnum.PIN.getType().equalsIgnoreCase(limitingFrequency.getLimitType())) {
                key = CacheConstant.SMS_CODE_LIMIT_PREFIX + ":" + limitingFrequency.getLimitSceneName() + ":" + userPin;
            }
            count = redisUtil.incrExpire(key, getExpireSeconds(Long.valueOf(limitingFrequency.getDuration()), limitingFrequency.getTimeUnit(), true), TimeUnit.SECONDS);
            // 记录第一个错误信息
            if (StringUtils.isBlank(errorMsg) && count > limitingFrequency.getMaxFrequency()) {
                errorMsg = limitingFrequency.getTips();
            }
        }
        
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new BusinessException(new DynamicErrorCode(SupportErrorCode.SMS_CODE_SEND_LIMIT_ERROR.getCode(), errorMsg));
        }
    }
    
    /**
     * 验证码限制
     *
     * @param expire expire
     * @param timeUnit timeUnit
     */
    private Long getExpireSeconds (Long expire, String timeUnit, Boolean natureTime) {
        if ("SECONDS".equalsIgnoreCase(timeUnit)) {
            return expire;
        } else if ("MINUTES".equalsIgnoreCase(timeUnit)) {
            return expire * 60;
        } else if ("HOURS".equalsIgnoreCase(timeUnit)) {
            return expire * 60 * 60;
        } else if ("DAYS".equalsIgnoreCase(timeUnit)) {
            if (Boolean.TRUE.equals(natureTime)) {
                Date now = new Date();
                return (TimeUtils.getDateEnding(TimeUtils.addDays(now, (int) (expire - 1))).getTime() - now.getTime()) / 1000;
            }
            return expire * 60 * 60 * 24;
        } else {
            // 默认秒
            return expire;
        }
    }
    
}
