package com.jdh.o2oservice.core.domain.support.via.enums;

/**
 * 履约服务抽象的页面展示状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/4 16:59
 */
public enum PromiseAggregateStatusEnum {

    /**
     * 订单状态 个位十位数与OrderStatusEnum保持一致
     */
    WAIT_PAY("waitPay", 101),
    CANCEL_PAY("cancelPay", 107),
    REFUND("refund", 108),
    REFUNDED("refunded", 109),

    /**
     * 派单
     */
    DISPATCH("dispatch", 1),
    /**
     * 赶往用户地址
     */
    TO_HOME("toHome", 2),
    /**
     * 采样
     */
    SERVICING("service", 3),
    /**
     * 送检
     */
    TO_LAB("toLab", 4),
    /**
     * 检测
     */
    TESTING("testing", 5),
    /**
     * 报告已出
     */
    REPORT("report", 6),
    /**
     * 服务已完成
     */
    FINISH("finish", 7),
    ;

    PromiseAggregateStatusEnum(String code, Integer type) {
        this.code = code;
    }

    private String code;

    private Integer type;

    public String getCode() {
        return code;
    }
}
