package com.jdh.o2oservice.core.domain.support.via.model;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;

import java.util.Map;
import java.util.Objects;

/**
 * 视图按钮配置
 *
 * <AUTHOR>
 * @date 2024/01/03
 */
@Data
public class ViaBtnInfo {

    /**
     * 标签
     */
    private String name;

    /**
     * 样式
     */
    private String style;

    /**
     * 按钮唯一标识
     */
    private String code;

    /**
     * 跳转链接规则
     */
    private String jumpUrlRule;

    /**
     * icon
     */
    private String icon;

    /**
     * 具体action
     */
    private ViaActionInfo action;

    /**
     * 按钮提示信息
     */
    private String btnTip;
    /**
     * 等级
     */
    private Integer level;


    public void init(Map<String, Object> data){
        if (CollectionUtil.isEmpty(data)){
            return;
        }

        if (Objects.isNull(action)){
            return;
        }
        action.init(data);
    }

}
