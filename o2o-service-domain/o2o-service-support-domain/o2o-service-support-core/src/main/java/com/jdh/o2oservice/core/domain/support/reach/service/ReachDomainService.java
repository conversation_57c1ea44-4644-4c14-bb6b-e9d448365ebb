package com.jdh.o2oservice.core.domain.support.reach.service;

import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.core.domain.support.reach.context.ExecuteReachTaskContext;
import com.jdh.o2oservice.core.domain.support.reach.context.ReachContext;
import com.jdh.o2oservice.core.domain.support.reach.context.ReadingMessageBO;
import com.jdh.o2oservice.core.domain.support.reach.context.SmsCodeSendContext;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachTask;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachTemplate;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachTrigger;
import com.jdh.o2oservice.core.domain.support.reach.model.PrivacyNumber;
import com.jdh.o2oservice.core.domain.support.reach.repository.query.PrivacyNumberQuery;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 触达领域服务接口
 * <AUTHOR>
 * @date 2023-12-21-6:18 下午
 */
public interface ReachDomainService {

    /**
     * 校验验证码
     */
    boolean checkSmsCode(String userPin, String phone, String verticalCode, String serviceType, String smsCode);

    /**
     * 发送短信验证码
     *
     * @param ctx CTX
     * @return {@link Boolean}
     */
    Boolean sendSmsCode(SmsCodeSendContext ctx);

    /**
     * 生成验证码缓存key
     * @param phone
     * @param verticalCode
     * @param serviceType
     * @return
     */
    String generateCacheKey(String userPin, String phone, String verticalCode, String serviceType);

    String generateCacheKeyByUnique(String uniqueStr);

    /**
     * 获取隐私号
     */
    PrivacyNumber getPrivacyNumber(PrivacyNumberQuery query);

    /**
     * 发送触达
     * @param reachContext
     * @return
     */
    Boolean sendReach(ReachContext reachContext);

    /**
     * 执行触达任务
     * @param context
     * @return
     */
    void buildReachMessage(ExecuteReachTaskContext context);

    /**
     * 发送消息
     * @param context
     * @return
     */
    void sendReachMessage(ExecuteReachTaskContext context);

    /**
     * 根据事件命中的trigger，初始化任务
     * @param event
     * @return
     */
    List<JdhReachTask> initTask(List<JdhReachTrigger> triggers, Event event, List<JdhReachTemplate> templates );

    /**
     * 发送消息
     * @param messageBO
     * @return
     */
    void readMessage(ReadingMessageBO messageBO);


    /**
     * 发送消息
     * @param tasks
     * @return
     */
    void submit(List<JdhReachTask> tasks);

    /**
     * 发送短信验证码(指定唯一ID)
     *
     * @param ctx CTX
     * @return {@link Boolean}
     */
    Boolean sendSmsCodeByUniqueId(SmsCodeSendContext ctx);
}
