package com.jdh.o2oservice.core.domain.support.vertical.context;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.ErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 和AbstractBusinessIdentity的属性保持一致，copy创建
 * @author: yangxiyu
 * @date: 2023/12/19 11:13 上午
 * @version: 1.0
 */
@Data
@Slf4j
public class BusinessContext {

    /**
     * 垂直身份Code
     */
    protected String verticalCode;
    /**
     * 服务类型，serviceType和serviceNo至少传一个，优先传serviceType
     */
    protected String serviceType;
    /**
     * 服务编号，POP为skuNo,一卡万店为抽象的服务编号
     */
    protected String serviceId;
    /**
     * 垂直业务身份
     */
    private JdhVerticalBusiness verticalBusiness;


    /** 客户端名称，不可为空 */
    protected String appName;
    /** 客户端ip，不可为空 */
    protected String clientIp;
    /** 授权的key */
    protected String accessKey;

    protected String envType;
    /** pin */
    protected String userPin;
    /**
     * 来自cookie中的unpl
     */
    private String unpl;
    /**
     * user agent 端
     */
    private Integer ua;

    /**
     * 微信openId
     */
    private String openId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作角色
     * @see com.jdh.o2oservice.common.enums.OperatorRoleTypeEnum
     */
    private Integer operatorRoleType;

    /**
     * 获取业务身份信息，优先从缓存中获取，缓存没有则从数据库获取
     * 1、如果当前context有serviceType则直接赋值；
     * 2、如果当前context没有serviceType，需要根据serviceId去获取serviceType，如果serviceId也不存在，则抛异常。
     */
    public void initVertical() {
        AssertUtils.hasText(verticalCode, "verticalCode is null");
        if (StrUtil.isEmpty(serviceType) && Objects.isNull(serviceId)){
            ErrorCode errorCode = SystemErrorCode.PARAM_NULL_ERROR.formatDescription("serviceType and serviceNo is null");
            log.warn("BusinessContext initVertical serviceType和serviceNo都为空");
            throw new BusinessException(errorCode);
        }

        VerticalBusinessRepository repository = SpringUtil.getBean(VerticalBusinessRepository.class);
        this.verticalBusiness = repository.find(verticalCode);
        if (Objects.isNull(this.verticalBusiness)) {
            throw new BusinessException(BusinessErrorCode.VERTICAL_CODE_NOT_EXIST);
        }
        if (Objects.nonNull(serviceType)){
            this.verticalBusiness.setServiceType(serviceType);
        } else{
            // 根据serviceId获取serviceType
            // 如果是一卡万店，直接查商家域，如果是POP，需要根据serviceId获取商品类目，根据类目获取到serviceType
            //todo
            //this.verticalBusiness = new JdhVerticalBusiness();
        }
        log.info("BusinessContext initVertical verticalBusiness={}", JSON.toJSONString(verticalBusiness));

    }

    public JdhVerticalBusiness getVerticalBusiness(){
        return verticalBusiness;
    }
}
