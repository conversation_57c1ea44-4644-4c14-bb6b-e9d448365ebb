package com.jdh.o2oservice.core.domain.report.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName ReportAIDoctorBo
 * @Description
 * <AUTHOR>
 * @Date 2025/7/14 14:56
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportAIDoctorBo {

    /**
     * pin
     */
    private String userPin;

    /**
     * 健康档案ID
     */
    private String patientId;

    /**
     * 主诉
     */
    private String diseaseDesc;

    /**
     * 主诉图片
     */
    private String diseaseImgUrl;

    /**
     * 页面来源ID，到家传BGJD
     */
    @Builder.Default
    private String pageSourceId = "BGJD";

    /**
     * 业务编码，到家传DJJump
     */
    @Builder.Default
    private String bizCode = "DJJump";

    /**
     * 到家传Daojia_BGJD
     */
    @Builder.Default
    private String hyEntry = "Daojia_BGJD";
}