package com.jdh.o2oservice.core.domain.product.repository.query;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description 题库查询
 */
@Data
public class QuestionDbQuery {

    private Long quesId;//题目id

    private String name;//题目名称

    private Integer type;//题目类型

    private Long serviceItemId;//服务项目id

    private Integer source;//1题库 2ducc

    private List<Long> quesIds;


    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageNum = 1;

    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageSize = 10;
}
