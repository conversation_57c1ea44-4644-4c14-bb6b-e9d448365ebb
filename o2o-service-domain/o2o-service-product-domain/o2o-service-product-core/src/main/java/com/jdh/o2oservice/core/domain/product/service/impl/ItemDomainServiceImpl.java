package com.jdh.o2oservice.core.domain.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.enums.DictKeyEnum;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.product.bo.JdhContentBO;
import com.jdh.o2oservice.core.domain.product.context.*;
import com.jdh.o2oservice.core.domain.product.converter.ItemDomainServiceConvert;
import com.jdh.o2oservice.core.domain.product.enums.ProductErrorCode;
import com.jdh.o2oservice.core.domain.product.model.*;
import com.jdh.o2oservice.core.domain.product.repository.db.*;
import com.jdh.o2oservice.core.domain.product.rpc.ContentExportRpc;
import com.jdh.o2oservice.core.domain.product.rpc.param.JdhContentContext;
import com.jdh.o2oservice.core.domain.product.service.ItemDomainService;
import com.jdh.o2oservice.core.domain.support.basic.dict.model.DictInfo;
import com.jdh.o2oservice.core.domain.support.basic.dict.repository.DictRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/18 6:15 下午
 * @Description:
 */
@Service
@Slf4j
public class ItemDomainServiceImpl implements ItemDomainService {


    @Autowired
    private JdhServiceItemRepository jdhServiceItemRepository;

    @Autowired
    private JdhServiceIndicatorRepository jdhServiceIndicatorRepository;

    @Autowired
    private JdhIndicatorCategoryRepository jdhIndicatorCategoryRepository;

    @Autowired
    private ContentExportRpc contentExportRpc;

    @Autowired
    private JdhBusinessIndicatorCategoryRepository jdhBusinessIndicatorCategoryRepository;
    @Autowired
    private JdhMaterialPackageRepository jdhMaterialPackageRepository;

    /**
     * 词典仓库
     */
    @Resource
    DictRepository dictRepository;


    @Override
    public JdhContentBO queryContentById(JdhContentContext contentContext) {
        Response<JdhContentBO> res = contentExportRpc.queryContentById(contentContext);
        if (!res.isSuccess()) {
            throw new ArgumentsException(ProductErrorCode.NULL_ERROR_FORMAT.formatDescription(res.getMsg()));
        }
        if (Objects.isNull(res.getData()) || !Objects.equals(res.getData().getContentStatus(), 1)) {
            throw new ArgumentsException(ProductErrorCode.PRODUCT_CONTENT_NO_EXIST);
        }
        return res.getData();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSaveServiceItem(List<ServiceItem> serviceItemList) {
        for (ServiceItem serviceItem : serviceItemList) {
            //1 参数校验
            String checkRes = checkItemParam(serviceItem);
            if (StringUtils.isNotBlank(checkRes)) {
                throw new ArgumentsException(ProductErrorCode.NULL_ERROR_FORMAT.formatDescription(checkRes));
            }
            //2 校验指标
            if (CollectionUtils.isNotEmpty(serviceItem.getIndicatorIdList())) {
                ServiceIndicatorQueryContext indicatorQueryContext = ServiceIndicatorQueryContext.builder().indicatorIds(serviceItem.getIndicatorIdList()).build();
                List<Indicator> indicators = jdhServiceIndicatorRepository.queryIndicatorList(indicatorQueryContext);
                if (CollectionUtils.isEmpty(indicators)) {
                    throw new ArgumentsException(ProductErrorCode.PRODUCT_ITEM_CODE_SAVE_FAIL);
                }
                Collection<Long> subIndicatorIds = CollectionUtils.subtract(serviceItem.getIndicatorIdList(), indicators.stream().map(Indicator::getIndicatorId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(subIndicatorIds)) {
                    throw new ArgumentsException(ProductErrorCode.PRODUCT_ITEM_CODE_SAVE_FAIL);
                }
                log.error("batchSaveServiceItem-itemId={},indicatorCount={},saveCount={}", serviceItem.getItemId(), serviceItem.getIndicatorIdList().size(), indicators.size());
                serviceItem.setIndicatorList(indicators);
            }
            log.info("batchSaveServiceItem serviceItem={}", JSON.toJSONString(serviceItem));
            //3 保存项目
            int offset = jdhServiceItemRepository.save(serviceItem);
            if (offset < 1) {
                throw new ArgumentsException(ProductErrorCode.PRODUCT_ITEM_CODE_SAVE_FAIL);
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 校验保存参数
     *
     * @param serviceItem
     * @return
     */
    private String checkItemParam(ServiceItem serviceItem) {
        if (Objects.isNull(serviceItem)) {
            return "入参不能为空";
        }
        Set<String> groups = new HashSet<>();
        groups.addAll(Arrays.stream(DictKeyEnum.values()).map(DictKeyEnum::getKey).collect(Collectors.toSet()));
        Map<String, List<DictInfo>> dic = dictRepository.queryMultiDictList(groups);
        if (MapUtils.isEmpty(dic)) {
            return "基础数据配置异常";
        }
        //新增校验
        if (Objects.nonNull(serviceItem.getItemId())) {
            if (Objects.isNull(serviceItem.getItemType())) {
                return "检测项目类型必填";
            } else {
                if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.ITEM_TYPE.getKey())) ||
                        !dic.get(DictKeyEnum.ITEM_TYPE.getKey()).stream().map(DictInfo::getValue).collect(Collectors.toList()).contains(serviceItem.getItemType())) {
                    return "不支持的检测项目类型";
                }
            }
            if (Objects.isNull(serviceItem.getItemSource())) {
                return "项目来源必填";
            } else {
                if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.ITEM_SOURCE.getKey())) ||
                        !dic.get(DictKeyEnum.ITEM_SOURCE.getKey()).stream().map(DictInfo::getValue).collect(Collectors.toList()).contains(serviceItem.getItemSource())) {
                    return "不支持的检测项目来源";
                }
            }
            if (Objects.equals(serviceItem.getItemSource(), 2) && Objects.isNull(serviceItem.getReferenceItemCode())) {
                return "全国医疗服务技术规范项目编码必填";
            }
            if (StringUtils.isBlank(serviceItem.getItemName())) {
                return "中文项目名称必填";
            }
            if (StringUtils.isBlank(serviceItem.getItemNameEn())) {
                return "英文项目名称必填";
            }
            if (CollectionUtils.isEmpty(serviceItem.getMaterialList())
                    || CollectionUtils.isEmpty(serviceItem.getMaterialList().stream().filter(e -> Objects.equals(e.getRequiredFlag(), 1)).collect(Collectors.toList()))) {
                return "必选耗材必填";
            }
            if (Objects.isNull(serviceItem.getServiceResourceType())) {
                return "所选服务资源必填";
            }
            if (Objects.isNull(serviceItem.getServiceDuration())) {
                return "服务时长必填";
            }
            if (StringUtils.isBlank(serviceItem.getSex())) {
                return "性别必填";
            }
        }
        if (Objects.equals(serviceItem.getItemType(), 2)) {
            if (CollectionUtils.isEmpty(serviceItem.getIndicatorIdList())) {
                return "检测类项目指标必填";
            }
            if (Objects.isNull(serviceItem.getSampleType())) {
                return "检测类项目样本类型必填";
            } else {
                if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.SAMPLE_TYPE.getKey())) ||
                        !dic.get(DictKeyEnum.SAMPLE_TYPE.getKey()).stream().map(DictInfo::getValue).collect(Collectors.toList()).contains(serviceItem.getSampleType())) {
                    return "不支持的样本类型";
                }
            }
            if (Objects.isNull(serviceItem.getTestWay())) {
                return "检测类项目检测方法学必填";
            } else {
                if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.TEST_WAY.getKey())) ||
                        !dic.get(DictKeyEnum.TEST_WAY.getKey()).stream().map(DictInfo::getValue).collect(Collectors.toList()).contains(serviceItem.getTestWay())) {
                    return "不支持的检测方法学类型";
                }
            }
        }
        if (Objects.nonNull(serviceItem.getMedicalRecordCostType())) {
            if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.MEDICAL_RECORD_COST_TYPE.getKey())) ||
                    !dic.get(DictKeyEnum.MEDICAL_RECORD_COST_TYPE.getKey()).stream().map(DictInfo::getValue).collect(Collectors.toList()).contains(serviceItem.getMedicalRecordCostType())) {
                return "不支持的病案首页费用分类";
            }
        }
        if (Objects.nonNull(serviceItem.getCollectNotesType())) {
            if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.COLLECT_NOTES_TYPE.getKey())) ||
                    !dic.get(DictKeyEnum.COLLECT_NOTES_TYPE.getKey()).stream().map(DictInfo::getValue).collect(Collectors.toList()).contains(serviceItem.getCollectNotesType())) {
                return "不支持的收费票据分类";
            }
        }
        if (Objects.nonNull(serviceItem.getLowValueMaterialLevel())) {
            if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.LOW_VALUE_MATERIAL_LEVEL.getKey())) ||
                    !dic.get(DictKeyEnum.LOW_VALUE_MATERIAL_LEVEL.getKey()).stream().map(DictInfo::getValue).collect(Collectors.toList()).contains(serviceItem.getLowValueMaterialLevel())) {
                return "不支持的低值耗材分档";
            }
        }
        return "";

    }

    @Override
    public Page<ServiceItem> queryServiceItemPage(ServiceItemQueryContext serviceItemQueryContext) {
        //1、分页查询项目
        Page<ServiceItem> serviceItemPage = jdhServiceItemRepository.queryServiceItemPageInfo(serviceItemQueryContext);

        if (serviceItemPage == null) {
            return null;
        }
        //2、补充指标id--指标名称
        Set<Long> itemIds = serviceItemPage.getRecords().stream().map(ServiceItem::getItemId).collect(Collectors.toSet());
        JdhItemIndicatorRelQueryContext relQueryContext = JdhItemIndicatorRelQueryContext.builder().serviceItemIds(itemIds).build();
        List<ServiceItemIndicatorRel> relList = jdhServiceItemRepository.queryServiceItemIndicatorRel(relQueryContext);
        //查询指标信息
        if (CollectionUtils.isNotEmpty(relList)) {
            Set<Long> indicatorIds = relList.stream().map(ServiceItemIndicatorRel::getIndicatorId).collect(Collectors.toSet());
            ServiceIndicatorQueryContext indicatorQueryContext = ServiceIndicatorQueryContext.builder().indicatorIds(indicatorIds).build();
            List<Indicator> indicators = jdhServiceIndicatorRepository.queryIndicatorList(indicatorQueryContext);
            ItemDomainServiceConvert.ins.packIndicators(serviceItemPage.getRecords(), relList, indicators);
        }
        return serviceItemPage;
    }


    @Override
    @LogAndAlarm
    public List<ServiceItem> queryServiceItemList(ServiceItemQueryContext serviceItemQueryContext) {
        if (Objects.isNull(serviceItemQueryContext) ||
                (Objects.isNull(serviceItemQueryContext.getItemId()) && CollectionUtils.isEmpty(serviceItemQueryContext.getItemIds()))) {
            throw new ArgumentsException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        //1、查询项目列表
        List<ServiceItem> serviceItemList = jdhServiceItemRepository.queryJdhItemList(serviceItemQueryContext);
        if (CollectionUtils.isEmpty(serviceItemList)) {
            return Collections.emptyList();
        }

        Set<Long> itemIds = serviceItemList.stream().map(ServiceItem::getItemId).collect(Collectors.toSet());
        JdhItemIndicatorRelQueryContext relQueryContext = JdhItemIndicatorRelQueryContext.builder().serviceItemIds(itemIds).build();
        List<ServiceItemIndicatorRel> relList = jdhServiceItemRepository.queryServiceItemIndicatorRel(relQueryContext);
        //查询指标信息
        if (CollectionUtils.isNotEmpty(relList)) {
            Set<Long> indicatorIds = relList.stream().map(ServiceItemIndicatorRel::getIndicatorId).collect(Collectors.toSet());
            ServiceIndicatorQueryContext indicatorQueryContext = ServiceIndicatorQueryContext.builder().indicatorIds(indicatorIds).build();
            List<Indicator> indicators = jdhServiceIndicatorRepository.queryIndicatorList(indicatorQueryContext);
            ItemDomainServiceConvert.ins.packIndicators(serviceItemList, relList, indicators);
        }
        //查询耗材
        JdhItemMaterialPackageRelQueryContext materialRelQueryContext = JdhItemMaterialPackageRelQueryContext.builder().serviceItemIds(itemIds).build();
        List<ServiceItemMaterialPackageRel> materialPackageRelList = jdhServiceItemRepository.queryServiceItemMaterialPackageRel(materialRelQueryContext);
        if (CollectionUtils.isNotEmpty(materialPackageRelList)) {
            List<JdhMaterialPackage> queryList = ItemDomainServiceConvert.ins.packJdhMaterialPackageQuery(materialPackageRelList);
            List<JdhMaterialPackage> packageList = jdhMaterialPackageRepository.queryList(queryList);
            ItemDomainServiceConvert.ins.packMaterials(serviceItemList, materialPackageRelList, packageList);
        }
        //查询技能
        JdhItemAngelSkillRelQueryContext queryContext = JdhItemAngelSkillRelQueryContext.builder().serviceItemIds(itemIds).build();
        List<ServiceItemAngelSkillRel> skillRelList = jdhServiceItemRepository.queryServiceItemAngelSkillRel(queryContext);
        if (CollectionUtils.isNotEmpty(skillRelList)) {
            ItemDomainServiceConvert.ins.packSkills(serviceItemList, skillRelList);
        }
        return serviceItemList;
    }

    @Override
    public List<IndicatorCategory> queryIndicatorCategory(IndicatorCategoryQueryContext queryContext) {
        BusinessIndicatorCategoryQueryContext context = BusinessIndicatorCategoryQueryContext.builder().indicatorCategoryType(queryContext.getIndicatorCategoryType()).build();
        List<BusinessIndicatorCategory> businessIndicatorCategoryList = jdhBusinessIndicatorCategoryRepository.queryBusinessIndicatorCategoryList(context);
        Set<Long> cateIdSet = businessIndicatorCategoryList.stream().map(BusinessIndicatorCategory::getFirstIndicatorCategory).collect(Collectors.toSet());
        cateIdSet.addAll(businessIndicatorCategoryList.stream().map(BusinessIndicatorCategory::getSecondIndicatorCategory).collect(Collectors.toSet()));
        cateIdSet.addAll(businessIndicatorCategoryList.stream().map(BusinessIndicatorCategory::getThirdIndicatorCategory).collect(Collectors.toSet()));
        queryContext.setCategoryIdSet(cateIdSet);
        List<IndicatorCategory> categoryList = jdhIndicatorCategoryRepository.queryIndicatorCategoryList(queryContext);
        return categoryList;
    }

    /**
     * 保存项目
     *
     * @param serviceItem serviceItem
     * @return true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveServiceItem(ServiceItem serviceItem) {
        return jdhServiceItemRepository.save(serviceItem) > 0;
    }

    /**
     * 查询服务列表
     *
     * @param serviceItemQueryContext
     * @return
     */
    @Override
    public List<ServiceItem> queryServiceItemListCondition(ServiceItemConditionQueryContext serviceItemQueryContext) {
        if (Objects.isNull(serviceItemQueryContext) ||
                CollectionUtils.isEmpty(serviceItemQueryContext.getItemIds())) {
            throw new ArgumentsException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        //1、查询项目列表
        List<ServiceItem> serviceItemList = jdhServiceItemRepository.queryJdhItemList(ServiceItemQueryContext.builder().itemIds(serviceItemQueryContext.getItemIds()).build());
        if (CollectionUtils.isEmpty(serviceItemList)) {
            return Collections.emptyList();
        }

        Set<Long> itemIds = serviceItemList.stream().map(ServiceItem::getItemId).collect(Collectors.toSet());
        if (Boolean.TRUE.equals(serviceItemQueryContext.getIndicatorQuery())){
            JdhItemIndicatorRelQueryContext relQueryContext = JdhItemIndicatorRelQueryContext.builder().serviceItemIds(itemIds).build();
            List<ServiceItemIndicatorRel> relList = jdhServiceItemRepository.queryServiceItemIndicatorRel(relQueryContext);
            //查询指标信息
            if (CollectionUtils.isNotEmpty(relList)) {
                Set<Long> indicatorIds = relList.stream().map(ServiceItemIndicatorRel::getIndicatorId).collect(Collectors.toSet());
                ServiceIndicatorQueryContext indicatorQueryContext = ServiceIndicatorQueryContext.builder().indicatorIds(indicatorIds).build();
                List<Indicator> indicators = jdhServiceIndicatorRepository.queryIndicatorList(indicatorQueryContext);
                ItemDomainServiceConvert.ins.packIndicators(serviceItemList, relList, indicators);
            }
        }
        return serviceItemList;
    }
}
