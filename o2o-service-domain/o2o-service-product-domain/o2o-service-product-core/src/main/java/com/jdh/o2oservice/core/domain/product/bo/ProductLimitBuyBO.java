package com.jdh.o2oservice.core.domain.product.bo;

import lombok.Data;

/**
 * @ClassName:ProductLimitBuyVO
 * @Description: TODO
 * @Author: yaoqing<PERSON>
 * @Date: 2023/12/11 21:19
 * @Vserion: 1.0
 **/
@Data
public class ProductLimitBuyBO {

    /**
     * 每单最多购买件数
     */
    private Integer limitPreOrderNum;

    /**
     * 每单最少购买件数
     */
    private Integer limitMinNum;

    /**
     * 合并最大购买数
     */
    private Integer mergeMaxBuyNum;

}
