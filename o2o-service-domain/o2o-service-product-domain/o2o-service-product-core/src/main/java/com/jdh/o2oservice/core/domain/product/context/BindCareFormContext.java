package com.jdh.o2oservice.core.domain.product.context;

import com.jdh.o2oservice.core.domain.product.context.careform.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description 护理单
 */
@Data
public class BindCareFormContext {

    private Long serviceItemId;//服务项目id

    private String userName;//操作者

    private PreReceiveAssessmentCxt preReceiveAssessment;//接单前评估

    private PreServiceAssessmentCxt preServiceAssessment;//服务前评估

    private PreServiceSignatureCxt preServiceSignature;//知情签字

    private SupplyVerificationCxt supplyVerification;//耗材确认

    private ServiceRecordCxt serviceRecord;//服务记录

    private HealthEduCxt healthEdu;//健康宣教

    private RecordUploadCxt recordUpload;//记录上传

    private SignConfirmCxt signConfirm;//签字确认

}
