package com.jdh.o2oservice.core.domain.product.service.careform;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.model.careform.QuestionGroupConfig;
import com.jdh.o2oservice.core.domain.product.context.BindCareFormContext;
import com.jdh.o2oservice.core.domain.product.context.careform.GroupCxt;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhItemQuesGroupRel;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhGroupQuesRelRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhItemQuesGroupRelRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @description 题节点接口
 */
public abstract class GroupAbstract implements GroupInterface{

     @Autowired
     private JdhItemQuesGroupRelRepository jdhItemQuesGroupRelRepository;

     @Autowired
     private JdhGroupQuesRelRepository jdhGroupQuesRelRepository;

     @LogAndAlarm
     public void process(BindCareFormContext bindCareFormContext, QuestionGroupConfig questionGroupConfig) throws IOException {
          //构建节点
          GroupCxt groupCxt = this.buildGroup(bindCareFormContext);
          //判断节点是否存在
          JdhItemQuesGroupRel jdhItemQuesGroupRel = jdhItemQuesGroupRelRepository.findByCodeAndItemId(bindCareFormContext.getServiceItemId(),questionGroupConfig.getCode());
          JdhItemQuesGroupRel jdhItemQuesGroupRelData = this.buildJdhItemQuesGroupRel(bindCareFormContext,questionGroupConfig,groupCxt);

          if(jdhItemQuesGroupRel==null){
               //新增节点
               jdhItemQuesGroupRelRepository.save(jdhItemQuesGroupRelData);
          }else{
               //修改节点
               jdhItemQuesGroupRelRepository.update(jdhItemQuesGroupRelData);
          }
          //处理题
          List<JdhGroupQuesRel> jdhGroupQuesRels = this.buildJdhGroupQuesRel(bindCareFormContext,groupCxt,questionGroupConfig);
          for (JdhGroupQuesRel jdhGroupQuesRel:jdhGroupQuesRels) {
               JdhGroupQuesRel jdhGroupQuesRelData = jdhGroupQuesRelRepository.findByCodeAndItemId(bindCareFormContext.getServiceItemId(),jdhGroupQuesRel.getGroupCode(),jdhGroupQuesRel.getQuesCode());
               if(jdhGroupQuesRelData==null){
                    jdhGroupQuesRelRepository.save(jdhGroupQuesRel);
               }else{
                    jdhGroupQuesRelRepository.update(jdhGroupQuesRel);
               }
          }

     }

     JdhItemQuesGroupRel buildJdhItemQuesGroupRel(BindCareFormContext bindCareFormContext, QuestionGroupConfig questionGroupConfig,GroupCxt groupCxt){
          JdhItemQuesGroupRel jdhItemQuesGroupRelInsert = new JdhItemQuesGroupRel();
          jdhItemQuesGroupRelInsert.setServiceItemId(bindCareFormContext.getServiceItemId());
          jdhItemQuesGroupRelInsert.setGroupCode(questionGroupConfig.getCode());
          jdhItemQuesGroupRelInsert.setGroupSnapshot(JSON.toJSONString(questionGroupConfig));
          jdhItemQuesGroupRelInsert.setShow((groupCxt==null||groupCxt.getShow()==null)?questionGroupConfig.getShow():groupCxt.getShow());
          jdhItemQuesGroupRelInsert.setCreateTime(new Date());
          jdhItemQuesGroupRelInsert.setUpdateTime(new Date());
          jdhItemQuesGroupRelInsert.setCreateUser(bindCareFormContext.getUserName());
          jdhItemQuesGroupRelInsert.setUpdateUser(bindCareFormContext.getUserName());
          jdhItemQuesGroupRelInsert.setSort(questionGroupConfig.getSort());
          return jdhItemQuesGroupRelInsert;
     }
}
