package com.jdh.o2oservice.core.domain.dispatch.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.dispatch.model.*;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchTeamRepQuery;

/**
 * @ClassName DispatchTeamRepository
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 15:08
 **/
public interface DispatchTeamRepository extends Repository<JdhDispatchTeam, JdhDispatchTeamIdentifier> {

    /**
     * findDispatch
     *
     * @param query 查询
     * @return {@link JdhDispatch}
     */
    Page<JdhDispatchTeam> findDispatchTeamPage(DispatchTeamRepQuery query);

    /**
     * 删除小队技能
     * @param dispatchTeamSkillRel
     * @return
     */
    int deleteDispatchTeamSkillRel(JdhDispatchTeamSkillRel dispatchTeamSkillRel);

    /**
     * 删除小队服务者
     * @param dispatchTeamAngelRel
     * @return
     */
    int deleteDispatchTeamAngelRel(JdhDispatchTeamAngelRel dispatchTeamAngelRel);
}