package com.jdh.o2oservice.core.domain.dispatch.enums;

import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;

/**
 * 派单域聚合类型
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
public enum DispatchAggregateEnum implements AggregateCode {
    /** */
    DISPATCH(DomainEnum.DISPATCH, "dispatch"),
    /** */
    DISPATCH_DETAIL(DomainEnum.DISPATCH, "dispatchDetail"),
    /** */
    DISPATCH_HISTORY(DomainEnum.DISPATCH, "dispatchHistory"),
    /** */
    DISPATCH_FACTOR(DomainEnum.DISPATCH, "dispatchFactor"),
    /** */
    DISPATCH_FLOW(DomainEnum.DISPATCH, "dispatchFlow"),
    /** */
    DISPATCH_TEAM(DomainEnum.DISPATCH, "dispatchTeam"),
    ;

    /** */
    private DomainCode domain;
    /** */
    private String code;
    /** */
    DispatchAggregateEnum(DomainCode domain, String code) {
        this.domain = domain;
        this.code = code;
    }

    /** */
    @Override
    public DomainCode getDomainCode() {
        return domain;
    }

    /** */
    @Override
    public String getCode() {
        return code;
    }

}
