package com.jdh.o2oservice.core.domain.dispatch.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName JdhDispatchTeamSkillRel
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 16:42
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhDispatchTeamSkillRel {

    /**
     * 主键
     */
    private Long id;

    /**
     * 派单小队ID
     */
    private Long dispatchTeamId;

    /**
     * 服务者技能code（全局唯一）
     */
    private String angelSkillCode;

    /**
     * 服务者技能名称
     */
    private String angelSkillName;

    /**
     * 服务类型 1:供应商体检项目 2:检测类 3:护理类
     */
    private Integer itemType;

    /**
     * 互医服务组id
     */
    private String serviceGroupId;

    /**
     * 数据来源分支
     */
    private String branch;
    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;

    /**
     * 创建人pin
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人pin
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}