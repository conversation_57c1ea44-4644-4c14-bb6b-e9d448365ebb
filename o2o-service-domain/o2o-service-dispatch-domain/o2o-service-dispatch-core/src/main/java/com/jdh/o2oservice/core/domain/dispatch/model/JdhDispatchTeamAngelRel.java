package com.jdh.o2oservice.core.domain.dispatch.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName JdhDispatchTeamAngelRel
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 16:43
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhDispatchTeamAngelRel {

    /**
     * 主键
     */
    private Long id;

    /**
     * 派单小队ID
     */
    private Long dispatchTeamId;

    /**
     * 服务者ID
     */
    private Long angelId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;

    /**
     * 创建人pin
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人pin
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}