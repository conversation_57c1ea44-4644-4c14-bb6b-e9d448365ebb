package com.jdh.o2oservice.core.domain.dispatch.model;

import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchAggregateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @ClassName JdhDispatchTeam
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 14:59
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhDispatchTeam implements Aggregate<JdhDispatchTeamIdentifier> {

    /**
     * id
     */
    private Long id;

    /**
     * 派单小队ID
     */
    private Long dispatchTeamId;

    /**
     * 派单小队名称
     */
    private String dispatchTeamName;

    /**
     * 派单小队状态：0-停用 1-启用
     */
    private Integer dispatchTeamStatus;

    /**
     * 备注
     */
    private String dispatchTeamRemark;

    /**
     * 小队负责人
     */
    private String dispatchTeamLeader;

    /**
     * 小队技能
     */
    private List<JdhDispatchTeamSkillRel> teamSkillRelList;

    /**
     * 小队服务者
     */
    private List<JdhDispatchTeamAngelRel> teamAngelRelList;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.DISPATCH;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return DispatchAggregateEnum.DISPATCH_TEAM;
    }

    @Override
    public Integer version() {
        return version;
    }

    @Override
    public void versionIncrease() {
        version++;
    }

    @Override
    public JdhDispatchTeamIdentifier getIdentifier() {
        return JdhDispatchTeamIdentifier.builder().dispatchTeamId(this.getDispatchTeamId()).build();
    }
}