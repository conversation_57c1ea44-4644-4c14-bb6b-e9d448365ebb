package com.jdh.o2oservice.core.domain.dispatch.repository.query;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @ClassName DispatchTeamRepQuery
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 15:09
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DispatchTeamRepQuery extends AbstractPageQuery {

    /**
     * 派单小队ID
     */
    private Long dispatchTeamId;

    /**
     * 派单小队名称-模糊查询
     */
    private String fuzzyDispatchTeamName;

    /**
     * 小队状态集合
     */
    private List<Integer> dispatchTeamStatusList;

    /**
     * 查询小队技能
     */
    @Builder.Default
    private Boolean queryTeamSkill = Boolean.FALSE;

    /**
     * 查询小队服务者
     */
    @Builder.Default
    private Boolean queryTeamAngel = Boolean.FALSE;
}