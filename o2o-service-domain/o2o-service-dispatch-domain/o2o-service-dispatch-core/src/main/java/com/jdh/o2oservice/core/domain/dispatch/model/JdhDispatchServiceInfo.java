package com.jdh.o2oservice.core.domain.dispatch.model;

import com.jd.health.medical.examination.export.dto.AppointmentTimeDTO;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.support.basic.model.DomainAppointmentTime;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @ClassName JdhDispatchServiceInfo
 * @Description 派单服务详细数据
 * <AUTHOR>
 * @Date 2024/4/26 18:38
 **/
@Data
public class JdhDispatchServiceInfo {

    /**
     * 预约时间
     */
    private DispatchCtxAppointmentTime appointmentTime;

    /**
     * 顾客
     */
    private List<AppointmentPatient> patients;

    /**
     * 服务地址经纬度
     */
    private GisPointBo gisPoint;

    /**
     * 服务时长分钟
     */
    private Integer serviceDuration;

    /**
     * 当前派单成功。结果为派单类型次数
     */
    private Integer currentAppointNum;

    /**
     * 当前派单成功。结果为抢单类型次数
     */
    private Integer currentGrabNum;

    /**
     * 当前轮次派单成功时间
     */
    private Date currentDispatchTime;

    /**
     * 当前轮次派单接单失效时间
     */
    private Date currentDispatchExpireTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 服务兑换来源id
     */
    private String sourceVoucherId;

    /**
     * 业务模式
     */
    private String businessModeCode;

    /**
     * 派单执行路由：flow nethp
     */
    private String dispatchExecuteRoute;

    /**
     *
     */
    private Long flowId;
    /**
     * 意向派单ID
     */
    private List<Long> intendedAngelIds;

    /**
     * 预约时间
     */
    private DomainAppointmentTime beforeTime;

    /**
     * 计划出门时间
     */
    private Date planOutTime;

    /**
     * 计划完成时间
     */
    private Date planFinishTime;

    /**
     * 派单类型次数增加
     */
    public void currentAppointNumIncrease() {
        currentAppointNum = Optional.ofNullable(currentAppointNum).map(num -> num + 1).orElse(1);
    }

    /**
     * 抢单类型次数增加
     */
    public void currentGrabNumIncrease() {
        currentGrabNum = Optional.ofNullable(currentGrabNum).map(num -> num + 1).orElse(1);
    }

    /**
     * 格式化派单时间
     * @return
     */
    public String formatDispatchTime(){
        if (Objects.nonNull(currentDispatchTime)){
            return TimeUtils.dateTimeToStr(currentDispatchTime);
        }
        return "";
    }

    /**
     * 计算有效时间
     * @return
     */
    public String calcExpireTime(){
        if (Objects.nonNull(currentDispatchTime) && Objects.nonNull(currentDispatchExpireTime)){
            long dispatchTime = currentDispatchTime.getTime();
            long expireTime = currentDispatchExpireTime.getTime();
            long second = (expireTime - dispatchTime)/ 1000L;
            return String.valueOf(second);
        }
        return null;
    }

}