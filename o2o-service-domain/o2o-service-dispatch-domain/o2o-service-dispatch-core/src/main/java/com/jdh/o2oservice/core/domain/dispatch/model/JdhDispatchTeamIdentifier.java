package com.jdh.o2oservice.core.domain.dispatch.model;

import com.jdh.o2oservice.base.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName JdhDispatchTeamIdentifier
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 14:59
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhDispatchTeamIdentifier implements Identifier {

    /**
     * dispatchTeamId
     */
    private Long dispatchTeamId;

    /**
     * 序列化
     *
     * @return {@link String}
     */
    @Override
    public String serialize() {
        return String.valueOf(dispatchTeamId);
    }
}