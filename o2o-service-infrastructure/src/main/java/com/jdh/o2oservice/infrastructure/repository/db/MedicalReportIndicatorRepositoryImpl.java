package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.report.bo.MedReportIndicatorQueryBO;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicator;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportIndicatorRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.MedicalReportConvert;
import com.jdh.o2oservice.infrastructure.repository.db.dao.MedicalReportIndicatorMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.MedicalReportIndicatorPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * MedicalReportIndicatorRepositoryImpl
 * <AUTHOR>
 * @date 2024-11-13 13:35
 */
@Component
@Slf4j
public class MedicalReportIndicatorRepositoryImpl implements MedicalReportIndicatorRepository {

    /**
     * medicalReportIndicatorMapper
     */
    @Resource
    private MedicalReportIndicatorMapper medicalReportIndicatorMapper;

    /**
     * 保存医疗报告指标。
     * @param medicalReportIndicators 医疗报告指标对象。
     * @return 保存结果，true表示成功，false表示失败。
     */
    @Override
    public Boolean saveMedicalReportIndicators(List<MedicalReportIndicator> medicalReportIndicators) {

        if(medicalReportIndicators == null || medicalReportIndicators.isEmpty()) {
            return Boolean.TRUE;
        }

        List<MedicalReportIndicatorPo> medicalReportIndicatorPos = MedicalReportConvert.INSTANCE.convertList(medicalReportIndicators);

        Map<String, List<MedicalReportIndicatorPo>> map = medicalReportIndicatorPos.stream().collect(Collectors.groupingBy(MedicalReportIndicatorPo::getReportId));


        for (Map.Entry<String, List<MedicalReportIndicatorPo>> entry : map.entrySet()) {


            LambdaQueryWrapper<MedicalReportIndicatorPo> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper
                    .eq(MedicalReportIndicatorPo::getReportId,entry.getKey());

            List<MedicalReportIndicatorPo> medicalReportIndicatorPos1 = medicalReportIndicatorMapper.selectList(queryWrapper);
            log.info("saveMedicalReportIndicators->medicalReportIndicatorPos1={}", JSONUtil.toJsonStr(medicalReportIndicatorPos1));
            if(CollectionUtils.isNotEmpty(medicalReportIndicatorPos1)) {
                LambdaUpdateWrapper<MedicalReportIndicatorPo> updateWrapper = Wrappers.lambdaUpdate();

                updateWrapper
                        .set(MedicalReportIndicatorPo::getYn, YnStatusEnum.NO.getCode())
                        .eq(MedicalReportIndicatorPo::getReportId,entry.getKey())
                        .setSql( CommonConstant.UPDATE_TIME_SQL_NOW);
                medicalReportIndicatorMapper.update(null,updateWrapper);
            }

            for (MedicalReportIndicatorPo medicalReportIndicatorPo : entry.getValue()) {
                int insert = medicalReportIndicatorMapper.insert(medicalReportIndicatorPo);
                log.info("saveMedicalReportIndicators->insert={}", insert);

            }

        }

        return Boolean.TRUE;
    }

    @Override
    public PageDto<MedicalReportIndicator> pageMedicalReportIndicators(Integer pageNum, Integer pageSize) {

        IPage<MedicalReportIndicatorPo> iPage = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<MedicalReportIndicatorPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(MedicalReportIndicatorPo::getYn, YnStatusEnum.YES.getCode())
                .isNull(MedicalReportIndicatorPo::getIndicatorId)
                .orderByAsc(MedicalReportIndicatorPo::getId)
        ;

        IPage<MedicalReportIndicatorPo> page = medicalReportIndicatorMapper.selectPage(iPage, queryWrapper);

        if (Objects.nonNull(page)){
            PageDto<MedicalReportIndicator> pageDto = new PageDto<>();
            pageDto.setPageSize(page.getSize());
            pageDto.setPageNum(page.getCurrent());
            pageDto.setTotalPage(page.getPages());
            pageDto.setTotalCount(page.getTotal());
            if (CollectionUtils.isNotEmpty(page.getRecords())){
                List<MedicalReportIndicator> medicalReportIndicators = MedicalReportConvert.INSTANCE.convertPoList(page.getRecords());
                pageDto.setList(medicalReportIndicators);
            }
            return pageDto;
        }
        return null;
    }

    /**
     * 更新医疗报告指标。
     * @param medicalReportIndicator 医疗报告指标列表。
     * @return 更新结果。
     */
    @Override
    public Boolean updateMedicalReportIndicators(List<MedicalReportIndicator> medicalReportIndicator) {

        if(medicalReportIndicator == null || medicalReportIndicator.isEmpty()){
            return Boolean.TRUE;
        }

        List<MedicalReportIndicatorPo> medicalReportIndicatorPos = MedicalReportConvert.INSTANCE.convertList(medicalReportIndicator);

        int count = 0;

        for (MedicalReportIndicatorPo medicalReportIndicatorPo : medicalReportIndicatorPos) {
            LambdaUpdateWrapper<MedicalReportIndicatorPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper
                    .set(Objects.nonNull(medicalReportIndicatorPo.getIndicatorId()),MedicalReportIndicatorPo::getIndicatorId, medicalReportIndicatorPo.getIndicatorId())
                    .set(Objects.nonNull(medicalReportIndicatorPo.getCheckTime()),MedicalReportIndicatorPo::getCheckTime,medicalReportIndicatorPo.getCheckTime())
                    .set(StringUtil.isNotBlank(medicalReportIndicatorPo.getCtValue()),MedicalReportIndicatorPo::getCtValue,medicalReportIndicatorPo.getCtValue())
                    .eq(MedicalReportIndicatorPo::getId, medicalReportIndicatorPo.getId())
                    .setSql(CommonConstant.UPDATE_TIME_SQL_NOW);
            ;
            count += medicalReportIndicatorMapper.update(null,updateWrapper);
        }

        return count>0;
    }

    /**
     * 分页获取医疗报告指标。
     *
     * @param pageNum  当前页码，从1开始。
     * @param pageSize 每页记录数。
     * @return 分页数据对象，包含医疗报告指标列表。
     */
    @Override
    public PageDto<MedicalReportIndicator> pageReportIndicatorsNoCheckTime(Integer pageNum, Integer pageSize) {
        IPage<MedicalReportIndicatorPo> iPage = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<MedicalReportIndicatorPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(MedicalReportIndicatorPo::getYn, YnStatusEnum.YES.getCode())
                .isNull(MedicalReportIndicatorPo::getCheckTime)
                .orderByAsc(MedicalReportIndicatorPo::getId)
        ;

        IPage<MedicalReportIndicatorPo> page = medicalReportIndicatorMapper.selectPage(iPage, queryWrapper);

        if (Objects.nonNull(page)){
            PageDto<MedicalReportIndicator> pageDto = new PageDto<>();
            pageDto.setPageSize(page.getSize());
            pageDto.setPageNum(page.getCurrent());
            pageDto.setTotalPage(page.getPages());
            pageDto.setTotalCount(page.getTotal());
            if (CollectionUtils.isNotEmpty(page.getRecords())){
                List<MedicalReportIndicator> medicalReportIndicators = MedicalReportConvert.INSTANCE.convertPoList(page.getRecords());
                pageDto.setList(medicalReportIndicators);
            }
            return pageDto;
        }
        return null;
    }

    /**
     * 根据查询条件获取医疗报告指标列表
     *
     * @param medReportIndicatorQueryBO 查询条件对象，包含需要查询的指标信息
     * @return 符合查询条件的医疗报告指标列表
     */
    @Override
    public List<MedicalReportIndicator> listMedicalReportIndicators(MedReportIndicatorQueryBO medReportIndicatorQueryBO) {
        LambdaQueryWrapper<MedicalReportIndicatorPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(MedicalReportIndicatorPo::getYn,YnStatusEnum.YES.getCode())
                .eq(StringUtils.isNotBlank(medReportIndicatorQueryBO.getStationId()),MedicalReportIndicatorPo::getStationId,medReportIndicatorQueryBO.getStationId())
                .eq(Objects.nonNull(medReportIndicatorQueryBO.getServiceItemId()),MedicalReportIndicatorPo::getServiceItemId,medReportIndicatorQueryBO.getServiceItemId())
                .ge(Objects.nonNull(medReportIndicatorQueryBO.getStartDate()),MedicalReportIndicatorPo::getCheckTime,medReportIndicatorQueryBO.getStartDate())
                .le(Objects.nonNull(medReportIndicatorQueryBO.getEndDate()),MedicalReportIndicatorPo::getCheckTime,medReportIndicatorQueryBO.getEndDate())
                .in(CollectionUtils.isNotEmpty(medReportIndicatorQueryBO.getReportIds()) , MedicalReportIndicatorPo::getReportId,medReportIndicatorQueryBO.getReportIds())
                .ne(Boolean.TRUE.equals(medReportIndicatorQueryBO.getAbnormalOnly()),MedicalReportIndicatorPo::getAbnormalMarkType,0)
        ;


        List<MedicalReportIndicatorPo> medicalReportIndicatorPos = medicalReportIndicatorMapper.selectList(queryWrapper);
        return MedicalReportConvert.INSTANCE.convertPoList(medicalReportIndicatorPos);
    }

    /**
     * 根据指定的id删除医疗报告指标
     *
     * @param reportId 要删除的医疗报告指标的id
     * @return 删除操作是否成功
     */
    @Override
    public Boolean deleteMedicalReportIndicators(String reportId) {
        LambdaUpdateWrapper<MedicalReportIndicatorPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(MedicalReportIndicatorPo::getYn,YnStatusEnum.NO.getCode())
                .eq(MedicalReportIndicatorPo::getReportId,reportId);
        medicalReportIndicatorMapper.update(null,updateWrapper);
        return Boolean.TRUE;
    }

    /**
     * 根据查询条件获取医疗报告指标列表
     *
     * @return 符合查询条件的医疗报告指标列表
     */
    @Override
    public List<MedicalReportIndicator> listNoIndicatorId(Integer startId,Integer endId) {
        LambdaQueryWrapper<MedicalReportIndicatorPo> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper
                .eq(MedicalReportIndicatorPo::getYn,CommonConstant.ONE)
                .isNull(MedicalReportIndicatorPo::getIndicatorId)
                .ge(MedicalReportIndicatorPo::getId,startId)
                .le(MedicalReportIndicatorPo::getId,endId)
                .orderByAsc(MedicalReportIndicatorPo::getId)
        ;
        List<MedicalReportIndicatorPo> medicalReportIndicatorPos = medicalReportIndicatorMapper.selectList(queryWrapper);
        return MedicalReportConvert.INSTANCE.convertPoList(medicalReportIndicatorPos);
    }

    /**
     * 保存医疗报告指标。
     *
     * @param medicalReportIndicator 医疗报告指标对象。
     * @return 保存是否成功。
     */
    @Override
    public Boolean saveIndicatorId(MedicalReportIndicator medicalReportIndicator) {
        LambdaUpdateWrapper<MedicalReportIndicatorPo> updateWrapper = Wrappers.lambdaUpdate();

        updateWrapper
                .set(MedicalReportIndicatorPo::getIndicatorId,medicalReportIndicator.getIndicatorId())
                .setSql(CommonConstant.UPDATE_TIME_SQL_NOW)
                .eq(MedicalReportIndicatorPo::getId,medicalReportIndicator.getId())
        ;
        return medicalReportIndicatorMapper.update(null,updateWrapper)>0;
    }


}
