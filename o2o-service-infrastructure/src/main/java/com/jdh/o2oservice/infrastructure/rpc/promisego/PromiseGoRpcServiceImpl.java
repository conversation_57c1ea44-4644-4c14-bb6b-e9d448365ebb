package com.jdh.o2oservice.infrastructure.rpc.promisego;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jdh.o2o.promisego.common.response.Response;
import com.jdh.o2o.promisego.export.dto.LabPromisegoDto;
import com.jdh.o2o.promisego.export.dto.PreUserPromisegoDto;
import com.jdh.o2o.promisego.export.dto.ScriptDto;
import com.jdh.o2o.promisego.export.dto.UserPromisegoDto;
import com.jdh.o2o.promisego.export.query.LabPromisegoRequest;
import com.jdh.o2o.promisego.export.query.PreUserPromisegoRequest;
import com.jdh.o2o.promisego.export.query.UserPromisegoRequest;
import com.jdh.o2o.promisego.export.service.PromisegoQueryExport;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.PromiseGoRpcService;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.*;
import com.jdh.o2oservice.infrastructure.rpc.promisego.convert.PromisegoRpcConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: PromiseGoRpcServiceImpl
 * @Author: wangpengfei144
 * @Date: 2024/11/6
 */
@Service
@Slf4j
public class PromiseGoRpcServiceImpl implements PromiseGoRpcService {

    /**
     * promisego
     */
    @Resource
    private PromisegoQueryExport promisegoQueryExport;

    /**
     * 查询履约时效
     *
     * @param userPromisegoRequestBo
     * @return
     */
    @Override
    @LogAndAlarm
    public UserPromisegoBo queryUserPromisego(UserPromisegoRequestBo userPromisegoRequestBo) {
        try {
            //转换入参
            UserPromisegoRequest userPromisegoRequest = PromisegoRpcConverter.INSTANCE.bo2Request(userPromisegoRequestBo);
            Response<UserPromisegoDto> userPromisegoDtoResponse = promisegoQueryExport.queryUserPromisego(userPromisegoRequest);
            log.info("PromiseGoRpcServiceImpl->queryUserPromisego,userPromisegoDtoResponse={}", JSON.toJSONString(userPromisegoDtoResponse));
            if (Objects.nonNull(userPromisegoDtoResponse) && userPromisegoDtoResponse.isSuccess()){
                //转换出参
                UserPromisegoBo userPromisegoBo = PromisegoRpcConverter.INSTANCE.dto2Bo(userPromisegoDtoResponse.getData());
                log.info("PromiseGoRpcServiceImpl->queryUserPromisego,userPromisegoBo={}", JSON.toJSONString(userPromisegoBo));
                return userPromisegoBo;
            }
            return null;
        }catch (Exception e){
            log.error("PromiseGoRpcServiceImpl->queryUserPromisego,error",e);
        }
        return null;
    }

    /**
     * 查询promisego信息 - 售前
     *
     * @param requestBo requestBo
     * @return {@link PreUserPromisegoBo }
     */
    @Override
    @LogAndAlarm
    public PreUserPromisegoBo queryPreUserPromisego(PreUserPromisegoRequestBo requestBo) {
        try {
            //转换入参
            PreUserPromisegoRequest preUserPromisegoRequest = PromisegoRpcConverter.INSTANCE.bo2Request(requestBo);
            log.info("PromiseGoRpcServiceImpl -> queryPreUserPromisego,preUserPromisegoRequest={}", JSON.toJSONString(preUserPromisegoRequest));
            Response<PreUserPromisegoDto> preUserPromisegoDtoResponse = promisegoQueryExport.queryPreUserPromisego(preUserPromisegoRequest);
            log.info("PromiseGoRpcServiceImpl -> queryPreUserPromisego,preUserPromisegoDtoResponse={}", JSON.toJSONString(preUserPromisegoDtoResponse));
            if(Objects.nonNull(preUserPromisegoDtoResponse) && preUserPromisegoDtoResponse.isSuccess()){
                PreUserPromisegoDto preUserPromisegoDto = preUserPromisegoDtoResponse.getData();
                ScriptDto script = preUserPromisegoDto.getScript();
                if(Objects.nonNull(script)){
                    //转换出参
                    PreUserPromisegoBo prePromisegoBo = PromisegoRpcConverter.INSTANCE.dto2Bo(preUserPromisegoDto);
                    log.info("PromiseGoRpcServiceImpl -> queryPreUserPromisego,prePromisegoBo={}", JSON.toJSONString(prePromisegoBo));
                    return prePromisegoBo;
                }
            }
            return null;
        } catch (Exception e) {
            log.error("PromiseGoRpcServiceImpl -> queryPreUserPromisego,error",e);
        }
        return null;
    }

    /**
     * 查询promisego信息 - 实验室
     *
     * @param requestBo 请求博
     * @return {@link LabPromisegoBo }
     */
    @Override
    public LabPromisegoBo queryLabPromisego(LabPromisegoRequestBo requestBo) {
        try {
            //转换入参
            LabPromisegoRequest labPromisegoRequest = PromisegoRpcConverter.INSTANCE.bo2LabRequest(requestBo);
            Response<LabPromisegoDto> labPromisegoDtoResponse = promisegoQueryExport.queryLabPromisego(labPromisegoRequest);
            log.info("PromiseGoRpcServiceImpl->queryLabPromisego,labPromisegoRequest={}, labPromisegoDtoResponse={}", JSON.toJSONString(labPromisegoRequest), JSON.toJSONString(labPromisegoDtoResponse));
            if (Objects.nonNull(labPromisegoDtoResponse) && labPromisegoDtoResponse.isSuccess()){
                //转换出参
                LabPromisegoBo userPromisegoBo = PromisegoRpcConverter.INSTANCE.dto2LabBo(labPromisegoDtoResponse.getData());
                log.info("PromiseGoRpcServiceImpl->queryLabPromisego,userPromisegoBo={}", JSON.toJSONString(userPromisegoBo));
                return userPromisegoBo;
            }
            return null;
        }catch (Exception e){
            log.error("PromiseGoRpcServiceImpl->queryLabPromisego,error",e);
        }
        return null;
    }

    @Override
    @LogAndAlarm
    public Map<Long, LabPromisegoBo> listLabPromisego(List<LabPromisegoRequestBo> params) {
        if (CollectionUtils.isEmpty(params)){
            return Collections.emptyMap();
        }

        List<LabPromisegoRequest> requests = Lists.newArrayList();
        for (LabPromisegoRequestBo param : params) {
            LabPromisegoRequest labPromisegoRequest = PromisegoRpcConverter.INSTANCE.bo2LabRequest(param);
            requests.add(labPromisegoRequest);

        }
        try {
            Response<Map<Long, LabPromisegoDto>> res = promisegoQueryExport.queryLabPromisegoBatch(requests);
            log.info("PromiseGoRpcServiceImpl->listLabPromisego,labPromisegoRequest={}, labPromisegoDtoResponse={}", JSON.toJSONString(requests), JSON.toJSONString(res));
            if (Objects.nonNull(res) && res.isSuccess()) {
                Map<Long, LabPromisegoDto> data = res.getData();
                if (MapUtils.isEmpty(data)) {
                    return Collections.emptyMap();
                }
                Map<Long, LabPromisegoBo> result = Maps.newHashMap();
                for (LabPromisegoDto value : data.values()) {
                    LabPromisegoBo userPromisegoBo = PromisegoRpcConverter.INSTANCE.dto2LabBo(value);
                    result.put(value.getMedicalPromiseId(), userPromisegoBo);
                }
                //转换出参
                return result;
            }
        }catch (Throwable e){
            log.error("PromiseGoRpcServiceImpl->listLabPromisego,error",e);
        }
        return Collections.emptyMap();
    }

}
