package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdh.o2oservice.base.mybatisplus.AcesCipherIndexHandler;
import com.jdh.o2oservice.base.mybatisplus.AcesCipherTextHandler;
import com.jdh.o2oservice.core.domain.support.basic.model.User;
import lombok.Data;

import java.util.Date;

/**
* @description jdh_promise_patient promise关联的服务履约人信息
* <AUTHOR>
* @date 2023-12-22 10:35:07
*/
@Data
@TableName(value = "jdh_promise_patient",autoResultMap = true)
public class JdhPromisePatientPo extends JdhBasicPo{

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 垂直业务身份编码
     */
    private String verticalCode;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 业务唯一id
     */
    private Long promisePatientId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 服务单ID
     */
    private Long voucherId;
    /**
     * 兑换id/orderId/outerOrderId
     */
    private String sourceVoucherId;
    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 婚否 0未婚 1已婚
     */
    private Integer marriage;

    /**
     * 用户性别 0未知 1男 2女
     */
    private Integer userGender;

    /**
     * 生日日期yyyy-MM-dd
     */
    private String birthday;

    /**
     * 亲属类型:0-父母 1-配偶 2-子女 3-其他
     */
    private Integer relativesType;

    /**
     * 预约人名称
     */
    @TableField(typeHandler = AcesCipherTextHandler.class)
    private String userName;

    /**
     * 预约人名称
     */
    @TableField(typeHandler = AcesCipherIndexHandler.class)
    private String userNameIndex;

    /**
     * 预约人电话
     */
    @TableField(typeHandler = AcesCipherTextHandler.class)
    private String userPhone;

    /**
     * 预约人电话
     */
    @TableField(typeHandler = AcesCipherIndexHandler.class)
    private String userPhoneIndex;

    /**
     * 证件类型 1-身份证 2-港澳通行证 3-台胞证 4-护照 5-军官证 6-警官证 7-其他证件
     */
    private Integer userCredentialType;

    /**
     * 预约证件编号
     */
    @TableField(typeHandler = AcesCipherTextHandler.class)
    private String userCredentialNo;

    /**
     * 预约证件编号
     */
    @TableField(typeHandler = AcesCipherIndexHandler.class)
    private String userCredentialNoIndex;

}