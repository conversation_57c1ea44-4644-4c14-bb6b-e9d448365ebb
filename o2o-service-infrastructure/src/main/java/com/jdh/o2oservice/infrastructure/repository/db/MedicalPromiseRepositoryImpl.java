package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.medpromise.bo.MedicalPromiseFreezeBO;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseIdentifier;
import com.jdh.o2oservice.core.domain.medpromise.model.UpdateMedicalPromiseStation;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseNestedQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.MedicalPromiseConvert;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhMedicalPromisePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhMedicalPromisePo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Description: 检测单仓储层Impl
 * @Author: wangpengfei144
 * @Date: 2024/4/16
 */
@Service
@Slf4j
public class MedicalPromiseRepositoryImpl implements MedicalPromiseRepository {

    /**
     * environment
     */
    @Value("${spring.profiles.active}")
    private String environment;

    /**
     * 检测单Mapper
     */
    @Resource
    private  JdhMedicalPromisePoMapper jdhMedicalPromisePoMapper;

    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * jimRedisService
     */
    @Resource
    private Cluster cluster;


    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param medicalPromiseIdentifier
     */
    @Override
    public MedicalPromise find(MedicalPromiseIdentifier medicalPromiseIdentifier) {
        LambdaQueryWrapper<JdhMedicalPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhMedicalPromisePo::getMedicalPromiseId,medicalPromiseIdentifier.getMedicalPromiseId());
        queryWrapper.eq(JdhMedicalPromisePo::getYn, YnStatusEnum.YES.getCode());
        JdhMedicalPromisePo po = jdhMedicalPromisePoMapper.selectOne(queryWrapper);
        return MedicalPromiseConvert.INSTANCE.convert(po);
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param aggregate
     */
    @Override
    public int remove(MedicalPromise aggregate) {
        return 0;
    }

    /**
     * 保存一个Aggregate
     * 保存后自动重置追踪条件
     *
     * @param aggregate
     */
    @Override
    public int save(MedicalPromise aggregate) {
        JdhMedicalPromisePo jdhMedicalPromisePo = MedicalPromiseConvert.INSTANCE.convert(aggregate);
        //新增
        if (Objects.isNull(aggregate.getId())) {
            JdhBasicPoConverter.initInsertBasicPo(jdhMedicalPromisePo);
            return jdhMedicalPromisePoMapper.insert(jdhMedicalPromisePo);
        }
        //修改
        Integer oldVersion = aggregate.getVersion();
        aggregate.versionIncrease();
        jdhMedicalPromisePo.setBranch(environment);
        jdhMedicalPromisePo.setUpdateTime(new Date());
        jdhMedicalPromisePo.setVersion(aggregate.getVersion());

        LambdaUpdateWrapper<JdhMedicalPromisePo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Objects.nonNull(aggregate.getStatus()), JdhMedicalPromisePo::getStatus, aggregate.getStatus())
                .set(StringUtils.isNotBlank(aggregate.getSpecimenCode()), JdhMedicalPromisePo::getSpecimenCode, aggregate.getSpecimenCode())
                .set(Objects.nonNull(aggregate.getProviderId()), JdhMedicalPromisePo::getProviderId, aggregate.getProviderId())
                .set(Objects.nonNull(aggregate.getStationId()), JdhMedicalPromisePo::getStationId, aggregate.getStationId())
                .set(StringUtils.isNotBlank(aggregate.getStationAddress()), JdhMedicalPromisePo::getStationAddress, aggregate.getStationAddress())
                .set(Objects.nonNull(aggregate.getFreeze()), JdhMedicalPromisePo::getFreeze, aggregate.getFreeze())
                .set(Objects.nonNull(aggregate.getCheckTime()), JdhMedicalPromisePo::getCheckTime, aggregate.getCheckTime())
                .set(Objects.nonNull(aggregate.getUpdateTime()), JdhMedicalPromisePo::getUpdateTime, aggregate.getUpdateTime())
                .set(Objects.nonNull(aggregate.getReportStatus()), JdhMedicalPromisePo::getReportStatus, aggregate.getReportStatus())
                .set(Objects.nonNull(aggregate.getReportTime()), JdhMedicalPromisePo::getReportTime, aggregate.getReportTime())
                .set(StringUtil.isNotBlank(aggregate.getStationName()), JdhMedicalPromisePo::getStationName, aggregate.getStationName())
                .set(StringUtil.isNotBlank(aggregate.getOuterId()), JdhMedicalPromisePo::getOuterId, aggregate.getOuterId())
                .set(StringUtil.isNotBlank(aggregate.getStationPhone()), JdhMedicalPromisePo::getStationPhone, aggregate.getStationPhone())
                .set(Objects.nonNull(aggregate.getVersion()), JdhMedicalPromisePo::getVersion, aggregate.getVersion())
                .set(Objects.nonNull(aggregate.getReportTime()), JdhMedicalPromisePo::getReportTime, aggregate.getReportTime())
                .set(Objects.nonNull(aggregate.getCheckStatus()), JdhMedicalPromisePo::getCheckStatus, aggregate.getCheckStatus())
                .set(StringUtil.isNotBlank(aggregate.getSerialNum()), JdhMedicalPromisePo::getSerialNum, aggregate.getSerialNum())
                .set(StringUtil.isNotBlank(aggregate.getAngelStationId()), JdhMedicalPromisePo::getAngelStationId, aggregate.getAngelStationId())
                .set(StringUtil.isNotBlank(aggregate.getAngelStationName()), JdhMedicalPromisePo::getAngelStationName, aggregate.getAngelStationName())
                //Eta相关数据
                .set(Objects.nonNull(aggregate.getWaitingTestTimeOutDate()),JdhMedicalPromisePo::getWaitingTestTimeOutDate, aggregate.getWaitingTestTimeOutDate())
                .set(Objects.nonNull(aggregate.getWaitingTestTimeOutStatus()),JdhMedicalPromisePo::getWaitingTestTimeOutStatus, aggregate.getWaitingTestTimeOutStatus())
                .set(Objects.nonNull(aggregate.getTestingTimeOutDate()),JdhMedicalPromisePo::getTestingTimeOutDate, aggregate.getTestingTimeOutDate())
                .set(Objects.nonNull(aggregate.getTestingTimeOutStatus()),JdhMedicalPromisePo::getTestingTimeOutStatus, aggregate.getTestingTimeOutStatus())
                .set(Objects.nonNull(aggregate.getIsTest()), JdhMedicalPromisePo::getIsTest, aggregate.getIsTest())
                .set(StringUtil.isNotBlank(aggregate.getDeliveryStepFlow()),JdhMedicalPromisePo::getDeliveryStepFlow,aggregate.getDeliveryStepFlow())
                .setSql("`update_time` = now()")
                .eq(JdhMedicalPromisePo::getVersion, oldVersion)
                .eq(JdhMedicalPromisePo::getMedicalPromiseId, aggregate.getMedicalPromiseId());
        return jdhMedicalPromisePoMapper.update(null, updateWrapper);
    }

    /**
     * findMedicalPromise
     *
     * @param query 查询
     * @return {@link MedicalPromise}
     */
    @Override
    public MedicalPromise findMedicalPromise(MedicalPromiseRepQuery query) {
        LambdaQueryWrapper<JdhMedicalPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(JdhMedicalPromisePo::getYn,CommonConstant.ONE)
                .eq(Objects.nonNull(query.getMedicalPromiseId()),JdhMedicalPromisePo::getMedicalPromiseId,query.getMedicalPromiseId())
                .eq(Objects.nonNull(query.getPromisePatientId()),JdhMedicalPromisePo::getPromisePatientId,query.getPromisePatientId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhMedicalPromisePo::getPromiseId,query.getPromiseId())
                .eq(Objects.nonNull(query.getServiceId()),JdhMedicalPromisePo::getServiceId,query.getServiceId())
                .eq(Objects.nonNull(query.getProviderId()),JdhMedicalPromisePo::getProviderId,query.getProviderId())
                .eq(StringUtils.isNotBlank(query.getSpecimenCode()),JdhMedicalPromisePo::getSpecimenCode,query.getSpecimenCode())
                .eq(StringUtils.isNotBlank(query.getStationId()),JdhMedicalPromisePo::getStationId,query.getStationId())
        ;
        JdhMedicalPromisePo po = jdhMedicalPromisePoMapper.selectOne(queryWrapper);
        return MedicalPromiseConvert.INSTANCE.convert(po);
    }

    /**
     * 分页查询
     *
     * @param medicalPromiseListQuery
     * @return
     */
    @Override
    public PageDto<MedicalPromise> queryMedicalPromisePage(MedicalPromiseListQuery medicalPromiseListQuery) {
        Page<JdhMedicalPromisePo> param = new Page<>(medicalPromiseListQuery.getPageNum(), medicalPromiseListQuery.getPageSize());
        LambdaQueryWrapper<JdhMedicalPromisePo> queryWrapper = Wrappers.lambdaQuery();

        boolean asc = StringUtil.equals("asc",medicalPromiseListQuery.getOrderBy());
        queryWrapper
                .eq(JdhMedicalPromisePo::getYn,CommonConstant.ONE)
                .notIn(JdhMedicalPromisePo::getStatus, Lists.newArrayList(MedicalPromiseStatusEnum.INVALID.getStatus()))
                .eq(StringUtil.isNotBlank(medicalPromiseListQuery.getFlag()), JdhMedicalPromisePo::getFlag, medicalPromiseListQuery.getFlag())
                .eq(Objects.nonNull(medicalPromiseListQuery.getMedicalPromiseId()), JdhMedicalPromisePo::getMedicalPromiseId, medicalPromiseListQuery.getMedicalPromiseId())
                .eq(Objects.nonNull(medicalPromiseListQuery.getPromiseId()), JdhMedicalPromisePo::getPromiseId, medicalPromiseListQuery.getPromiseId())
                .eq(Objects.nonNull(medicalPromiseListQuery.getStatus()), JdhMedicalPromisePo::getStatus, medicalPromiseListQuery.getStatus())
                .in(CollectionUtils.isNotEmpty(medicalPromiseListQuery.getServiceItemIds()), JdhMedicalPromisePo::getServiceItemId, medicalPromiseListQuery.getServiceItemIds())
                .orderBy(StringUtils.isNotBlank(medicalPromiseListQuery.getOrderBy()),asc,JdhMedicalPromisePo::getId);

        Page<JdhMedicalPromisePo> jdhMedicalPromisePoPage = jdhMedicalPromisePoMapper.selectPage(param, queryWrapper);
        return MedicalPromiseConvert.INSTANCE.convert(jdhMedicalPromisePoPage);
    }

    /**
     * 查询检测单 不分页
     *
     * @param medicalPromiseListQuery
     * @return
     */
    @Override
    public List<MedicalPromise> queryMedicalPromiseList(MedicalPromiseListQuery medicalPromiseListQuery) {
        LambdaQueryWrapper<JdhMedicalPromisePo> queryWrapper = Wrappers.lambdaQuery();
        boolean asc = StringUtil.equals("asc",medicalPromiseListQuery.getOrderBy());

        queryWrapper
                .eq(!Objects.equals(Boolean.TRUE,medicalPromiseListQuery.getAllQuery()),JdhMedicalPromisePo::getYn,CommonConstant.ONE)
                .notIn(Boolean.FALSE.equals(medicalPromiseListQuery.getInvalid()),JdhMedicalPromisePo::getStatus, Lists.newArrayList(MedicalPromiseStatusEnum.INVALID.getStatus()))
                .eq(Boolean.FALSE.equals(medicalPromiseListQuery.getFreezeQuery()),JdhMedicalPromisePo::getFreeze, CommonConstant.ZERO)
                .eq(Objects.nonNull(medicalPromiseListQuery.getMedicalPromiseId()), JdhMedicalPromisePo::getMedicalPromiseId, medicalPromiseListQuery.getMedicalPromiseId())
                .eq(Objects.nonNull(medicalPromiseListQuery.getPromiseId()), JdhMedicalPromisePo::getPromiseId, medicalPromiseListQuery.getPromiseId())
                .eq(Objects.nonNull(medicalPromiseListQuery.getStatus()), JdhMedicalPromisePo::getStatus, medicalPromiseListQuery.getStatus())
                .eq(Objects.nonNull(medicalPromiseListQuery.getReportStatus()), JdhMedicalPromisePo::getReportStatus, medicalPromiseListQuery.getReportStatus())
                .eq(StringUtils.isNotBlank(medicalPromiseListQuery.getServiceId()), JdhMedicalPromisePo::getServiceId, medicalPromiseListQuery.getServiceId())
                .eq(StringUtils.isNotBlank(medicalPromiseListQuery.getStationId()), JdhMedicalPromisePo::getStationId, medicalPromiseListQuery.getStationId())
                .eq(Objects.nonNull(medicalPromiseListQuery.getMergeMedicalId()), JdhMedicalPromisePo::getMergeMedicalId, medicalPromiseListQuery.getMergeMedicalId())
                .eq(StringUtil.isNotBlank(medicalPromiseListQuery.getFlag()), JdhMedicalPromisePo::getFlag, medicalPromiseListQuery.getFlag())
                .eq(Objects.nonNull(medicalPromiseListQuery.getPromisePatientId()), JdhMedicalPromisePo::getPromisePatientId, medicalPromiseListQuery.getPromisePatientId())
                .in(CollectionUtils.isNotEmpty(medicalPromiseListQuery.getPromisePatientIdList()),JdhMedicalPromisePo::getPromisePatientId,medicalPromiseListQuery.getPromisePatientIdList())
                .in(CollectionUtils.isNotEmpty(medicalPromiseListQuery.getSpecimenCodeList()), JdhMedicalPromisePo::getSpecimenCode, medicalPromiseListQuery.getSpecimenCodeList())
                .in(CollectionUtils.isNotEmpty(medicalPromiseListQuery.getPromiseIdList()), JdhMedicalPromisePo::getPromiseId, medicalPromiseListQuery.getPromiseIdList())
                .in(CollectionUtils.isNotEmpty(medicalPromiseListQuery.getMedicalPromiseIds()), JdhMedicalPromisePo::getMedicalPromiseId, medicalPromiseListQuery.getMedicalPromiseIds())
                .in(CollectionUtils.isNotEmpty(medicalPromiseListQuery.getVerticalCodeSet()), JdhMedicalPromisePo::getVerticalCode, medicalPromiseListQuery.getVerticalCodeSet())
                .gt(Objects.nonNull(medicalPromiseListQuery.getReportStartTime()),JdhMedicalPromisePo::getReportTime,medicalPromiseListQuery.getReportStartTime())
                .gt(Objects.nonNull(medicalPromiseListQuery.getStartDate()),JdhMedicalPromisePo::getCreateTime,medicalPromiseListQuery.getStartDate())
                .gt(Objects.nonNull(medicalPromiseListQuery.getCheckStartTime()),JdhMedicalPromisePo::getCheckTime,medicalPromiseListQuery.getCheckStartTime())
                .lt(Objects.nonNull(medicalPromiseListQuery.getReportEndTime()),JdhMedicalPromisePo::getReportTime,medicalPromiseListQuery.getReportEndTime())
                .lt(Objects.nonNull(medicalPromiseListQuery.getEndDate()),JdhMedicalPromisePo::getCreateTime,medicalPromiseListQuery.getEndDate())
                .lt(Objects.nonNull(medicalPromiseListQuery.getCheckEndTime()),JdhMedicalPromisePo::getCheckTime,medicalPromiseListQuery.getCheckEndTime())
                .eq(StringUtil.isNotBlank(medicalPromiseListQuery.getServiceItemId()),JdhMedicalPromisePo::getServiceItemId,medicalPromiseListQuery.getServiceItemId())
                .orderBy(StringUtils.isNotBlank(medicalPromiseListQuery.getOrderBy()),asc,JdhMedicalPromisePo::getId)
        ;

        if (CollectionUtils.isNotEmpty(medicalPromiseListQuery.getMedicalPromiseNestedQueries())) {
            int i = 0;
            for (MedicalPromiseNestedQuery query : medicalPromiseListQuery.getMedicalPromiseNestedQueries()) {
                queryWrapper.and(nestQuery -> nestQuery.eq(Objects.nonNull(query.getPromisePatientId()), JdhMedicalPromisePo::getPromisePatientId, query.getPromisePatientId())
                        .eq(Objects.nonNull(query.getServiceId()), JdhMedicalPromisePo::getServiceId, query.getServiceId()));
                if (i < medicalPromiseListQuery.getMedicalPromiseNestedQueries().size() - 1) {
                    queryWrapper.or();
                }
                i++;
            }
        }


        List<JdhMedicalPromisePo> jdhMedicalPromisePos = jdhMedicalPromisePoMapper.selectList(queryWrapper);
        log.info("MedicalPromiseRepositoryImpl->jdhMedicalPromisePos={}", JsonUtil.toJSONString(jdhMedicalPromisePos));
        return MedicalPromiseConvert.INSTANCE.convert(jdhMedicalPromisePos);
    }

    /**
     * 批量更新
     *
     * @param medicalPromises
     * @return
     */
    @Override
    public Integer updateFreezeBatch(List<MedicalPromise> medicalPromises) {
        List<JdhMedicalPromisePo> convert = MedicalPromiseConvert.INSTANCE.convertList(medicalPromises);
        MedicalPromiseFreezeBO medicalPromiseFreezeBO = new MedicalPromiseFreezeBO();
        medicalPromiseFreezeBO.setFreeze(convert.get(0).getFreeze());
        Set<Long> collect = convert.stream().map(JdhMedicalPromisePo::getMedicalPromiseId).collect(Collectors.toSet());
        medicalPromiseFreezeBO.setMedicalPromiseIds(collect);
        return jdhMedicalPromisePoMapper.updateBatchFreeze(medicalPromiseFreezeBO);
    }

    /**
     * 批量更新
     *
     * @param medicalPromise
     * @return
     */
    @Override
    public Integer updateSettleSatus(MedicalPromise medicalPromise) {
        LambdaUpdateWrapper<JdhMedicalPromisePo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Objects.nonNull(medicalPromise.getSettleStatus()), JdhMedicalPromisePo::getSettleStatus, medicalPromise.getSettleStatus())
                .set(JdhMedicalPromisePo::getUpdateTime, new Date())
                .eq(Objects.nonNull(medicalPromise.getServiceId()), JdhMedicalPromisePo::getServiceId, medicalPromise.getServiceId())
                .eq(Objects.nonNull(medicalPromise.getPromisePatientId()), JdhMedicalPromisePo::getPromisePatientId, medicalPromise.getPromisePatientId())
                .in(CollectionUtils.isNotEmpty(medicalPromise.getMedicalPromiseIds()), JdhMedicalPromisePo::getMedicalPromiseId, medicalPromise.getMedicalPromiseIds());
        return jdhMedicalPromisePoMapper.update(null, updateWrapper);
    }

    /**
     * 批量更新
     *
     * @param medicalPromises
     * @return
     */
    @Override
    public Integer updateBatch(List<MedicalPromise> medicalPromises) {
        List<JdhMedicalPromisePo> convert = MedicalPromiseConvert.INSTANCE.convertList(medicalPromises);
        return jdhMedicalPromisePoMapper.updateBatch(convert);
    }

    /**
     * @param medicalPromises
     * @return
     */
    @Override
    public Integer updateStoreDispatch(List<MedicalPromise> medicalPromises) {
        AtomicReference<Integer> count = new AtomicReference<>(CommonConstant.ZERO);
        medicalPromises.forEach(medicalPromise -> {
            int save = save(medicalPromise);
            count.set(count.get() + save);
            if (save<=0){
                throw new BusinessException(MedPromiseErrorCode.UPDATE_MEDICAL_PROMISE_LOCK_ERROR);
            }
        });
        return count.get();
    }

    @Override
    public List<MedicalPromise> listByPatientAndService(MedicalPromiseListQuery medicalPromiseListQuery) {

        List<JdhMedicalPromisePo> jdhMedicalPromisePos = jdhMedicalPromisePoMapper.listByPatients(medicalPromiseListQuery);
        log.info("MedicalPromiseRepositoryImpl->jdhMedicalPromisePos={}", JsonUtil.toJSONString(jdhMedicalPromisePos));
        return MedicalPromiseConvert.INSTANCE.convert(jdhMedicalPromisePos);
    }

    /**
     * 更新序号
     *
     * @param medicalPromise
     * @return
     */
    @Override
    public Boolean updateSerialNum(MedicalPromise medicalPromise) {
        LambdaUpdateWrapper<JdhMedicalPromisePo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper
                .set(JdhMedicalPromisePo::getSerialNum,medicalPromise.getSerialNum())
                .set(JdhMedicalPromisePo::getUpdateTime, new Date())
                .eq(JdhMedicalPromisePo::getMedicalPromiseId,medicalPromise.getMedicalPromiseId());
        jdhMedicalPromisePoMapper.update(null,updateWrapper);
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateInit(MedicalPromise medicalPromise) {
        LambdaUpdateWrapper<JdhMedicalPromisePo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper
                .set(Objects.nonNull(medicalPromise.getCheckStatus()),JdhMedicalPromisePo::getCheckStatus,medicalPromise.getCheckStatus())
                .set(Objects.nonNull(medicalPromise.getReportTime()),JdhMedicalPromisePo::getReportTime,medicalPromise.getReportTime())
                .set(JdhMedicalPromisePo::getUpdateTime, new Date())
                .eq(JdhMedicalPromisePo::getMedicalPromiseId,medicalPromise.getMedicalPromiseId())
        ;
        return jdhMedicalPromisePoMapper.update(null,updateWrapper)>0;
    }

    /**
     * 获取序号
     * @param medicalPromiseId checkDate
     * @return
     */
    @Override
    public String generalSerialNum(Long medicalPromiseId,Date checkDate){
        log.info("MedicalPromiseRepositoryImpl->generalSerialNum={},{}",medicalPromiseId.toString(),checkDate);
        MedicalPromise medicalPromise = findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(medicalPromiseId).build());
        log.info("MedicalPromiseRepositoryImpl->medicalPromise={}",JsonUtil.toJSONString(medicalPromise));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(medicalPromise.getSerialNum())){
            return medicalPromise.getSerialNum();
        }
        if (Objects.isNull(checkDate)){
            throw new BusinessException(MedPromiseErrorCode.PARAM_NULL);
        }
        String checkDateStr = DateUtil.format(checkDate, CommonConstant.YMD);
        String stationSerialNumKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.MEDICAL_PROMISE_SERIAL_KEY,medicalPromise.getStationId(),checkDateStr);
        Long incr = cluster.incr(stationSerialNumKey);
        log.info("MedicalPromiseRepositoryImpl->incr={}",incr);
        cluster.expireAt(stationSerialNumKey,DateUtil.endOfDay(checkDate));
        return String.valueOf(incr);

    }

    /**
     * 根据promisePatientId和sergiceItemId来新增或者更新MedicalPromise信息，返回medicalPromiseId
     * @param aggregate
     * @return
     */
    @Override
    public Long insertOrUpdateByPromisePatientAndServiceItem(MedicalPromise aggregate) {
        JdhMedicalPromisePo jdhMedicalPromisePo = MedicalPromiseConvert.INSTANCE.convert(aggregate);
        LambdaUpdateWrapper<JdhMedicalPromisePo> medicalPromiseWrapper = Wrappers.lambdaUpdate();
        medicalPromiseWrapper
                .eq(JdhMedicalPromisePo::getPromisePatientId, jdhMedicalPromisePo.getPromisePatientId())
                .eq(Objects.nonNull(jdhMedicalPromisePo.getServiceItemId()), JdhMedicalPromisePo::getServiceItemId, jdhMedicalPromisePo.getServiceItemId());
        JdhMedicalPromisePo jdhMedicalPromisePoFromDb = jdhMedicalPromisePoMapper.selectOne(medicalPromiseWrapper);
        //新增
        if (Objects.isNull(jdhMedicalPromisePoFromDb)) {
            jdhMedicalPromisePo.setMedicalPromiseId(generateIdFactory.getId());
            JdhBasicPoConverter.initInsertBasicPo(jdhMedicalPromisePo);
            jdhMedicalPromisePoMapper.insert(jdhMedicalPromisePo);
            return jdhMedicalPromisePo.getMedicalPromiseId();
        }
        //修改
        medicalPromiseWrapper.setSql("`version` = `version` + 1")
                .set(JdhMedicalPromisePo::getUpdateTime, new Date());
        jdhMedicalPromisePo.setMedicalPromiseId(jdhMedicalPromisePoFromDb.getMedicalPromiseId());
        if(jdhMedicalPromisePo.getStatus() == 4 && Objects.nonNull(jdhMedicalPromisePo.getCheckTime())){
            jdhMedicalPromisePo.setSerialNum(generalSerialNum(jdhMedicalPromisePo.getMedicalPromiseId(), jdhMedicalPromisePo.getCheckTime()));
        }
        jdhMedicalPromisePoMapper.update(jdhMedicalPromisePo, medicalPromiseWrapper);
        return jdhMedicalPromisePo.getMedicalPromiseId();
    }

    /**
     * 根据ID删除检测单
     *
     * @param medicalPromiseIds
     * @return
     */
    @Override
    public Integer deleteByMedicalPromiseIds(List<Long> medicalPromiseIds,Long mergeId) {
        LambdaUpdateWrapper<JdhMedicalPromisePo> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper
                .in(JdhMedicalPromisePo::getMedicalPromiseId,medicalPromiseIds)
                .set(JdhMedicalPromisePo::getYn,YnStatusEnum.NO.getCode())
                .set(JdhMedicalPromisePo::getUpdateTime, new Date())
                .set(Objects.nonNull(mergeId),JdhMedicalPromisePo::getMergeMedicalId,mergeId);
        return jdhMedicalPromisePoMapper.update(null,lambdaUpdateWrapper);
    }

    /**
     * 更新检测单实验室信息
     * @param updateMedicalPromiseStation
     * @return
     */
    @LogAndAlarm
    @Override
    public Boolean updateMedicalPromiseStation(UpdateMedicalPromiseStation updateMedicalPromiseStation) {
        LambdaUpdateWrapper<JdhMedicalPromisePo> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.in(JdhMedicalPromisePo::getMedicalPromiseId,updateMedicalPromiseStation.getMedPromiseIds());
        JdhMedicalPromisePo jdhMedicalPromisePo = JSON.parseObject(JSON.toJSONString(updateMedicalPromiseStation),JdhMedicalPromisePo.class);
        jdhMedicalPromisePo.setUpdateTime(new Date());
        return jdhMedicalPromisePoMapper.update(jdhMedicalPromisePo,lambdaUpdateWrapper)>0;
    }

    /**
     * 查询检测单 不分页
     *
     * @param medicalPromiseListQuery
     * @return
     */
    @Override
    public List<MedicalPromise> queryMedicalPromiseNoSettleList(MedicalPromiseListQuery medicalPromiseListQuery) {
        LambdaQueryWrapper<JdhMedicalPromisePo> queryWrapper = Wrappers.lambdaQuery();
        boolean asc = StringUtil.equals("asc",medicalPromiseListQuery.getOrderBy());
        queryWrapper.in(CollectionUtils.isNotEmpty(medicalPromiseListQuery.getVerticalCodeSet()), JdhMedicalPromisePo::getVerticalCode, medicalPromiseListQuery.getVerticalCodeSet())
                .eq(JdhMedicalPromisePo::getYn,CommonConstant.ONE)
                .eq(Objects.nonNull(medicalPromiseListQuery.getStatus()), JdhMedicalPromisePo::getStatus, medicalPromiseListQuery.getStatus())
                .eq(JdhMedicalPromisePo::getSettleStatus, CommonConstant.ZERO)
                .gt(Objects.nonNull(medicalPromiseListQuery.getStartDate()),JdhMedicalPromisePo::getUpdateTime,medicalPromiseListQuery.getStartDate())
                .orderBy(StringUtils.isNotBlank(medicalPromiseListQuery.getOrderBy()),asc,JdhMedicalPromisePo::getId)
        ;
        List<JdhMedicalPromisePo> jdhMedicalPromisePos = jdhMedicalPromisePoMapper.selectList(queryWrapper);
        log.info("MedicalPromiseRepositoryImpl->jdhMedicalPromisePos size={}", jdhMedicalPromisePos.size());
        return MedicalPromiseConvert.INSTANCE.convert(jdhMedicalPromisePos);
    }

    @Override
    public Boolean existSpecimenCode(List<String> specimenCodes) {
        if (CollectionUtils.isEmpty(specimenCodes)) {
            return false;
        }
        LambdaQueryWrapper<JdhMedicalPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(JdhMedicalPromisePo::getSpecimenCode,specimenCodes)
                .eq(JdhMedicalPromisePo::getYn, YnStatusEnum.YES.getCode());

        return jdhMedicalPromisePoMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 查询总量
     *
     * @param medicalPromiseListQuery
     * @return
     */
    @Override
    public Integer count(MedicalPromiseListQuery medicalPromiseListQuery) {
        LambdaQueryWrapper<JdhMedicalPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhMedicalPromisePo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.eq(JdhMedicalPromisePo::getVoucherId, medicalPromiseListQuery.getVoucherId());
        return jdhMedicalPromisePoMapper.selectCount(queryWrapper);
    }

}
