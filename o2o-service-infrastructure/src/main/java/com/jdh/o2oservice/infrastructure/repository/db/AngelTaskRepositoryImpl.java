package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jd.common.util.StringUtils;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTaskIdentifier;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelTaskBizStatusDbCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelTaskStartEndDateCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.cmd.AngelTaskStatusCmd;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelTaskRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelTaskDBQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhAngelTaskPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhAngelWorkPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelTaskPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelTaskPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @author:lichen55
 * @createTime: 2024-04-17 16:06
 * @Description:
 */
@Component
@Slf4j
public class AngelTaskRepositoryImpl implements AngelTaskRepository {
    /** */
    @Resource
    private JdhAngelTaskPoMapper jdhAngelTaskPoMapper;


    @Override
    public AngelTask find(AngelTaskIdentifier angelTaskIdentifier) {
        LambdaQueryWrapper<JdhAngelTaskPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhAngelTaskPo::getTaskId, angelTaskIdentifier.getTaskId())
                .eq(JdhAngelTaskPo::getYn, YnStatusEnum.YES.getCode());
        JdhAngelTaskPo jdhAngelTaskPo = jdhAngelTaskPoMapper.selectOne(queryWrapper);
        AngelTask angelTask = JdhAngelTaskPoConverter.INSTANCE.convertToAngelTask(jdhAngelTaskPo);
        return angelTask;
    }

    @Override
    public int remove(AngelTask aggregate) {
        LambdaUpdateWrapper<JdhAngelTaskPo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(JdhAngelTaskPo::getTaskId, aggregate.getTaskId());
        return jdhAngelTaskPoMapper.delete(wrapper);
    }

    @Override
    public int save(AngelTask aggregate) {

        JdhAngelTaskPo taskPo = JdhAngelTaskPoConverter.INSTANCE.convertToPo(aggregate);
        aggregate.versionIncrease();
        LambdaUpdateWrapper<JdhAngelTaskPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhAngelTaskPo::getVersion, aggregate.getVersion())
                .set(Objects.nonNull(taskPo.getTaskStatus()), JdhAngelTaskPo::getTaskStatus, taskPo.getTaskStatus())
                .set(Objects.nonNull(taskPo.getStopStatus()), JdhAngelTaskPo::getStopStatus, taskPo.getStopStatus())
                .set(Objects.nonNull(taskPo.getBizExtStatus()), JdhAngelTaskPo::getBizExtStatus,taskPo.getBizExtStatus())
                .set(StringUtils.isNotBlank(taskPo.getExtend()), JdhAngelTaskPo::getExtend, taskPo.getExtend())
                .set(Objects.nonNull(taskPo.getTaskStartTime()), JdhAngelTaskPo::getTaskStartTime, taskPo.getTaskStartTime())
                .set(Objects.nonNull(taskPo.getTaskEndTime()), JdhAngelTaskPo::getTaskEndTime, taskPo.getTaskEndTime())
                .eq(JdhAngelTaskPo::getTaskId, taskPo.getTaskId())
                .eq(JdhAngelTaskPo::getVersion, taskPo.getVersion());
        return jdhAngelTaskPoMapper.update(taskPo, updateWrapper);
    }

    @Override
    public List<AngelTask> findList(AngelTaskDBQuery angelTaskDBQuery) {
        LambdaQueryWrapper<JdhAngelTaskPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollectionUtils.isNotEmpty(angelTaskDBQuery.getTaskIds()), JdhAngelTaskPo::getTaskId, angelTaskDBQuery.getTaskIds())
                .in(CollectionUtils.isNotEmpty(angelTaskDBQuery.getTaskStatusSet()), JdhAngelTaskPo::getTaskStatus, angelTaskDBQuery.getTaskStatusSet())
                .notIn(CollectionUtils.isNotEmpty(angelTaskDBQuery.getNotInTaskIds()), JdhAngelTaskPo::getTaskId, angelTaskDBQuery.getNotInTaskIds())
                .eq(Objects.nonNull(angelTaskDBQuery.getWorkId()), JdhAngelTaskPo::getWorkId, angelTaskDBQuery.getWorkId())
                .eq(Objects.nonNull(angelTaskDBQuery.getStatus()), JdhAngelTaskPo::getTaskStatus, angelTaskDBQuery.getStatus())
                .eq(Objects.nonNull(angelTaskDBQuery.getPatientId()), JdhAngelTaskPo::getPatientId, angelTaskDBQuery.getPatientId())
                .eq(JdhAngelTaskPo::getYn, YnStatusEnum.YES.getCode())
            .in(CollectionUtils.isNotEmpty(angelTaskDBQuery.getWorkIds()),  JdhAngelTaskPo::getWorkId, angelTaskDBQuery.getWorkIds())
        ;
        List<JdhAngelTaskPo> jdhAngelTaskPos = jdhAngelTaskPoMapper.selectList(queryWrapper);

        List<AngelTask> angelTasks = JdhAngelTaskPoConverter.INSTANCE.convertToAngelTaskList(jdhAngelTaskPos);
        return angelTasks;
    }

    /**
     * 批量写入工单任务
     *
     * @param angelTaskList
     * @return
     */
    @Override
    public int batchSave(List<AngelTask> angelTaskList) {
        if(CollectionUtils.isEmpty(angelTaskList)){
            return CommonConstant.ZERO;
        }
        AtomicInteger retNum = new AtomicInteger();
        List<JdhAngelTaskPo> jdhAngelTaskPos = JdhAngelTaskPoConverter.INSTANCE.convertToPoList(angelTaskList);
        jdhAngelTaskPos.stream().forEach(task -> {
            if(Objects.isNull(task.getId())){
                retNum.addAndGet(jdhAngelTaskPoMapper.insert(task));
            }else {
                LambdaUpdateWrapper<JdhAngelTaskPo> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(StringUtils.isNotBlank(task.getUpdater()), JdhAngelTaskPo::getUpdater, task.getUpdater())
                        .set(Objects.nonNull(task.getStopStatus()), JdhAngelTaskPo::getStopStatus, task.getStopStatus())
                        .set(Objects.nonNull(task.getUpdateTime()), JdhAngelTaskPo::getUpdateTime, task.getUpdateTime())
                        .set(StringUtils.isNotBlank(task.getExtend()), JdhAngelTaskPo::getExtend, task.getExtend())
                        .set(Objects.nonNull(task.getPatientAddressLat()), JdhAngelTaskPo::getPatientAddressLat, task.getPatientAddressLat())
                        .set(Objects.nonNull(task.getPatientAddressLng()), JdhAngelTaskPo::getPatientAddressLng, task.getPatientAddressLng())
                        .set(StringUtils.isNotBlank(task.getPatientFullAddress()), JdhAngelTaskPo::getPatientFullAddress, task.getPatientFullAddress())
                        .set(StringUtils.isNotBlank(task.getPatientId()), JdhAngelTaskPo::getPatientId, task.getPatientId())
                        .set(Objects.nonNull(task.getTaskEndTime()), JdhAngelTaskPo::getTaskEndTime, task.getTaskEndTime())
                        .set(Objects.nonNull(task.getTaskStartTime()), JdhAngelTaskPo::getTaskStartTime, task.getTaskStartTime())
                        .set(JdhAngelTaskPo::getVersion, task.getVersion()+1)
                        .set(Objects.nonNull(task.getYn()), JdhAngelTaskPo::getYn, task.getYn())
                        .eq(Objects.nonNull(task.getTaskId()), JdhAngelTaskPo::getTaskId, task.getTaskId())
                        .eq(JdhAngelTaskPo::getWorkId, task.getWorkId())
                        .eq(JdhAngelTaskPo::getVersion, task.getVersion());

                retNum.addAndGet(jdhAngelTaskPoMapper.update(null, updateWrapper));
            }
        });
        return retNum.get();
    }

    /**
     * @param taskStatusCmdList
     * @return
     */
    @Override
    public int updateStatus(List<AngelTaskStatusCmd> taskStatusCmdList) {
        AtomicInteger retNum = new AtomicInteger();
        taskStatusCmdList.stream().forEach(task -> {
            LambdaUpdateWrapper<JdhAngelTaskPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(Objects.nonNull(task.getTaskStatus()), JdhAngelTaskPo::getTaskStatus, task.getTaskStatus())
                    .set(Objects.nonNull(task.getStopStatus()), JdhAngelTaskPo::getStopStatus, task.getStopStatus())
                    .set(StringUtils.isNotBlank(task.getOperator()), JdhAngelTaskPo::getUpdater, task.getOperator())
                    .setSql("`update_time` = now()")
//                    .setSql("`version` = `version` + 1")
                    .eq(JdhAngelTaskPo::getTaskId, task.getTaskId());
//                    .eq(JdhAngelTaskPo::getVersion, task.getVersion());
            retNum.addAndGet(jdhAngelTaskPoMapper.update(null, updateWrapper));
        });
        return retNum.get();
    }

    /**
     * 更新任务单业务状态
     *
     * @param bizStatusDbCmdList
     */
    @Override
    public int updateExtStatus(List<AngelTaskBizStatusDbCmd> bizStatusDbCmdList) {
        AtomicInteger retNum = new AtomicInteger();
        bizStatusDbCmdList.stream().forEach(task -> {
            LambdaUpdateWrapper<JdhAngelTaskPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(Objects.nonNull(task.getNewStatus()), JdhAngelTaskPo::getBizExtStatus, task.getNewStatus())
                    .set(StringUtils.isNotBlank(task.getOperator()), JdhAngelTaskPo::getUpdater, task.getOperator())
                    .setSql("`version` = `version` + 1")
                    .setSql("`update_time` = now()")
                    .eq(JdhAngelTaskPo::getTaskId, task.getTaskId());
            retNum.addAndGet(jdhAngelTaskPoMapper.update(null, updateWrapper));
        });
        return retNum.get();
    }

    /**
     * 更新服务时间
     *
     * @param angelTaskStartEndDateCmd
     * @return
     */
    @Override
    public int modifyServiceDate(AngelTaskStartEndDateCmd angelTaskStartEndDateCmd) {
        if (angelTaskStartEndDateCmd == null || angelTaskStartEndDateCmd.getWorkId() == null
                || angelTaskStartEndDateCmd.getTaskStartTime() ==  null || angelTaskStartEndDateCmd.getTaskEndTime() == null) {
            return 0;
        }
        LambdaUpdateWrapper<JdhAngelTaskPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(JdhAngelTaskPo::getTaskStartTime, angelTaskStartEndDateCmd.getTaskStartTime())
                .set(JdhAngelTaskPo::getTaskEndTime, angelTaskStartEndDateCmd.getTaskEndTime())
                .setSql("`version` = `version` + 1")
                .setSql("`update_time` = now()")
                .eq(JdhAngelTaskPo::getWorkId, angelTaskStartEndDateCmd.getWorkId());
        return jdhAngelTaskPoMapper.update(null, updateWrapper);
    }

    /**
     * 更新任务单状态
     *
     * @param taskStatusCmd taskStatusCmd
     * @return
     */
    @Override
    public int updateStatusByWorkId(AngelTaskStatusCmd taskStatusCmd) {
        LambdaUpdateWrapper<JdhAngelTaskPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Objects.nonNull(taskStatusCmd.getTaskStatus()), JdhAngelTaskPo::getTaskStatus, taskStatusCmd.getTaskStatus())
                .set(Objects.nonNull(taskStatusCmd.getBizExtStatus()), JdhAngelTaskPo::getBizExtStatus, taskStatusCmd.getBizExtStatus())
                .set(Objects.nonNull(taskStatusCmd.getStopStatus()), JdhAngelTaskPo::getStopStatus, taskStatusCmd.getStopStatus())
                .set(StringUtils.isNotBlank(taskStatusCmd.getOperator()), JdhAngelTaskPo::getUpdater, taskStatusCmd.getOperator())
                .setSql("`update_time` = now()")
                .setSql("`version` = `version` + 1")
                .eq(JdhAngelTaskPo::getWorkId, taskStatusCmd.getWorkId());
        return jdhAngelTaskPoMapper.update(null, updateWrapper);
    }
}
