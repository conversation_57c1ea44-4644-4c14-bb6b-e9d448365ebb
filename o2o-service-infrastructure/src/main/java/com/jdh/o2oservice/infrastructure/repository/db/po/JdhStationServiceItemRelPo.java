package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <pre>
 *  门店项目关系表
 * </pre>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "jdh_station_service_item_rel", autoResultMap = true)
public class JdhStationServiceItemRelPo extends JdhBasicPo {

    /**
     * <pre>
     * 主键
     * </pre>
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * <pre>
     * 京东门店id
     * </pre>
     */
    private String stationId;

    /**
     * 门店所在省份
     */
    private Integer provinceId;

    /**
     * 门店所在城市
     */
    private Integer cityId;
    
    /**
     * <pre>
     * 项目id
     * </pre>
     */
    private Long serviceItemId;
    
    /**
     * <pre>
     * 实验室结算价格,单位:元
     * </pre>
     */
    private BigDecimal settlementPrice;
    
    /**
     * <pre>
     * 耗材包id
     * </pre>
     */
    private Long materialPackageId;
    
    /**
     * <pre>
     * 采样方式
     * </pre>
     */
    private String specimenWay;
    
    /**
     * <pre>
     * 样本保存时长,单位小时
     * </pre>
     */
    private Integer specimenPreserveDuration;
    
    /**
     * <pre>
     * 样本量,数值及单位
     * </pre>
     */
    private String specimenNum;
    
    /**
     * <pre>
     * 服务时长,单位分钟
     * </pre>
     */
    private Integer serviceDuration;
    
    /**
     * <pre>
     * 样本存放要求
     * </pre>
     */
    private String specimenPreserveCondition;
    
    /**
     * <pre>
     * 检测时长,单位分钟
     * </pre>
     */
    private Integer testDuration;
    
    /**
     * <pre>
     * 服务要求
     * </pre>
     */
    private String serviceCondition;

    /**
     * 上下架状态
     */
    private Integer onOffShelf;
    
    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;
    
    /**
     * <pre>
     * 版本号
     * </pre>
     */
    private Integer version;
    
    /**
     * <pre>
     * 是否有效 0-无效 1-有效
     * </pre>
     */
    private Integer yn;
    
    /**
     * <pre>
     * 创建人
     * </pre>
     */
    private String createUser;
    
    /**
     * <pre>
     * 更新人
     * </pre>
     */
    private String updateUser;
    
    /**
     * <pre>
     * 创建时间
     * </pre>
     */
    private Date createTime;
    
    /**
     * <pre>
     * 更新时间
     * </pre>
     */
    private Date updateTime;
}