package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.core.domain.dispatch.model.*;
import com.jdh.o2oservice.core.domain.dispatch.repository.db.DispatchTeamRepository;
import com.jdh.o2oservice.core.domain.dispatch.repository.query.DispatchTeamRepQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhDispatchTeamInfrastructureConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhDispatchTeamAngelRelPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhDispatchTeamPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhDispatchTeamSkillRelPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName DispatchTeamRepositoryImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 15:11
 **/
@Component
@Slf4j
public class DispatchTeamRepositoryImpl implements DispatchTeamRepository {

    /**
     * environment
     */
    @Value("${spring.profiles.active}")
    private String environment;

    /**
     * jdhDispatchTeamPoMapper
     */
    @Resource
    private JdhDispatchTeamPoMapper jdhDispatchTeamPoMapper;

    /**
     * jdhDispatchTeamSkillRelPoMapper
     */
    @Resource
    private JdhDispatchTeamSkillRelPoMapper jdhDispatchTeamSkillRelPoMapper;

    /**
     * 小队护士关系表
     */
    @Resource
    private JdhDispatchTeamAngelRelPoMapper jdhDispatchTeamAngelRelPoMapper;


    /**
     *
     * @param query 查询
     * @return
     */
    @Override
    public Page<JdhDispatchTeam> findDispatchTeamPage(DispatchTeamRepQuery query) {
        Page<JdhDispatchTeamPo> param = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<JdhDispatchTeamPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getDispatchTeamId()),JdhDispatchTeamPo::getDispatchTeamId, query.getDispatchTeamId())
                .like(StringUtils.isNotBlank(query.getFuzzyDispatchTeamName()), JdhDispatchTeamPo::getDispatchTeamName, query.getFuzzyDispatchTeamName())
                .in(CollectionUtils.isNotEmpty(query.getDispatchTeamStatusList()), JdhDispatchTeamPo::getDispatchTeamStatus, query.getDispatchTeamStatusList())
                .eq(JdhDispatchTeamPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByAsc(JdhDispatchTeamPo::getCreateTime);
        IPage<JdhDispatchTeamPo> page = jdhDispatchTeamPoMapper.selectPage(param, queryWrapper);
        if (page == null || CollectionUtil.isEmpty(page.getRecords())){
            return null;
        }
        List<JdhDispatchTeam> list = JdhDispatchTeamInfrastructureConverter.INSTANCE.po2Entity(page.getRecords());
        Map<Long, JdhDispatchTeam> dispatchTeamMap = null;
        //查询小队技能
        if (Objects.equals(query.getQueryTeamSkill(), true)) {
            dispatchTeamMap = list.stream().collect(Collectors.toMap(JdhDispatchTeam::getDispatchTeamId, dispatchTeam -> dispatchTeam, (t, t2) -> t2));
            List<JdhDispatchTeamSkillRel> dispatchTeamSkillList = findDispatchTeamSkillList(Lists.newArrayList(dispatchTeamMap.keySet()));
            Map<Long, List<JdhDispatchTeamSkillRel>> listMap = dispatchTeamSkillList.stream().filter(teamSkillRel -> Objects.nonNull(teamSkillRel.getDispatchTeamId())).collect(Collectors.groupingBy(JdhDispatchTeamSkillRel::getDispatchTeamId));
            for (Map.Entry<Long, List<JdhDispatchTeamSkillRel>> entry : listMap.entrySet()) {
                if (dispatchTeamMap.containsKey(entry.getKey())) {
                    dispatchTeamMap.get(entry.getKey()).setTeamSkillRelList(entry.getValue());
                }
            }
        }
        //查询小队服务者
        if (Objects.equals(query.getQueryTeamAngel(), true)) {
            dispatchTeamMap = MapUtils.isEmpty(dispatchTeamMap) ? list.stream().collect(Collectors.toMap(JdhDispatchTeam::getDispatchTeamId, dispatchTeam -> dispatchTeam, (t, t2) -> t2)) : dispatchTeamMap;
            List<JdhDispatchTeamAngelRel> dispatchTeamAngelList = findDispatchTeamAngelList(Lists.newArrayList(dispatchTeamMap.keySet()));
            Map<Long, List<JdhDispatchTeamAngelRel>> listMap = dispatchTeamAngelList.stream().filter(teamAngelRel -> Objects.nonNull(teamAngelRel.getDispatchTeamId())).collect(Collectors.groupingBy(JdhDispatchTeamAngelRel::getDispatchTeamId));
            for (Map.Entry<Long, List<JdhDispatchTeamAngelRel>> entry : listMap.entrySet()) {
                if (dispatchTeamMap.containsKey(entry.getKey())) {
                    dispatchTeamMap.get(entry.getKey()).setTeamAngelRelList(entry.getValue());
                }
            }
        }
        return JdhBasicPoConverter.initPage(page, list);
    }

    /**
     * 删除小队技能
     * @param dispatchTeamSkillRel
     * @return
     */
    @Override
    public int deleteDispatchTeamSkillRel(JdhDispatchTeamSkillRel dispatchTeamSkillRel) {
        //先删除再新增
        LambdaUpdateWrapper<JdhDispatchTeamSkillRelPo> updateTeamSkillWrapper = Wrappers.lambdaUpdate();
        updateTeamSkillWrapper.setSql("`version` = `version` + 1").set(JdhDispatchTeamSkillRelPo::getYn, YnStatusEnum.NO.getCode())
                .eq(JdhDispatchTeamSkillRelPo::getYn,YnStatusEnum.YES.getCode())
                .eq(JdhDispatchTeamSkillRelPo::getDispatchTeamId, dispatchTeamSkillRel.getDispatchTeamId())
                .eq(JdhDispatchTeamSkillRelPo::getAngelSkillCode, dispatchTeamSkillRel.getAngelSkillCode());
        return jdhDispatchTeamSkillRelPoMapper.update(null, updateTeamSkillWrapper);
    }

    /**
     * 删除小队服务者
     * @param dispatchTeamAngelRel
     * @return
     */
    @Override
    public int deleteDispatchTeamAngelRel(JdhDispatchTeamAngelRel dispatchTeamAngelRel) {
        //先删除再新增
        LambdaUpdateWrapper<JdhDispatchTeamAngelRelPo> updateTeamAngelWrapper = Wrappers.lambdaUpdate();
        updateTeamAngelWrapper.setSql("`version` = `version` + 1").set(JdhDispatchTeamAngelRelPo::getYn, YnStatusEnum.NO.getCode())
                .eq(JdhDispatchTeamAngelRelPo::getYn,YnStatusEnum.YES.getCode())
                .eq(JdhDispatchTeamAngelRelPo::getDispatchTeamId, dispatchTeamAngelRel.getDispatchTeamId())
                .eq(JdhDispatchTeamAngelRelPo::getAngelId, dispatchTeamAngelRel.getAngelId());
        return jdhDispatchTeamAngelRelPoMapper.update(null, updateTeamAngelWrapper);
    }

    /**
     *
     * @param identifier
     * @return
     */
    @Override
    public JdhDispatchTeam find(JdhDispatchTeamIdentifier identifier) {
        LambdaQueryWrapper<JdhDispatchTeamPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhDispatchTeamPo::getDispatchTeamId, identifier.getDispatchTeamId());
        queryWrapper.eq(JdhDispatchTeamPo::getYn, YnStatusEnum.YES.getCode());
        JdhDispatchTeamPo po = jdhDispatchTeamPoMapper.selectOne(queryWrapper);
        return wrapDetail(po);
    }

    /**
     * wrapDetail
     * @param po
     * @return
     */
    private JdhDispatchTeam wrapDetail(JdhDispatchTeamPo po) {
        JdhDispatchTeam dispatchTeam = JdhDispatchTeamInfrastructureConverter.INSTANCE.po2Entity(po);
        if (Objects.isNull(dispatchTeam) || Objects.isNull(dispatchTeam.getDispatchTeamId())) {
            return dispatchTeam;
        }
        dispatchTeam.setTeamSkillRelList(findDispatchTeamSkillList(Lists.newArrayList(dispatchTeam.getDispatchTeamId())));
        dispatchTeam.setTeamAngelRelList(findDispatchTeamAngelList(Lists.newArrayList(dispatchTeam.getDispatchTeamId())));
        return dispatchTeam;
    }

    @Override
    public int remove(JdhDispatchTeam entity) {
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int save(JdhDispatchTeam entity) {
        JdhDispatchTeamPo dispatchTeamPo = JdhDispatchTeamInfrastructureConverter.INSTANCE.entity2Po(entity);
        //新增
        if (Objects.isNull(dispatchTeamPo.getId())) {
            JdhBasicPoConverter.initInsertBasicPo(dispatchTeamPo);
            int count = jdhDispatchTeamPoMapper.insert(dispatchTeamPo);
            //新增小队技能、小队服务者信息
            saveDispatchTeamSkill(entity);
            saveDispatchTeamAngel(entity);
            return count;
        }
        //修改
        Integer oldVersion = entity.getVersion();
        entity.versionIncrease();
        dispatchTeamPo.setBranch(environment);
        dispatchTeamPo.setUpdateTime(new Date());
        dispatchTeamPo.setVersion(entity.getVersion());

        LambdaUpdateWrapper<JdhDispatchTeamPo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(JdhDispatchTeamPo::getVersion,oldVersion)
                .eq(JdhDispatchTeamPo::getDispatchTeamId,dispatchTeamPo.getDispatchTeamId());

        int count = jdhDispatchTeamPoMapper.update(dispatchTeamPo, updateWrapper);
        if (count < 1) {
            throw new BusinessException(DispatchErrorCode.DISPATCH_CALLBACK_BUSY);
        }
        saveDispatchTeamSkill(entity);
        saveDispatchTeamAngel(entity);
        return count;
    }

    /**
     * 保存派单小队技能
     * @param entity
     * @return
     */
    private int saveDispatchTeamSkill(JdhDispatchTeam entity) {
        if (Objects.isNull(entity) || Objects.isNull(entity.getDispatchTeamId())) {
            return 0;
        }
        //先删除再新增
        LambdaUpdateWrapper<JdhDispatchTeamSkillRelPo> updateTeamSkillWrapper = Wrappers.lambdaUpdate();
        updateTeamSkillWrapper.setSql("`version` = `version` + 1").set(JdhDispatchTeamSkillRelPo::getYn, YnStatusEnum.NO.getCode())
                .eq(JdhDispatchTeamSkillRelPo::getYn,YnStatusEnum.YES.getCode())
                .eq(JdhDispatchTeamSkillRelPo::getDispatchTeamId, entity.getDispatchTeamId());
        jdhDispatchTeamSkillRelPoMapper.update(null, updateTeamSkillWrapper);
        //新增
        List<JdhDispatchTeamSkillRel> teamSkillRelList = entity.getTeamSkillRelList();
        if (CollectionUtils.isNotEmpty(teamSkillRelList)) {
            List<JdhDispatchTeamSkillRelPo> teamSkillRelPos = JdhDispatchTeamInfrastructureConverter.INSTANCE.entity2TeamSkillPo(teamSkillRelList);
            return jdhDispatchTeamSkillRelPoMapper.batchInsert(teamSkillRelPos);
        }
        return 0;
    }

    /**
     * 查询派单小队技能列表
     * @param dispatchTeamIdList
     * @return
     */
    public List<JdhDispatchTeamSkillRel> findDispatchTeamSkillList(List<Long> dispatchTeamIdList) {
        LambdaQueryWrapper<JdhDispatchTeamSkillRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(JdhDispatchTeamSkillRelPo::getDispatchTeamId, dispatchTeamIdList);
        queryWrapper.eq(JdhDispatchTeamSkillRelPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhDispatchTeamSkillRelPo> teamSkillRelPos = jdhDispatchTeamSkillRelPoMapper.selectList(queryWrapper);
        return JdhDispatchTeamInfrastructureConverter.INSTANCE.po2TeamSkillEntity(teamSkillRelPos);
    }

    /**
     * 保存派单小队服务者
     * @param entity
     * @return
     */
    private int saveDispatchTeamAngel(JdhDispatchTeam entity) {
        if (Objects.isNull(entity) || Objects.isNull(entity.getDispatchTeamId())) {
            return 0;
        }
        //先删除再新增
        LambdaUpdateWrapper<JdhDispatchTeamAngelRelPo> updateTeamAngelWrapper = Wrappers.lambdaUpdate();
        updateTeamAngelWrapper.setSql("`version` = `version` + 1").set(JdhDispatchTeamAngelRelPo::getYn, YnStatusEnum.NO.getCode())
                .eq(JdhDispatchTeamAngelRelPo::getYn,YnStatusEnum.YES.getCode())
                .eq(JdhDispatchTeamAngelRelPo::getDispatchTeamId, entity.getDispatchTeamId());
        jdhDispatchTeamAngelRelPoMapper.update(null, updateTeamAngelWrapper);
        //新增
        List<JdhDispatchTeamAngelRel> teamAngelRelList = entity.getTeamAngelRelList();
        if (CollectionUtils.isNotEmpty(teamAngelRelList)) {
            List<JdhDispatchTeamAngelRelPo> teamAngelRelPoList = JdhDispatchTeamInfrastructureConverter.INSTANCE.entity2TeamAngelPo(teamAngelRelList);
            return jdhDispatchTeamAngelRelPoMapper.batchInsert(teamAngelRelPoList);
        }
        return 0;
    }

    /**
     * 查询派单小队服务者列表
     * @param dispatchTeamIdList
     * @return
     */
    public List<JdhDispatchTeamAngelRel> findDispatchTeamAngelList(List<Long> dispatchTeamIdList) {
        LambdaQueryWrapper<JdhDispatchTeamAngelRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(JdhDispatchTeamAngelRelPo::getDispatchTeamId, dispatchTeamIdList);
        queryWrapper.eq(JdhDispatchTeamAngelRelPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhDispatchTeamAngelRelPo> teamSkillRelPos = jdhDispatchTeamAngelRelPoMapper.selectList(queryWrapper);
        return JdhDispatchTeamInfrastructureConverter.INSTANCE.po2TeamAngelEntity(teamSkillRelPos);
    }

}