package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSONObject;
import com.jd.fce.orb.domain.CancelRequest;
import com.jd.fce.orb.domain.CancelResponse;
import com.jd.fce.orb.service.OrbOomCancelOrderService;
import com.jd.jr.order.export.rest.domain.cashier.CashierOrderInfoBean;
import com.jd.medicine.base.common.annotation.LogAndUmp;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.pay.platform.api.domain.channel.GenPayIdRequestVo;
import com.jd.pay.platform.api.domain.channel.GenPayIdResult;
import com.jd.pay.platform.api.domain.request.PlatPayRequest;
import com.jd.pay.platform.api.domain.response.PlatPayResponse;
import com.jd.pay.platform.api.provider.channel.PlatPayChannelService;
import com.jd.paytrade.front.export.PaymentAccResource;
import com.jd.paytrade.front.export.vo.acc.PaymentAccReqVo;
import com.jd.paytrade.front.export.vo.acc.PaymentAccResVo;
import com.jd.trade2.base.export.identity.IdentityInfoParam;
import com.jd.trade2.base.export.result.Result;
import com.jd.trade2.core.export.param.action.UserActionParam;
import com.jd.trade2.core.export.param.submit.SubmitOrderParam;
import com.jd.trade2.core.export.result.action.UserActionResult;
import com.jd.trade2.core.export.result.submit.SubmitOrderResult;
import com.jd.trade2.core.export.service.SubmitOrderExportService;
import com.jd.trade2.core.export.service.UserActionExportService;
import com.jd.ump.profiler.proxy.Profiler;
import com.jdh.o2oservice.application.product.ProductExtApplication;
import com.jdh.o2oservice.application.trade.TradeExtApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.RpcBusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.core.domain.trade.bo.OrderPayBo;
import com.jdh.o2oservice.core.domain.trade.context.SkipPayUrlContext;
import com.jdh.o2oservice.core.domain.trade.context.OrderSubmitContext;
import com.jdh.o2oservice.core.domain.trade.context.OrderUserActionContext;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.rpc.TradeInfoRpc;
import com.jdh.o2oservice.core.domain.trade.vo.CancelPayRequestObject;
import com.jdh.o2oservice.core.domain.trade.vo.OrderSubmitValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.OrderUserActionValueObject;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuAreaFeeQuery;
import com.jdh.o2oservice.export.product.query.JdhSkuRequest;
import com.jdh.o2oservice.infrastructure.enums.IdentityInfoEnum;
import com.jdh.o2oservice.infrastructure.rpc.convert.TradeInfoConverter;
import com.jdh.trade.basis.base.ResultBuilder;
import com.jdh.trade.settle.biz.sdk.dto.pay.PayInfoRequest;
import com.jdh.trade.settle.biz.sdk.dto.pay.PayInfoResponse;
import com.jdh.trade.settle.biz.sdk.service.JdhPayJsfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.Set;

/**
 * TradeInfoRpcImpl 交易域rpc实现
 *
 * <AUTHOR>
 * @version 2024/3/1 16:14
 **/
@Slf4j
@Service
public class TradeInfoRpcImpl implements TradeInfoRpc {

    @Resource
    private UserActionExportService userActionExportService;

    @Resource
    private SubmitOrderExportService submitOrderExportService;

    @Resource
    private TradeInfoConverter tradeInfoConverter;

    /**
     * jdhPayJsfService
     */
    @Resource
    private JdhPayJsfService jdhPayJsfService;
    /**
     * 新版收银台接口
     */
    @Resource
    private PaymentAccResource paymentAccResource;
    /**
     * platPayChannelService
     */
    @Resource
    private PlatPayChannelService platPayChannelService;
    /**
     * 取消订单接口
     * 接口文档：old：  https://cf.jd.com/pages/viewpage.action?pageId=404962797
     * fix  ->
     * new  ：  https://cf.jd.com/pages/viewpage.action?pageId=404962806
     * orbOrderCancelReasonService
     */
    @Resource
    private OrbOomCancelOrderService orbOomCancelOrderService;
    /**
     * productExtApplication
     */
    @Autowired
    private ProductExtApplication productExtApplication;
    /**
     * tradeExtApplication
     */
    @Autowired
    private TradeExtApplication tradeExtApplication;


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.TradeInfoRpcImpl.executeAction")
    public OrderUserActionValueObject executeAction(OrderUserActionContext orderUserActionContext) {
        try {
            IdentityInfoParam identityInfoParam = tradeInfoConverter.buildIdentityInfoParam();
            UserActionParam userActionParam = tradeInfoConverter.convertUserActionParam(orderUserActionContext);
            Result<UserActionResult> result = userActionExportService.executeAction(identityInfoParam, userActionParam);
            log.info("TradeInfoRpcImpl executeAction 结算页用户行为 userPin:{} identityInfoParam:{} userActionParam:{} result:{}",
                    orderUserActionContext.getUserPin(), JSONObject.toJSONString(identityInfoParam), JSONObject.toJSONString(userActionParam), JSONObject.toJSONString(result));
            if (result == null) {
                throw BusinessException.asBusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }

            this.handleExceptionByIdentity(result, orderUserActionContext);

//            if (result.requestSuccess() || TradeErrorCodeConstant.getHandlerAddressSuccessCodeList().contains(result.getResultCode())) {
              if (result.requestSuccess()) {
                return tradeInfoConverter.convertOrderUserActionResult(result.getResultInfo(), orderUserActionContext);
            }
            throw BusinessException.asBusinessException(new RpcBusinessErrorCode(result.getResultCode(), result.getMessage(), result.getHelpMessage()));
        } catch (BusinessException e) {
            log.error("TradeInfoRpcImpl executeAction 结算页用户行为 BusinessException orderUserActionInfo:{}", JSONObject.toJSONString(orderUserActionContext), e);
            throw e;
        } catch (Throwable e) {
            log.error("TradeInfoRpcImpl executeAction 结算页用户行为 error orderUserActionInfo:{}", JSONObject.toJSONString(orderUserActionContext), e);
            throw BusinessException.asBusinessException(new RpcBusinessErrorCode(BusinessErrorCode.UNKNOWN_ERROR.getCode(), BusinessErrorCode.UNKNOWN_ERROR.getDescription()));
        }
    }

    @Override
    public OrderSubmitValueObject submitOrder(OrderSubmitContext orderSubmitContext) {
        try {
            IdentityInfoParam identityInfoParam = tradeInfoConverter.buildIdentityInfoParam();
            SubmitOrderParam submitOrderParam = tradeInfoConverter.convertSubmitOrderParam(orderSubmitContext);
            Result<SubmitOrderResult> result = submitOrderExportService.submitOrder(identityInfoParam, submitOrderParam);
            log.info("TradeInfoRpcImpl submitOrder 提交订单 userPin:{} identityInfoParam:{} submitOrderParam:{} result:{}",
                    orderSubmitContext.getUserPin(), JSONObject.toJSONString(identityInfoParam), JSONObject.toJSONString(submitOrderParam), JSONObject.toJSONString(result));
            if (result == null) {
                throw BusinessException.asBusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
            if (result.requestSuccess()) {
                 return tradeInfoConverter.convertOrderSubmitResult(result.getResultInfo());
            }
            return tradeInfoConverter.convertOrderSubmitErrorResult(result, IdentityInfoEnum.CN_RETAIL_JDH_XFYL_HOME.getIdentity());
        } catch (BusinessException e) {
            log.error("TradeInfoRpcImpl submitOrder 提交订单 BusinessException orderSubmitInfo:{}", JSONObject.toJSONString(orderSubmitContext), e);
            throw e;
        } catch (Throwable e) {
            log.error("TradeInfoRpcImpl submitOrder 提交订单 error orderSubmitInfo:{}", JSONObject.toJSONString(orderSubmitContext), e);
            throw BusinessException.asBusinessException(new RpcBusinessErrorCode(BusinessErrorCode.UNKNOWN_ERROR.getCode(), BusinessErrorCode.UNKNOWN_ERROR.getDescription()));
        }
    }

    /**
     * 京东健康app 收银台
     * @param orderPayBo
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.TradeInfoRpcImpl.getJdhAppPayUrl")
    public String getJdhAppPayUrl(OrderPayBo orderPayBo) {
        PayInfoRequest request = new PayInfoRequest();
        request.setOrderId(String.valueOf(orderPayBo.getOrderId()));
        request.setOrderType(CommonConstant.ORDER_TYPE_STR);
        request.setPayOrderType(CommonConstant.SIX);
        request.setPin(orderPayBo.getUserPin());
        request.setSubmitTime(orderPayBo.getSubmitTime());
        request.setTotal(orderPayBo.getOrderAmount());
        ResultBuilder<PayInfoResponse> result = jdhPayJsfService.queryPayInfoForH5(request);
        if (Objects.isNull(result) || !result.isSuccess()) {
            log.error("TradeInfoRpcImpl -> queryPayInfoForH5 error, result={}", JSONObject.toJSONString(result));
            throw BusinessException.asBusinessException(new RpcBusinessErrorCode(BusinessErrorCode.UNKNOWN_ERROR.getCode(), BusinessErrorCode.UNKNOWN_ERROR.getDescription()));
        }
        return result.getData().getCashierUrl();
    }

    /**
     * 微信小程序支付
     *
     * @param appId
     * @param cashierOrderInfoBean
     * @return
     */
    @Override
    @LogAndUmp(jKey = "com.jdh.o2oservice.infrastructure.rpc.TradeInfoRpcImpl.cashierMiniprogram", errorReturnJsfResult = false)
    public PaymentAccResVo cashierMiniprogram(String appId, CashierOrderInfoBean cashierOrderInfoBean) {
        try {
            PaymentAccReqVo paymentAccReqVo = new PaymentAccReqVo();
            paymentAccReqVo.setAppId(appId);
            paymentAccReqVo.setPayInfo(cashierOrderInfoBean);
            log.info("CommonOrderRpcImpl -> cashierMiniprogram , paymentAccReqVo={}", JsonUtil.toJSONString(paymentAccReqVo));
            PaymentAccResVo paymentAccResVo = paymentAccResource.accV2(paymentAccReqVo);
            log.info("CommonOrderRpcImpl -> cashierMiniprogram , res={}", JsonUtil.toJSONString(paymentAccResVo));
            if (null != paymentAccReqVo) {
                return paymentAccResVo;
            }
            throw BusinessException.asBusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        } catch (BusinessException e) {
            log.error("CommonOrderRpcImpl -> cashierMiniprogram business exception:",e);
            throw e;
        } catch (Throwable e) {
            log.error("CommonOrderRpcImpl -> cashierMiniprogram exception, cashierOrderInfoBean={}",JsonUtil.toJSONString(cashierOrderInfoBean));
            throw BusinessException.asBusinessException(BusinessErrorCode.RPC_JSF_ERROR);
        }
    }

    /**
     * 新版收银台获取支付url
     *
     * @param platPayRequest
     * @return
     */
    @Override
    @LogAndUmp(jKey = "com.jdh.o2oservice.infrastructure.rpc.TradeInfoRpcImpl.genPayId", errorReturnJsfResult = false)
    public PlatPayResponse<GenPayIdResult> genPayId(PlatPayRequest<GenPayIdRequestVo> platPayRequest) {
        log.info("TradeInfoRpcImpl -> genPayId , platPayRequest={}", JsonUtil.toJSONString(platPayRequest));
        try {
            return platPayChannelService.genPayId(platPayRequest);
        } catch (Throwable e) {
            log.error("TradeInfoRpcImpl -> genPayId exception, errMessage={}", e.getMessage());
            throw BusinessException.asBusinessException(new RpcBusinessErrorCode(BusinessErrorCode.UNKNOWN_ERROR.getCode(), BusinessErrorCode.UNKNOWN_ERROR.getDescription()));
        }
    }

    /**
     * 取消订单支付
     *
     * @param orderId
     * @param userPin
     * @return
     */
    @Override
    @LogAndUmp(jKey = "com.jdh.o2oservice.infrastructure.rpc.TradeInfoRpcImpl.cancelOrder", errorReturnJsfResult = false)
    public Boolean cancelOrder(Long orderId, String userPin) {
        log.info("TradeInfoRpcImpl -> cancelOrder,orderId={},userPin={}",orderId, userPin);
        try {
            CancelRequest request = getCancelRequest(orderId,userPin);

            CancelResponse result = orbOomCancelOrderService.cancelOrder(request);
            log.info("TradeInfoRpcImpl -> cancelOrder end, userPin={},result={}", userPin,JsonUtil.toJSONString(result));
            if (1 == result.getResponseCode() || 1000 == result.getCancelNotice()) {
                return true;
            }else if( -1003 == result.getCancelNotice()){
                log.info("TradeInfoRpcImpl->cancelOrder end,重复申请 orderId={}", orderId);
                return true;
            }else if( -1000 == result.getCancelNotice()){
                log.error("TradeInfoRpcImpl -> cancelOrder exception, orderId={}", orderId);
                throw BusinessException.asBusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }else{
                log.error("TradeInfoRpcImpl -> cancelOrder exception, orderId={} ", orderId);
                throw BusinessException.asBusinessException(BusinessErrorCode.RPC_JSF_ERROR);
            }
        } catch (Throwable e) {
            log.error("TradeInfoRpcImpl -> cancelOrder exception, errMessage={}", e.getMessage());
            throw BusinessException.asBusinessException(new RpcBusinessErrorCode(BusinessErrorCode.UNKNOWN_ERROR.getCode(), BusinessErrorCode.UNKNOWN_ERROR.getDescription()));
        }
    }

    /**
     * @param skuId
     * @return
     */
    @Override
    public Integer querySkuServiceType(Long skuId) {
        JdhSkuDto jdhSkuDto = productExtApplication.queryJdhSkuInfoBySkuId(skuId);
        AssertUtils.nonNull(jdhSkuDto, TradeErrorCode.ORDER_IS_NULL);
        return jdhSkuDto.getServiceType();
    }

    /**
     *
     * @param skuIds
     * @param areaFeeConfigId
     * @return
     */
    @Override
    public String getUpgrageAngelFee(Set<String> skuIds, Long areaFeeConfigId) {
        return tradeExtApplication.getUpgrageAngelFee(skuIds,areaFeeConfigId);
    }

    /**
     *
     * @param skuIds
     * @param provinceCode
     * @param channelId
     * @param serviceType
     * @return
     */
    @Override
    public String getUpgrageAngelFeeByParam(Set<String> skuIds,String provinceCode,String channelId,Integer serviceType) {
        JdhSkuAreaFeeQuery jdhSkuAreaFeeQuery = new JdhSkuAreaFeeQuery();
        jdhSkuAreaFeeQuery.setSkuIds(skuIds);
        jdhSkuAreaFeeQuery.setProvinceCode(provinceCode);
        jdhSkuAreaFeeQuery.setChannelId(channelId);
        jdhSkuAreaFeeQuery.setServiceType(serviceType);
        return tradeExtApplication.getUpgrageAngelFeeByParam(jdhSkuAreaFeeQuery);
    }


    /**
     * 根据业务身份处理中台异常信息
     *
     * @param result              中台返回结果
     * @param orderUserActionInfo 订单用户行为
     */
    private void handleExceptionByIdentity(Result<UserActionResult> result, OrderUserActionContext orderUserActionInfo) {
		if (result == null || orderUserActionInfo == null) {
			return;
		}
		if (result.requestSuccess()) {
			return;
		}
    }

    /**
     * 取消订单支付中台接口参数拼接
     * @param orderId
     * @param applyPin
     * @return
     */
    private static CancelRequest getCancelRequest(Long orderId,String applyPin){
        CancelRequest request = new CancelRequest();
        request.setOrderId(orderId);
        CancelPayRequestObject cancelPayRequest = new CancelPayRequestObject();
        cancelPayRequest.setOperationType(120);
        cancelPayRequest.setRefundType(9);
        cancelPayRequest.setApplyDate(System.currentTimeMillis());
        cancelPayRequest.setSource(211);
        cancelPayRequest.setApplyPin(applyPin);
        cancelPayRequest.setApplyName(applyPin);
        request.setExtraParam(JsonUtil.toJSONString(cancelPayRequest));
        return request;
    }
}
