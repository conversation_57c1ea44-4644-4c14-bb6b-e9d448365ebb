package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @ClassName JdhDispatchTeamSkillRelPo
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 15:43
 **/
@Data
@TableName(value = "jdh_dispatch_team_skill_rel",autoResultMap = true)
public class JdhDispatchTeamSkillRelPo extends JdhBasicPo{

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 派单小队ID
     */
    private Long dispatchTeamId;

    /**
     * 服务者技能code（全局唯一）
     */
    private String angelSkillCode;

    /**
     * 服务者技能名称
     */
    private String angelSkillName;

    /**
     * 服务类型 1:供应商体检项目 2:检测类 3:护理类
     */
    private Integer itemType;

    /**
     * 互医服务组id
     */
    private String serviceGroupId;
}