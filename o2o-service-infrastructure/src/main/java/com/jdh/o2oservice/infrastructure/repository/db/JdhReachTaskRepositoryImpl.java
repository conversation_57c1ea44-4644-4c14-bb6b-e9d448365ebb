package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachTask;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachTaskIdentifier;
import com.jdh.o2oservice.core.domain.support.reach.repository.db.JdhReachTaskRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhReachConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhReachTaskPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePatientPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachTaskPo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 触达任务仓储
 * @author: yangxiyu
 * @date: 2024/4/15 6:09 下午
 * @version: 1.0
 */
@Component
public class JdhReachTaskRepositoryImpl implements JdhReachTaskRepository {

    /** jdhReachTaskPoMapper */
    @Resource
    private JdhReachTaskPoMapper jdhReachTaskPoMapper;

    /**
     *
     * @param jdhReachTaskIdentifier
     * @return
     */
    @Override
    public JdhReachTask find(JdhReachTaskIdentifier jdhReachTaskIdentifier) {
        LambdaQueryWrapper<JdhReachTaskPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhReachTaskPo::getTaskId, jdhReachTaskIdentifier.getTaskId());
        JdhReachTaskPo taskPo = jdhReachTaskPoMapper.selectOne(queryWrapper);
        return JdhReachConverter.INSTANCE.convert2ReachTask(taskPo);
    }

    /**
     *
     * @param aggregate
     * @return
     */
    @Override
    public int remove(JdhReachTask aggregate) {
        return 0;
    }

    /**
     *
     * @param aggregate
     * @return
     */
    @Override
    public int save(JdhReachTask aggregate) {
        JdhReachTaskPo taskPo = JdhReachConverter.INSTANCE.convert2ReachTaskPo(aggregate);
        LambdaUpdateWrapper<JdhReachTaskPo> updateWrapper = Wrappers.lambdaUpdate();

        updateWrapper.set(JdhReachTaskPo::getErrorInfo, aggregate.getErrorInfo())
                .set(JdhReachTaskPo::getStatus, aggregate.getStatus())
                .set(JdhReachTaskPo::getUpdateTime, new Date())
                .eq(JdhReachTaskPo::getTaskId, aggregate.getTaskId());
        return jdhReachTaskPoMapper.update(taskPo, updateWrapper);
    }


    /**
     * 批量保存
     */
    @Override
    public int batchSave(List<JdhReachTask> tasks) {
        if (CollectionUtils.isEmpty(tasks)){
            return 0;
        }
        List<JdhReachTaskPo> pos = JdhReachConverter.INSTANCE.convert2ReachTaskPo(tasks);
        for (JdhReachTaskPo po : pos) {
            JdhBasicPoConverter.initInsertBasicTimePo(po);
        }
        return jdhReachTaskPoMapper.batchInsert(pos);
    }
}
