package com.jdh.o2oservice.infrastructure.rpc.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.tp.common.masterdata.UniformBizInfoBuilder;
import com.jd.trade2.base.export.biztag.HorizontalTag;
import com.jd.trade2.base.export.description.VerticalTagDescription;
import com.jd.trade2.base.export.identity.IdentityInfoParam;
import com.jd.trade2.base.export.identity.systeminfo.SystemInfo;
import com.jd.trade2.base.export.identity.tag.RequestTagParam;
import com.jd.trade2.base.export.model.adapter.AdapterManagerImpl;
import com.jd.trade2.base.export.model.domain.DomainCollectionDTO;
import com.jd.trade2.base.export.model.domain.DomainCollectionParam;
import com.jd.trade2.base.export.relation.BasketRelation;
import com.jd.trade2.base.export.relation.BundleRelation;
import com.jd.trade2.base.export.relation.OrderRelation;
import com.jd.trade2.base.export.relation.Relation;
import com.jd.trade2.base.export.result.Result;
import com.jd.trade2.base.export.sign.SignParam;
import com.jd.trade2.core.domain.coupon.export.param.model.CouponCollectionParam;
import com.jd.trade2.core.domain.order.export.dto.model.AbstractOrderItemDTO;
import com.jd.trade2.core.domain.order.export.dto.model.OrderCollectionDTO;
import com.jd.trade2.core.domain.price.export.param.model.AbstractPriceItemParam;
import com.jd.trade2.core.domain.price.export.param.model.DefaultPriceItemParam;
import com.jd.trade2.core.domain.price.export.param.model.PriceCollectionParam;
import com.jd.trade2.core.domain.product.export.param.AbstractProductItemParam;
import com.jd.trade2.core.domain.product.export.param.DefaultProductItemParam;
import com.jd.trade2.core.domain.product.export.param.ProductCollectionParam;
import com.jd.trade2.core.domain.quantity.export.param.model.AbstractQuantityItemParam;
import com.jd.trade2.core.domain.quantity.export.param.model.DefaultQuantityItemParam;
import com.jd.trade2.core.domain.quantity.export.param.model.QuantityCollectionParam;
import com.jd.trade2.core.domain.shoppinglist.export.param.model.AbstractShoppingItemParam;
import com.jd.trade2.core.domain.shoppinglist.export.param.model.DefaultShoppingItemParam;
import com.jd.trade2.core.domain.shoppinglist.export.param.model.ShoppingListCollectionParam;
import com.jd.trade2.core.domain.terminal.export.adapter.param.AbstractTerminalItemParam;
import com.jd.trade2.core.domain.terminal.export.adapter.param.DefaultTerminalItemParam;
import com.jd.trade2.core.domain.terminal.export.adapter.param.TerminalCollectionParam;
import com.jd.trade2.core.domain.trade.export.param.TradeCollectionParam;
import com.jd.trade2.core.domain.vender.export.param.model.AbstractVenderItemParam;
import com.jd.trade2.core.domain.vender.export.param.model.DefaultVenderItemParam;
import com.jd.trade2.core.domain.vender.export.param.model.VenderCollectionParam;
import com.jd.trade2.core.export.param.action.UserActionBusinessParam;
import com.jd.trade2.core.export.param.action.UserActionParam;
import com.jd.trade2.core.export.param.submit.SubmitOrderParam;
import com.jd.trade2.core.export.result.action.UserActionResult;
import com.jd.trade2.core.export.result.submit.SubmitOrderResult;
import com.jd.trade2.horizontal.base.domain.product.export.param.decorator.item.HJBProductItemAttrDecoratorParam;
import com.jd.trade2.vertical.tool.ParseTool;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.TradeErrorCodeConstant;
import com.jdh.o2oservice.base.enums.CartExtInfoMapEnum;
import com.jdh.o2oservice.base.enums.UserActionEnum;
import com.jdh.o2oservice.base.exception.RpcBusinessErrorCode;
import com.jdh.o2oservice.base.util.IpUtil;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import com.jdh.o2oservice.core.domain.trade.context.*;
import com.jdh.o2oservice.core.domain.trade.vo.*;
import com.jdh.o2oservice.infrastructure.enums.IdentityInfoEnum;
import com.jdh.o2oservice.infrastructure.rpc.convert.dto.*;
import com.jdh.o2oservice.infrastructure.rpc.convert.param.AbstractUserActionParamConverter;
import com.jdh.o2oservice.infrastructure.rpc.convert.param.CouponUserActionParamConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.Mapper;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * TradeInfoConverter 交易域的实体转换
 *
 * <AUTHOR>
 * @version 2024/3/1 18:14
 **/
@Slf4j
@Mapper(componentModel = "spring")
@SuppressWarnings("unchecked")
public abstract class TradeInfoConverter {

    @Resource
    private List<AbstractUserActionParamConverter> abstractUserActionParamConverterList;

    @Resource
    private List<AbstractUserActionDTOConverter> abstractUserActionDTOConverterList;

    @Resource
    private AmountUserActionDTOConverter amountUserActionDTOConverter;

    @Resource
    private ProductUserActionDTOConverter productUserActionDTOConverter;

    @Resource
    private VenderUserActionDTOConverter venderUserActionDTOConverter;

    @Resource
    private PaymentUserActionDTOConverter paymentUserActionDTOConverter;

    @Resource
    private StockUserActionDTOConverter stockUserActionDTOConverter;

    @Resource
    private CouponUserActionParamConverter couponUserActionParamAssembler;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private LimitBuyUserActionDTOConverter limitBuyUserActionDTOConverter;

    /**
     * 转换结算页用户行为的入参
     *
     * @param orderUserActionInfo 入参
     * @return UserActionParam
     */
    public UserActionParam convertUserActionParam(OrderUserActionContext orderUserActionInfo) {
        UserActionParam userActionParam = new UserActionParam();

        String userPin = orderUserActionInfo.getUserPin();
        // 登录用户
        SignParam signParam = new SignParam();
        signParam.setPin(userPin);
        userActionParam.setSignParam(signParam);

        Relation relation = new Relation(String.valueOf(userPin.hashCode()));

        // 清单域
        CartInfoContext cartInfoQuery = orderUserActionInfo.getCartInfoQuery();
        ShoppingListCollectionParam shoppingListCollectionParam = buildShoppingListCollectionParam(cartInfoQuery);

        // 终端域
        TerminalCollectionParam terminalCollectionParam = buildTerminalCollectionParam(orderUserActionInfo);

        CouponCollectionParam couponCollectionParam = buildCouponCollectionParam(orderUserActionInfo, shoppingListCollectionParam, relation);

        TradeCollectionParam tradeCollectionParam = buildTradeCollectionParam(orderUserActionInfo, orderUserActionInfo.getAppointmentTimeValueObject(), orderUserActionInfo.getUsp());

        // 用户行为
        List<UserActionBusinessParam> userActionBusinessParamList = new ArrayList<>();
        for (AbstractUserActionParamConverter abstractUserActionParamAssembler : abstractUserActionParamConverterList) {
            if (abstractUserActionParamAssembler.needExecute(orderUserActionInfo)) {
                UserActionBusinessParam userActionBusinessParam = abstractUserActionParamAssembler.execute(orderUserActionInfo, shoppingListCollectionParam);
                if (userActionBusinessParam == null) {
                    continue;
                }
                userActionBusinessParam.getDomainCollectionParams().add(shoppingListCollectionParam);
                userActionBusinessParam.getDomainCollectionParams().add(terminalCollectionParam);
                userActionBusinessParam.getDomainCollectionParams().add(tradeCollectionParam);
                if (couponCollectionParam != null) {
                    userActionBusinessParam.getDomainCollectionParams().add(couponCollectionParam);
                }
                userActionBusinessParamList.add(userActionBusinessParam);
                // 一次只支持一个用户行为所以break 如果之后支持多个可以放开
                break;
            }
        }
        userActionParam.setUserActions(userActionBusinessParamList);
        return userActionParam;
    }

    /**
     * 转换结算页用户行为的出参
     *
     * @param resultInfo 出参
     * @return OrderUserActionResult
     */
    public OrderUserActionValueObject convertOrderUserActionResult(UserActionResult resultInfo, OrderUserActionContext orderUserActionInfo) {
        OrderUserActionValueObject orderUserActionResult = new OrderUserActionValueObject();
        if (resultInfo == null) {
            return null;
        }
        List<DomainCollectionDTO> domainCollectionDTOList = resultInfo.getDomainCollectionDTOs();
        if (CollectionUtils.isEmpty(domainCollectionDTOList)) {
            return null;
        }
        orderUserActionResult.setUserPin(orderUserActionInfo.getUserPin());
        Relation relation = resultInfo.getRelation();
        // 获取业务身份
        List<String> identityUserActionList = duccConfig.getIdentityUserActionList(IdentityInfoEnum.CN_RETAIL_JDH_XFYL_HOME.getIdentity());
        if (CollectionUtils.isEmpty(identityUserActionList)) {
            log.info("convertOrderUserActionResult 用户行为 ducc 业务身份为空 orderTradeInfo:{} ", JSONObject.toJSONString(orderUserActionInfo));
            return null;
        }

        ParseTool parseTool = new ParseTool(domainCollectionDTOList);
        for (AbstractUserActionDTOConverter abstractUserActionDTOAssembler : abstractUserActionDTOConverterList) {
            if (abstractUserActionDTOAssembler.identityContainsUserAction(identityUserActionList)) {
                abstractUserActionDTOAssembler.handlerIdentityUserAction(parseTool, orderUserActionResult, relation, IdentityInfoEnum.CN_RETAIL_JDH_XFYL_HOME.getIdentity());
            }
        }
        return orderUserActionResult;
    }

    /**
     * 转换通下提交订单的入参
     *
     * @param orderSubmitContext 入参
     * @return SubmitOrderParam
     */
    public SubmitOrderParam convertSubmitOrderParam(OrderSubmitContext orderSubmitContext) {
        SubmitOrderParam submitOrderParam = new SubmitOrderParam();

        // 登录用户
        SignParam signParam = new SignParam();
        signParam.setPin(orderSubmitContext.getUserPin());
        submitOrderParam.setSignParam(signParam);

        // 组装各个域参数
        List<DomainCollectionParam> domainCollectionParamList = new ArrayList<>();

        // 清单域
        CartInfoContext cartInfoQuery = orderSubmitContext.getCartInfoQuery();
        ShoppingListCollectionParam shoppingListCollectionParam = buildShoppingListCollectionParam(cartInfoQuery);

        // 终端域
        TerminalCollectionParam terminalCollectionParam = buildTerminalCollectionParam(orderSubmitContext);
        domainCollectionParamList.add(terminalCollectionParam);

        TradeCollectionParam tradeCollectionParam = buildTradeCollectionParam(orderSubmitContext, orderSubmitContext.getAppointmentTimeValueObject(), orderSubmitContext.getUsp());
        domainCollectionParamList.add(tradeCollectionParam);

        // 获取业务身份
        List<String> identityUserActionList = duccConfig.getIdentityUserActionList(IdentityInfoEnum.CN_RETAIL_JDH_XFYL_HOME.getIdentity());
        if (CollectionUtils.isEmpty(identityUserActionList)) {
            log.info("convertOrderUserActionResult 用户行为 ducc 业务身份为空 orderSubmitInfo:{} ", JSONObject.toJSONString(orderSubmitContext));
            return null;
        }

        // TODO 先实现， 之后可以在策略中添加排序顺序
        Relation relation = handlerSkuInfo(orderSubmitContext, shoppingListCollectionParam, domainCollectionParamList);

        // 如果其他域有值需要传给通下（有可能垂直业务没有进入结算页和用户行为的功能）
        for (AbstractUserActionParamConverter abstractUserActionParamAssembler : abstractUserActionParamConverterList) {
            if (abstractUserActionParamAssembler.identityContainsUserAction(identityUserActionList)) {
                abstractUserActionParamAssembler.buildUserActionParam(orderSubmitContext, shoppingListCollectionParam, domainCollectionParamList, relation);
            }
        }
        domainCollectionParamList.add(shoppingListCollectionParam);
        submitOrderParam.setDomainCollectionParams(domainCollectionParamList);
        submitOrderParam.setRelationParam(relation);
        return submitOrderParam;
    }

    /**
     * 转换通下提交订单的出参
     *
     * @param resultInfo 出参
     * @return OrderSubmitResult
     */
    public OrderSubmitValueObject convertOrderSubmitResult(SubmitOrderResult resultInfo) {
        OrderSubmitValueObject orderSubmitResult = new OrderSubmitValueObject();
        if (resultInfo == null) {
            return null;
        }
        List<DomainCollectionDTO> domainCollectionDTOList = resultInfo.getDomainCollectionDTOs();
        if (CollectionUtils.isEmpty(domainCollectionDTOList)) {
            return null;
        }
        Relation relation = resultInfo.getRelation();
        ParseTool parseTool = new ParseTool(domainCollectionDTOList);
        OrderCollectionDTO orderCollectionDTO = parseTool.getCollectionDTO(OrderCollectionDTO.class);

        // 解析订单号
        if (orderCollectionDTO == null || CollectionUtils.isEmpty(orderCollectionDTO.getChildList()) || orderCollectionDTO.getChildList().get(0) == null) {
            return null;
        }
        AbstractOrderItemDTO abstractOrderItemDTO = orderCollectionDTO.getChildList().get(0);
        String orderIdStr = abstractOrderItemDTO.getOrderId();
        String orderType = abstractOrderItemDTO.getOrderType();
        if (StringUtils.isNumeric(orderIdStr)) {
            orderSubmitResult.setOrderId(Long.parseLong(orderIdStr));
        }
        orderSubmitResult.setOrderType(orderType);

        AmountInfoValueObject amountInfo = amountUserActionDTOConverter.buildAmountInfo(parseTool);
        if (amountInfo != null) {
            orderSubmitResult.setFactOrderAmount(amountInfo.getFactOrderAmount());
        }
        List<VenderInfoValueObject> venderInfos = venderUserActionDTOConverter.buildVenderInfo(parseTool, relation);
        if (CollectionUtils.isNotEmpty(venderInfos)) {
            orderSubmitResult.setVenderInfoList(venderInfos);
        }

        List<PaymentInfoValueObject> paymentInfos = paymentUserActionDTOConverter.buildPaymentInfoList(parseTool);
        if (CollectionUtils.isNotEmpty(paymentInfos)) {
            PaymentInfoValueObject paymentInfo = paymentInfos.get(0);
            orderSubmitResult.setPayType(Integer.valueOf(paymentInfo.getPaymentType()));
        }
        return orderSubmitResult;
    }

    public OrderSubmitValueObject convertOrderSubmitErrorResult(Result<SubmitOrderResult> result, String identityInfo) {
        OrderSubmitValueObject orderSubmitResult = new OrderSubmitValueObject();
        orderSubmitResult.setErrorCode(new RpcBusinessErrorCode(result.getResultCode(), result.getMessage(), result.getHelpMessage()));
        SubmitOrderResult resultInfo = result.getResultInfo();
        if (resultInfo == null) {
            return orderSubmitResult;
        }
        List<DomainCollectionDTO> domainCollectionDTOList = resultInfo.getDomainCollectionDTOs();
        if (CollectionUtils.isEmpty(domainCollectionDTOList)) {
            return orderSubmitResult;
        }
        ParseTool parseTool = new ParseTool(domainCollectionDTOList);
        Relation relation = resultInfo.getRelation();

        List<SkuItemValueObject> skuItemVoList = productUserActionDTOConverter.getSkuItemVoList(relation, parseTool);
        orderSubmitResult.setSkuItemVoList(skuItemVoList);

        //如果是无货的错误码
        if (TradeErrorCodeConstant.getHandlerNoStockErrorCodeList().contains(result.getResultCode())) {
            List<SkuItemValueObject> noStockSkuItemVoList = stockUserActionDTOConverter.buildNoStockSku(relation, parseTool);
            orderSubmitResult.setNoStockSkuItemVoList(noStockSkuItemVoList);
        }
        // 处理sku限购信息
        orderSubmitResult.setSkuItemLimitBuyVoList(limitBuyUserActionDTOConverter.buildLimitBuySku(relation, parseTool));
        return orderSubmitResult;
    }

    /**
     * 组装通下基础入参
     *
     * @return IdentityInfoParam
     */
    public IdentityInfoParam buildIdentityInfoParam() {
        IdentityInfoParam identityInfoParam = new IdentityInfoParam();

        // 设置系统信息
        SystemInfo systemInfo = new SystemInfo();
        systemInfo.setBusinessUnitId(301);

        systemInfo.setBuId(String.valueOf(CommonConstant.BU_ID));
        systemInfo.setUniformBizInfo(new UniformBizInfoBuilder().tenantId(1024).buId(CommonConstant.BU_ID).ua(1).build());
        systemInfo.setCallerDataCenter("lf");
        systemInfo.setLanguage("zh_CN");
        systemInfo.setLanguageCode("zh_CN");
        systemInfo.setSdkVersion("1.0.0-SNAPSHOT");
        systemInfo.setCallerAppName("trade");
        systemInfo.setCallerIP(IpUtil.getInet4AddressNoException());
        systemInfo.setUserAgentId(2);
        identityInfoParam.setSystemInfo(systemInfo);
        // 获取业务身份的tag
        VerticalTagDescription tag = IdentityInfoEnum.CN_RETAIL_JDH_XFYL_HOME.getVerticalTag();
        // 设置身份信息
        RequestTagParam tagParam = new RequestTagParam();
        tagParam.setVerticalTag(tag);
        tagParam.addHorizontalTag(HorizontalTag.JD_BASE);
        identityInfoParam.setRequestTags(tagParam);
        return identityInfoParam;
    }

    /**
     * 组装交易入参
     * @param context
     * @param appointmentTimeValueObject
     * @param usp
     * @return
     */
    private TradeCollectionParam buildTradeCollectionParam(BusinessContext context, AppointmentTimeValueObject appointmentTimeValueObject, String usp) {
        TradeCollectionParam tradeCollectionParam = new TradeCollectionParam();
        Map<String, String> extendInfo = new HashMap<>();
        if(appointmentTimeValueObject != null){
            extendInfo.put("appointmentTime", JSON.toJSONString(appointmentTimeValueObject));
        }
        extendInfo.put("calcServiceFeeFlag", usp != null ? "true" : "false");
        tradeCollectionParam.setExtParamMap(extendInfo);
        return tradeCollectionParam;
    }

    /**
     * 组装终端域的参数
     *
     * @param context 终端
     * @return TerminalCollectionParam
     */
    private TerminalCollectionParam buildTerminalCollectionParam(BusinessContext context) {
        TerminalCollectionParam terminalCollectionParam = new TerminalCollectionParam();
        AbstractTerminalItemParam terminalItemParam = new DefaultTerminalItemParam();
        terminalItemParam.setServerName(UmpUtil.APP_NAME);
        terminalItemParam.setUserIP(context.getClientIp());
        terminalItemParam.setSdkServerIp(IpUtil.getInet4AddressNoException());
        terminalItemParam.setClientSystemName(UmpUtil.APP_NAME);
        terminalItemParam.setUnpl(context.getUnpl());
//        terminalItemParam.setPartnerSource(identityInfo.getPartnerSource());
//        //设备指纹
//        String deviceNumber = TransmittableThreadLocalUtil.get("deviceNumber");
//        if (StringUtils.isNotBlank(deviceNumber)) {
//            terminalItemParam.setEid(deviceNumber);
//        }
//        //设备号
//        String deviceUuid = TransmittableThreadLocalUtil.get(ThreadParamNameConstant.DEVICE_UUID);
//        if (StringUtils.isNotBlank(deviceUuid)) {
//            terminalItemParam.setDeviceUuid(deviceUuid);
//        }
//        //如果没有显示入参传入用户ip，尝试获取隐藏参数
//        if (StringUtils.isBlank(terminalItemParam.getUserIP())) {
//            //用户ip
//            String userIp = TransmittableThreadLocalUtil.get(ThreadParamNameConstant.USER_IP);
//            if (StringUtils.isNotBlank(userIp)) {
//                terminalItemParam.setUserIP(userIp);
//            }
//        }
//
//        String identityScene = identityInfo.getIdentityScene();
//        Map<String, String> extendInfo = new HashMap<>();
//        if (StringUtils.isNotEmpty(identityScene)) {
//            extendInfo.put("identityScene", identityScene);
//        }
//        Map<String, String> extendInfoIdentity = identityInfo.getExtendInfo();
//        if (MapUtils.isNotEmpty(extendInfoIdentity)) {
//            for (String key : extendInfoIdentity.keySet()) {
//                extendInfo.put(key, extendInfoIdentity.get(key));
//            }
//        }
//        terminalItemParam.setExtendInfo(extendInfo);

        // 设置unpl
        terminalItemParam.setUnpl(context.getUnpl());

        terminalCollectionParam.addChild(terminalItemParam);

        return terminalCollectionParam;
    }

    /**
     * 组装清单域
     *
     * @param cartInfoQuery cartInfoQuery
     * @return ShoppingListCollectionParam
     */
    private ShoppingListCollectionParam buildShoppingListCollectionParam(CartInfoContext cartInfoQuery) {
        ShoppingListCollectionParam param = new ShoppingListCollectionParam();
        List<Integer> requestConditionTypes = new ArrayList<>();
        // 一品多价格
        requestConditionTypes.add(504);
        // plus95折
        requestConditionTypes.add(105);
        param.setRequestConditionTypes(requestConditionTypes);
        if(cartInfoQuery == null){
            return param;
        }
        CartExtInfoContext cartExtInfo = cartInfoQuery.getCartExtInfo();
        param.setCartType(cartExtInfo.getCartType());
        param.setTokenKey(cartExtInfo.getTokenKey());
        param.setTokenValue(cartExtInfo.getTokenValue());
        List<String> tags = cartInfoQuery.getTags();
        if (CollectionUtils.isNotEmpty(tags)) {
            for (String tag : tags) {
//                if (BooleanUtils.isTrue(cartInfoQuery.getPresaleSku())) {
//                    if ("preSaleOrder".equalsIgnoreCase(tag)) {
//                        continue;
//                    }
//                }
                if ("mrCoupon".equalsIgnoreCase(tag)) {
                    continue;
                }
                if ("stock".equalsIgnoreCase(tag)) {
                    continue;
                }
                if ("zydp".equalsIgnoreCase(tag)) {
                    continue;
                }
                param.addIntoMainFlowTags(tag);
            }
        }
        Map<String, Object> extInfoMap = cartInfoQuery.getExtInfoMap();
        if (MapUtils.isNotEmpty(extInfoMap)) {
            Collection<String> validStoreIds = (Collection<String>) extInfoMap.get(CartExtInfoMapEnum.VALID_STORE_ID_SET.getCartExtKey());
            if (CollectionUtils.isNotEmpty(validStoreIds)) {
                param.setValidStoreIdSet(new HashSet<>(validStoreIds));
            }
            String fixedVenderId = (String) extInfoMap.get(CartExtInfoMapEnum.FIXED_VENDER_ID.getCartExtKey());
            if (StringUtils.isNotEmpty(fixedVenderId)) {
                param.setFixedVenderId(fixedVenderId);
            }
        }
        return param;
    }

    /**
     * 处理sku信息
     *
     * @param orderSubmitInfo             提单
     * @param shoppingListCollectionParam shoppingListCollectionParam
     * @param domainCollectionParamList   domainCollectionParamList
     */
    private Relation handlerSkuInfo(OrderSubmitContext orderSubmitInfo, ShoppingListCollectionParam shoppingListCollectionParam, List<DomainCollectionParam> domainCollectionParamList) {
        List<SkuItemValueObject> skuItemVoList = orderSubmitInfo.getSkuItemVoList();
        String userPin = orderSubmitInfo.getUserPin();
        Relation relation = new Relation(String.valueOf(userPin.hashCode()));
        if (CollectionUtils.isEmpty(skuItemVoList)) {
            return relation;
        }
        ProductCollectionParam productCollectionParam = new ProductCollectionParam();
        QuantityCollectionParam quantityCollectionParam = new QuantityCollectionParam();
        VenderCollectionParam venderCollectionParam = new VenderCollectionParam();
        PriceCollectionParam priceCollectionParam = new PriceCollectionParam();


        // 构建整体的relation（关系表），默认一个子单一个包裹
        // 构造默认的子单
        BasketRelation basket = new BasketRelation();
        // 绑定子单到整单上
        relation.putBasketToOrder(new OrderRelation(relation.obtainOrder()), basket);
        // 构造默认的包裹
        BundleRelation defaultBundle = new BundleRelation();
        // 绑定包裹到子单上
        relation.putBundleToBasket(basket, defaultBundle);
        BundleRelation bundleRelation = new BundleRelation(relation.obtainAllBundle().iterator().next());

        for (SkuItemValueObject skuItemVo : skuItemVoList) {
            AbstractShoppingItemParam abstractShoppingItemParam = new DefaultShoppingItemParam();
            abstractShoppingItemParam.setItemType("1");
            relation.putShoppingItemToBundle(bundleRelation, abstractShoppingItemParam);

            AbstractQuantityItemParam abstractQuantityItemParam = new DefaultQuantityItemParam();
            AbstractProductItemParam abstractProductItemParam = new DefaultProductItemParam();
            AbstractPriceItemParam abstractPriceItemParam = new DefaultPriceItemParam();
            AbstractVenderItemParam abstractVenderItemParam = new DefaultVenderItemParam();
            buildAbstractProductItemParam(skuItemVo, abstractProductItemParam);
            abstractQuantityItemParam.setNum(BigDecimal.valueOf(skuItemVo.getBuyNum()));
            abstractQuantityItemParam.setBuyNum(BigDecimal.valueOf(skuItemVo.getBuyNum()));
            abstractVenderItemParam.setVenderId(String.valueOf(skuItemVo.getVenderId()));
            abstractPriceItemParam.setPrice(new BigDecimal(skuItemVo.getPrice()));
            // 绑定商品到对应的清单上
            relation.bindToShoppingItem(abstractProductItemParam, abstractShoppingItemParam);
            relation.bindToShoppingItem(abstractQuantityItemParam, abstractShoppingItemParam);
            relation.bindToShoppingItem(abstractVenderItemParam, abstractShoppingItemParam);
            relation.bindToShoppingItem(abstractPriceItemParam, abstractShoppingItemParam);

            productCollectionParam.addChild(abstractProductItemParam);
            quantityCollectionParam.addChild(abstractQuantityItemParam);
            venderCollectionParam.addChild(abstractVenderItemParam);
            priceCollectionParam.addChild(abstractPriceItemParam);
            shoppingListCollectionParam.addChild(abstractShoppingItemParam);
        }
        domainCollectionParamList.add(productCollectionParam);
        domainCollectionParamList.add(quantityCollectionParam);
        domainCollectionParamList.add(venderCollectionParam);
        domainCollectionParamList.add(priceCollectionParam);

        return relation;
    }

    /**
     * 处理sku信息
     *
     * @param orderTradeInfo              提单
     * @param shoppingListCollectionParam shoppingListCollectionParam
     * @param domainCollectionParamList   domainCollectionParamList
     */
    private Relation handlerSkuInfoForTrade(OrderTradeInfoContext orderTradeInfo, ShoppingListCollectionParam shoppingListCollectionParam, List<DomainCollectionParam> domainCollectionParamList) {
        List<SkuItemValueObject> skuItemVoList = orderTradeInfo.getSkuItemVoList();
        Relation relation = new Relation();
        if (CollectionUtils.isEmpty(skuItemVoList)) {
            return relation;
        }
        ProductCollectionParam productCollectionParam = new ProductCollectionParam();
        QuantityCollectionParam quantityCollectionParam = new QuantityCollectionParam();

        // 构建整体的relation（关系表），默认一个子单一个包裹
        // 构造默认的子单
        BasketRelation basket = new BasketRelation();
        // 绑定子单到整单上
        relation.putBasketToOrder(new OrderRelation(relation.obtainOrder()), basket);
        // 构造默认的包裹
        BundleRelation defaultBundle = new BundleRelation();
        // 绑定包裹到子单上
        relation.putBundleToBasket(basket, defaultBundle);
        BundleRelation bundleRelation = new BundleRelation(relation.obtainAllBundle().iterator().next());

        for (SkuItemValueObject skuItemVo : skuItemVoList) {
            AbstractShoppingItemParam abstractShoppingItemParam = new DefaultShoppingItemParam();
            abstractShoppingItemParam.setItemType("1");
            relation.putShoppingItemToBundle(bundleRelation, abstractShoppingItemParam);

            AbstractQuantityItemParam abstractQuantityItemParam = new DefaultQuantityItemParam();
            AbstractProductItemParam abstractProductItemParam = new DefaultProductItemParam();

            buildAbstractProductItemParam(skuItemVo, abstractProductItemParam);
            abstractQuantityItemParam.setNum(BigDecimal.valueOf(skuItemVo.getBuyNum()));
            abstractQuantityItemParam.setBuyNum(BigDecimal.valueOf(skuItemVo.getBuyNum()));

            // 绑定商品到对应的清单上
            relation.bindToShoppingItem(abstractProductItemParam, abstractShoppingItemParam);
            relation.bindToShoppingItem(abstractQuantityItemParam, abstractShoppingItemParam);

            productCollectionParam.addChild(abstractProductItemParam);
            quantityCollectionParam.addChild(abstractQuantityItemParam);
            shoppingListCollectionParam.addChild(abstractShoppingItemParam);
        }
        domainCollectionParamList.add(productCollectionParam);
        domainCollectionParamList.add(quantityCollectionParam);

        return relation;
    }

    /**
     * 组装商品信息
     *
     * @param skuItemVo                skuItemVo
     * @param abstractProductItemParam abstractProductItemParam
     */
    private void buildAbstractProductItemParam(SkuItemValueObject skuItemVo, AbstractProductItemParam abstractProductItemParam) {
        abstractProductItemParam.setSkuId(skuItemVo.getSkuId());
        // 传cartxml的扩展节点，对应submitOrderUnNecessary：skuExtInfoMap
        HJBProductItemAttrDecoratorParam itemAttrDecoratorParam = (HJBProductItemAttrDecoratorParam) AdapterManagerImpl.getInstance().getAdapter(abstractProductItemParam, HJBProductItemAttrDecoratorParam.class);
        HashMap<String, String> cartExtendMap = new HashMap<>();
        Map<String, Object> extInfoMap = skuItemVo.getExtInfoMap();
        if (MapUtils.isNotEmpty(extInfoMap)) {
            for (String key : extInfoMap.keySet()) {
                cartExtendMap.put(key, (String) extInfoMap.get(key));
            }
        }
        //cart xml 扩展节点
        itemAttrDecoratorParam.setConvertXMLProperties(cartExtendMap);
        itemAttrDecoratorParam.setConvertXMLProperties(cartExtendMap);
    }


    private CouponCollectionParam buildCouponCollectionParam(OrderUserActionContext orderUserActionInfo, ShoppingListCollectionParam shoppingListCollectionParam, Relation relation) {
        String userActionType = orderUserActionInfo.getUserActionType();
        if (UserActionEnum.COUPON_SELECT.getUserActionType().equals(userActionType) || UserActionEnum.COUPON_DESELECT.getUserActionType().equals(userActionType) || UserActionEnum.COUPON_QUERY_LIST.getUserActionType().equals(userActionType)) {
            return null;
        }
        List<CouponTradeInfoValueObject> couponTradeInfoList = orderUserActionInfo.getCouponTradeInfoList();
        if (CollectionUtils.isEmpty(couponTradeInfoList)) {
            return null;
        }
        return couponUserActionParamAssembler.buildCouponCollectionParam(couponTradeInfoList);
    }


}
