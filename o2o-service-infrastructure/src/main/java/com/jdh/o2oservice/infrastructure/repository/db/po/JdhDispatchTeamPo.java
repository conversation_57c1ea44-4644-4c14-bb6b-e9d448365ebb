package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName JdhDispatchTeamPo
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 15:25
 **/
@Data
@TableName(value = "jdh_dispatch_team",autoResultMap = true)
public class JdhDispatchTeamPo extends JdhBasicPo{

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 派单小队ID
     */
    private Long dispatchTeamId;

    /**
     * 派单小队名称
     */
    private String dispatchTeamName;

    /**
     * 派单小队状态：0-停用 1-启用
     */
    private Integer dispatchTeamStatus;

    /**
     * 备注
     */
    private String dispatchTeamRemark;

    /**
     * 小队负责人
     */
    private String dispatchTeamLeader;
}