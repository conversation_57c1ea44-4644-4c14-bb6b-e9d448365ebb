package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchTeam;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchTeamAngelRel;
import com.jdh.o2oservice.core.domain.dispatch.model.JdhDispatchTeamSkillRel;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhDispatchTeamAngelRelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhDispatchTeamPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhDispatchTeamSkillRelPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @ClassName JdhDispatchTeamInfrastructureConverter
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 16:54
 **/
@Mapper
public interface JdhDispatchTeamInfrastructureConverter {

    /**
     *
     */
    JdhDispatchTeamInfrastructureConverter INSTANCE = Mappers.getMapper(JdhDispatchTeamInfrastructureConverter.class);

    /**
     *
     * @param dispatchTeam
     * @return
     */
    JdhDispatchTeamPo entity2Po(JdhDispatchTeam dispatchTeam);

    /**
     *
     * @param dispatchTeamPo
     * @return
     */
    JdhDispatchTeam po2Entity(JdhDispatchTeamPo dispatchTeamPo);

    /**
     *
     * @param list
     * @return
     */
    List<JdhDispatchTeam> po2Entity(List<JdhDispatchTeamPo> list);

    /**
     *
     * @param teamSkillRelPo
     * @return
     */
    JdhDispatchTeamSkillRel po2TeamSkillEntity(JdhDispatchTeamSkillRelPo teamSkillRelPo);

    /**
     *
     * @param teamSkillRelPoList
     * @return
     */
    List<JdhDispatchTeamSkillRel> po2TeamSkillEntity(List<JdhDispatchTeamSkillRelPo> teamSkillRelPoList);

    /**
     *
     * @param teamSkillRel
     * @return
     */
    JdhDispatchTeamSkillRelPo entity2TeamSkillPo(JdhDispatchTeamSkillRel teamSkillRel);

    /**
     *
     * @param teamSkillRelList
     * @return
     */
    List<JdhDispatchTeamSkillRelPo> entity2TeamSkillPo(List<JdhDispatchTeamSkillRel> teamSkillRelList);

    /**
     *
     * @param teamAngelRelPo
     * @return
     */
    JdhDispatchTeamAngelRel po2TeamAngelEntity(JdhDispatchTeamAngelRelPo teamAngelRelPo);

    /**
     *
     * @param teamAngelRelPoList
     * @return
     */
    List<JdhDispatchTeamAngelRel> po2TeamAngelEntity(List<JdhDispatchTeamAngelRelPo> teamAngelRelPoList);

    /**
     *
     * @param teamAngelRel
     * @return
     */
    JdhDispatchTeamAngelRelPo entity2TeamAngelPo(JdhDispatchTeamAngelRel teamAngelRel);

    /**
     *
     * @param teamAngelRelList
     * @return
     */
    List<JdhDispatchTeamAngelRelPo> entity2TeamAngelPo(List<JdhDispatchTeamAngelRel> teamAngelRelList);
}