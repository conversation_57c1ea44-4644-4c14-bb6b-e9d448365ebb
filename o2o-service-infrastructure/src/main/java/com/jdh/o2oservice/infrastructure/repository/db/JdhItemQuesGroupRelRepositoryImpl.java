package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.product.model.JdhItemQuesGroupRel;
import com.jdh.o2oservice.core.domain.product.model.JdhItemQuesGroupRelIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhItemQuesGroupRelRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhItemQuesGroupRelPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhQuestionPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhItemQuesGroupRelMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhItemQuesGroupRelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhQuestionPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Repository
@Slf4j
public class JdhItemQuesGroupRelRepositoryImpl implements JdhItemQuesGroupRelRepository {


    @Autowired
    private JdhItemQuesGroupRelMapper jdhItemQuesGroupRelMapper;



    @Override
    public List<JdhItemQuesGroupRel> findList(JdhItemQuesGroupRel jdhItemQuesGroupRel){
        LambdaQueryWrapper<JdhItemQuesGroupRelPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JdhItemQuesGroupRelPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.eq(JdhItemQuesGroupRelPo::getServiceItemId,jdhItemQuesGroupRel.getServiceItemId());
        List<JdhItemQuesGroupRelPo> jdhItemQuesGroupRelPos = jdhItemQuesGroupRelMapper.selectList(queryWrapper);
        return JdhItemQuesGroupRelPoConverter.INSTANCE.toJdhItemQuesGroupRels(jdhItemQuesGroupRelPos);
    }

    @Override
    public JdhItemQuesGroupRel findByCodeAndItemId(Long serviceItemId, String code) {
        LambdaQueryWrapper<JdhItemQuesGroupRelPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JdhItemQuesGroupRelPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.eq(JdhItemQuesGroupRelPo::getServiceItemId,serviceItemId);
        queryWrapper.eq(JdhItemQuesGroupRelPo::getGroupCode,code);
        JdhItemQuesGroupRelPo jdhItemQuesGroupRelPo = jdhItemQuesGroupRelMapper.selectOne(queryWrapper);
        return JdhItemQuesGroupRelPoConverter.INSTANCE.toJdhItemQuesGroupRel(jdhItemQuesGroupRelPo);
    }

    @Override
    public Boolean update(JdhItemQuesGroupRel jdhItemQuesGroupRelUpdate) {
        //where条件
        LambdaUpdateWrapper<JdhItemQuesGroupRelPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(JdhItemQuesGroupRelPo::getServiceItemId,jdhItemQuesGroupRelUpdate.getServiceItemId());
        updateWrapper.eq(JdhItemQuesGroupRelPo::getGroupCode,jdhItemQuesGroupRelUpdate.getGroupCode());

        //修改数据
        JdhItemQuesGroupRelPo jdhItemQuesGroupRelPo = JdhItemQuesGroupRelPoConverter.INSTANCE.toJdhItemQuesGroupRelPo(jdhItemQuesGroupRelUpdate);
        jdhItemQuesGroupRelPo.setUpdateTime(new Date());
        updateWrapper.setSql("`version` = `version` + 1");

        return jdhItemQuesGroupRelMapper.update(jdhItemQuesGroupRelPo,updateWrapper)>0;
    }


    @Override
    public JdhItemQuesGroupRel find(JdhItemQuesGroupRelIdentifier jdhItemQuesGroupRelIdentifier) {
        return null;
    }

    @Override
    public int remove(JdhItemQuesGroupRel entity) {
        return 0;
    }

    @Override
    public int save(JdhItemQuesGroupRel entity) {
        JdhItemQuesGroupRelPo jdhItemQuesGroupRelPo = JdhItemQuesGroupRelPoConverter.INSTANCE.toJdhItemQuesGroupRelPo(entity);
        return jdhItemQuesGroupRelMapper.insert(jdhItemQuesGroupRelPo);
    }
}
