package com.jdh.o2oservice.infrastructure.repository.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhDispatchTeamSkillRelPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName JdhDispatchTeamSkillRelPoMapper
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 15:47
 **/
@Mapper
public interface JdhDispatchTeamSkillRelPoMapper extends BaseMapper<JdhDispatchTeamSkillRelPo> {

    /**
     * 批量插入
     *
     * @param list
     * @return {@link Integer}
     */
    Integer batchInsert(@Param("list") List<JdhDispatchTeamSkillRelPo> list);
}