package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.infrastructure.repository.db.po.*;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import titan.profiler.shade.com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * JdhPromiseInfrastructureConverter
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Mapper
public interface PromiseConverter {

    /**
     * JdhPromiseInfrastructureConverter
     */
    PromiseConverter INSTANCE = Mappers.getMapper(PromiseConverter.class);

    /**
     *
     * @param promiseService
     * @return
     */
    PromiseService servicePo2PromiseService(JdhPromiseServiceDetailPo promiseService);

    /**
     * 患者信息转实体
     * @param patientPo
     * @return
     */
    default JdhPromisePatient convert2PromisePatient(JdhPromisePatientPo patientPo){

        JdhPromisePatient user = new JdhPromisePatient();
        user.setUserPin(patientPo.getUserPin());
        user.setPatientId(patientPo.getPatientId());
        user.initBirthday(patientPo.getBirthday());
        user.setGender(patientPo.getUserGender());
        user.setMarriage(patientPo.getMarriage());
        user.initUserName(patientPo.getUserName());
        user.initPhoneNumber(patientPo.getUserPhone());
        user.initCredentialNum(patientPo.getUserCredentialType(), patientPo.getUserCredentialNo());
        user.setRelativesType(patientPo.getRelativesType());
        user.setPromisePatientId(patientPo.getPromisePatientId());
        user.setPromiseId(patientPo.getPromiseId());
        user.setVersion(patientPo.getVersion());
        user.setId(patientPo.getId());
        return user;
    }

    /**
     * 履约单转换
     * @param promisePo
     * @return
     */
    JdhPromise promisePo2Promise(JdhPromisePo promisePo);

    /**
     *
     * @param promisePo
     * @return
     */
    List<JdhPromisePatient> convert2PromisePatient(List<JdhPromisePatientPo> promisePo);
    /**
     * 履约单PO转聚合
     * @param promisePo
     * @return
     */
    default JdhPromise convert2ProviderPromise(JdhPromisePo promisePo){
        if (promisePo == null){
            return null;
        }
        JdhPromise promise = INSTANCE.promisePo2Promise(promisePo);
        promise.setAppointmentId(promisePo.getAppointmentId());
        promise.setChannelNo(promisePo.getChannelNo());
        promise.setChannelAppointmentId(promisePo.getChannelAppointmentId());
        promise.setAppointmentPhone(promisePo.getAppointmentPhone());

        // 预约时间
        PromiseAppointmentTime appointmentTime = new PromiseAppointmentTime();
        appointmentTime.setDateType(promisePo.getDateType());
        if (Objects.nonNull(promisePo.getAppointmentStartTime())){
            appointmentTime.setAppointmentStartTime(TimeUtils.dateToLocalDateTime(promisePo.getAppointmentStartTime()));
        }
        if (Objects.nonNull(promisePo.getAppointmentEndTime())){
            appointmentTime.setAppointmentEndTime(TimeUtils.dateToLocalDateTime(promisePo.getAppointmentEndTime()));
        }
        if (Objects.equals(promisePo.getImmediately(), YnStatusEnum.YES.getCode())){
            appointmentTime.setIsImmediately(Boolean.TRUE);
        }else{
            appointmentTime.setIsImmediately(Boolean.FALSE);
        }
        promise.setAppointmentTime(appointmentTime);


        PromiseStation store = new PromiseStation();
        store.setChannelNo(promisePo.getChannelNo());
        store.setStoreId(promisePo.getStoreId());
        store.setStoreName(promisePo.getStoreName());
        store.setStoreAddr(promisePo.getStoreAddr());
        store.setStorePhone(promisePo.getStorePhone());

        store.setProvinceCode(promisePo.getProvinceCode());
        store.setCityCode(promisePo.getCityCode());
        store.setDistrictCode(promisePo.getDistrictCode());
        store.setTownCode(promisePo.getTownCode());

        store.setProvinceName(promisePo.getProvinceName());
        store.setCityName(promisePo.getCityName());
        store.setDistrictName(promisePo.getDistrictName());
        store.setTownName(promisePo.getTownName());

        promise.setStore(store);
        return promise;
    }

    /**
     *
     * @param promise
     * @param serviceDetailPoList
     * @return
     */
    default JdhPromise convertPromiseServiceList(JdhPromise promise, List<JdhPromiseServiceDetailPo> serviceDetailPoList){
        if (promise == null || CollectionUtils.isEmpty(serviceDetailPoList)){
            return promise;
        }
        List<PromiseService> services = Lists.newArrayListWithExpectedSize(serviceDetailPoList.size());
        for (JdhPromiseServiceDetailPo jdhPromiseServiceDetailPo : serviceDetailPoList) {
            PromiseService service = INSTANCE.servicePo2PromiseService(jdhPromiseServiceDetailPo);
            services.add(service);
        }
        promise.setServices(services);
        return promise;
    }

    /**
     *
     * @param promise
     * @param patientPos
     * @return
     */
    default JdhPromise convertPromisePatientList(JdhPromise promise, List<JdhPromisePatientPo> patientPos){
        if (promise == null || CollectionUtils.isEmpty(patientPos)){
            return promise;
        }
        List<JdhPromisePatient> patients = Lists.newArrayListWithExpectedSize(patientPos.size());
        for (JdhPromisePatientPo patientPo : patientPos) {
            JdhPromisePatient patient = INSTANCE.convert2PromisePatient(patientPo);
            patients.add(patient);
        }
        promise.setPatients(patients);
        return promise;
    }

    /**
     * extendPo2Entity
     *
     * @param extendPo extendPo
     * @return {@link JdhPromiseExtend}
     */
    default JdhPromiseExtend extendPo2Entity(JdhPromiseExtendPo extendPo){
        if(Objects.isNull(extendPo)){
            return null;
        }
        JdhPromiseExtend jdhPromiseExtend = new JdhPromiseExtend();
        jdhPromiseExtend.setPromiseId(extendPo.getPromiseId());
        jdhPromiseExtend.setAttribute(extendPo.getAttribute());
        jdhPromiseExtend.setValue(extendPo.getValue());
        jdhPromiseExtend.setId(extendPo.getId());
        return jdhPromiseExtend;
    }

    /**
     * convertPromiseExtendList
     *
     * @param promise             promise
     * @param jdhPromiseExtendPos jdhPromiseExtendPos
     * @return {@link JdhPromise}
     */
    default JdhPromise convertPromiseExtendList(JdhPromise promise, List<JdhPromiseExtendPo> jdhPromiseExtendPos){
        if (promise == null || CollectionUtils.isEmpty(jdhPromiseExtendPos)){
            return promise;
        }
        List<JdhPromiseExtend> extendList = Lists.newArrayListWithExpectedSize(jdhPromiseExtendPos.size());
        for (JdhPromiseExtendPo jdhPromiseExtendPo : jdhPromiseExtendPos) {
            extendList.add(INSTANCE.extendPo2Entity(jdhPromiseExtendPo));
        }
        promise.setPromiseExtends(extendList);
        return promise;
    }

    /**
     * historyPo2Entity
     *
     * @param jdhPromiseHistoryPo jdhPromiseHistoryPo
     * @return {@link JdhPromiseHistory}
     */
    JdhPromiseHistory historyPo2Entity(JdhPromiseHistoryPo jdhPromiseHistoryPo);

}
