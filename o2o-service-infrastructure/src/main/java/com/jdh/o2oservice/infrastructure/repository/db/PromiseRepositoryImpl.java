package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jd.store.enums.YNEnum;
import com.jdh.o2oservice.base.constatnt.CacheConstant;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseExtQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepPageQuery;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhPromiseInfrastructureConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.PromiseConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.*;
import com.jdh.o2oservice.infrastructure.repository.db.po.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 履约单仓储实现
 * @author: yangxiyu
 * @date: 2024/4/24 5:48 下午
 * @version: 1.0
 */
@Component
@Slf4j
public class PromiseRepositoryImpl implements PromiseRepository {

    /**
     * environment
     */
    @Value("${spring.profiles.active}")
    private String environment;
    /**
     * jdhPromisePoMapper
     */
    @Resource
    private JdhPromisePoMapper jdhPromisePoMapper;


    /**
     * jdhPromiseServiceDetailPoMapper
     */
    @Resource
    private JdhPromiseServiceDetailPoMapper jdhPromiseServiceDetailPoMapper;
    /**
     * jdhPromisePatientPoMapper
     */
    @Resource
    private JdhPromisePatientPoMapper jdhPromisePatientPoMapper;

    /**
     * jdhPromiseExtendPoMapper
     */
    @Resource
    private JdhPromiseExtendPoMapper jdhPromiseExtendPoMapper;

    /**
     * jdhPromiseHistoryPoMapper
     */
    @Resource
    private JdhPromiseHistoryPoMapper jdhPromiseHistoryPoMapper;

    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * 删除
     *
     * @param aggregate
     */
    @Override
    public int remove(JdhPromise aggregate) {
        log.info("PromiseRepositoryImpl -> remove aggregate:{}", JSON.toJSONString(aggregate));
        LambdaUpdateWrapper<JdhPromisePo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.setSql("`version` = `version` + 1").set(JdhPromisePo::getYn, YnStatusEnum.NO.getCode())
                .set(JdhPromisePo::getUpdateTime, new Date())
                .eq(JdhPromisePo::getPromiseId, aggregate.getPromiseId());
        return jdhPromisePoMapper.update(null, updateWrapper);
    }


    /**
     * 保存
     *
     * @param jdhPromise
     */
    @SuppressWarnings("JdJDMethodBodyCount")
    @Override
    @CacheEvict(cacheNames = CacheConstant.PROMISE_CACHE,key = "#jdhPromise.getIdentifier().serialize()",condition = "#jdhPromise.getId() != null")
    public int save(JdhPromise jdhPromise) {
        JdhPromisePo jdhPromisePo = JdhPromiseInfrastructureConverter.INSTANCE.entity2Po(jdhPromise);
        if (Objects.isNull(jdhPromisePo.getId())) {
            JdhBasicPoConverter.initInsertBasicPo(jdhPromisePo);
            int count = jdhPromisePoMapper.insert(jdhPromisePo);
            List<PromiseService> serviceDetailList = jdhPromise.getServices();
            for (PromiseService serviceDetail : serviceDetailList) {
                JdhPromiseServiceDetailPo serviceDetailPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2Po(serviceDetail);
                JdhBasicPoConverter.initInsertBasicPo(serviceDetailPo);
                jdhPromiseServiceDetailPoMapper.insert(serviceDetailPo);
            }

            List<JdhPromisePatient> patients = jdhPromise.getPatients();
            if(CollUtil.isNotEmpty(patients)){
                for (JdhPromisePatient patient : patients) {
                    JdhPromisePatientPo patientPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2PatientPo(patient, jdhPromise);
                    JdhBasicPoConverter.initInsertBasicPo(patientPo);
                    jdhPromisePatientPoMapper.insert(patientPo);
                }
            }

            List<JdhPromiseExtend> promiseExtends = jdhPromise.getPromiseExtends();
            if(CollUtil.isNotEmpty(promiseExtends)){
                for (JdhPromiseExtend promiseExtend : promiseExtends) {
                    JdhPromiseExtendPo extendPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2ExtendPo(promiseExtend);
                    JdhBasicPoConverter.initInsertBasicTimePo(extendPo);
                    jdhPromiseExtendPoMapper.insert(extendPo);
                }
            }

            return count;
        }else{
            Integer oldVersion = jdhPromise.getVersion();
            // 版本加1
            jdhPromise.versionIncrease();
            jdhPromisePo.setBranch(environment);
            jdhPromisePo.setUpdateTime(new Date());
            jdhPromisePo.setVersion(jdhPromise.getVersion());

            LambdaUpdateWrapper<JdhPromisePo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(JdhPromisePo::getVersion,oldVersion)
                    .eq(JdhPromisePo::getPromiseId,jdhPromisePo.getPromiseId());

            int count = jdhPromisePoMapper.update(jdhPromisePo,updateWrapper);
            List<PromiseService> serviceDetailList = jdhPromise.getServices();
            for (PromiseService serviceDetail : serviceDetailList) {
                JdhPromiseServiceDetailPo serviceDetailPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2Po(serviceDetail);
                LambdaUpdateWrapper<JdhPromiseServiceDetailPo> serviceWrapper = Wrappers.lambdaUpdate();
                Integer serviceVersion = serviceDetail.getVersion();

                serviceWrapper.set(JdhPromiseServiceDetailPo::getVersion, serviceVersion + 1)
                        .eq(JdhPromiseServiceDetailPo::getVersion, serviceVersion)
                        .eq(JdhPromiseServiceDetailPo::getPromiseId,serviceDetail.getPromiseId())
                        .eq(JdhPromiseServiceDetailPo::getServiceId, serviceDetail.getServiceId());
                jdhPromiseServiceDetailPoMapper.update(serviceDetailPo, serviceWrapper);
            }

            List<JdhPromisePatient> patients = jdhPromise.getPatients();
            if (CollectionUtils.isNotEmpty(patients)){
                for (JdhPromisePatient patient : patients) {
                    JdhPromisePatientPo patientPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2PatientPo(patient, jdhPromise);
                    if (Objects.nonNull(patientPo.getId())){
                        LambdaUpdateWrapper<JdhPromisePatientPo> patientWrapper = Wrappers.lambdaUpdate();
                        patientWrapper
                                .eq(JdhPromisePatientPo::getPromiseId,patientPo.getPromiseId())
                                .eq(JdhPromisePatientPo::getPromisePatientId, patientPo.getPromisePatientId());
                        jdhPromisePatientPoMapper.update(patientPo, patientWrapper);
                    }else{
                        JdhBasicPoConverter.initInsertBasicPo(patientPo);
                        jdhPromisePatientPoMapper.insert(patientPo);
                    }

                }
            }


            List<JdhPromiseExtend> extendList = jdhPromise.getPromiseExtends();
            if(CollUtil.isNotEmpty(extendList)){
                for (JdhPromiseExtend promiseExtend : extendList) {
                    JdhPromiseExtendPo extendPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2ExtendPo(promiseExtend);
                    //更新
                    if(Objects.nonNull(promiseExtend.getId())){
                        LambdaUpdateWrapper<JdhPromiseExtendPo> extendLambdaUpdateWrapper = Wrappers.lambdaUpdate();
                        extendLambdaUpdateWrapper
                                .set(JdhPromiseExtendPo::getValue,extendPo.getValue())
                                .eq(JdhPromiseExtendPo::getPromiseId,extendPo.getPromiseId())
                                .eq(JdhPromiseExtendPo::getAttribute,extendPo.getAttribute());
                        jdhPromiseExtendPoMapper.update(extendPo, extendLambdaUpdateWrapper);
                    }else{
                        //新增
                        JdhBasicPoConverter.initInsertBasicTimePo(extendPo);
                        jdhPromiseExtendPoMapper.insert(extendPo);
                    }
                }
            }
            return count;
        }
    }

    /**
     * 通过Identify 查询
     *
     */
    @Override
//    @Cacheable(cacheNames = CacheConstant.PROMISE_CACHE,key = "#identifier.serialize()")
    public JdhPromise find(JdhPromiseIdentifier identifier) {
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhPromisePo::getPromiseId,identifier.getPromiseId());
        queryWrapper.eq(JdhPromisePo::getYn,YnStatusEnum.YES.getCode());
        JdhPromisePo po = jdhPromisePoMapper.selectOne(queryWrapper);
        return wrap(po);
    }

    /**
     * batchSave
     *
     * @param list 列表
     * @return int
     */
    @Override
    public int batchSave(List<JdhPromise> list) {
        for (JdhPromise jdhPromise : list) {
            JdhPromisePo jdhPromisePo = JdhPromiseInfrastructureConverter.INSTANCE.entity2Po(jdhPromise);
            JdhBasicPoConverter.initInsertBasicPo(jdhPromisePo);
            jdhPromisePoMapper.insert(jdhPromisePo);
            List<PromiseService> serviceDetailList = jdhPromise.getServices();
            if(CollUtil.isNotEmpty(serviceDetailList)){
                for (PromiseService serviceDetail : serviceDetailList) {
                    JdhPromiseServiceDetailPo serviceDetailPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2Po(serviceDetail);
                    JdhBasicPoConverter.initInsertBasicPo(serviceDetailPo);
                    jdhPromiseServiceDetailPoMapper.insert(serviceDetailPo);
                }
            }

            List<JdhPromisePatient> patients = jdhPromise.getPatients();
            if(CollUtil.isNotEmpty(patients)){
                for (JdhPromisePatient patient : patients) {
                    JdhPromisePatientPo patientPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2PatientPo(patient, jdhPromise);
                    JdhBasicPoConverter.initInsertBasicPo(patientPo);
                    jdhPromisePatientPoMapper.insert(patientPo);
                }
            }


            List<JdhPromiseExtend> promiseExtends = jdhPromise.getPromiseExtends();
            if(CollUtil.isNotEmpty(promiseExtends)){
                for (JdhPromiseExtend promiseExtend : promiseExtends) {
                    JdhPromiseExtendPo extendPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2ExtendPo(promiseExtend);
                    JdhBasicPoConverter.initInsertBasicTimePo(extendPo);
                    jdhPromiseExtendPoMapper.insert(extendPo);
                }
            }
        }
        return list.size();
    }

    /**
     * findList
     *
     * @param query 查询
     * @return {@link List}<{@link JdhPromise}>
     */
    @Override
    public List<JdhPromise> findList(PromiseRepQuery query) {
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getPromiseId()),JdhPromisePo::getPromiseId,query.getPromiseId());
        queryWrapper.eq(StrUtil.isNotEmpty(query.getUserPin()),JdhPromisePo::getUserPin,query.getUserPin());
        queryWrapper.eq(StrUtil.isNotEmpty(query.getSourceVoucherId()),JdhPromisePo::getSourceVoucherId,query.getSourceVoucherId());
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getSourceVoucherIdList()),JdhPromisePo::getSourceVoucherId,query.getSourceVoucherIdList());
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getVoucherIds()),JdhPromisePo::getVoucherId,query.getVoucherIds());
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getPromiseIds()),JdhPromisePo::getPromiseId,query.getPromiseIds());
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getPromiseStatusList()), JdhPromisePo::getPromiseStatus, query.getPromiseStatusList());


        if (CollectionUtils.isEmpty(queryWrapper.getExpression().getNormal())){
            return Collections.emptyList();
        }
        queryWrapper.eq(JdhPromisePo::getYn,YnStatusEnum.YES.getCode());
        List<JdhPromisePo> jdhPromisePos = jdhPromisePoMapper.selectList(queryWrapper);

        if(CollUtil.isNotEmpty(jdhPromisePos)){
//            List<Long> promiseIds = jdhPromisePos.stream().map(JdhPromisePo::getPromiseId).collect(Collectors.toList());
//            Map<Long, List<JdhPromiseServiceDetailPo>> serviceMap = getServiceMap(promiseIds);
//            Map<Long, List<JdhPromisePatientPo>> patientMap = getPatientMap(promiseIds);
           /* List<JdhPromise> res = Lists.newArrayListWithExpectedSize(jdhPromisePos.size());
            for (JdhPromisePo jdhPromisePo : jdhPromisePos) {
                JdhPromise wrap = wrap(jdhPromisePo);
//                JdhPromise promise = PromiseConverter.INSTANCE.convert2ProviderPromise(jdhPromisePo);
//                promise = PromiseConverter.INSTANCE.convertPromiseServiceList(promise, serviceMap.get(jdhPromisePo.getPromiseId()));
                res.add(wrap);
            }
            return res;*/
            return wraps(jdhPromisePos, query);
        }
        return null;
    }

    /**
     * 只查询预约单
     *
     * @param query
     * @return
     */
    @Override
    public List<JdhPromise> findPromiseList(PromiseRepQuery query) {
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getPromiseId()),JdhPromisePo::getPromiseId,query.getPromiseId());
        queryWrapper.eq(StrUtil.isNotEmpty(query.getUserPin()),JdhPromisePo::getUserPin,query.getUserPin());
        queryWrapper.eq(StrUtil.isNotEmpty(query.getSourceVoucherId()),JdhPromisePo::getSourceVoucherId,query.getSourceVoucherId());
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getSourceVoucherIdList()),JdhPromisePo::getSourceVoucherId,query.getSourceVoucherIdList());
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getVoucherIds()),JdhPromisePo::getVoucherId,query.getVoucherIds());
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getPromiseIds()),JdhPromisePo::getPromiseId,query.getPromiseIds());
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getPromiseStatusList()), JdhPromisePo::getPromiseStatus, query.getPromiseStatusList());

        List<JdhPromisePo> jdhPromisePos = jdhPromisePoMapper.selectList(queryWrapper);

        List<JdhPromise> jdhPromiseList = Lists.newArrayList();
        if(CollUtil.isEmpty(jdhPromisePos)){
            return jdhPromiseList;
        }
        for (JdhPromisePo jdhPromisePo : jdhPromisePos) {
            JdhPromise promise = PromiseConverter.INSTANCE.convert2ProviderPromise(jdhPromisePo);
            jdhPromiseList.add(promise);
        }
        return jdhPromiseList;
    }

    /**
     * 查询扩展信息
     *
     * @param query
     * @return
     */
    @Override
    public List<JdhPromiseExtend> findPromiseExtList(PromiseExtQuery query) {
        if(Objects.isNull(query) || Objects.isNull(query.getPromiseId())) {
            log.error("PromiseRepositoryImpl -> findPromiseExtList, 查询参数错误");
            return Collections.emptyList();
        }

        List<JdhPromiseExtend> extendList = Lists.newArrayList();
        LambdaQueryWrapper<JdhPromiseExtendPo> detailQueryWrapper = Wrappers.lambdaQuery();
        detailQueryWrapper.eq(JdhPromiseExtendPo::getPromiseId, query.getPromiseId())
                .in(CollectionUtils.isNotEmpty(query.getAttributeCode()), JdhPromiseExtendPo::getAttribute, query.getAttributeCode())
                .eq(JdhPromiseExtendPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhPromiseExtendPo> jdhPromiseExtendPos = jdhPromiseExtendPoMapper.selectList(detailQueryWrapper);

        if(CollUtil.isEmpty(jdhPromiseExtendPos)){
            return extendList;
        }

        for (JdhPromiseExtendPo jdhPromisePo : jdhPromiseExtendPos) {
            JdhPromiseExtend jdhPromiseExtend = PromiseConverter.INSTANCE.extendPo2Entity(jdhPromisePo);
            extendList.add(jdhPromiseExtend);
        }
        return extendList;
    }

    /**
     * findJdhPromiseList
     *
     * @param query 查询
     * @return {@link List}<{@link JdhPromise}>
     */
    @Override
    public List<JdhPromise> findJdhPromiseList(PromiseRepQuery query) {
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getVerticalCodeList()),JdhPromisePo::getVerticalCode,query.getVerticalCodeList());
        queryWrapper.gt(Objects.nonNull(query.getAppointmentUmpTime()),JdhPromisePo::getAppointmentStartTime,query.getAppointmentUmpTime());
        queryWrapper.le(Objects.nonNull(query.getAppointmentUmpEndTime()),JdhPromisePo::getAppointmentStartTime,query.getAppointmentUmpEndTime());
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getPromiseStatusList()),JdhPromisePo::getPromiseStatus,query.getPromiseStatusList());
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getPromiseIds()),JdhPromisePo::getPromiseId, query.getPromiseIds());
        queryWrapper.eq(StrUtil.isNotEmpty(query.getSourceVoucherId()),JdhPromisePo::getSourceVoucherId,query.getSourceVoucherId());

        if (CollectionUtils.isEmpty(queryWrapper.getExpression().getNormal())){
            return Collections.emptyList();
        }
        queryWrapper.in(JdhPromisePo::getYn,YnStatusEnum.YES.getCode());
        List<JdhPromisePo> jdhPromisePos = jdhPromisePoMapper.selectList(queryWrapper);
        return wraps(jdhPromisePos, query);
    }
    /**
     * 查询 promise信息
     *
     * @param query 查询
     * @return {@link JdhPromise}
     */
    @Override
    public JdhPromise findPromise(PromiseRepQuery query){
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getAppointmentId()),JdhPromisePo::getAppointmentId, query.getAppointmentId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhPromisePo::getPromiseId,query.getPromiseId())
                .eq(StrUtil.isNotEmpty(query.getUserPin()),JdhPromisePo::getUserPin,query.getUserPin())
                .eq(StrUtil.isNotEmpty(query.getSourceVoucherId()),JdhPromisePo::getSourceVoucherId,query.getSourceVoucherId())
                .eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode());

        JdhPromisePo po = jdhPromisePoMapper.selectOne(queryWrapper);
        return wrap(po);
    }

    /**
     * 分页查询履约单
     * @param query
     * @return
     */
    @Override
    public Page<JdhPromise> page(PromiseRepPageQuery query) {
        log.info("PromiseRepositoryImpl->queryByPage query={}", JSON.toJSONString(query));
        Page<JdhPromisePo> param = new Page<>(query.getPageNum(), query.getPageSize());

        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(query.getPromiseId()),JdhPromisePo::getPromiseId,query.getPromiseId());
        queryWrapper.eq(StringUtils.isNotBlank(query.getUserPin()),JdhPromisePo::getUserPin,query.getUserPin());
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getVoucherIds()),JdhPromisePo::getVoucherId,query.getVoucherIds());
        queryWrapper.gt(Objects.nonNull(query.getCreateTimeStart()),JdhPromisePo::getCreateTime,query.getCreateTimeStart());
        queryWrapper.eq(StrUtil.isNotEmpty(query.getVerticalCode()),JdhPromisePo::getVerticalCode,query.getVerticalCode());
        queryWrapper.eq(StrUtil.isNotEmpty(query.getServiceType()),JdhPromisePo::getServiceType,query.getServiceType());

        IPage<JdhPromisePo> page = jdhPromisePoMapper.selectPage(param, queryWrapper);
        if (page == null || CollectionUtil.isEmpty(page.getRecords())){
            return null;
        }
        List<Long> promiseIds = page.getRecords().stream().map(JdhPromisePo::getPromiseId).collect(Collectors.toList());
        Map<Long, List<JdhPromiseServiceDetailPo>> serviceMap = getServiceMap(promiseIds);
        Map<Long, List<JdhPromisePatientPo>> patientMap = getPatientMap(promiseIds);
        List<JdhPromise> list = Lists.newArrayListWithExpectedSize(page.getRecords().size());
        for (JdhPromisePo jdhPromisePo : page.getRecords()) {
            JdhPromise promise = PromiseConverter.INSTANCE.convert2ProviderPromise(jdhPromisePo);
            promise = PromiseConverter.INSTANCE.convertPromiseServiceList(promise, serviceMap.get(jdhPromisePo.getPromiseId()));
            promise = PromiseConverter.INSTANCE.convertPromisePatientList(promise, patientMap.get(jdhPromisePo.getPromiseId()));
            list.add(promise);
        }
        return JdhBasicPoConverter.initPage(page, list);
    }

    /**
     * 获取服务信息
     * @param promiseIds
     * @return
     */
    private Map<Long, List<JdhPromiseServiceDetailPo>> getServiceMap(List<Long> promiseIds ){
        LambdaQueryWrapper<JdhPromiseServiceDetailPo> detailQueryWrapper = Wrappers.lambdaQuery();
        detailQueryWrapper.in(JdhPromiseServiceDetailPo::getPromiseId, promiseIds)
                .eq(JdhPromiseServiceDetailPo::getYn, YnStatusEnum.YES.getCode());

        List<JdhPromiseServiceDetailPo> serviceDetailPos = jdhPromiseServiceDetailPoMapper.selectList(detailQueryWrapper);
        return serviceDetailPos.stream().collect(Collectors.groupingBy(JdhPromiseServiceDetailPo::getPromiseId));
    }

    /**
     * 获取检测人信息
     * @param promiseIds
     * @return
     */
    private Map<Long, List<JdhPromisePatientPo>> getPatientMap(List<Long> promiseIds ){
        LambdaQueryWrapper<JdhPromisePatientPo> detailQueryWrapper = Wrappers.lambdaQuery();
        detailQueryWrapper.in(JdhPromisePatientPo::getPromiseId, promiseIds)
                .eq(JdhPromisePatientPo::getYn, YnStatusEnum.YES.getCode());

        List<JdhPromisePatientPo> patientPos = jdhPromisePatientPoMapper.selectList(detailQueryWrapper);
        return patientPos.stream().collect(Collectors.groupingBy(JdhPromisePatientPo::getPromiseId));
    }

    /**
     * 获取扩展信息
     *
     * @param promiseIds promiseIds
     * @return {@link Map}<{@link Long}, {@link List}<{@link JdhPromiseExtendPo}>>
     */
    private Map<Long, List<JdhPromiseExtendPo>> getExtendMap(List<Long> promiseIds) {
        LambdaQueryWrapper<JdhPromiseExtendPo> detailQueryWrapper = Wrappers.lambdaQuery();
        detailQueryWrapper.in(JdhPromiseExtendPo::getPromiseId, promiseIds)
                .eq(JdhPromiseExtendPo::getYn, YnStatusEnum.YES.getCode());

        List<JdhPromiseExtendPo> jdhPromiseExtendPos = jdhPromiseExtendPoMapper.selectList(detailQueryWrapper);
        return jdhPromiseExtendPos.stream().collect(Collectors.groupingBy(JdhPromiseExtendPo::getPromiseId));
    }

    /**
     * 获取history信息
     *
     * @param promiseIds promiseIds
     * @return {@link Map}<{@link Long}, {@link List}<{@link JdhPromiseExtendPo}>>
     */
    private Map<Long, List<JdhPromiseHistoryPo>> getHistoryMap(ArrayList<Long> promiseIds) {
        LambdaQueryWrapper<JdhPromiseHistoryPo> historyQueryWrapper = Wrappers.lambdaQuery();
        historyQueryWrapper.in(JdhPromiseHistoryPo::getPromiseId, promiseIds)
                .eq(JdhPromiseHistoryPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhPromiseHistoryPo> promiseHistoryPos = jdhPromiseHistoryPoMapper.selectList(historyQueryWrapper);
        return promiseHistoryPos.stream().collect(Collectors.groupingBy(JdhPromiseHistoryPo::getPromiseId));
    }

    /**
     * findAppointment
     *
     * @param appointmentId appointmentId
     * @return {@link JdhPromise}
     */
    @Override
    public JdhPromise findAppointment(Long appointmentId) {
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhPromisePo::getAppointmentId, appointmentId)
                .eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode());

        JdhPromisePo po = jdhPromisePoMapper.selectOne(queryWrapper);
        return wrap(po);
    }

    /**
     *
     * @param userPin
     * @param writeOffPwd
     * @return
     */
    @Override
    public JdhPromise findAppointment(String userPin, String writeOffPwd) {
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhPromisePo::getUserPin, userPin)
                .eq(JdhPromisePo::getCodePwd, writeOffPwd)
                .eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode());

        JdhPromisePo po = jdhPromisePoMapper.selectOne(queryWrapper);
        return wrap(po);
    }

    @Override
    public JdhPromise findByCodeId(String codeId) {
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhPromisePo::getCodeId, codeId)
                .eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode());

        JdhPromisePo po = jdhPromisePoMapper.selectOne(queryWrapper);
        return wrap(po);
    }

    /**
     *
     * @param codeId
     * @return
     */
    @Override
    public JdhPromise findByCodeId(String codeId, String verticalCode) {
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhPromisePo::getCodeId, codeId)
                .eq(JdhPromisePo::getVerticalCode, verticalCode)
                .eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode());

        JdhPromisePo po = jdhPromisePoMapper.selectOne(queryWrapper);
        return wrap(po);
    }

    @Override
    public JdhPromise findJdPromiseByOrderItem(String orderItemId, String codeRelationId) {
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhPromisePo::getSourceVoucherId, orderItemId)
                .eq(JdhPromisePo::getCodeId, codeRelationId)
                .eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode());
        JdhPromisePo jdhPromisePo = jdhPromisePoMapper.selectOne(queryWrapper);
        return PromiseConverter.INSTANCE.convert2ProviderPromise(jdhPromisePo);
    }

    /**
     * 刷数据需要,根据订单号删除数据
     * @param orderIds
     * @return
     */
    @Override
    public Integer removeByOrderId(List<Long> orderIds) {
        List<String> param = Lists.newArrayList();
        for (Long orderId : orderIds) {
            param.add(String.valueOf(orderId));
        }
        LambdaQueryWrapper<JdhPromisePo> deleteWrapper = new LambdaQueryWrapper<JdhPromisePo>();
        deleteWrapper.in(JdhPromisePo::getUpdateUser, param);
        int count = jdhPromisePoMapper.delete(deleteWrapper);

        LambdaQueryWrapper<JdhPromiseServiceDetailPo> deleteServiceWrapper = new LambdaQueryWrapper<JdhPromiseServiceDetailPo>();
        deleteServiceWrapper.in(JdhPromiseServiceDetailPo::getUpdateUser, param);
        jdhPromiseServiceDetailPoMapper.delete(deleteServiceWrapper);


        LambdaQueryWrapper<JdhPromisePatientPo> patientPoLambdaQueryWrapper = new LambdaQueryWrapper();
        patientPoLambdaQueryWrapper.in(JdhPromisePatientPo::getUpdateUser, param);
        jdhPromisePatientPoMapper.delete(patientPoLambdaQueryWrapper);
        return count;
    }

    /**
     *
     * @param promisePatientIds
     * @return
     */
    @Override
    public List<JdhPromisePatient> listPatient(Set<Long> promisePatientIds) {

        LambdaQueryWrapper<JdhPromisePatientPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(JdhPromisePatientPo::getPromisePatientId, promisePatientIds)
                .eq(JdhPromisePatientPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhPromisePatientPo> pos = jdhPromisePatientPoMapper.selectList(queryWrapper);

        return PromiseConverter.INSTANCE.convert2PromisePatient(pos);
    }

    /**
     * 根据原单号来新增或者更新promise信息，返回promiseId
     * promiseServiceDetail及promiseExtend不做更新，只新增
     * @param jdhPromise
     * @return promiseId为Key，promisePatientId集合为value
     */
    @Override
    public Map<Long, List<Long>> insertOrUpdateBySourceVoucherId(JdhPromise jdhPromise) {
        Map<Long, List<Long>> result = new HashMap<>();
        List<Long> subRsult = new ArrayList<>();
        JdhPromisePo jdhPromisePo = JdhPromiseInfrastructureConverter.INSTANCE.entity2Po(jdhPromise);
        LambdaQueryWrapper<JdhPromisePo> oneWrapper = new LambdaQueryWrapper<JdhPromisePo>();
        oneWrapper.in(JdhPromisePo::getVoucherId, jdhPromisePo.getVoucherId());
        JdhPromisePo jdhPromisePoFromDb = jdhPromisePoMapper.selectOne(oneWrapper);
        if (Objects.isNull(jdhPromisePoFromDb)) {
            jdhPromisePo.setPromiseId(generateIdFactory.getId());
            JdhBasicPoConverter.initInsertBasicPo(jdhPromisePo);
            jdhPromisePoMapper.insert(jdhPromisePo);
            List<PromiseService> serviceDetailList = jdhPromise.getServices();
            for (PromiseService serviceDetail : serviceDetailList) {
                JdhPromiseServiceDetailPo serviceDetailPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2Po(serviceDetail);
                serviceDetailPo.setPromiseId(jdhPromisePo.getPromiseId());
                jdhPromiseServiceDetailPoMapper.insert(serviceDetailPo);
            }

            List<JdhPromisePatient> patients = jdhPromise.getPatients();
            if(CollUtil.isNotEmpty(patients)){
                for (JdhPromisePatient patient : patients) {
                    JdhPromisePatientPo patientPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2PatientPo(patient, jdhPromise);
                    patientPo.setPromiseId(jdhPromisePo.getPromiseId());
                    patientPo.setPromisePatientId(generateIdFactory.getId());
                    jdhPromisePatientPoMapper.insert(patientPo);
                    subRsult.add(patientPo.getPromisePatientId());
                }
            }

            List<JdhPromiseExtend> promiseExtends = jdhPromise.getPromiseExtends();
            if(CollUtil.isNotEmpty(promiseExtends)){
                for (JdhPromiseExtend promiseExtend : promiseExtends) {
                    JdhPromiseExtendPo extendPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2ExtendPo(promiseExtend);
                    extendPo.setPromiseId(jdhPromisePo.getPromiseId());
                    jdhPromiseExtendPoMapper.insert(extendPo);
                }
            }
            result.put(jdhPromisePo.getPromiseId(), subRsult);
        }else{

            LambdaUpdateWrapper<JdhPromisePo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.setSql("`version` = `version` + 1").eq(JdhPromisePo::getVoucherId, jdhPromisePo.getVoucherId());
            jdhPromisePo.setUpdateTime(new Date());
            jdhPromisePoMapper.update(jdhPromisePo,updateWrapper);

            List<JdhPromisePatient> patients = jdhPromise.getPatients();
            if (CollectionUtils.isNotEmpty(patients)){
                for (JdhPromisePatient patient : patients) {
                    JdhPromisePatientPo patientPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2PatientPo(patient, jdhPromise);
                    LambdaUpdateWrapper<JdhPromisePatientPo> patientWrapper = Wrappers.lambdaUpdate();
                    patientWrapper
                            .eq(JdhPromisePatientPo::getVoucherId,patientPo.getVoucherId())
                            .eq(JdhPromisePatientPo::getPatientId, patientPo.getPatientId());
                    JdhPromisePatientPo promisePatientPoFromDb = jdhPromisePatientPoMapper.selectOne(patientWrapper);
                    if (Objects.nonNull(promisePatientPoFromDb)){
                        patientPo.setPromiseId(jdhPromisePoFromDb.getPromiseId());
                        jdhPromisePatientPoMapper.update(patientPo, patientWrapper);
                        subRsult.add(promisePatientPoFromDb.getPromisePatientId());
                    }else{
                        patientPo.setPromisePatientId(generateIdFactory.getId());
                        patientPo.setPromiseId(jdhPromisePoFromDb.getPromiseId());
                        jdhPromisePatientPoMapper.insert(patientPo);
                        subRsult.add(patientPo.getPromisePatientId());
                    }

                }
            }
            result.put(jdhPromisePoFromDb.getPromiseId(), subRsult);
        }
        return result;
    }

    /**
     * 批量更新履约单
     *
     * @param promise
     * @return
     */
    @Override
    public int update(JdhPromise promise) {
        JdhPromisePo jdhPromisePo = JdhPromiseInfrastructureConverter.INSTANCE.entity2Po(promise);
        LambdaUpdateWrapper<JdhPromisePo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Objects.nonNull(jdhPromisePo.getProvinceCode()), JdhPromisePo::getProvinceCode, jdhPromisePo.getProvinceCode())
                .set(StringUtils.isNotBlank(jdhPromisePo.getProvinceName()), JdhPromisePo::getProvinceName, jdhPromisePo.getProvinceName())
                .set(Objects.nonNull(jdhPromisePo.getCityCode()), JdhPromisePo::getCityCode, jdhPromisePo.getCityCode())
                .set(StringUtils.isNotBlank(jdhPromisePo.getCityName()), JdhPromisePo::getCityName, jdhPromisePo.getCityName())
                .set(Objects.nonNull(jdhPromisePo.getDistrictCode()), JdhPromisePo::getDistrictCode, jdhPromisePo.getDistrictCode())
                .set(StringUtils.isNotBlank(jdhPromisePo.getDistrictName()), JdhPromisePo::getDistrictName, jdhPromisePo.getDistrictName())
                .set(Objects.nonNull(jdhPromisePo.getTownCode()), JdhPromisePo::getTownCode, jdhPromisePo.getTownCode())
                .set(StringUtils.isNotBlank(jdhPromisePo.getTownName()), JdhPromisePo::getTownName, jdhPromisePo.getTownName())
                .set(Objects.nonNull(jdhPromisePo.getPromiseStatus()), JdhPromisePo::getPromiseStatus, jdhPromisePo.getPromiseStatus())
                .set(JdhPromisePo::getUpdateTime, new Date())
                .eq(JdhPromisePo::getPromiseId, jdhPromisePo.getPromiseId())
                .eq(JdhPromisePo::getYn, YNEnum.Y.getValue());
        return jdhPromisePoMapper.update(null, updateWrapper);
    }

    @Override
    public int countCompleteUser(Long voucherId) {
        QueryWrapper<JdhPromisePo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("promise_id");
        queryWrapper.eq("voucher_id", voucherId);
        queryWrapper.eq("yn",  YnStatusEnum.YES.getCode());
        queryWrapper.eq("promise_status", JdhPromiseStatusEnum.COMPLETE.getStatus());

        List<Object> promiseIds = jdhPromisePoMapper.selectObjs(queryWrapper);

        LambdaQueryWrapper<JdhPromisePatientPo> patientPoLambdaQueryWrapper = Wrappers.lambdaQuery();
        patientPoLambdaQueryWrapper.in(JdhPromisePatientPo::getPromiseId, promiseIds)
                .eq(JdhPromisePatientPo::getYn, YnStatusEnum.YES.getCode());
        return jdhPromisePatientPoMapper.selectCount(patientPoLambdaQueryWrapper);
    }

    /**
     * 更新被服务人信息
     *
     * @param item
     * @return
     */
    @Override
    public int updatePromisePatient(JdhPromisePatient item) {
        if(Objects.isNull(item)) {
            return 0;
        }
        JdhPromisePatientPo patientPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2PatientPo2(item);
        LambdaUpdateWrapper<JdhPromisePatientPo> patientWrapper = Wrappers.lambdaUpdate();
        patientWrapper.set(JdhPromisePatientPo::getPatientExt, patientPo.getPatientExt())
                .eq(JdhPromisePatientPo::getPromiseId,patientPo.getPromiseId())
                .eq(JdhPromisePatientPo::getPromisePatientId, patientPo.getPromisePatientId());
        return jdhPromisePatientPoMapper.update(null, patientWrapper);
    }

    /**
     * @param jdhPromiseExtend
     * @return
     */
    @Override
    public int savePromiseExtend(JdhPromiseExtend jdhPromiseExtend) {



        if (Objects.nonNull(jdhPromiseExtend.getId())){

            LambdaUpdateWrapper<JdhPromiseExtendPo> updateWrapper = Wrappers.lambdaUpdate();

            updateWrapper.set(JdhPromiseExtendPo::getValue, jdhPromiseExtend.getValue())
                    .setSql(CommonConstant.UPDATE_TIME_SQL_NOW)
                    .eq(JdhPromiseExtendPo::getId, jdhPromiseExtend.getId());
            jdhPromiseExtendPoMapper.update(null,updateWrapper);

        }

        JdhPromiseExtendPo extendPo = JdhPromiseInfrastructureConverter.INSTANCE.entity2ExtendPo(jdhPromiseExtend);
        extendPo.setUpdateTime(new Date());
        extendPo.setCreateTime(new Date());
        jdhPromiseExtendPoMapper.insert(extendPo);


        return 0;
    }

    /**
     *
     * @param po
     * @return
     */
    private JdhPromise wrap(JdhPromisePo po){
        if (Objects.isNull(po)){
            return null;
        }
        JdhPromise promise = PromiseConverter.INSTANCE.convert2ProviderPromise(po);
        Map<Long, List<JdhPromiseServiceDetailPo>> serviceMap = getServiceMap(Lists.newArrayList(po.getPromiseId()));
        Map<Long, List<JdhPromisePatientPo>> patientMap = getPatientMap(Lists.newArrayList(po.getPromiseId()));
        Map<Long,List<JdhPromiseExtendPo>> extendMap = getExtendMap(Lists.newArrayList(po.getPromiseId()));

        promise = PromiseConverter.INSTANCE.convertPromiseServiceList(promise, serviceMap.get(po.getPromiseId()));
        promise = PromiseConverter.INSTANCE.convertPromisePatientList(promise, patientMap.get(po.getPromiseId()));
        if(MapUtil.isNotEmpty(extendMap)){
            promise = PromiseConverter.INSTANCE.convertPromiseExtendList(promise, extendMap.get(po.getPromiseId()));
        }
        return promise;
    }


    /**
     *
     * @param pos
     * @return
     */
    private List<JdhPromise> wraps(List<JdhPromisePo> pos, PromiseRepQuery query){
        if (CollectionUtils.isEmpty(pos)){
            return null;
        }
        List<Long> promiseIds = pos.stream().map(JdhPromisePo::getPromiseId).collect(Collectors.toList());

        Map<Long, List<JdhPromiseServiceDetailPo>> serviceMap = new HashMap<>();
        if (query.getQueryPromiseService()){
            serviceMap = getServiceMap(promiseIds);
        }

        Map<Long, List<JdhPromisePatientPo>> patientMap = new HashMap<>();
        if (query.getQueryPatients()){
            patientMap = getPatientMap(promiseIds);
        }

        Map<Long,List<JdhPromiseExtendPo>> extendMap = new HashMap<>();
        if (query.getQueryPromiseExtend()){
            extendMap = getExtendMap(promiseIds);
        }

        List<JdhPromise> res = new ArrayList<>();
        for (JdhPromisePo po : pos) {
            JdhPromise promise = PromiseConverter.INSTANCE.convert2ProviderPromise(po);
            if (MapUtil.isNotEmpty(serviceMap) && query.getQueryPromiseService()){
                promise = PromiseConverter.INSTANCE.convertPromiseServiceList(promise, serviceMap.get(po.getPromiseId()));
            }
            if (MapUtil.isNotEmpty(patientMap) && query.getQueryPatients()){
                promise = PromiseConverter.INSTANCE.convertPromisePatientList(promise, patientMap.get(po.getPromiseId()));
            }
            if(MapUtil.isNotEmpty(extendMap) && query.getQueryPromiseExtend()){
                promise = PromiseConverter.INSTANCE.convertPromiseExtendList(promise, extendMap.get(po.getPromiseId()));
            }
            res.add(promise);
        }
        return res;
    }


}
