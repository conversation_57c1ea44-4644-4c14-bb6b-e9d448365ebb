package com.jdh.o2oservice.infrastructure.repository.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhDispatchTeamAngelRelPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName JdhDispatchTeamAngelRelPoMapper
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 15:52
 **/
@Mapper
public interface JdhDispatchTeamAngelRelPoMapper extends BaseMapper<JdhDispatchTeamAngelRelPo> {

    /**
     * 批量插入
     *
     * @param list
     * @return {@link Integer}
     */
    Integer batchInsert(@Param("list") List<JdhDispatchTeamAngelRelPo> list);
}