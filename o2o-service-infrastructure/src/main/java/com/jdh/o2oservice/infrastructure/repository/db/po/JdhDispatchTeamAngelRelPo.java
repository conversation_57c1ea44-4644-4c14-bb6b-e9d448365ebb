package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName JdhDispatchTeamAngelRelPo
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 15:51
 **/
@Data
@TableName(value = "jdh_dispatch_team_angel_rel",autoResultMap = true)
public class JdhDispatchTeamAngelRelPo {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 派单小队ID
     */
    private Long dispatchTeamId;

    /**
     * 服务者ID
     */
    private Long angelId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;

    /**
     * 创建人pin
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人pin
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}