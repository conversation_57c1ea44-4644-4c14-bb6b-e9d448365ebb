package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.RiskAssessmentDetail;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.RiskAssDetailRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.RiskAssessmentDetailQuery;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.RiskAssRepositoryConvert;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhRiskAssessmentDetailMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhRiskAssessmentDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/26
 */
@Component
@Slf4j
public class RiskAssDetailRepositoryImpl implements RiskAssDetailRepository {



    @Resource
    private JdhRiskAssessmentDetailMapper jdhRiskAssessmentDetailMapper;


    /**
     * 保存风险评估细节信息。
     *
     * @param riskAssessmentDetail 风险评估细节对象，包含要保存的详细信息。
     * @return true 如果保存成功，false 否则。
     */
    @Override
    public Boolean save(RiskAssessmentDetail riskAssessmentDetail) {
        log.info("RiskAssDetailRepositoryImpl->save->riskAssessmentDetail:{}", JsonUtil.toJSONString(riskAssessmentDetail));
        JdhRiskAssessmentDetailPo po = RiskAssRepositoryConvert.INSTANCE.convert(riskAssessmentDetail);

        //新增
        if (Objects.isNull(po.getId())){
            JdhBasicPoConverter.initInsertBasicPo(po);
            jdhRiskAssessmentDetailMapper.insert(po);
        }

        //更新
        LambdaUpdateWrapper<JdhRiskAssessmentDetailPo> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();

        lambdaUpdateWrapper
                .set(Objects.nonNull(po.getAssessmentTime()),JdhRiskAssessmentDetailPo::getAssessmentTime,po.getAssessmentTime())
                .set(StringUtils.isNotBlank(po.getAssessmentUser()),JdhRiskAssessmentDetailPo::getAssessmentUser,po.getAssessmentUser())
                .set(StringUtils.isNotBlank(po.getRiskQuestionRes()), JdhRiskAssessmentDetailPo::getRiskQuestionRes, po.getRiskQuestionRes())
                .set(Objects.nonNull(po.getRiskAssessmentStatus()),JdhRiskAssessmentDetailPo::getRiskAssessmentStatus,po.getRiskAssessmentStatus())
                .set(Objects.nonNull(po.getRiskQuestionStatus()),JdhRiskAssessmentDetailPo::getRiskQuestionStatus,po.getRiskQuestionStatus())
                .set(Objects.nonNull(po.getYn()),JdhRiskAssessmentDetailPo::getYn,po.getYn())
                .setSql(CommonConstant.UPDATE_TIME_SQL_NOW)
                .eq(JdhRiskAssessmentDetailPo::getId, po.getId());


        return jdhRiskAssessmentDetailMapper.update(null ,lambdaUpdateWrapper)>0;

    }

    /**
     * 根据查询条件获取风险评估明细列表
     *
     * @param query 查询条件对象
     * @return 风险评估明细列表
     */
    @Override
    public List<RiskAssessmentDetail> queryRiskAssessmentDetailList(RiskAssessmentDetailQuery query) {
        LambdaQueryWrapper<JdhRiskAssessmentDetailPo> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper
                .eq(Objects.nonNull(query.getRiskAssessmentId()), JdhRiskAssessmentDetailPo::getRiskAssessmentId, query.getRiskAssessmentId())
                .eq(Objects.nonNull(query.getRiskQuestionId()),JdhRiskAssessmentDetailPo::getRiskQuestionId,query.getRiskQuestionId())
                .in(CollectionUtils.isNotEmpty(query.getRiskAssessmentIds()),JdhRiskAssessmentDetailPo::getRiskAssessmentId,query.getRiskAssessmentIds())
                .in(CollectionUtils.isNotEmpty(query.getRiskQuestionIds()),JdhRiskAssessmentDetailPo::getRiskQuestionId,query.getRiskQuestionIds())
                .eq(Objects.nonNull(query.getPromisePatientId()),JdhRiskAssessmentDetailPo::getPromisePatientId,query.getPromisePatientId())
                .eq(Objects.nonNull(query.getPromiseId()),JdhRiskAssessmentDetailPo::getPromiseId,query.getPromiseId())
                .in(CollectionUtils.isNotEmpty(query.getPromiseIdList()),JdhRiskAssessmentDetailPo::getPromiseId,query.getPromiseIdList())
                .in(CollectionUtils.isNotEmpty(query.getPromisePatientIds()),JdhRiskAssessmentDetailPo::getPromisePatientId,query.getPromisePatientIds())
                .eq(JdhRiskAssessmentDetailPo::getYn, YnStatusEnum.YES.getCode())
        ;

        List<JdhRiskAssessmentDetailPo> pos = jdhRiskAssessmentDetailMapper.selectList(queryWrapper);
        return RiskAssRepositoryConvert.INSTANCE.convert(pos);
    }

    /**
     * 保存风险评估细节信息。
     *
     * @param riskAssessmentDetails 风险评估细节对象，包含要保存的详细信息。
     * @return true 如果保存成功，false 否则。
     */
    @Override
    public Boolean saveBatch(List<RiskAssessmentDetail> riskAssessmentDetails) {
        List<JdhRiskAssessmentDetailPo> jdhRiskAssessmentDetailPos = RiskAssRepositoryConvert.INSTANCE.convertList(riskAssessmentDetails);
        for (JdhRiskAssessmentDetailPo jdhRiskAssessmentDetailPo : jdhRiskAssessmentDetailPos) {
            JdhBasicPoConverter.initInsertBasicPo(jdhRiskAssessmentDetailPo);
            jdhRiskAssessmentDetailMapper.insert(jdhRiskAssessmentDetailPo);
        }

        return Boolean.TRUE;
    }

    /**
     * @param id
     * @return
     */
    @Override
    public Boolean deleteByIds(Set<Long> id) {
        LambdaUpdateWrapper<JdhRiskAssessmentDetailPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .set(JdhRiskAssessmentDetailPo::getYn, YnStatusEnum.NO.getCode())
                .in(JdhRiskAssessmentDetailPo::getId, id);
        jdhRiskAssessmentDetailMapper.update(null, updateWrapper);
        return Boolean.TRUE;
    }

    /**
     * 修改风险评估单明细状态
     *
     * @param riskAssessmentId
     * @param promisePatientId
     * @param riskAssPatientStatus
     * @return
     */
    @Override
    public boolean updateRiskAssessmentDetailStatus(Long riskAssessmentId, Long promisePatientId, Short riskAssPatientStatus) {
        LambdaUpdateWrapper<JdhRiskAssessmentDetailPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(JdhRiskAssessmentDetailPo::getRiskAssessmentStatus, riskAssPatientStatus)
                .set(JdhRiskAssessmentDetailPo::getUpdateTime, new Date())
                .eq(JdhRiskAssessmentDetailPo::getRiskAssessmentId, riskAssessmentId)
                .eq(JdhRiskAssessmentDetailPo::getPromisePatientId, promisePatientId)
                .eq(JdhRiskAssessmentDetailPo::getYn, YnStatusEnum.YES.getCode());
        jdhRiskAssessmentDetailMapper.update(null, updateWrapper);
        return Boolean.TRUE;
    }
}
