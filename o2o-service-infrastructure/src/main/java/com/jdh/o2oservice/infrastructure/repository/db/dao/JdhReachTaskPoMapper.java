package com.jdh.o2oservice.infrastructure.repository.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachTaskPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @description JdhPromisePoMapper简介
* <AUTHOR>
* @date 2023-12-22 10:35:07
*/
@Mapper
public interface JdhReachTaskPoMapper extends BaseMapper<JdhReachTaskPo> {

    /**
     * 批量插入触达任务
     * @param taskPoList
     * @return
     */
    Integer batchInsert(@Param("tasks") List<JdhReachTaskPo> taskPoList);

}