package com.jdh.o2oservice.infrastructure.rpc;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.limitbuy.domain.request.limit.SkuInfoRequest;
import com.jd.limitbuy.domain.request.limit.StrategyBatchRequest;
import com.jd.limitbuy.domain.response.limit.StrategyBatchResponse;
import com.jd.limitbuy.service.StrategyMetadataService;
import com.jd.medicine.base.common.annotation.LogAndUmp;
import com.jd.tp.common.masterdata.BU;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.core.domain.product.bo.ProductLimitBuyBO;
import com.jdh.o2oservice.core.domain.product.bo.ProductLimitBuyStrategyBO;
import com.jdh.o2oservice.core.domain.product.rpc.ProductLimitbuyRpc;
import com.jdh.o2oservice.core.domain.product.rpc.param.SkuInfoRequestParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName:ProductLimitbuyRpcImpl
 * @Description: 商品限购信息查询
 * @Author: yaoqinghai
 * @Date: 2023/12/11 19:00
 * @Vserion: 1.0
 **/
@Slf4j
@Service
public class ProductLimitbuyRpcImpl implements ProductLimitbuyRpc {

    @Resource
    private StrategyMetadataService strategyMetadataService;

    private static final Integer COMMON_CLIENT = 999;

    private static final Integer COMMON_FLOW_ID = 119;


    private static final String COMMON_EMPTY_SMARK = "";

    /**
     * 查询商品限购信息
     *
     * @return
     */
    @Override
    @LogAndUmp(jKey = "com.jdh.o2oservice.infrastructure.rpc.ProductLimitbuyRpcImpl.getProductLimitStrategy", errorReturnJsfResult = false)
    public List<ProductLimitBuyBO> getProductLimitStrategy(List<SkuInfoRequestParam> skuInfoRequestParams, String pin) {
        List<ProductLimitBuyBO> limitBuyVOList = Lists.newArrayList();

        StrategyBatchRequest strategyBatchRequest = new StrategyBatchRequest();
        if (CollectionUtils.isEmpty(skuInfoRequestParams)) {
            throw new SystemException(BusinessErrorCode.GOODS_NOT_EXISTS);
        }

        List<SkuInfoRequest> skuInfoRequestList = Lists.newArrayList();
        for (SkuInfoRequestParam skuInfoRequestBO : skuInfoRequestParams) {
            SkuInfoRequest skuInfoRequest = new SkuInfoRequest();
            skuInfoRequest.setSkuId(skuInfoRequestBO.getSkuId());
            skuInfoRequest.setSkuName(skuInfoRequestBO.getSkuName());
            skuInfoRequest.setNum(Objects.isNull(skuInfoRequestBO.getNum()) ? 1 : skuInfoRequestBO.getNum());

            skuInfoRequestList.add(skuInfoRequest);
        }
        strategyBatchRequest.setSkuInfoRequestList(skuInfoRequestList);
        strategyBatchRequest.setPin(pin);

        //组装限购通用参数
        swapProductLimitCommonParam(strategyBatchRequest);

        //调用限购中台
        StrategyBatchResponse strategyBatch = strategyMetadataService.getStrategyBatch(strategyBatchRequest);
        log.info("ProductLimitbuyRpcImpl getProductLimitStrategy strategyBatchRequest={}, strategyBatch={}", JSON.toJSONString(strategyBatchRequest), JSON.toJSONString(strategyBatch));
        if (Objects.isNull(strategyBatch) || !strategyBatch.isResultFlag()) {
            log.error("[ProductLimitbuyRpcImpl->getProductLimitStrategy]查询限购中台失败!param={}, strategyBatch={}",
                    JSON.toJSONString(skuInfoRequestList), JSON.toJSONString(strategyBatch));
            return limitBuyVOList;
        }


        Map<String, Map<String, String>> resultExtMap = strategyBatch.getResultExtMap();
        if (MapUtils.isEmpty(resultExtMap)) {
            log.error("[ProductLimitbuyRpcImpl->getProductLimitStrategy]查询限购中台，无限购信息! pin={} param={}, strategyBatch={}",
                    pin, JSON.toJSONString(skuInfoRequestParams), JSON.toJSONString(strategyBatch));
            return limitBuyVOList;
        }
        for (SkuInfoRequestParam infoRequestParam : skuInfoRequestParams) {
            ProductLimitBuyBO limitBuyVO = new ProductLimitBuyBO();

            Map<String, String> stringStringMap = resultExtMap.get(infoRequestParam.getSkuId());
            String limitPreOrderNum = stringStringMap.get("limitPreOrderNum");
            String limitMinNum = stringStringMap.get("limitMinNum");
            String mergeMaxBuyNum = stringStringMap.get("mergeMaxBuyNum");

            limitBuyVO.setLimitMinNum(StringUtils.isEmpty(limitMinNum) ? null : Integer.valueOf(limitMinNum));
            limitBuyVO.setLimitPreOrderNum(StringUtils.isEmpty(limitPreOrderNum) ? null : Integer.valueOf(limitPreOrderNum));
            limitBuyVO.setMergeMaxBuyNum(StringUtils.isEmpty(mergeMaxBuyNum) ? null : Integer.valueOf(mergeMaxBuyNum));

            limitBuyVOList.add(limitBuyVO);
        }
         return limitBuyVOList;
    }

    /**
     * 组装限购通用参数
     *
     * @param strategyBatchRequest
     */
    private void swapProductLimitCommonParam(StrategyBatchRequest strategyBatchRequest) {
        strategyBatchRequest.setClient(COMMON_CLIENT);
        strategyBatchRequest.setBuId(CommonConstant.BU_ID);
        strategyBatchRequest.setFlowId(COMMON_FLOW_ID);
        strategyBatchRequest.setSmark(COMMON_EMPTY_SMARK);
        strategyBatchRequest.setTags(Maps.newHashMap());
    }

}
