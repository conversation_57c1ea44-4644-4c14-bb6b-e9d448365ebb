package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseExtend;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.model.PromiseService;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromiseExtendPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePatientPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromiseServiceDetailPo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * JdhPromiseInfrastructureConverter
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Mapper
public interface JdhPromiseInfrastructureConverter {

    /**
     * JdhPromiseInfrastructureConverter
     */
    JdhPromiseInfrastructureConverter INSTANCE = Mappers.getMapper(JdhPromiseInfrastructureConverter.class);

    /**
     * 实体2 PO
     *
     * @param jdhPromise jdhPromise
     * @return {@link JdhPromisePo}
     */
    default JdhPromisePo entity2Po(JdhPromise jdhPromise){
        {
            if ( jdhPromise == null ) {
                return null;
            }

            JdhPromisePo jdhPromisePo = new JdhPromisePo();
            jdhPromisePo.setId(jdhPromise.getId());
            jdhPromisePo.setVerticalCode( jdhPromise.getVerticalCode() );
            jdhPromisePo.setServiceType( jdhPromise.getServiceType() );
            jdhPromisePo.setPromiseId( jdhPromise.getPromiseId() );
            jdhPromisePo.setVoucherId( jdhPromise.getVoucherId() );
            jdhPromisePo.setSourceVoucherId( jdhPromise.getSourceVoucherId() );
            jdhPromisePo.setSerialNum( jdhPromise.getSerialNum() );
            jdhPromisePo.setFreeze(jdhPromise.getFreeze());
            jdhPromisePo.setPromiseStatus(jdhPromise.getPromiseStatus() );
            jdhPromisePo.setPromiseType( jdhPromise.getPromiseType() );
            jdhPromisePo.setUserPin(jdhPromise.getUserPin());
            if ( jdhPromise.getExpireDate() != null ) {
                jdhPromisePo.setExpireDate( Date.from( jdhPromise.getExpireDate().toInstant( ZoneOffset.UTC ) ) );
            }
            jdhPromisePo.setCodeId( jdhPromise.getCodeId() );
            jdhPromisePo.setCode( jdhPromise.getCode() );
            jdhPromisePo.setCodePwd( jdhPromise.getCodePwd() );
            jdhPromisePo.setChannelNo( jdhPromise.getChannelNo() );
            jdhPromisePo.setAppointmentId( jdhPromise.getAppointmentId() );
            jdhPromisePo.setChannelAppointmentId( jdhPromise.getChannelAppointmentId() );
            if (Objects.nonNull(jdhPromise.getAppointmentTime())){
                if (Objects.nonNull(jdhPromise.getAppointmentTime().getAppointmentStartTime())){
                    jdhPromisePo.setAppointmentStartTime(TimeUtils.localDateTimeToDate(jdhPromise.getAppointmentTime().getAppointmentStartTime()));
                }
                if (Objects.nonNull(jdhPromise.getAppointmentTime().getAppointmentEndTime())){
                    jdhPromisePo.setAppointmentEndTime(TimeUtils.localDateTimeToDate(jdhPromise.getAppointmentTime().getAppointmentEndTime()));
                }
                if (Objects.nonNull(jdhPromise.getAppointmentTime().getDateType())){
                    jdhPromisePo.setDateType(jdhPromise.getAppointmentTime().getDateType());
                }
                if (Objects.nonNull(jdhPromise.getAppointmentTime().getIsImmediately())){
                   if (jdhPromise.getAppointmentTime().getIsImmediately()){
                       jdhPromisePo.setImmediately(YnStatusEnum.YES.getCode());
                   }else{
                       jdhPromisePo.setImmediately(YnStatusEnum.NO.getCode());
                   }
                }
            }
            if (Objects.nonNull(jdhPromise.getStore())){
                if (StringUtils.isNotBlank(jdhPromise.getStore().getStoreId())){
                    jdhPromisePo.setStoreId(jdhPromise.getStore().getStoreId());
                }
                if (StringUtils.isNotBlank(jdhPromise.getStore().getStoreAddr())){
                    jdhPromisePo.setStoreAddr(jdhPromise.getStore().getStoreAddr());
                }
                if (StringUtils.isNotBlank(jdhPromise.getStore().getStoreName())){
                    jdhPromisePo.setStoreName(jdhPromise.getStore().getStoreName());
                }
                if (StringUtils.isNotBlank(jdhPromise.getStore().getStorePhone())){
                    jdhPromisePo.setStorePhone(jdhPromise.getStore().getStorePhone());
                }
                //省
                if (Objects.nonNull(jdhPromise.getStore().getProvinceCode())){
                    jdhPromisePo.setProvinceCode(jdhPromise.getStore().getProvinceCode());
                }
                if (StringUtils.isNotBlank(jdhPromise.getStore().getProvinceName())){
                    jdhPromisePo.setProvinceName(jdhPromise.getStore().getProvinceName());
                }
                //市
                if (Objects.nonNull(jdhPromise.getStore().getCityCode())){
                    jdhPromisePo.setCityCode(jdhPromise.getStore().getCityCode());
                }
                if (StringUtils.isNotBlank(jdhPromise.getStore().getCityName())){
                    jdhPromisePo.setCityName(jdhPromise.getStore().getCityName());
                }
                //区
                if (Objects.nonNull(jdhPromise.getStore().getDistrictCode())){
                    jdhPromisePo.setDistrictCode(jdhPromise.getStore().getDistrictCode());
                }
                if (StringUtils.isNotBlank(jdhPromise.getStore().getDistrictName())){
                    jdhPromisePo.setDistrictName(jdhPromise.getStore().getDistrictName());
                }
                //街道
                if (Objects.nonNull(jdhPromise.getStore().getTownCode())){
                    jdhPromisePo.setTownCode(jdhPromise.getStore().getTownCode());
                }
                if (StringUtils.isNotBlank(jdhPromise.getStore().getTownName())){
                    jdhPromisePo.setTownName(jdhPromise.getStore().getTownName());
                }
            }
            if(Objects.nonNull(jdhPromise.getBranch())){
                jdhPromisePo.setBranch(jdhPromise.getBranch());
            }
            if(Objects.nonNull(jdhPromise.getVersion())){
                jdhPromisePo.setVersion(jdhPromise.getVersion());
            }
            if(Objects.nonNull(jdhPromise.getYn())){
                jdhPromisePo.setYn(jdhPromise.getYn());
            }
            if(Objects.nonNull(jdhPromise.getUpdateTime())){
                jdhPromisePo.setUpdateTime(jdhPromise.getUpdateTime());
            }
            if(Objects.nonNull(jdhPromise.getCreateTime())){
                jdhPromisePo.setCreateTime(jdhPromise.getCreateTime());
            }
            // 预约人电信
            jdhPromisePo.setAppointmentPhone(jdhPromise.getAppointmentPhone());
            jdhPromisePo.setAppointmentPhoneIndex(jdhPromise.getAppointmentPhone());
            jdhPromisePo.setCreateUser(jdhPromise.getCreateUser());
            jdhPromisePo.setUpdateUser(jdhPromise.getUpdateUser());

            return jdhPromisePo;
        }
    }

    /**
     * 实体2 PO
     *
     * @param jdhPromiseList jdhPromiseList
     * @return {@link JdhPromisePo}
     */
    List<JdhPromisePo> entity2PoList(List<JdhPromise> jdhPromiseList);

    /**
     * 实体2 PO
     *
     * @param promiseService jdhPromiseServiceDetail
     * @return {@link JdhPromiseServiceDetailPo}
     */
    JdhPromiseServiceDetailPo entity2Po(PromiseService promiseService);

    /**
     *
     * @param patient
     * @return
     */
    default JdhPromisePatientPo entity2PatientPo(JdhPromisePatient patient, JdhPromise jdhPromise){
        if (Objects.isNull(patient)){
            return null;
        }
        JdhPromisePatientPo patientPo = new JdhPromisePatientPo();
        patientPo.setId(patient.getId());
        if (Objects.nonNull(patient.getUserName())){
            patientPo.setUserName(patient.getUserName().getName());
            patientPo.setUserNameIndex(patient.getUserName().getName());
        }
        if (Objects.nonNull(patient.getPhoneNumber())){
            patientPo.setUserPhone(patient.getPhoneNumber().getPhone());
            patientPo.setUserPhoneIndex(patient.getPhoneNumber().getPhone());
        }
        if (Objects.nonNull(patient.getCredentialNum()) && StringUtils.isNotBlank(patient.getPhoneNumber().getPhone())){
            patientPo.setUserCredentialType(patient.getCredentialNum().getCredentialType());
            patientPo.setUserCredentialNo(patient.getCredentialNum().getCredentialNo());
            patientPo.setUserCredentialNoIndex(patient.getCredentialNum().getCredentialNo());
        }
        if (Objects.nonNull(patient.getBirthday())){
            patientPo.setBirthday(patient.getBirthday().getBirth());
        }
        patientPo.setRelativesType(patient.getRelativesType());
        patientPo.setUserGender(patient.getGender());
        patientPo.setMarriage(patient.getMarriage());
        patientPo.setUserPin( patient.getUserPin() );
        patientPo.setPatientId(patient.getPatientId() );
        // 冗余字段
        patientPo.setVerticalCode(jdhPromise.getVerticalCode());
        patientPo.setServiceType(jdhPromise.getServiceType());
        patientPo.setPromiseId(jdhPromise.getPromiseId());
        patientPo.setPromisePatientId(patient.getPromisePatientId());
        patientPo.setVoucherId(jdhPromise.getVoucherId());
        patientPo.setSourceVoucherId(jdhPromise.getSourceVoucherId());

        if(Objects.nonNull(patient.getBranch())){
            patientPo.setBranch(patient.getBranch());
        }
        if(Objects.nonNull(patient.getVersion())){
            patientPo.setVersion(jdhPromise.getVersion());
        }
        if(Objects.nonNull(patient.getYn())){
            patientPo.setYn(patient.getYn());
        }
        if(Objects.nonNull(patient.getUpdateTime())){
            patientPo.setUpdateTime(patient.getUpdateTime());
        }
        if(Objects.nonNull(patient.getCreateTime())){
            patientPo.setCreateTime(patient.getCreateTime());
        }

        return patientPo;

    }


    JdhPromiseExtendPo entity2ExtendPo(JdhPromiseExtend promiseExtend);
}
