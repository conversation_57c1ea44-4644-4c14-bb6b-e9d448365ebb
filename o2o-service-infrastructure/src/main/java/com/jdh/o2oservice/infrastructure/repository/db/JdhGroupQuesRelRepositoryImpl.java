package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRel;
import com.jdh.o2oservice.core.domain.product.model.JdhGroupQuesRelIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhGroupQuesRelRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhGroupQuesRelConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhGroupQuesRelMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhGroupQuesRelPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/25
 * @description 题库
 */
@Repository
@Slf4j
public class JdhGroupQuesRelRepositoryImpl implements JdhGroupQuesRelRepository {

    @Autowired
    private JdhGroupQuesRelMapper jdhGroupQuesRelMapper;

    @Override
    public JdhGroupQuesRel find(JdhGroupQuesRelIdentifier jdhGroupQuesRelIdentifier) {
        return null;
    }

    @Override
    public int remove(JdhGroupQuesRel entity) {
        return 0;
    }

    @Override
    public int save(JdhGroupQuesRel entity) {
        JdhGroupQuesRelPo jdhGroupQuesRelPo = JdhGroupQuesRelConverter.INSTANCE.toJdhGroupQuesRelPo(entity);
        return jdhGroupQuesRelMapper.insert(jdhGroupQuesRelPo);
    }

    @Override
    public JdhGroupQuesRel findByCodeAndItemId(Long serviceItemId, String groupCode, String quesCode) {
        LambdaQueryWrapper<JdhGroupQuesRelPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JdhGroupQuesRelPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.eq(JdhGroupQuesRelPo::getServiceItemId,serviceItemId);
        queryWrapper.eq(JdhGroupQuesRelPo::getGroupCode,groupCode);
        queryWrapper.eq(JdhGroupQuesRelPo::getQuesCode,quesCode);
        JdhGroupQuesRelPo jdhGroupQuesRelPo = jdhGroupQuesRelMapper.selectOne(queryWrapper);
        return JdhGroupQuesRelConverter.INSTANCE.toJdhGroupQuesRel(jdhGroupQuesRelPo);
    }

    @Override
    public List<JdhGroupQuesRel> findByItemId(Long serviceItemId) {
        LambdaQueryWrapper<JdhGroupQuesRelPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JdhGroupQuesRelPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.eq(JdhGroupQuesRelPo::getServiceItemId,serviceItemId);
        List<JdhGroupQuesRelPo> jdhGroupQuesRelPos = jdhGroupQuesRelMapper.selectList(queryWrapper);
        return JdhGroupQuesRelConverter.INSTANCE.toJdhGroupQuesRels(jdhGroupQuesRelPos);
    }

    @Override
    public Boolean update(JdhGroupQuesRel jdhGroupQuesRel) {
        //where条件
        LambdaUpdateWrapper<JdhGroupQuesRelPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(JdhGroupQuesRelPo::getServiceItemId,jdhGroupQuesRel.getServiceItemId());
        updateWrapper.eq(JdhGroupQuesRelPo::getGroupCode,jdhGroupQuesRel.getGroupCode());
        updateWrapper.eq(JdhGroupQuesRelPo::getQuesCode,jdhGroupQuesRel.getQuesCode());

        //修改数据
        JdhGroupQuesRelPo jdhGroupQuesRelPo = JdhGroupQuesRelConverter.INSTANCE.toJdhGroupQuesRelPo(jdhGroupQuesRel);
        jdhGroupQuesRelPo.setUpdateTime(new Date());
        updateWrapper.setSql("`version` = `version` + 1");

        return jdhGroupQuesRelMapper.update(jdhGroupQuesRelPo,updateWrapper)>0;
    }
}
