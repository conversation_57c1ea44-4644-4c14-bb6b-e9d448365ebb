<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdh.o2oservice.infrastructure.repository.db.dao.JdhDispatchTeamSkillRelPoMapper">

    <insert id="batchInsert">
        INSERT INTO jdh_dispatch_team_skill_rel
        (dispatch_team_id, angel_skill_code, angel_skill_name, service_group_id, item_type, version, branch,
        create_user, create_time, update_user, update_time, yn)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.dispatchTeamId}, #{item.angelSkillCode}, #{item.angelSkillName}, #{item.serviceGroupId}, #{item.itemType},
            #{item.version}, #{item.branch}, #{item.createUser}, #{item.createTime}, #{item.updateUser}, #{item.updateTime}, #{item.yn})
        </foreach>
    </insert>
</mapper>