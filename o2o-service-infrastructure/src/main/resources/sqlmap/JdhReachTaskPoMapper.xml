<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdh.o2oservice.infrastructure.repository.db.dao.JdhReachTaskPoMapper">

  <insert id="batchInsert" parameterType="com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachTaskPo">
    <!--@mbg.generated-->
      insert into jdh_reach_task (task_id, domain_code,
    aggregate_code, event_code,
    aggregate_id, status, type,
    select_user_function_id,  template_id,
    yn, create_time, update_time, event_id, event_body, trigger_time, delay, trigger_id)
        values
        <foreach collection="tasks" item="task"  separator=",">
          (#{task.taskId,jdbcType=BIGINT}, #{task.domainCode,jdbcType=VARCHAR},
          #{task.aggregateCode,jdbcType=VARCHAR}, #{task.eventCode,jdbcType=VARCHAR},
          #{task.aggregateId,jdbcType=VARCHAR}, #{task.status,jdbcType=INTEGER},#{task.type,jdbcType=INTEGER},
          #{task.selectUserFunctionId,jdbcType=VARCHAR}, #{task.templateId,jdbcType=BIGINT},
          #{task.yn,jdbcType=INTEGER}, #{task.createTime,jdbcType=TIMESTAMP}, #{task.updateTime,jdbcType=TIMESTAMP},
          #{task.eventId,jdbcType=BIGINT},
          #{task.eventBody,jdbcType=VARCHAR},
          #{task.triggerTime, jdbcType=TIMESTAMP},
          #{task.delay, jdbcType=INTEGER},
          #{task.triggerId,jdbcType=BIGINT}
          )
        </foreach>
  </insert>

</mapper>