<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdh.o2oservice.infrastructure.repository.db.dao.JdhDispatchTeamAngelRelPoMapper">

    <insert id="batchInsert">
        INSERT INTO jdh_dispatch_team_angel_rel
        (dispatch_team_id, angel_id, version, create_user, create_time, update_user, update_time, yn)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.dispatchTeamId}, #{item.angelId}, #{item.version}, #{item.createUser}, #{item.createTime}, #{item.updateUser}, #{item.updateTime}, #{item.yn})
        </foreach>
    </insert>
</mapper>